/**
 * 验证工具函数 - 完全类型安全
 */

// 验证结果 - 完全类型安全
export interface ValidationResult {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly status: 'success' | 'warning' | 'error' | 'validating'
}

// 验证规则 - 完全类型安全
export interface ValidationRule {
  readonly type: 'required' | 'pattern' | 'min' | 'max' | 'minLength' | 'maxLength' | 'email' | 'url' | 'number' | 'integer' | 'phone' | 'custom'
  readonly message: string
  readonly value?: unknown
  readonly trigger?: 'blur' | 'change' | 'input' | 'submit'
}

// 验证单个值
export function validateValue(value: unknown, rules: readonly ValidationRule[]): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  for (const rule of rules) {
    const result = validateSingleRule(value, rule)
    if (!result.isValid) {
      if (rule.type === 'required') {
        errors.push(result.message)
      } else {
        warnings.push(result.message)
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    status: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'success'
  }
}

// 验证单个规则
function validateSingleRule(value: unknown, rule: ValidationRule): { isValid: boolean; message: string } {
  switch (rule.type) {
    case 'required':
      return validateRequired(value, rule.message)
    case 'pattern':
      return validatePattern(value, rule.value as string, rule.message)
    case 'min':
      return validateMin(value, rule.value as number, rule.message)
    case 'max':
      return validateMax(value, rule.value as number, rule.message)
    case 'minLength':
      return validateMinLength(value, rule.value as number, rule.message)
    case 'maxLength':
      return validateMaxLength(value, rule.value as number, rule.message)
    case 'email':
      return validateEmail(value, rule.message)
    case 'url':
      return validateUrl(value, rule.message)
    case 'number':
      return validateNumber(value, rule.message)
    case 'integer':
      return validateInteger(value, rule.message)
    case 'phone':
      return validatePhone(value, rule.message)
    default:
      return { isValid: true, message: '' }
  }
}

// 必填验证
function validateRequired(value: unknown, message: string): { isValid: boolean; message: string } {
  const isValid = value !== null && value !== undefined && value !== ''
  return { isValid, message: isValid ? '' : message }
}

// 正则表达式验证
function validatePattern(value: unknown, pattern: string, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'string') {
    return { isValid: true, message: '' }
  }
  
  try {
    const regex = new RegExp(pattern)
    const isValid = regex.test(value)
    return { isValid, message: isValid ? '' : message }
  } catch {
    return { isValid: true, message: '' }
  }
}

// 最小值验证
function validateMin(value: unknown, min: number, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'number') {
    return { isValid: true, message: '' }
  }
  
  const isValid = value >= min
  return { isValid, message: isValid ? '' : message }
}

// 最大值验证
function validateMax(value: unknown, max: number, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'number') {
    return { isValid: true, message: '' }
  }
  
  const isValid = value <= max
  return { isValid, message: isValid ? '' : message }
}

// 最小长度验证
function validateMinLength(value: unknown, minLength: number, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'string') {
    return { isValid: true, message: '' }
  }
  
  const isValid = value.length >= minLength
  return { isValid, message: isValid ? '' : message }
}

// 最大长度验证
function validateMaxLength(value: unknown, maxLength: number, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'string') {
    return { isValid: true, message: '' }
  }
  
  const isValid = value.length <= maxLength
  return { isValid, message: isValid ? '' : message }
}

// 邮箱验证
function validateEmail(value: unknown, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'string') {
    return { isValid: true, message: '' }
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  const isValid = emailRegex.test(value)
  return { isValid, message: isValid ? '' : message }
}

// URL验证
function validateUrl(value: unknown, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'string') {
    return { isValid: true, message: '' }
  }
  
  try {
    new URL(value)
    return { isValid: true, message: '' }
  } catch {
    return { isValid: false, message }
  }
}

// 数字验证
function validateNumber(value: unknown, message: string): { isValid: boolean; message: string } {
  if (typeof value === 'number') {
    return { isValid: true, message: '' }
  }
  
  if (typeof value === 'string') {
    const num = parseFloat(value)
    const isValid = !isNaN(num)
    return { isValid, message: isValid ? '' : message }
  }
  
  return { isValid: false, message }
}

// 整数验证
function validateInteger(value: unknown, message: string): { isValid: boolean; message: string } {
  if (typeof value === 'number') {
    const isValid = Number.isInteger(value)
    return { isValid, message: isValid ? '' : message }
  }
  
  if (typeof value === 'string') {
    const num = parseInt(value, 10)
    const isValid = !isNaN(num) && Number.isInteger(num)
    return { isValid, message: isValid ? '' : message }
  }
  
  return { isValid: false, message }
}

// 手机号验证
function validatePhone(value: unknown, message: string): { isValid: boolean; message: string } {
  if (typeof value !== 'string') {
    return { isValid: true, message: '' }
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/
  const isValid = phoneRegex.test(value)
  return { isValid, message: isValid ? '' : message }
}

// 批量验证
export function validateBatch(data: Record<string, unknown>, rules: Record<string, readonly ValidationRule[]>): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {}
  
  for (const [field, fieldRules] of Object.entries(rules)) {
    const value = data[field]
    results[field] = validateValue(value, fieldRules)
  }
  
  return results
}

// 检查表单是否有效
export function isFormValid(results: Record<string, ValidationResult>): boolean {
  return Object.values(results).every(result => result.isValid)
}

// 获取所有错误信息
export function getAllErrors(results: Record<string, ValidationResult>): string[] {
  return Object.values(results).flatMap(result => result.errors)
}

// 获取所有警告信息
export function getAllWarnings(results: Record<string, ValidationResult>): string[] {
  return Object.values(results).flatMap(result => result.warnings)
} 