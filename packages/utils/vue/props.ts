import type { PropType } from '@vue/runtime-core'

// Element Plus 风格的 PropType 定义工具
export const definePropType = <T>(val: unknown): PropType<T> => val as PropType<T>

// 组件尺寸常量
export const componentSizes = ['large', 'default', 'small'] as const
export type ComponentSize = typeof componentSizes[number]

// 组件类型常量
export const componentTypes = ['primary', 'success', 'warning', 'danger', 'info'] as const
export type ComponentType = typeof componentTypes[number]

// 可复用的 Props 定义
export const sizeProp = {
    type: String,
    values: componentSizes,
    default: 'default'
} as const

export const typeProp = {
    type: String,
    values: componentTypes,
    default: ''
} as const

export const disabledProp = {
    type: Boolean,
    default: false
} as const

// 通用组件 Props 接口
export interface ComponentProps {
    size?: ComponentSize
    disabled?: boolean
}

// 带类型的组件 Props 接口
export interface TypedComponentProps extends ComponentProps {
    type?: ComponentType
}

/**
 * 简化的 Props 构建工具
 */

// 基础 Prop 配置接口
export interface PropConfig {
    type?: unknown
    default?: unknown
    required?: boolean
    values?: readonly unknown[]
    validator?: (value: unknown) => boolean
}

/**
 * 构建单个 prop 配置
 * @param config prop 配置
 * @returns Vue prop 定义
 */
export const buildProp = (config: PropConfig | unknown): unknown => {
    // 如果传入的不是配置对象，直接返回
    if (typeof config !== 'object' || config === null) {
        return config
    }

    // 如果已经是 Vue prop 格式，直接返回
    if (!('type' in config || 'default' in config || 'required' in config || 'values' in config)) {
        return config
    }

    const propConfig = config as PropConfig
    const prop: Record<string, unknown> = {}

    // 设置类型
    if (propConfig.type !== undefined) {
        prop.type = propConfig.type
    }

    // 设置默认值
    if (propConfig.default !== undefined) {
        prop.default = propConfig.default
    }

    // 设置是否必需
    if (propConfig.required !== undefined) {
        prop.required = propConfig.required
    }

    // 设置验证器（包含 values 验证）
    if (propConfig.values || propConfig.validator) {
        prop.validator = (value: unknown) => {
            // values 验证
            if (propConfig.values && !propConfig.values.includes(value)) {
                console.warn(`Invalid prop value: ${value}. Expected one of: ${propConfig.values.join(', ')}`)
                return false
            }

            // 自定义验证器
            if (propConfig.validator && !propConfig.validator(value)) {
                return false
            }

            return true
        }
    }

    return prop
}

/**
 * 构建 props 对象
 * @param props props 配置对象
 * @returns Vue props 定义
 */
export const buildProps = <T extends Record<string, unknown>>(props: T): T => {
    const result = {} as Record<string, unknown>

    for (const [key, config] of Object.entries(props)) {
        result[key] = buildProp(config)
    }

    return result as T
}

// Props 类型提取工具
export type ExtractPropTypes<T> = {
    [K in keyof T]: T[K] extends { type: infer U }
    ? U extends new (...args: unknown[]) => infer R
    ? R
    : U extends readonly (infer V)[]
    ? V
    : unknown
    : unknown
}