import type { SFCWithInstall } from '../types'
import { withInstall } from './install'
import type { Component, App } from '@vue/runtime-core'

/**
 * 组件名称生成器
 * 根据组件代码生成标准的组件名称
 * 
 * @param code - 组件代码（如 'button', 'input', 'text-area'）
 * @param prefix - 前缀，默认 'Crf'
 * @returns 组件名称（如 'CrfButton', 'CrfInput', 'CrfTextArea'）
 * 
 * @example
 * generateComponentName('button') => 'CrfButton'
 * generateComponentName('text-area') => 'CrfTextArea'
 * generateComponentName('input', 'Custom') => 'CustomInput'
 */
export function generateComponentName(
    code: string,
    prefix = 'Crf'
): string {
    // 将 kebab-case 转换为 PascalCase
    const pascalCode = code
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('')

    return `${prefix}${pascalCode}`
}

/**
 * 组件代码生成器
 * 根据组件名称生成标准的组件代码
 * 
 * @param name - 组件名称（如 'CrfButton', 'CrfInput', 'CrfTextArea'）
 * @param prefix - 前缀，默认 'Crf'
 * @returns 组件代码（如 'crf-button', 'crf-input', 'crf-text-area'）
 * 
 * @example
 * generateComponentCode('CrfButton') => 'crf-button'
 * generateComponentCode('CrfTextArea') => 'crf-text-area'
 * generateComponentCode('CustomInput', 'Custom') => 'custom-input'
 */
export function generateComponentCode(
    name: string,
    prefix = 'Crf'
): string {
    // 移除前缀
    const baseName = name.replace(new RegExp(`^${prefix}`), '')

    // 将 PascalCase 转换为 kebab-case
    const kebabCode = baseName
        .replace(/([A-Z])/g, '-$1')
        .toLowerCase()
        .replace(/^-/, '')

    // 添加前缀
    const prefixKebab = prefix.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '')

    return `${prefixKebab}-${kebabCode}`
}

/**
 * 验证组件名称格式
 * 
 * @param name - 组件名称
 * @param prefix - 前缀，默认 'Crf'
 * @returns 是否为有效的组件名称格式
 * 
 * @example
 * isValidComponentName('CrfButton') => true
 * isValidComponentName('ElButton') => false
 * isValidComponentName('button') => false
 */
export function isValidComponentName(
    name: string,
    prefix = 'Crf'
): boolean {
    // 检查是否以指定前缀开头
    if (!name.startsWith(prefix)) {
        return false
    }

    // 检查是否符合 PascalCase 命名规范
    const pascalRegex = new RegExp(`^${prefix}[A-Z][a-zA-Z0-9]*$`)
    return pascalRegex.test(name)
}

/**
 * 验证组件代码格式
 * 
 * @param code - 组件代码
 * @param prefix - 前缀，默认 'crf'
 * @returns 是否为有效的组件代码格式
 * 
 * @example
 * isValidComponentCode('crf-button') => true
 * isValidComponentCode('el-button') => false
 * isValidComponentCode('Button') => false
 */
export function isValidComponentCode(
    code: string,
    prefix = 'crf'
): boolean {
    // 检查是否以指定前缀开头
    if (!code.startsWith(prefix)) {
        return false
    }

    // 检查是否符合 kebab-case 命名规范
    const kebabRegex = new RegExp(`^${prefix}(-[a-z0-9]+)*$`)
    return kebabRegex.test(code)
}

/**
 * 获取组件基础名称
 * 
 * @param name - 组件名称
 * @param prefix - 前缀，默认 'Crf'
 * @returns 基础名称
 * 
 * @example
 * getBaseComponentName('CrfButton') => 'Button'
 * getBaseComponentName('CrfTextArea') => 'TextArea'
 */
export function getBaseComponentName(
    name: string,
    prefix = 'Crf'
): string {
    return name.replace(new RegExp(`^${prefix}`), '')
}

/**
 * 获取组件基础代码
 * 
 * @param code - 组件代码
 * @param prefix - 前缀，默认 'crf'
 * @returns 基础代码
 * 
 * @example
 * getBaseComponentCode('crf-button') => 'button'
 * getBaseComponentCode('crf-text-area') => 'text-area'
 */
export function getBaseComponentCode(
    code: string,
    prefix = 'crf'
): string {
    return code.replace(new RegExp(`^${prefix}-`), '')
}

/**
 * 组件元数据接口
 */
export interface ComponentMetadata {
    name: string
    code: string
    baseName: string
    baseCode: string
    prefix: string
    isValid: boolean
}

/**
 * 解析组件元数据
 * 
 * @param nameOrCode - 组件名称或代码
 * @param prefix - 前缀，默认 'Crf'
 * @returns 组件元数据
 * 
 * @example
 * parseComponentMetadata('CrfButton') => {
 *   name: 'CrfButton',
 *   code: 'crf-button',
 *   baseName: 'Button',
 *   baseCode: 'button',
 *   prefix: 'Crf',
 *   isValid: true
 * }
 */
export function parseComponentMetadata(
    nameOrCode: string,
    prefix = 'Crf'
): ComponentMetadata {
    let name: string
    let code: string

    // 判断输入是名称还是代码
    if (nameOrCode.includes('-')) {
        // 输入是代码
        code = nameOrCode
        name = generateComponentName(getBaseComponentCode(code, prefix.toLowerCase()), prefix)
    } else {
        // 输入是名称
        name = nameOrCode
        code = generateComponentCode(name, prefix)
    }

    const baseName = getBaseComponentName(name, prefix)
    const baseCode = getBaseComponentCode(code, prefix.toLowerCase())
    const isValid = isValidComponentName(name, prefix) && isValidComponentCode(code, prefix.toLowerCase())

    return {
        name,
        code,
        baseName,
        baseCode,
        prefix,
        isValid
    }
}

/**
 * 组件注册选项
 */
export interface ComponentRegistryOptions {
    /** 组件前缀 */
    prefix?: string
    /** 是否覆盖已存在的组件 */
    override?: boolean
    /** 注册前的钩子函数 */
    beforeRegister?: (name: string, component: Component) => void
    /** 注册后的钩子函数 */
    afterRegister?: (name: string, component: Component) => void
}

/**
 * 组件注册器
 * 用于批量注册组件
 */
export class ComponentRegistry {
    private components = new Map<string, Component>()
    private options: ComponentRegistryOptions

    constructor(options: ComponentRegistryOptions = {}) {
        this.options = {
            prefix: 'Crf',
            override: false,
            ...options
        }
    }

    /**
     * 注册组件
     * 
     * @param nameOrCode - 组件名称或代码
     * @param component - 组件实例
     * @returns 是否注册成功
     */
    register(nameOrCode: string, component: Component): boolean {
        const metadata = parseComponentMetadata(nameOrCode, this.options.prefix)

        if (!metadata.isValid) {
            console.warn(`Invalid component name or code: ${nameOrCode}`)
            return false
        }

        if (this.components.has(metadata.name) && !this.options.override) {
            console.warn(`Component ${metadata.name} already exists`)
            return false
        }

        this.options.beforeRegister?.(metadata.name, component)
        this.components.set(metadata.name, component)
        this.options.afterRegister?.(metadata.name, component)

        return true
    }

    /**
     * 获取组件
     * 
     * @param nameOrCode - 组件名称或代码
     * @returns 组件实例
     */
    get(nameOrCode: string): Component | undefined {
        const metadata = parseComponentMetadata(nameOrCode, this.options.prefix)
        return this.components.get(metadata.name)
    }

    /**
     * 检查组件是否存在
     * 
     * @param nameOrCode - 组件名称或代码
     * @returns 是否存在
     */
    has(nameOrCode: string): boolean {
        const metadata = parseComponentMetadata(nameOrCode, this.options.prefix)
        return this.components.has(metadata.name)
    }

    /**
     * 删除组件
     * 
     * @param nameOrCode - 组件名称或代码
     * @returns 是否删除成功
     */
    delete(nameOrCode: string): boolean {
        const metadata = parseComponentMetadata(nameOrCode, this.options.prefix)
        return this.components.delete(metadata.name)
    }

    /**
     * 清空所有组件
     */
    clear(): void {
        this.components.clear()
    }

    /**
     * 获取所有组件
     * 
     * @returns 所有组件的映射
     */
    getAll(): Map<string, Component> {
        return new Map(this.components)
    }

    /**
     * 获取组件数量
     * 
     * @returns 组件数量
     */
    size(): number {
        return this.components.size
    }
}

/**
 * 创建可安装的组件
 * @param component Vue 组件
 * @param name 组件名称（可选，用于覆盖组件的默认名称）
 * @returns 带有 install 方法的组件
 */
export function createComponent<T extends Component>(
    component: T,
    name?: string
): SFCWithInstall<T> {
    const installableComponent = withInstall(component)

    // 如果提供了名称，设置组件名称
    if (name && component.name !== name) {
        Object.defineProperty(component, 'name', {
            value: name,
            configurable: true
        })
    }

    return installableComponent
}

/**
 * 导出单个组件的辅助函数
 * @param name 组件名称（不包含前缀）
 * @param component Vue 组件
 * @param prefix 前缀，默认 'Jz'
 * @returns 包含命名导出和默认导出的对象
 */
export function exportComponent<T extends Component>(
    name: string,
    component: T,
    prefix = 'Jz'
) {
    const installableComponent = createComponent(component, name)
    const exportName = `${prefix}${name}`

    return {
        [exportName]: installableComponent,
        default: installableComponent
    } as {
        [K in `${typeof prefix}${typeof name}`]: SFCWithInstall<T>
    } & {
        default: SFCWithInstall<T>
    }
}

/**
 * 批量导出多个组件
 * @param components 组件映射 { 'Text': TextComponent, 'Card': CardComponent }
 * @param prefix 前缀，默认 'Jz'
 * @returns 处理后的组件映射表
 */
export function exportComponents<T extends Record<string, Component>>(
    components: T,
    prefix = 'Jz'
): { [K in keyof T as `${typeof prefix}${string & K}`]: SFCWithInstall<T[K]> } {
    const result = {} as { [K in keyof T as `${typeof prefix}${string & K}`]: SFCWithInstall<T[K]> }

    for (const [name, component] of Object.entries(components)) {
        const installableComponent = createComponent(component, name)
        ;(result as any)[`${prefix}${name}`] = installableComponent
    }

    return result
}

/**
 * 创建组件库安装函数
 * @param components 组件数组或组件映射对象
 * @returns Vue 插件对象
 */
export function createInstaller(
    components: SFCWithInstall<Component>[] | Record<string, SFCWithInstall<Component>>
) {
    const componentList = Array.isArray(components)
        ? components
        : Object.values(components)

    return {
        install(app: App) {
            componentList.forEach(component => {
                app.use(component)
            })
        }
    }
}
