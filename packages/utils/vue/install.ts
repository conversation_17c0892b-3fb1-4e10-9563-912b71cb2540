import type { App, Component } from '@vue/runtime-core'

export type SFCWithInstall<T> = T & { install(app: App): void }

export type SFCInstallWithContext<T> = SFCWithInstall<T> & {
  _context: App | null
}

/**
 * 为组件添加 install 方法
 * @param main - 主组件
 * @param name - 组件名称（可选，会自动生成）
 * @returns 带有 install 方法的组件
 */
export const withInstall = <T, E extends Record<string, unknown>>(
  main: T,
  name?: string,
  extra?: E
) => {
  const componentName = name || (main as Record<string, unknown>).name || 'UnknownComponent'

    ; (main as SFCWithInstall<T>).install = (app: App): void => {
      app.component(componentName as string, main as Component)
      if (extra) {
        for (const [key, comp] of Object.entries(extra)) {
          app.component(key, comp as Component)
        }
      }
    }

  if (extra) {
    for (const [key, comp] of Object.entries(extra)) {
      ; (main as Record<string, unknown>)[key] = comp
    }
  }

  return main as SFCWithInstall<T> & E
}

/**
 * 根据代码生成组件名并安装
 * @param main - 主组件
 * @param code - 组件代码（如 'crf-button'）
 * @param namespace - 命名空间前缀，默认 'Crf'
 * @returns 带有 install 方法的组件
 */
export const withInstallByCode = <T>(
  main: T,
  code: string,
  namespace: string = 'Crf'
) => {
  // 自动生成组件名：Crf + PascalCase(code)
  const componentName = code
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')

  const fullComponentName = componentName.startsWith(namespace)
    ? componentName
    : `${namespace}${componentName.replace(/^[A-Z]/, match => match.toLowerCase())}`

    ; (main as SFCWithInstall<T>).install = (app: App): void => {
      // 使用代码作为标签名注册组件
      app.component(code, main as Component)
      // 同时使用生成的组件名注册（用于编程式访问）
      app.component(fullComponentName, main as Component)
    }

  return main as SFCWithInstall<T>
}

/**
 * 注册函数助手
 * @param component - 组件
 * @param alias - 别名
 */
export const withNoopInstall = <T>(component: T) => {
  ; (component as SFCWithInstall<T>).install = () => { }
  return component as SFCWithInstall<T>
}
