// CRF Schema AJV验证器
import Ajv, { ErrorObject, ValidateFunction } from 'ajv'
import type { CRFFormSchema, ValidationResult } from './types'

// AJV验证器配置
const ajv = new Ajv({
    allErrors: true,
    verbose: true,
    removeAdditional: false
})

// 手动添加常用格式（替代ajv-formats以避免兼容性问题）
ajv.addFormat('email', /^[^\s@]+@[^\s@]+\.[^\s@]+$/)
ajv.addFormat('date', /^\d{4}-\d{2}-\d{2}$/)
ajv.addFormat('time', /^\d{2}:\d{2}:\d{2}$/)
ajv.addFormat('date-time', /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/)
ajv.addFormat('uri', /^[a-zA-Z][a-zA-Z0-9+.-]*:/)

// 添加医疗CRF特有格式验证
ajv.addFormat('patient-id', {
    type: 'string',
    validate: (data: string) => /^[A-Z]{2,3}\d{4,8}$/.test(data)
})

ajv.addFormat('icd-10', {
    type: 'string',
    validate: (data: string) => /^[A-Z]\d{2}(\.\d{1,2})?$/.test(data)
})

ajv.addFormat('phone-cn', {
    type: 'string',
    validate: (data: string) => /^1[3-9]\d{9}$/.test(data)
})

ajv.addFormat('id-card-cn', {
    type: 'string',
    validate: (data: string) => /^[1-9]\d{5}(18|19|20|21|22)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(data)
})

// 医疗编号格式（字母数字组合）
ajv.addFormat('medical-code', {
    type: 'string',
    validate: (data: string) => /^[A-Za-z0-9]+$/.test(data)
})

// SNOMED CT编码格式
ajv.addFormat('snomed-ct', {
    type: 'string',
    validate: (data: string) => /^\d{6,18}$/.test(data)
})

// 验证器管理类
export class CRFValidator {
    private validators: Map<string, ValidateFunction> = new Map()

    /**
     * 编译Schema验证器
     */
    compileSchema(schema: CRFFormSchema): boolean {
        try {
            const validator = ajv.compile(schema) as ValidateFunction
            this.validators.set(schema.$id, validator)
            return true
        } catch (error) {
            console.error(`Schema编译失败: ${schema.$id}`, error)
            return false
        }
    }

    /**
     * 验证数据
     */
    async validateData(schemaId: string, data: Record<string, unknown>): Promise<ValidationResult> {
        const validator = this.validators.get(schemaId)
        if (!validator) {
            return {
                isValid: false,
                errors: [{ field: '', message: `Schema不存在: ${schemaId}` }]
            }
        }

        const isValid = await validator(data)
        const errors: Array<{ field: string; message: string; value?: unknown }> = []

        if (!isValid && validator.errors) {
            validator.errors.forEach((error: ErrorObject) => {
                const instancePath = (error as any).instancePath ?? ''
                const schemaPath = error.schemaPath ?? ''
                errors.push({
                    field: (instancePath ? instancePath.replace(/^\//, '').replace(/\//g, '.') : schemaPath),
                    message: this.formatErrorMessage(error),
                    value: error.data
                })
            })
        }

        return { isValid, errors }
    }

    /**
     * 验证Schema结构本身
     */
    validateSchemaStructure(schema: CRFFormSchema): ValidationResult {
        const errors: Array<{ field: string; message: string }> = []

        // 检查必需字段
        if (!schema.$id) {
            errors.push({ field: '$id', message: 'Schema ID为必填项' })
        }
        if (!schema.title) {
            errors.push({ field: 'title', message: 'Schema标题为必填项' })
        }
        if (!schema.version) {
            errors.push({ field: 'version', message: 'Schema版本为必填项' })
        }

        if (schema.type !== 'object' || !schema.properties) {
            errors.push({ field: 'type', message: 'Schema必须是object类型且包含properties' })
        }

        // 验证每个字段的配置
        if (schema.properties) {
            for (const [key, fieldSchema] of Object.entries(schema.properties)) {
                if (!fieldSchema.fieldConfig || !fieldSchema.fieldConfig.code) {
                    errors.push({ field: `properties.${key}`, message: `字段 ${key} 缺少fieldConfig或code` })
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        }
    }

    /**
     * 移除验证器
     */
    removeValidator(schemaId: string): void {
        this.validators.delete(schemaId)
    }

    /**
     * 获取所有已注册的验证器ID
     */
    getRegisteredValidators(): string[] {
        return Array.from(this.validators.keys())
    }

    /**
     * 格式化错误信息为中文
     */
    private formatErrorMessage(error: ErrorObject): string {
        const keyword = error.keyword
        // const schemaPath = error.schemaPath  // 未使用
        const data = error.data
        const params = error.params as Record<string, unknown>

        switch (keyword) {
            case 'required':
                return `缺少必填字段: ${params?.missingProperty || ''}`
            case 'type':
                return `字段类型错误，期望 ${params?.type || ''}，实际为 ${typeof data}`
            case 'format':
                return `格式不正确，期望格式: ${params?.format || ''}`
            case 'minimum':
                return `数值不能小于 ${params?.limit || ''}`
            case 'maximum':
                return `数值不能大于 ${params?.limit || ''}`
            case 'minLength':
                return `长度不能少于 ${params?.limit || ''} 个字符`
            case 'maxLength':
                return `长度不能超过 ${params?.limit || ''} 个字符`
            case 'pattern':
                return `格式不符合要求`
            case 'enum':
                return `值必须是以下之一: ${(params.allowedValues as string[] | undefined)?.join(', ') ?? ''}`
            default:
                return (error.message as string) || '验证失败'
        }
    }
}

// 全局验证器实例
export const crfValidator = new CRFValidator()

// 便捷验证函数
export const validateMedicalFormat = {
    /**
     * 验证患者ID格式
     */
    patientId: (value: string): boolean => {
        return /^[A-Z]{2,3}\d{4,8}$/.test(value)
    },

    /**
     * 验证ICD-10编码
     */
    icd10: (value: string): boolean => {
        return /^[A-Z]\d{2}(\.\d{1,2})?$/.test(value)
    },

    /**
     * 验证中国手机号
     */
    phoneCN: (value: string): boolean => {
        return /^1[3-9]\d{9}$/.test(value)
    },

    /**
     * 验证中国身份证号
     */
    idCardCN: (value: string): boolean => {
        return /^[1-9]\d{5}(18|19|20|21|22)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)
    },

    /**
     * 验证医疗编号
     */
    medicalCode: (value: string): boolean => {
        return /^[A-Za-z0-9]+$/.test(value)
    }
}