// 医疗CRF预设字段配置
import type { FieldConfig } from './types'

// 预定义的医疗CRF字段配置
export const MedicalFieldConfigs = {
    // === 患者基本信息 ===

    /**
     * 患者ID字段配置
     */
    patientId: (): FieldConfig => ({
        code: 'config-input',
        label: '患者ID',
        description: '患者唯一标识符，格式：2-3位字母+4-8位数字',
        placeholder: '如：ABC12345',
        showInConfig: true,
        configGroup: 'basic',
        order: 1,
        helpIcon: true,
        pattern: '^[A-Z]{2,3}\\d{4,8}$'
    }),

    /**
     * 患者姓名字段配置
     */
    patientName: (): FieldConfig => ({
        code: 'config-input',
        label: '患者姓名',
        description: '患者真实姓名',
        placeholder: '请输入患者姓名',
        showInConfig: true,
        configGroup: 'basic',
        order: 2,
        minLength: 2,
        maxLength: 50
    }),

    /**
     * 年龄字段配置
     */
    age: (): FieldConfig => ({
        code: 'config-number',
        label: '年龄',
        description: '患者年龄（岁）',
        placeholder: '请输入年龄',
        showInConfig: true,
        configGroup: 'basic',
        order: 3,
        min: 0,
        max: 150,
        step: 1
    }),

    /**
     * 性别字段配置
     */
    gender: (): FieldConfig => ({
        code: 'config-select',
        label: '性别',
        description: '患者性别',
        showInConfig: true,
        configGroup: 'basic',
        order: 4,
        options: [
            { label: '男', value: 'male' },
            { label: '女', value: 'female' },
            { label: '其他', value: 'other' }
        ]
    }),

    /**
     * 出生日期字段配置
     */
    birthDate: (): FieldConfig => ({
        code: 'config-input',
        label: '出生日期',
        description: '患者出生日期',
        placeholder: 'YYYY-MM-DD',
        showInConfig: true,
        configGroup: 'basic',
        order: 5,
        pattern: '^\\d{4}-\\d{2}-\\d{2}$'
    }),

    /**
     * 联系电话字段配置
     */
    phone: (): FieldConfig => ({
        code: 'config-input',
        label: '联系电话',
        description: '患者联系电话',
        placeholder: '请输入11位手机号',
        showInConfig: true,
        configGroup: 'basic',
        order: 6,
        pattern: '^1[3-9]\\d{9}$'
    }),

    /**
     * 身份证号字段配置
     */
    idCard: (): FieldConfig => ({
        code: 'config-input',
        label: '身份证号',
        description: '患者身份证号码',
        placeholder: '请输入18位身份证号',
        showInConfig: true,
        configGroup: 'basic',
        order: 7,
        pattern: '^[1-9]\\d{5}(18|19|20|21|22)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$'
    }),

    // === 诊断信息 ===

    /**
     * 主要诊断字段配置
     */
    primaryDiagnosis: (): FieldConfig => ({
        code: 'config-textarea',
        label: '主要诊断',
        description: '患者的主要诊断信息',
        placeholder: '请输入主要诊断',
        showInConfig: true,
        configGroup: 'basic',
        order: 1,
        maxLength: 500
    }),

    /**
     * ICD-10编码字段配置
     */
    icdCode: (): FieldConfig => ({
        code: 'config-input',
        label: 'ICD-10编码',
        description: '国际疾病分类第10版编码',
        placeholder: '如：I25.1',
        showInConfig: true,
        configGroup: 'basic',
        order: 2,
        helpIcon: true,
        pattern: '^[A-Z]\\d{2}(\\.\\d{1,2})?$'
    }),

    /**
     * 诊断日期字段配置
     */
    diagnosisDate: (): FieldConfig => ({
        code: 'config-input',
        label: '诊断日期',
        description: '确诊日期',
        placeholder: 'YYYY-MM-DD',
        showInConfig: true,
        configGroup: 'basic',
        order: 3,
        pattern: '^\\d{4}-\\d{2}-\\d{2}$'
    }),

    // === 治疗信息 ===

    /**
     * 治疗方案字段配置
     */
    treatmentPlan: (): FieldConfig => ({
        code: 'config-textarea',
        label: '治疗方案',
        description: '详细的治疗方案描述',
        placeholder: '请输入治疗方案',
        showInConfig: true,
        configGroup: 'basic',
        order: 1,
        maxLength: 1000
    }),

    /**
     * 用药信息字段配置
     */
    medication: (): FieldConfig => ({
        code: 'config-textarea',
        label: '用药信息',
        description: '患者用药详情',
        placeholder: '请输入用药信息',
        showInConfig: true,
        configGroup: 'basic',
        order: 2,
        maxLength: 800
    }),

    /**
     * 手术信息字段配置
     */
    surgery: (): FieldConfig => ({
        code: 'config-textarea',
        label: '手术信息',
        description: '手术相关信息',
        placeholder: '请输入手术信息',
        showInConfig: true,
        configGroup: 'basic',
        order: 3,
        maxLength: 800
    }),

    // === 检验结果 ===

    /**
     * 血压字段配置
     */
    bloodPressure: (): FieldConfig => ({
        code: 'config-input',
        label: '血压',
        description: '血压值（收缩压/舒张压），单位：mmHg',
        placeholder: '如：120/80',
        showInConfig: true,
        configGroup: 'basic',
        order: 1,
        pattern: '^\\d{2,3}/\\d{2,3}$'
    }),

    /**
     * 体温字段配置
     */
    temperature: (): FieldConfig => ({
        code: 'config-number',
        label: '体温',
        description: '体温，单位：℃',
        placeholder: '请输入体温',
        showInConfig: true,
        configGroup: 'basic',
        order: 2,
        min: 30,
        max: 45,
        step: 0.1
    }),

    /**
     * 心率字段配置
     */
    heartRate: (): FieldConfig => ({
        code: 'config-number',
        label: '心率',
        description: '心率，单位：次/分',
        placeholder: '请输入心率',
        showInConfig: true,
        configGroup: 'basic',
        order: 3,
        min: 30,
        max: 200,
        step: 1
    }),

    /**
     * 血糖字段配置
     */
    bloodGlucose: (): FieldConfig => ({
        code: 'config-number',
        label: '血糖',
        description: '血糖值，单位：mmol/L',
        placeholder: '请输入血糖值',
        showInConfig: true,
        configGroup: 'basic',
        order: 4,
        min: 0,
        max: 50,
        step: 0.1
    }),

    // === 生活方式 ===

    /**
     * 吸烟史字段配置
     */
    smokingHistory: (): FieldConfig => ({
        code: 'config-select',
        label: '吸烟史',
        description: '患者吸烟情况',
        showInConfig: true,
        configGroup: 'basic',
        order: 1,
        options: [
            { label: '从不吸烟', value: 'never' },
            { label: '已戒烟', value: 'former' },
            { label: '现在吸烟', value: 'current' }
        ]
    }),

    /**
     * 饮酒史字段配置
     */
    drinkingHistory: (): FieldConfig => ({
        code: 'config-select',
        label: '饮酒史',
        description: '患者饮酒情况',
        showInConfig: true,
        configGroup: 'basic',
        order: 2,
        options: [
            { label: '从不饮酒', value: 'never' },
            { label: '偶尔饮酒', value: 'occasional' },
            { label: '经常饮酒', value: 'regular' },
            { label: '已戒酒', value: 'former' }
        ]
    }),

    /**
     * 运动习惯字段配置
     */
    exercise: (): FieldConfig => ({
        code: 'config-select',
        label: '运动习惯',
        description: '患者运动锻炼情况',
        showInConfig: true,
        configGroup: 'basic',
        order: 3,
        options: [
            { label: '无运动', value: 'none' },
            { label: '轻度运动', value: 'light' },
            { label: '中度运动', value: 'moderate' },
            { label: '重度运动', value: 'intense' }
        ]
    }),

    // === 不良事件 ===

    /**
     * 不良事件描述字段配置
     */
    adverseEvent: (): FieldConfig => ({
        code: 'config-textarea',
        label: '不良事件描述',
        description: '详细描述不良事件的发生情况',
        placeholder: '请详细描述不良事件',
        showInConfig: true,
        configGroup: 'basic',
        order: 1,
        maxLength: 1000
    }),

    /**
     * 严重程度字段配置
     */
    severity: (): FieldConfig => ({
        code: 'config-select',
        label: '严重程度',
        description: '不良事件的严重程度评估',
        showInConfig: true,
        configGroup: 'basic',
        order: 2,
        options: [
            { label: '轻微', value: 'mild' },
            { label: '中度', value: 'moderate' },
            { label: '严重', value: 'severe' },
            { label: '危及生命', value: 'life_threatening' }
        ]
    }),

    /**
     * 与研究药物关系字段配置
     */
    drugRelationship: (): FieldConfig => ({
        code: 'config-select',
        label: '与研究药物关系',
        description: '不良事件与研究药物的关联性',
        showInConfig: true,
        configGroup: 'basic',
        order: 3,
        options: [
            { label: '无关', value: 'unrelated' },
            { label: '可能无关', value: 'unlikely' },
            { label: '可能相关', value: 'possible' },
            { label: '很可能相关', value: 'probable' },
            { label: '肯定相关', value: 'definite' }
        ]
    })
}

// 按类别组织的字段配置
export const MedicalFieldGroups = {
    patientInfo: {
        label: '患者基本信息',
        fields: [
            'patientId', 'patientName', 'age', 'gender',
            'birthDate', 'phone', 'idCard'
        ]
    },
    diagnosis: {
        label: '诊断信息',
        fields: ['primaryDiagnosis', 'icdCode', 'diagnosisDate']
    },
    treatment: {
        label: '治疗信息',
        fields: ['treatmentPlan', 'medication', 'surgery']
    },
    labResults: {
        label: '检验结果',
        fields: ['bloodPressure', 'temperature', 'heartRate', 'bloodGlucose']
    },
    lifestyle: {
        label: '生活方式',
        fields: ['smokingHistory', 'drinkingHistory', 'exercise']
    },
    adverseEvents: {
        label: '不良事件',
        fields: ['adverseEvent', 'severity', 'drugRelationship']
    }
}

// 快速创建常用医疗字段组合的工具函数
export const createMedicalFieldSet = {
    /**
     * 创建基本患者信息字段集
     */
    basicPatientInfo: () => {
        return MedicalFieldGroups.patientInfo.fields.map(fieldName => ({
            key: fieldName,
            config: MedicalFieldConfigs[fieldName as keyof typeof MedicalFieldConfigs]()
        }))
    },

    /**
     * 创建完整的患者信息字段集
     */
    fullPatientProfile: () => {
        const fields: Array<{ key: string; config: FieldConfig }> = []

        Object.values(MedicalFieldGroups).forEach(group => {
            group.fields.forEach(fieldName => {
                fields.push({
                    key: fieldName,
                    config: MedicalFieldConfigs[fieldName as keyof typeof MedicalFieldConfigs]()
                })
            })
        })

        return fields
    },

    /**
     * 创建指定类别的字段集
     */
    byCategory: (category: keyof typeof MedicalFieldGroups) => {
        const group = MedicalFieldGroups[category]
        if (!group) return []

        return group.fields.map(fieldName => ({
            key: fieldName,
            config: MedicalFieldConfigs[fieldName as keyof typeof MedicalFieldConfigs]()
        }))
    }
} 