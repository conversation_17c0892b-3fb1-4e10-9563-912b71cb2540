// 简化的事件系统模块 - 适用于医疗CRF系统
type EventHandler<T = unknown> = (data: T) => void

// 医疗CRF系统事件类型
export enum CRFEventTypes {
    // 数据相关事件
    VALUE_CHANGE = 'value_change',
    FIELD_VALIDATE = 'field_validate',
    FORM_SUBMIT = 'form_submit',
    FORM_RESET = 'form_reset',

    // 配置相关事件
    CONFIG_CHANGE = 'config_change',
    CONFIG_PANEL_SHOW = 'config_panel_show',
    CONFIG_PANEL_HIDE = 'config_panel_hide',
    SCHEMA_UPDATE = 'schema_update',

    // 编辑器相关事件
    BLOCK_SELECT = 'block_select',
    BLOCK_ADD = 'block_add',
    BLOCK_DELETE = 'block_delete',
    BLOCK_MOVE = 'block_move',

    // 字段编辑事件
    FIELD_EDIT_START = 'field_edit_start',
    FIELD_EDIT_END = 'field_edit_end',
    FIELD_EDIT_CANCEL = 'field_edit_cancel',

    // 组件交互事件
    COMPONENT_CLICK = 'component_click',
    COMPONENT_FOCUS = 'component_focus',
    COMPONENT_BLUR = 'component_blur'
}

// 事件总线实现
export class CRFEventBus {
    private events: Map<string, Set<EventHandler<any>>> = new Map()

    on<T = unknown>(type: string, handler: EventHandler<T>): () => void {
        if (!this.events.has(type)) {
            this.events.set(type, new Set())
        }
        this.events.get(type)!.add(handler as EventHandler<any>)

        // 返回取消监听函数
        return () => this.off(type, handler)
    }

    off<T = unknown>(type: string, handler: EventHandler<T>): void {
        const handlers = this.events.get(type)
        if (handlers) {
            handlers.delete(handler as EventHandler<any>)
        }
    }

    emit<T = unknown>(type: string, data?: T): void {
        const handlers = this.events.get(type)
        if (handlers) {
            for (const handler of handlers) {
                try {
                    handler(data as any)
                } catch (error) {
                    console.error(`Error in event handler for ${type}:`, error)
                }
            }
        }
    }

    clear(): void {
        this.events.clear()
    }

    hasListeners(type: string): boolean {
        return this.events.has(type) && this.events.get(type)!.size > 0
    }
}

// 全局事件总线实例
export const eventBus = new CRFEventBus()

// 医疗CRF专用事件工具
export const CRFEvents = {
    // 字段值变更
    onValueChange: (fieldId: string, value: unknown) => {
        eventBus.emit(CRFEventTypes.VALUE_CHANGE, { fieldId, value, timestamp: Date.now() })
    },

    // 字段验证
    onFieldValidate: (fieldId: string, isValid: boolean, message?: string) => {
        eventBus.emit(CRFEventTypes.FIELD_VALIDATE, { fieldId, isValid, message, timestamp: Date.now() })
    },

    // 表单提交
    onFormSubmit: (formData: Record<string, unknown>) => {
        eventBus.emit(CRFEventTypes.FORM_SUBMIT, { formData, timestamp: Date.now() })
    },

    // 配置更新
    onConfigChange: (config: Record<string, unknown>) => {
        eventBus.emit(CRFEventTypes.CONFIG_CHANGE, { config, timestamp: Date.now() })
    },

    // 编辑器操作
    onBlockSelect: (blockId: string) => {
        eventBus.emit(CRFEventTypes.BLOCK_SELECT, { blockId, timestamp: Date.now() })
    }
}

// 导出类型
export type { EventHandler }