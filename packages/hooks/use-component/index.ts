// 医疗CRF组件Hook
import { ref, computed, inject, provide, type Ref, type ComputedRef, watch } from 'vue'
import type { ComponentConfig, EventHandler } from '@crf/types'

// 组件状态接口
export interface ComponentState {
    value: unknown
    isValid: boolean
    isDirty: boolean
    isTouched: boolean
    errorMessage?: string
}

// 组件Hook返回类型
export interface UseComponentReturn {
    // 基础状态
    config: Ref<ComponentConfig>
    state: Ref<ComponentState>

    // 兼容旧接口的属性
    componentConfig: ComputedRef<ComponentConfig>

    // 计算属性
    isDisabled: ComputedRef<boolean>
    isRequired: ComputedRef<boolean>
    isVisible: ComputedRef<boolean>
    isReadonly: ComputedRef<boolean>
    hasError: ComputedRef<boolean>

    // 方法
    updateConfig: (newConfig: Partial<ComponentConfig>) => void
    updateValue: (newValue: unknown) => void
    getInitialValue: () => unknown
    validate: () => boolean
    reset: () => void

    // 新增：text.vue组件期望的方法
    updateField: (fieldName: string, value: unknown) => void
    getField: (fieldName: string) => unknown
    registerField: (fieldName: string, config: Record<string, unknown>) => void
    unregisterField: (fieldName: string) => void

    // 事件处理
    onValueChange: EventHandler
    onConfigChange: EventHandler
}

/**
 * 医疗CRF组件基础Hook
 * 管理组件的配置、状态和基础交互
 */
export function useComponent(config: ComponentConfig) {
    // 组件配置状态
    const componentConfig = ref<ComponentConfig>({ ...config })

    // 组件状态
    const isDisabled = computed(() => false) // ComponentConfig没有disabled属性
    const isReadonly = computed(() => false) // ComponentConfig没有readonly属性
    const isVisible = computed(() => true) // ComponentConfig没有visible属性，默认可见

    // 更新配置
    const updateConfig = (newConfig: Partial<ComponentConfig>) => {
        Object.assign(componentConfig.value, newConfig)
    }

    // 获取初始值
    const getInitialValue = () => {
        // ComponentConfig没有defaultValue和value属性，返回空字符串
        return ''
    }

    // 设置值
    const setValue = (_value: unknown) => {
        // ComponentConfig没有value属性，这里不做任何操作
        // 实际的值管理应该在组件内部处理
    }

    // 重置配置
    const resetConfig = () => {
        componentConfig.value = { ...config }
    }

    // 监听配置变化
    watch(() => config, (newConfig) => {
        componentConfig.value = { ...newConfig }
    }, { deep: true })

    return {
        componentConfig,
        isDisabled,
        isReadonly,
        isVisible,
        updateConfig,
        getInitialValue,
        setValue,
        resetConfig
    }
}

// 提供给子组件的注入键
export const ComponentContextKey = Symbol('component-context')

/**
 * 提供组件上下文
 */
export function provideComponentContext(context: UseComponentReturn) {
    provide(ComponentContextKey, context)
}

/**
 * 注入组件上下文
 */
export function injectComponentContext(): UseComponentReturn | undefined {
    return inject(ComponentContextKey)
}