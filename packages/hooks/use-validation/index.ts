// 医疗CRF验证Hook
import { ref, computed, type Ref } from 'vue'
import type { ValidationRule as BaseValidationRule } from '@crf/types'

// 验证规则类型
export type ValidationRule = {
    type: 'required' | 'pattern' | 'length' | 'range' | 'custom' | 'minLength' | 'maxLength' | 'email' | 'phone' | 'url' | 'number' | 'patientId' | 'idCard'
    message?: string
    // 正则表达式验证
    pattern?: RegExp
    // 长度验证
    minLength?: number
    maxLength?: number
    // 数值范围验证
    min?: number
    max?: number
    // 自定义验证函数
    validator?: (value: unknown) => boolean | string
    // 验证值
    value?: unknown
    // 触发条件
    trigger?: 'blur' | 'change' | 'input' | 'submit'
}

// 验证结果接口
export interface ValidationResult {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
}

// 验证状态接口
export interface ValidationState {
    status: '' | 'success' | 'warning' | 'error' | 'validating'
    message: string
    errors: Array<{ field: string; message: string }>
}

// 字段验证状态
export interface FieldValidation {
    fieldId: string
    rules: ValidationRule[]
    touched: boolean
    result: ValidationResult
}

// 单字段验证Hook配置
export interface UseValidationConfig {
    fieldId: string
    rules: ValidationRule[]
    required: boolean
}

// 单字段验证Hook返回类型
export interface UseValidationReturn {
    // 验证状态
    validationState: Ref<ValidationState>

    // 方法
    validate: (value: unknown) => Promise<ValidationResult>
    clearValidation: () => void
    addRule: (rule: ValidationRule) => void
    removeRule: (ruleType: string) => void
}

// 表单验证Hook返回类型
export interface UseFormValidationReturn {
    // 状态
    validations: Ref<Map<string, FieldValidation>>
    isFormValid: Ref<boolean>

    // 方法
    addField: (fieldId: string, rules: ValidationRule[]) => void
    removeField: (fieldId: string) => void
    validateField: (fieldId: string, value: unknown) => ValidationResult
    validateForm: (formData: Record<string, unknown>) => ValidationResult
    markFieldTouched: (fieldId: string) => void
    resetValidation: (fieldId?: string) => void

    // 获取验证状态
    getFieldValidation: (fieldId: string) => FieldValidation | undefined
    getFieldErrors: (fieldId: string) => string[]
    isFieldValid: (fieldId: string) => boolean
}

// 验证配置接口
export interface ValidationConfig {
    fieldId: string
    rules: BaseValidationRule[]
    required?: boolean
    trigger?: 'blur' | 'change' | 'input' | 'submit'
}

/**
 * 验证Hook
 * 提供表单验证功能
 */
export function useValidation(config: ValidationConfig) {
    // 验证状态
    const validationState = ref<ValidationState>({
        status: '',
        message: '',
        errors: []
    })

    // 验证函数
    const validate = async (value: unknown): Promise<ValidationResult> => {
        const errors: Array<{ field: string; message: string }> = []

        // 必填验证
        if (config.required && (value === null || value === undefined || value === '')) {
            errors.push({
                field: config.fieldId,
                message: '此字段为必填项'
            })
        }

        // 规则验证
        if (value && config.rules) {
            for (const rule of config.rules) {
                const isValid = await validateRule(value, rule)
                if (!isValid) {
                    errors.push({
                        field: config.fieldId,
                        message: rule.message || '验证失败'
                    })
                }
            }
        }

        // 更新验证状态
        const isValid = errors.length === 0
        validationState.value = {
            status: isValid ? 'success' : 'error',
            message: isValid ? '' : errors[0]?.message || '',
            errors
        }

        return {
            isValid,
            errors
        }
    }

    // 清除验证状态
    const clearValidation = () => {
        validationState.value = {
            status: '',
            message: '',
            errors: []
        }
    }

    // 验证单个规则
    const validateRule = async (value: unknown, rule: BaseValidationRule): Promise<boolean> => {
        switch (rule.type) {
            case 'required':
                return value !== null && value !== undefined && value !== ''

            case 'minLength':
                const minLen = typeof rule.value === 'number' ? rule.value : 0
                return String(value).length >= minLen

            case 'maxLength':
                const maxLen = typeof rule.value === 'number' ? rule.value : 0
                return String(value).length <= maxLen

            case 'pattern':
                if (rule.value && typeof rule.value === 'string') {
                    const regex = new RegExp(rule.value)
                    return regex.test(String(value))
                }
                return true

            case 'email':
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(String(value))

            case 'phone':
                return /^1[3-9]\d{9}$/.test(String(value))

            case 'url':
                return /^https?:\/\/.+/.test(String(value))

            case 'number':
                return /^\d+$/.test(String(value))

            case 'custom':
                if (rule.value && typeof rule.value === 'function') {
                    try {
                        const result = await (rule.value as Function)(value)
                        return Boolean(result)
                    } catch {
                        return false
                    }
                }
                return true

            default:
                return true
        }
    }

    return {
        validationState,
        validate,
        clearValidation
    }
}

/**
 * 表单级别验证Hook
 * 统一管理整个表单的验证逻辑
 */
export function useFormValidation(): UseFormValidationReturn {

    // 验证状态存储
    const validations = ref<Map<string, FieldValidation>>(new Map())

    // 表单整体验证状态
    const isFormValid = computed(() => {
        for (const validation of validations.value.values()) {
            if (!validation.result.isValid && validation.touched) {
                return false
            }
        }
        return true
    })

    // 添加字段验证
    const addField = (fieldId: string, rules: ValidationRule[]) => {
        validations.value.set(fieldId, {
            fieldId,
            rules,
            touched: false,
            result: { isValid: true, errors: [] }
        })
    }

    // 移除字段验证
    const removeField = (fieldId: string) => {
        validations.value.delete(fieldId)
    }

    // 执行单个规则验证（复用上面的函数逻辑）
    const executeRule = (rule: ValidationRule, value: unknown): string | null => {
        switch (rule.type) {
            case 'required':
                if (value === null || value === undefined || value === '') {
                    return rule.message || '此字段为必填项'
                }
                break

            case 'pattern':
                if (rule.pattern && value && !rule.pattern.test(String(value))) {
                    return rule.message || '格式不正确'
                }
                break

            case 'length':
                const strValue = String(value || '')
                if (rule.minLength !== undefined && strValue.length < rule.minLength) {
                    return rule.message || `最少需要${rule.minLength}个字符`
                }
                if (rule.maxLength !== undefined && strValue.length > rule.maxLength) {
                    return rule.message || `最多允许${rule.maxLength}个字符`
                }
                break

            case 'range':
                const numValue = Number(value)
                if (!isNaN(numValue)) {
                    if (rule.min !== undefined && numValue < rule.min) {
                        return rule.message || `数值不能小于${rule.min}`
                    }
                    if (rule.max !== undefined && numValue > rule.max) {
                        return rule.message || `数值不能大于${rule.max}`
                    }
                }
                break

            case 'custom':
                if (rule.validator) {
                    const result = rule.validator(value)
                    if (result === false) {
                        return rule.message || '验证失败'
                    }
                    if (typeof result === 'string') {
                        return result
                    }
                }
                break
        }
        return null
    }

    // 验证单个字段
    const validateField = (fieldId: string, value: unknown): ValidationResult => {
        const fieldValidation = validations.value.get(fieldId)
        if (!fieldValidation) {
            return { isValid: true, errors: [] }
        }

        const errors: Array<{ field: string; message: string }> = []

        // 执行所有验证规则
        for (const rule of fieldValidation.rules) {
            const error = executeRule(rule, value)
            if (error) {
                errors.push({ field: fieldId, message: error })
            }
        }

        const result: ValidationResult = {
            isValid: errors.length === 0,
            errors
        }

        // 更新验证状态
        fieldValidation.result = result

        return result
    }

    // 验证整个表单
    const validateForm = (formData: Record<string, unknown>): ValidationResult => {
        const allErrors: Array<{ field: string; message: string }> = []
        let isValid = true

        // 验证所有字段
        for (const [fieldId, fieldValidation] of validations.value.entries()) {
            const value = formData[fieldId]
            const result = validateField(fieldId, value)

            if (!result.isValid) {
                isValid = false
                allErrors.push(...result.errors)
            }

            // 标记为已触摸
            fieldValidation.touched = true
        }

        const result: ValidationResult = {
            isValid,
            errors: allErrors
        }

        return result
    }

    // 标记字段为已触摸
    const markFieldTouched = (fieldId: string) => {
        const fieldValidation = validations.value.get(fieldId)
        if (fieldValidation) {
            fieldValidation.touched = true
        }
    }

    // 重置验证
    const resetValidation = (fieldId?: string) => {
        if (fieldId) {
            const fieldValidation = validations.value.get(fieldId)
            if (fieldValidation) {
                fieldValidation.touched = false
                fieldValidation.result = { isValid: true, errors: [] }
            }
        } else {
            // 重置所有字段
            for (const fieldValidation of validations.value.values()) {
                fieldValidation.touched = false
                fieldValidation.result = { isValid: true, errors: [] }
            }
        }
    }

    // 获取字段验证状态
    const getFieldValidation = (fieldId: string): FieldValidation | undefined => {
        return validations.value.get(fieldId)
    }

    // 获取字段错误信息
    const getFieldErrors = (fieldId: string): string[] => {
        const fieldValidation = validations.value.get(fieldId)
        return fieldValidation ? fieldValidation.result.errors.map(e => e.message) : []
    }

    // 检查字段是否有效
    const isFieldValid = (fieldId: string): boolean => {
        const fieldValidation = validations.value.get(fieldId)
        return fieldValidation ? fieldValidation.result.isValid : true
    }

    return {
        validations,
        isFormValid,
        addField,
        removeField,
        validateField,
        validateForm,
        markFieldTouched,
        resetValidation,
        getFieldValidation,
        getFieldErrors,
        isFieldValid
    }
}

// 常用的医疗CRF验证规则预设
export const CRFValidationRules = {
    // 必填
    required: (message?: string): ValidationRule => ({
        type: 'required',
        message: message || '此字段为必填项'
    }),

    // 身份证号
    idCard: (message?: string): ValidationRule => ({
        type: 'pattern',
        pattern: /^[1-9]\d{5}(18|19|20|21|22)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: message || '请输入正确的身份证号'
    }),

    // 手机号
    phone: (message?: string): ValidationRule => ({
        type: 'pattern',
        pattern: /^1[3-9]\d{9}$/,
        message: message || '请输入正确的手机号'
    }),

    // 年龄范围
    age: (min = 0, max = 150, message?: string): ValidationRule => ({
        type: 'range',
        min,
        max,
        message: message || `年龄应在${min}-${max}岁之间`
    }),

    // 医疗编号（字母数字组合）
    medicalCode: (message?: string): ValidationRule => ({
        type: 'pattern',
        pattern: /^[A-Za-z0-9]+$/,
        message: message || '医疗编号只能包含字母和数字'
    })
}