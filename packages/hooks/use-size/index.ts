import { buildProp } from '@crf/utils'
import { computed, inject, unref, type Ref, type InjectionKey } from 'vue'
import type { ComponentSize } from '@crf/constants'
import { componentSizes } from '@crf/constants'

/**
 * @description 构建size属性配置
 * useSizeProp 用于定义组件的size属性
 * - 类型为String
 * - 可选值为componentSizes中定义的值
 * - 非必填
 */
export const useSizeProp = buildProp({
  type: String,
  values: componentSizes,
  required: false,
} as const)

/**
 * @description 组件size相关属性集合
 * useSizeProps 包含了size属性的配置对象
 */
export const useSizeProps = {
  size: useSizeProp,
}

/**
 * @description Size上下文接口定义
 * SizeContext 定义了size上下文的类型
 * - size: 响应式的ComponentSize类型
 */
export interface SizeContext {
  size: Ref<ComponentSize>
}

/**
 * @description Size注入的key
 * SIZE_INJECTION_KEY 用于provide/inject的唯一标识符
 * 用Symbol确保唯一性
 */
export const SIZE_INJECTION_KEY: InjectionKey<SizeContext> = Symbol('size')

/**
 * @description 获取全局size的hook
 * useGlobalSize 用于获取全局注入的size值
 * @returns {ComputedRef<ComponentSize>} 返回计算属性,当前生效的size值
 * - 通过inject获取上层注入的size context
 * - 如果没有注入则返回空字符串
 */
export const useGlobalSize = () => {
  const injectedSize = inject(SIZE_INJECTION_KEY, {} as SizeContext)

  return computed<ComponentSize>(() => {
    return unref(injectedSize.size) || ''
  })
}

/**
 * @description 使用size的hook
 * useSize 用于处理组件的size属性
 * @param size 组件size属性的响应式引用
 * @returns {ComputedRef<ComponentSize>} 返回计算属性，处理后的size值
 */
export const useSize = (size?: Ref<ComponentSize> | ComponentSize) => {
  const globalSize = useGlobalSize()

  return computed<ComponentSize>(() => {
    return unref(size) || globalSize.value || 'default'
  })
}
