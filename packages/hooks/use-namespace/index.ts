import { computed } from 'vue'

/**
 * 命名空间相关hooks
 * 默认命名空间前缀 'crf'
 */

export const defaultNamespace = 'crf'

/**
 * 获取派生的命名空间
 * 
 * @param namespace - 命名空间，默认为 'crf'
 * @returns 响应式的命名空间值
 * 
 * @example
 * const namespace = useGetDerivedNamespace()
 * namespace.value => 'crf'
 */
export function useGetDerivedNamespace(
  namespace: string = defaultNamespace
) {
  return computed(() => namespace)
}

/**
 * 生成BEM风格的类名
 * 
 * @param namespace - 命名空间
 * @param block - 块名
 * @param element - 元素名
 * @param modifier - 修饰符名
 * @param state - 状态名
 * @returns BEM风格的类名
 * 
 * @example
 * _bem('crf', 'button', '', '', '') => 'crf-button'
 * _bem('crf', 'button', 'primary', '', '') => 'crf-button-primary'
 * _bem('crf', 'button', '', 'label', '') => 'crf-button__label'
 * _bem('crf', 'button', '', '', 'disabled') => 'crf-button--disabled'
 */
function _bem(
  namespace: string,
  block: string,
  element: string,
  modifier: string,
  state: string
): string {
  let cls = `${namespace}-${block}`
  if (element) cls += `__${element}`
  if (modifier) cls += `-${modifier}`
  if (state) cls += `--${state}`
  return cls
}

/**
 * 创建命名空间处理函数
 * 
 * @param namespace - 命名空间,默认为 'crf'
 * @returns 命名空间处理函数集合
 * 
 * @example
 * const ns = useNamespace('button')
 * ns.b() => 'crf-button'
 * ns.b('primary') => 'crf-button-primary'
 * ns.e('label') => 'crf-button__label'
 * ns.m('disabled') => 'crf-button--disabled'
 */
export function useNamespace(
  block: string,
  namespace: string = defaultNamespace
) {
  const b = (modifier = '') => _bem(namespace, block, '', modifier, '')
  const e = (element: string) => _bem(namespace, block, element, '', '')
  const m = (modifier: string) => _bem(namespace, block, '', modifier, '')
  const s = (state: string) => _bem(namespace, block, '', '', state)
  const be = (element: string, modifier = '') => _bem(namespace, block, element, modifier, '')
  const bm = (modifier: string, state = '') => _bem(namespace, block, '', modifier, state)
  const em = (element: string, modifier: string) => _bem(namespace, block, element, modifier, '')
  const es = (element: string, state: string) => _bem(namespace, block, element, '', state)
  const bem = (element: string, modifier: string, state: string) => _bem(namespace, block, element, modifier, state)
  const is = (name: string, state: boolean | undefined = undefined) => {
    const prefix = `is-${name}`
    return state === undefined ? prefix : state ? prefix : ''
  }

  return {
    namespace,
    b,
    e,
    m,
    s,
    be,
    bm,
    em,
    es,
    bem,
    is
  }
}

/**
 * 生成组件名称
 * 
 * @param name - 组件名称
 * @param namespace - 命名空间前缀，默认 'Crf'
 * @returns 组件名称
 * 
 * @example
 * useComponentName('button') => 'CrfButton'
 * useComponentName('input') => 'CrfInput'
 * useComponentName('text-area') => 'CrfTextArea'
 */
export function useComponentName(
  name: string,
  namespace: string = 'Crf'
): string {
  // 将kebab-case转换为PascalCase
  const pascalName = name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')

  return `${namespace}${pascalName}`
}

/**
 * 生成组件代码名称
 * 
 * @param name - 组件名称
 * @param namespace - 命名空间前缀，默认 'crf'
 * @returns 组件代码名称
 * 
 * @example
 * useComponentCode('Button') => 'crf-button'
 * useComponentCode('Input') => 'crf-input'
 * useComponentCode('TextArea') => 'crf-text-area'
 */
export function useComponentCode(
  name: string,
  namespace: string = 'crf'
): string {
  // 将PascalCase转换为kebab-case
  const kebabName = name
    .replace(/([A-Z])/g, '-$1')
    .toLowerCase()
    .replace(/^-/, '')

  return `${namespace}-${kebabName}`
}

/**
 * 获取CSS变量名
 * 
 * @param name - 变量名
 * @param namespace - 命名空间前缀，默认 'crf'
 * @returns CSS变量名
 * 
 * @example
 * useCssVar('primary-color') => '--crf-primary-color'
 * useCssVar('button-height') => '--crf-button-height'
 */
export function useCssVar(
  name: string,
  namespace: string = 'crf'
): string {
  return `--${namespace}-${name}`
}

/**
 * 获取CSS变量值
 * 
 * @param name - 变量名
 * @param namespace - 命名空间前缀，默认 'crf'
 * @returns CSS变量值
 * 
 * @example
 * getCssVarValue('primary-color') => 'var(--crf-primary-color)'
 * getCssVarValue('button-height') => 'var(--crf-button-height)'
 */
export function getCssVarValue(
  name: string,
  namespace: string = 'crf'
): string {
  return `var(${useCssVar(name, namespace)})`
}

/**
 * 创建CSS变量对象
 * 
 * @param vars - 变量对象
 * @param namespace - 命名空间前缀，默认 'crf'
 * @returns CSS变量对象
 * 
 * @example
 * createCssVars({
 *   'primary-color': '#007bff',
 *   'button-height': '32px'
 * }) => {
 *   '--crf-primary-color': '#007bff',
 *   '--crf-button-height': '32px'
 * }
 */
export function createCssVars(
  vars: Record<string, string>,
  namespace: string = 'crf'
): Record<string, string> {
  const cssVars: Record<string, string> = {}

  for (const [key, value] of Object.entries(vars)) {
    cssVars[useCssVar(key, namespace)] = value
  }

  return cssVars
}

/**
 * 检查是否为CRF组件
 * 
 * @param name - 组件名称
 * @returns 是否为CRF组件
 * 
 * @example
 * isCrfComponent('CrfButton') => true
 * isCrfComponent('ElButton') => false
 */
export function isCrfComponent(name: string): boolean {
  return name.startsWith('Crf')
}

/**
 * 提取组件基础名称
 * 
 * @param name - 组件名称
 * @returns 基础名称
 * 
 * @example
 * getBaseComponentName('CrfButton') => 'Button'
 * getBaseComponentName('ElButton') => 'Button'
 */
export function getBaseComponentName(name: string): string {
  return name.replace(/^(Crf|El|Ant)/, '')
}

/**
 * 组件名称转换为标签名
 * 
 * @param name - 组件名称
 * @returns 标签名
 * 
 * @example
 * componentNameToTag('CrfButton') => 'crf-button'
 * componentNameToTag('CrfTextArea') => 'crf-text-area'
 */
export function componentNameToTag(name: string): string {
  return name
    .replace(/([A-Z])/g, '-$1')
    .toLowerCase()
    .replace(/^-/, '')
}

/**
 * 标签名转换为组件名称
 * 
 * @param tag - 标签名
 * @returns 组件名称
 * 
 * @example
 * tagToComponentName('crf-button') => 'CrfButton'
 * tagToComponentName('crf-text-area') => 'CrfTextArea'
 */
export function tagToComponentName(tag: string): string {
  return tag
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
}
