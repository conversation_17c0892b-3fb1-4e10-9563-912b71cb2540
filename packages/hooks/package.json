{"name": "@crf/hooks", "version": "1.0.1", "description": "CRF composables", "private": true, "main": "index.ts", "module": "index.ts", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.ts", "require": "./index.ts"}}, "scripts": {"build": "unbuild", "dev": "unbuild --watch", "stub": "unbuild --stub", "type-check": "echo 'Type checking hooks package...'", "lint": "echo 'Linting hooks package...'", "test": "echo 'Testing hooks package...'", "clean": "rm -rf dist"}, "files": ["dist"], "peerDependencies": {"vue": "^3.5.13"}, "devDependencies": {"@crf/build-config": "workspace:*"}}