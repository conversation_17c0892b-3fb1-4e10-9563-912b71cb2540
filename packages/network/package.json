{"name": "@crf/network", "version": "1.0.0", "description": "Network management utilities for CRF applications", "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.mts", "exports": {".": {"types": "./dist/index.d.mts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --watch", "type-check": "echo 'Type checking network package...'", "lint": "echo 'Linting network package...'", "test": "echo 'Testing network package...'", "clean": "rm -rf dist"}, "peerDependencies": {"vue": "^3.3.0"}, "dependencies": {"@crf/composables": "workspace:*", "@crf/constants": "workspace:*", "@crf/types": "workspace:*", "@crf/utils": "workspace:*", "axios": "^1.10.0", "naive-ui": "^2.42.0"}, "devDependencies": {"@crf/build-config": "workspace:*", "@crf/tsconfig": "workspace:*", "typescript": "^5.2.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}}