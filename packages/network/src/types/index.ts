import type { AxiosRequestConfig } from 'axios'

// 基础API响应接口
export interface APIResponse<T = unknown> {
  success: boolean
  data: T
  message?: string
  code?: string
  status?: number
  headers?: Record<string, string>
  timestamp?: number
  originalError?: unknown
}

// API错误接口
export interface APIError extends Omit<APIResponse<null>, 'data'> {
  success: false
  data: null
  originalError?: unknown
  context?: {
    operation?: string
    resource?: string
    userId?: string
    timestamp?: string
    requestId?: string
    [key: string]: unknown
  }
}

// 请求配置接口
export interface RequestConfig extends Omit<AxiosRequestConfig, 'url' | 'method'> {
  enableCache?: boolean
  retries?: number
  retryDelay?: number
  abortController?: AbortController
  cacheTime?: number
}

// HTTP客户端配置
export interface HttpClientConfig {
  baseURL: string
  timeout?: number
  withCredentials?: boolean
  apiPrefix?: string
  defaultHeaders?: Record<string, string>
  auth?: AuthConfig
  errorHandling?: ErrorHandlingConfig
  responseHandling?: ResponseHandlingConfig
}

// 认证配置
export interface AuthConfig {
  tokenKey?: string
  refreshTokenKey?: string
  userInfoKey?: string
  refreshThreshold?: number
  rememberMeThreshold?: number
  refreshEndpoint?: string
  loginRedirect?: string
  enableAutoRefresh?: boolean
  enableI18n?: boolean
  supportedLanguages?: string[]
  defaultLanguage?: string
}

// 错误处理配置
export interface ErrorHandlingConfig {
  enableLogging?: boolean
  enableUserFriendlyMessages?: boolean
  retryAttempts?: number
  retryDelay?: number
}

// 响应处理配置
export interface ResponseHandlingConfig {
  enableLogging?: boolean
  enableDataTransform?: boolean
  enableMetadataExtraction?: boolean
  successCodes?: number[]
  dataField?: string
  messageField?: string
  codeField?: string
  successField?: string
}

// Token信息接口
export interface TokenInfo {
  access_token: string
  refresh_token?: string
  user?: unknown
  remember_me?: boolean
}



// 网络状态接口
export interface NetworkStatus {
  online: boolean
  type: string | null
  downlink: number | null
  rtt: number | null
}

// 缓存项接口
export interface CacheItem<T = unknown> {
  data: T
  timestamp: number
  expiry: number
}

// 请求状态
export interface RequestState {
  loading: boolean
  error: Error | null
  timestamp: number
}

// 请求结果接口
export interface RequestResult<T = unknown> {
  data: T
  status: number
  headers: Headers
  fromCache?: boolean
  timestamp?: number
  success?: boolean
  message?: string
}

// 导出所有类型
export type { AxiosRequestConfig }