/**
 * 统一的HTTP客户端
 * 提供标准化的HTTP请求方法，支持认证、错误处理、响应处理等功能
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { APIResponse, RequestConfig, HttpClientConfig } from '../types'
import { AuthManager } from '../auth/AuthManager'
import { ErrorHandler } from '../handlers/ErrorHandler'
import { ResponseHandler } from '../handlers/ResponseHandler'

export class HttpClient {
  private axios: AxiosInstance
  private authManager: AuthManager
  private errorHandler: ErrorHandler
  private responseHandler: ResponseHandler
  private config: HttpClientConfig

  constructor(config: HttpClientConfig) {
    this.config = config
    
    // 创建axios实例
    this.axios = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      withCredentials: config.withCredentials ?? true,
      headers: {
        'Content-Type': 'application/json',
        ...config.defaultHeaders
      }
    })

    // 初始化管理器
    this.authManager = new AuthManager(config.auth)
    this.errorHandler = new ErrorHandler(config.errorHandling)
    this.responseHandler = new ResponseHandler(config.responseHandling)

    // 设置拦截器
    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.axios.interceptors.request.use(
      async (config) => {
        // 添加认证头
        const authHeaders = await this.authManager.getAuthHeaders()
        config.headers = { ...config.headers, ...authHeaders }

        // 添加语言头
        const langHeaders = this.authManager.getLanguageHeaders()
        config.headers = { ...config.headers, ...langHeaders }

        // 处理API前缀
        if (this.config.apiPrefix && config.url && !config.url.startsWith('http')) {
          config.url = this.normalizeUrl(config.url)
        }

        return config
      },
      (error) => {
        return Promise.reject(this.errorHandler.handleRequestError(error))
      }
    )

    // 响应拦截器
    this.axios.interceptors.response.use(
      (response) => {
        return this.responseHandler.handleSuccessResponse(response)
      },
      async (error) => {
        // 处理token刷新
        if (this.authManager.shouldRefreshToken(error)) {
          try {
            await this.authManager.refreshToken()
            // 重新发送原始请求
            return this.axios.request(error.config)
          } catch (refreshError) {
            return Promise.reject(this.errorHandler.handleResponseError(refreshError))
          }
        }

        return Promise.reject(this.errorHandler.handleResponseError(error))
      }
    )
  }

  /**
   * 标准化URL路径
   */
  private normalizeUrl(url: string): string {
    const { apiPrefix } = this.config
    
    // 确保URL以/开头
    const normalizedUrl = url.startsWith('/') ? url : `/${url}`
    
    // 如果已经包含API前缀，直接返回
    if (apiPrefix && normalizedUrl.startsWith(apiPrefix)) {
      return normalizedUrl
    }
    
    // 添加API前缀
    return apiPrefix ? `${apiPrefix}${normalizedUrl}` : normalizedUrl
  }

  /**
   * GET请求
   */
  async get<T = unknown>(
    url: string, 
    config?: RequestConfig
  ): Promise<APIResponse<T>> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        method: 'GET',
        url,
        ...config,
        params: config?.params
      }

      const response = await this.axios.request<T>(axiosConfig)
      return this.responseHandler.transformResponse<T>(response)
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * POST请求
   */
  async post<T = unknown>(
    url: string, 
    data?: unknown, 
    config?: RequestConfig
  ): Promise<APIResponse<T>> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        method: 'POST',
        url,
        data,
        ...config
      }

      const response = await this.axios.request<T>(axiosConfig)
      return this.responseHandler.transformResponse<T>(response)
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * PUT请求
   */
  async put<T = unknown>(
    url: string, 
    data?: unknown, 
    config?: RequestConfig
  ): Promise<APIResponse<T>> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        method: 'PUT',
        url,
        data,
        ...config
      }

      const response = await this.axios.request<T>(axiosConfig)
      return this.responseHandler.transformResponse<T>(response)
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * DELETE请求
   */
  async delete<T = unknown>(
    url: string, 
    config?: RequestConfig
  ): Promise<APIResponse<T>> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        method: 'DELETE',
        url,
        ...config
      }

      const response = await this.axios.request<T>(axiosConfig)
      return this.responseHandler.transformResponse<T>(response)
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * PATCH请求
   */
  async patch<T = unknown>(
    url: string, 
    data?: unknown, 
    config?: RequestConfig
  ): Promise<APIResponse<T>> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        method: 'PATCH',
        url,
        data,
        ...config
      }

      const response = await this.axios.request<T>(axiosConfig)
      return this.responseHandler.transformResponse<T>(response)
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * 通用请求方法
   */
  async request<T = unknown>(config: AxiosRequestConfig): Promise<APIResponse<T>> {
    try {
      const response = await this.axios.request<T>(config)
      return this.responseHandler.transformResponse<T>(response)
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * 批量请求
   */
  async batch<T = unknown>(configs: AxiosRequestConfig[]): Promise<APIResponse<T>[]> {
    try {
      const promises = configs.map(config => this.axios.request<T>(config))
      const responses = await Promise.allSettled(promises)
      
      return responses.map((result, index) => {
        if (result.status === 'fulfilled') {
          return this.responseHandler.transformResponse<T>(result.value)
        } else {
          return {
            success: false,
            data: null as T,
            message: this.errorHandler.extractErrorMessage(result.reason),
            error: result.reason
          }
        }
      })
    } catch (error) {
      throw this.errorHandler.handleRequestError(error)
    }
  }

  /**
   * 取消所有请求
   */
  cancelAllRequests(): void {
    // 创建新的axios实例来替代当前实例，自动取消所有pending请求
    const oldAxios = this.axios
    this.axios = axios.create(oldAxios.defaults)
    this.setupInterceptors()
  }

  /**
   * 获取当前配置
   */
  getConfig(): HttpClientConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<HttpClientConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 更新axios配置
    if (newConfig.baseURL) {
      this.axios.defaults.baseURL = newConfig.baseURL
    }
    if (newConfig.timeout) {
      this.axios.defaults.timeout = newConfig.timeout
    }
    if (newConfig.defaultHeaders) {
      this.axios.defaults.headers.common = {
        ...this.axios.defaults.headers.common,
        ...newConfig.defaultHeaders
      }
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.cancelAllRequests()
    this.authManager.dispose()
  }
}