/**
 * 网络请求管理器
 * 支持请求缓存、重试、并发控制、超时处理等
 */

import { ref, shallowRef, computed, readonly } from 'vue'
import type { RequestConfig, RequestResult, NetworkStatus, CacheItem, RequestState, APIResponse } from '../types'
import { ErrorCategory } from '@crf/types'
import axios from 'axios'

// 临时的错误处理函数，后续会从正确的包导入
function useErrorHandler() {
  return {
    handleError: (error: Error, category: ErrorCategory = ErrorCategory.NETWORK) => {
      console.error(`[${category}]`, error)
    }
  }
}

// 临时的定时器管理函数，后续会从正确的包导入
function useTimerManager() {
  return {
    safeSetTimeout: (callback: () => void, delay: number) => setTimeout(callback, delay),
    clearAllTimers: () => { }
  }
}

// 临时的环境配置，后续会从正确的包导入
const EnvConfig = {
  API_TIMEOUT: 30000
}

// 错误处理函数
function handleRequestError(error: unknown) {
  if (axios.isAxiosError(error)) {
    console.error('Axios错误:', error.response?.data || error.message)
  } else {
    console.error('未知错误:', error)
  }
}

/**
 * 网络管理器组合式API
 */
export function useNetworkManager() {
  const { handleError } = useErrorHandler()
  const { safeSetTimeout } = useTimerManager()

  // 缓存存储
  const cache = shallowRef<Map<string, CacheItem>>(new Map())

  // 并发控制
  const activeRequests = shallowRef<Map<string, AbortController>>(new Map())
  const maxConcurrentRequests = ref(6)
  const requestQueue = shallowRef<RequestConfig[]>([])

  // 网络状态
  const networkStatus = shallowRef<NetworkStatus>({
    online: navigator.onLine,
    connectionType: 'unknown',
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0
  })

  // 全局统计
  const stats = shallowRef({
    totalRequests: 0,
    cachedRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    totalCacheSize: 0
  })

  // 计算属性
  const isOnline = computed(() => networkStatus.value.online)
  const activeRequestCount = computed(() => activeRequests.value.size)
  const cacheSize = computed(() => cache.value.size)
  const canMakeRequest = computed(() =>
    activeRequestCount.value < maxConcurrentRequests.value
  )

  /**
   * 简单哈希函数，用于生成缓存键
   */
  function simpleHash(str: string): string {
    let hash = 0
    if (str.length === 0) return hash.toString()

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }

    return Math.abs(hash).toString(36)
  }

  /**
   * 生成缓存键
   */
  function generateCacheKey(config: RequestConfig): string {
    const { url, method = 'GET', body } = config
    const key = `${method}:${url}`

    if (body) {
      const bodyStr = typeof body === 'string' ? body : JSON.stringify(body)
      const hash = simpleHash(bodyStr)
      return `${key}:${hash}`
    }

    return key
  }

  /**
   * 检查缓存
   */
  function getCachedResponse<T>(cacheKey: string): RequestResult<T> | null {
    const cached = cache.value.get(cacheKey)

    if (!cached) return null

    // 检查是否过期
    if (Date.now() > cached.expireTime) {
      cache.value.delete(cacheKey)
      updateCacheStats()
      return null
    }

    stats.value.cachedRequests++
    updateCacheStats()

    return {
      data: cached.data,
      status: 200,
      headers: new Headers(),
      fromCache: true,
      timestamp: cached.timestamp
    }
  }

  /**
   * 设置缓存
   */
  function setCachedResponse<T>(
    cacheKey: string,
    data: T,
    cacheTime: number = 300000 // 5分钟默认缓存
  ): void {
    const size = JSON.stringify(data).length

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expireTime: Date.now() + cacheTime,
      size
    }

    cache.value.set(cacheKey, cacheItem)
    updateCacheStats()

    // 缓存清理
    cleanupCache()
  }

  /**
   * 缓存清理
   */
  function cleanupCache(): void {
    const now = Date.now()
    const maxCacheSize = 50 * 1024 * 1024 // 50MB

    // 删除过期项
    for (const [key, item] of cache.value.entries()) {
      if (now > item.expireTime) {
        cache.value.delete(key)
      }
    }

    // 如果缓存仍然过大，删除最旧的项
    if (stats.value.totalCacheSize > maxCacheSize) {
      const sortedEntries = Array.from(cache.value.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)

      const targetSize = maxCacheSize * 0.8 // 清理到80%
      let currentSize = stats.value.totalCacheSize

      for (const [key] of sortedEntries) {
        if (currentSize <= targetSize) break
        const item = cache.value.get(key)
        if (item) {
          currentSize -= item.size
          cache.value.delete(key)
        }
      }
    }

    updateCacheStats()
  }

  /**
   * 更新缓存统计
   */
  function updateCacheStats(): void {
    let totalSize = 0
    for (const item of cache.value.values()) {
      totalSize += item.size
    }
    stats.value.totalCacheSize = totalSize
    stats.value.cacheHitRate = stats.value.totalRequests > 0
      ? (stats.value.cachedRequests / stats.value.totalRequests) * 100
      : 0
  }

  /**
   * 等待请求槽位
   */
  async function waitForSlot(): Promise<void> {
    return new Promise(resolve => {
      const checkSlot = () => {
        if (canMakeRequest.value) {
          resolve()
        } else {
          safeSetTimeout(checkSlot, 100)
        }
      }
      checkSlot()
    })
  }

  /**
   * 处理请求队列
   */
  function processQueue(): void {
    if (requestQueue.value.length > 0 && canMakeRequest.value) {
      const nextRequest = requestQueue.value.shift()
      if (nextRequest) {
        request(nextRequest)
      }
    }
  }

  /**
   * 发送请求
   */
  async function request<T = unknown>(config: RequestConfig & { data?: unknown }): Promise<RequestResult<T>> {
    const { url, method = 'GET', data, headers = {}, enableCache = false, ...rest } = config

    // 使用相对路径，让浏览器自动处理基础URL
    const requestUrl = url.startsWith('/') ? url : `/${url}`

    try {
      const response = await axios.request<APIResponse<T>>({
        url: requestUrl,
        method,
        data,
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true,
        timeout: EnvConfig.API_TIMEOUT,
        ...rest
      })

      return {
        success: response.data.success,
        data: response.data.data,
        message: response.data.message,
        status: response.status
      }
    } catch (error: unknown) {
      console.error('请求错误:', error)
      handleRequestError(error)
      throw error
    }
  }

  /**
   * 执行实际请求
   */
  async function executeRequest<T>(
    config: RequestConfig,
    abortController: AbortController,
    startTime: number
  ): Promise<RequestResult<T>> {
    const {
      url,
      timeout = 30000,
      onProgress,
      enableCache,
      cacheTime,
      retries,
      retryDelay,
      abortController: _abortController,
      ...fetchConfig
    } = config

    // 设置超时
    const timeoutId = safeSetTimeout(() => {
      abortController.abort()
    }, timeout)

    try {
      const response = await fetch(url, {
        ...fetchConfig,
        signal: abortController.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP Error: ${response.status} ${response.statusText}`)
      }

      // 处理进度
      if (onProgress && response.body) {
        const contentLength = response.headers.get('Content-Length')
        if (contentLength) {
          const total = parseInt(contentLength, 10)
          let loaded = 0

          const reader = response.body.getReader()
          const chunks: Uint8Array[] = []

          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            chunks.push(value)
            loaded += value.length
            onProgress(loaded / total)
          }

          const allChunks = new Uint8Array(loaded)
          let position = 0
          for (const chunk of chunks) {
            allChunks.set(chunk, position)
            position += chunk.length
          }

          const text = new TextDecoder().decode(allChunks)
          const data = JSON.parse(text)

          return {
            data,
            status: response.status,
            headers: response.headers,
            fromCache: false,
            timestamp: startTime
          }
        }
      }

      // 普通响应处理
      const contentType = response.headers.get('Content-Type') || ''
      let data: T

      if (contentType.includes('application/json')) {
        data = await response.json()
      } else if (contentType.includes('text/')) {
        data = await response.text() as unknown as T
      } else {
        data = await response.blob() as unknown as T
      }

      return {
        data,
        status: response.status,
        headers: response.headers,
        fromCache: false,
        timestamp: startTime
      }

    } catch (error) {
      clearTimeout(timeoutId)

      if (abortController.signal.aborted) {
        throw new Error('请求超时或被取消')
      }

      throw error
    }
  }

  /**
   * GET请求
   */
  async function get<T = unknown>(url: string, config?: Partial<RequestConfig> & { params?: Record<string, unknown> }): Promise<RequestResult<T>> {
    let fullUrl = url

    // 添加查询参数
    if (config?.params) {
      const searchParams = new URLSearchParams()
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      const paramString = searchParams.toString()
      if (paramString) {
        fullUrl += (fullUrl.includes('?') ? '&' : '?') + paramString
      }
    }

    const { params, ...restConfig } = config || {}

    return request<T>({
      url: fullUrl,
      method: 'GET',
      enableCache: true,
      ...restConfig
    })
  }

  /**
   * POST请求
   */
  async function post<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<RequestResult<T>> {
    return request<T>({
      url: url,
      method: 'POST',
      data, // 直接传递data，而不是body
      headers: {
        'Content-Type': 'application/json',
        ...config?.headers
      },
      enableCache: false,
      ...config
    })
  }

  /**
   * PUT请求
   */
  async function put<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<RequestResult<T>> {
    return request<T>({
      url: url,
      method: 'PUT',
      data, // 直接传递data，而不是body
      headers: {
        'Content-Type': 'application/json',
        ...config?.headers
      },
      enableCache: false,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  async function del<T = unknown>(url: string, config?: Partial<RequestConfig>): Promise<RequestResult<T>> {
    return request<T>({
      url: url,
      method: 'DELETE',
      enableCache: false,
      ...config
    })
  }

  /**
   * 取消请求
   */
  function cancelRequest(url: string): void {
    for (const [key, controller] of activeRequests.value.entries()) {
      if (key.includes(url)) {
        controller.abort()
        activeRequests.value.delete(key)
      }
    }
  }

  /**
   * 取消所有请求
   */
  function cancelAllRequests(): void {
    for (const controller of activeRequests.value.values()) {
      controller.abort()
    }
    activeRequests.value.clear()
    requestQueue.value = []
  }

  /**
   * 清空缓存
   */
  function clearCache(): void {
    cache.value.clear()
    updateCacheStats()
  }

  /**
   * 预加载资源
   */
  async function preload(urls: string[]): Promise<void> {
    const promises = urls.map(url =>
      get(url, { enableCache: true, cacheTime: 3600000 }) // 1小时缓存
        .catch(error => {
          console.warn(`预加载失败: ${url}`, error)
        })
    )

    await Promise.allSettled(promises)
  }

  /**
   * 监听网络状态
   */
  function setupNetworkListener(): (() => void) {
    const updateNetworkStatus = () => {
      networkStatus.value.online = navigator.onLine

      // 尝试获取连接信息 (如果支持)
      const connection = (navigator as Record<string, unknown>).connection
      if (connection) {
        networkStatus.value.connectionType = connection.type || 'unknown'
        networkStatus.value.effectiveType = connection.effectiveType || 'unknown'
        networkStatus.value.downlink = connection.downlink || 0
        networkStatus.value.rtt = connection.rtt || 0
      }
    }

    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)

    // 初始化
    updateNetworkStatus()

    // 定期检查网络状态
    const networkCheckInterval = setInterval(updateNetworkStatus, 30000)

    return () => {
      window.removeEventListener('online', updateNetworkStatus)
      window.removeEventListener('offline', updateNetworkStatus)
      clearInterval(networkCheckInterval)
    }
  }

  /**
   * 批量请求
   */
  async function batch<T = unknown>(requests: RequestConfig[]): Promise<RequestResult<T | null>[]> {
    const promises = requests.map(config =>
      request<T>(config).catch(error => ({
        data: null as T | null,
        status: 0,
        headers: new Headers(),
        fromCache: false,
        timestamp: Date.now(),
        error
      } as RequestResult<T | null>))
    )

    return Promise.all(promises)
  }

  // 初始化网络监听
  const cleanupNetworkListener = setupNetworkListener()

  return {
    // 状态
    networkStatus: readonly(networkStatus),
    stats: readonly(stats),
    activeRequestCount,
    cacheSize,
    isOnline,
    canMakeRequest,

    // 请求方法
    request,
    get,
    post,
    put,
    delete: del,
    batch,

    // 管理方法
    cancelRequest,
    cancelAllRequests,
    clearCache,
    preload,

    // 缓存管理
    getCachedResponse,
    setCachedResponse,
    cleanupCache,

    // 配置
    maxConcurrentRequests,

    // 清理
    cleanup: () => {
      cancelAllRequests()
      clearCache()
      cleanupNetworkListener()
    }
  }
}

/**
 * 创建请求状态管理
 */
export function useRequestState() {
  const state = shallowRef<RequestState>({
    loading: false,
    error: null,
    completed: false,
    progress: 0,
    fromCache: false
  })

  const setLoading = (loading: boolean) => {
    state.value.loading = loading
    if (loading) {
      state.value.error = null
      state.value.completed = false
    }
  }

  const setError = (error: Error | null) => {
    state.value.error = error
    state.value.loading = false
    state.value.completed = true
  }

  const setProgress = (progress: number) => {
    state.value.progress = Math.max(0, Math.min(100, progress))
  }

  const setCompleted = (fromCache = false) => {
    state.value.loading = false
    state.value.completed = true
    state.value.error = null
    state.value.fromCache = fromCache
  }

  const reset = () => {
    state.value = {
      loading: false,
      error: null,
      completed: false,
      progress: 0,
      fromCache: false
    }
  }

  return {
    state: readonly(state),
    setLoading,
    setError,
    setProgress,
    setCompleted,
    reset
  }
}