/**
 * 统一响应处理器
 * 处理HTTP响应，提供标准化的响应格式转换
 */

import type { AxiosResponse } from 'axios'
import type { APIResponse, ResponseHandlingConfig } from '../types'

export class ResponseHandler {
  private config: ResponseHandlingConfig

  constructor(config: ResponseHandlingConfig = {}) {
    this.config = {
      enableLogging: false,
      enableDataTransform: true,
      enableMetadataExtraction: true,
      successCodes: [200, 201, 202, 204],
      dataField: 'data',
      messageField: 'message',
      codeField: 'code',
      successField: 'success',
      ...config
    }
  }

  /**
   * 处理成功响应
   */
  handleSuccessResponse<T>(response: AxiosResponse): AxiosResponse<T> {
    if (this.config.enableLogging) {
      console.log('Response Success:', response.status, response.config.url)
    }

    // 如果启用了数据转换，对响应数据进行预处理
    if (this.config.enableDataTransform) {
      response.data = this.transformResponseData(response.data)
    }

    return response
  }

  /**
   * 转换响应为标准API响应格式
   */
  transformResponse<T>(response: AxiosResponse): APIResponse<T> {
    try {
      const { data, status, statusText, headers } = response
      
      // 检查是否为成功状态码
      const isSuccess = this.config.successCodes!.includes(status)
      
      // 如果响应数据已经是标准格式
      if (this.isStandardAPIResponse(data)) {
        return {
          ...data,
          success: data.success ?? isSuccess,
          status,
          headers: this.config.enableMetadataExtraction ? this.extractHeaders(headers) : undefined
        }
      }

      // 转换为标准格式
      return this.createStandardResponse<T>(data, status, statusText, headers, isSuccess)
      
    } catch (error) {
      console.error('Response transformation failed:', error)
      
      return {
        success: false,
        data: null as T,
        message: '响应处理失败',
        code: 'RESPONSE_TRANSFORM_ERROR',
        status: response.status
      }
    }
  }

  /**
   * 创建标准响应格式
   */
  private createStandardResponse<T>(
    data: unknown,
    status: number,
    statusText: string,
    headers: unknown,
    isSuccess: boolean
  ): APIResponse<T> {
    const response: APIResponse<T> = {
      success: isSuccess,
      data: this.extractResponseData<T>(data),
      message: this.extractMessage(data) || statusText,
      code: this.extractCode(data) || `HTTP_${status}`,
      status
    }

    // 添加元数据
    if (this.config.enableMetadataExtraction) {
      response.headers = this.extractHeaders(headers)
      response.timestamp = Date.now()
    }

    return response
  }

  /**
   * 转换响应数据
   */
  private transformResponseData(data: unknown): unknown {
    if (!data || typeof data !== 'object') {
      return data
    }

    // 处理数组响应
    if (Array.isArray(data)) {
      return data.map(item => this.transformResponseData(item))
    }

    // 处理对象响应
    const transformed = { ...data as Record<string, unknown> }

    // 日期字符串转换
    this.transformDates(transformed)

    // 数值字符串转换
    this.transformNumbers(transformed)

    return transformed
  }

  /**
   * 转换日期字段
   */
  private transformDates(obj: Record<string, unknown>): void {
    const dateFields = ['created_at', 'updated_at', 'deleted_at', 'date', 'time', 'timestamp']
    
    for (const key in obj) {
      const value = obj[key]
      
      if (typeof value === 'string') {
        // 检查是否为日期字段或ISO日期格式
        const isDateField = dateFields.includes(key.toLowerCase())
        const isISODate = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)
        
        if (isDateField || isISODate) {
          const date = new Date(value)
          if (!isNaN(date.getTime())) {
            obj[key] = date.toISOString()
          }
        }
      } else if (value && typeof value === 'object' && !Array.isArray(value)) {
        this.transformDates(value as Record<string, unknown>)
      }
    }
  }

  /**
   * 转换数值字段
   */
  private transformNumbers(obj: Record<string, unknown>): void {
    const numberFields = ['id', 'count', 'total', 'size', 'limit', 'offset', 'page', 'per_page']
    
    for (const key in obj) {
      const value = obj[key]
      
      if (typeof value === 'string' && numberFields.includes(key.toLowerCase())) {
        const num = Number(value)
        if (!isNaN(num) && isFinite(num)) {
          obj[key] = num
        }
      } else if (value && typeof value === 'object' && !Array.isArray(value)) {
        this.transformNumbers(value as Record<string, unknown>)
      }
    }
  }

  /**
   * 检查是否为标准API响应格式
   */
  private isStandardAPIResponse(data: unknown): boolean {
    if (!data || typeof data !== 'object') {
      return false
    }

    const obj = data as Record<string, unknown>
    const hasSuccessField = this.config.successField! in obj
    const hasDataField = this.config.dataField! in obj
    const hasMessageField = this.config.messageField! in obj

    return hasSuccessField || (hasDataField && hasMessageField)
  }

  /**
   * 提取响应数据
   */
  private extractResponseData<T>(data: unknown): T {
    if (!data || typeof data !== 'object') {
      return data as T
    }

    const obj = data as Record<string, unknown>
    
    // 尝试从标准字段提取数据
    if (this.config.dataField! in obj) {
      return obj[this.config.dataField!] as T
    }

    // 如果没有data字段，返回整个对象
    return data as T
  }

  /**
   * 提取消息
   */
  private extractMessage(data: unknown): string | undefined {
    if (!data || typeof data !== 'object') {
      return undefined
    }

    const obj = data as Record<string, unknown>
    
    const messageField = this.config.messageField!
    if (messageField in obj && typeof obj[messageField] === 'string') {
      return obj[messageField] as string
    }

    // 尝试其他常见字段
    const fallbackFields = ['msg', 'error', 'description']
    for (const field of fallbackFields) {
      if (field in obj && typeof obj[field] === 'string') {
        return obj[field] as string
      }
    }

    return undefined
  }

  /**
   * 提取错误代码
   */
  private extractCode(data: unknown): string | undefined {
    if (!data || typeof data !== 'object') {
      return undefined
    }

    const obj = data as Record<string, unknown>
    
    const codeField = this.config.codeField!
    if (codeField in obj) {
      const code = obj[codeField]
      return typeof code === 'string' ? code : String(code)
    }

    // 尝试其他常见字段
    const fallbackFields = ['error_code', 'errno', 'errorCode']
    for (const field of fallbackFields) {
      if (field in obj) {
        const code = obj[field]
        return typeof code === 'string' ? code : String(code)
      }
    }

    return undefined
  }

  /**
   * 提取响应头信息
   */
  private extractHeaders(headers: unknown): Record<string, string> | undefined {
    if (!headers || typeof headers !== 'object') {
      return undefined
    }

    const extracted: Record<string, string> = {}
    const importantHeaders = [
      'content-type',
      'content-length',
      'cache-control',
      'etag',
      'last-modified',
      'x-request-id',
      'x-rate-limit-remaining',
      'x-rate-limit-reset'
    ]

    const headerObj = headers as Record<string, unknown>
    for (const key of importantHeaders) {
      const value = headerObj[key] || headerObj[key.toLowerCase()]
      if (typeof value === 'string') {
        extracted[key] = value
      }
    }

    return Object.keys(extracted).length > 0 ? extracted : undefined
  }

  /**
   * 验证响应数据
   */
  validateResponse<T>(response: APIResponse<T>): boolean {
    // 基本结构验证
    if (!response || typeof response !== 'object') {
      return false
    }

    // 必须字段验证
    if (typeof response.success !== 'boolean') {
      return false
    }

    if (response.success && response.data === undefined) {
      return false
    }

    return true
  }

  /**
   * 获取分页信息
   */
  extractPaginationInfo(response: APIResponse<unknown>): {
    page?: number
    per_page?: number
    total?: number
    total_pages?: number
  } | undefined {
    if (!this.config.enableMetadataExtraction) {
      return undefined
    }

    const data = response.data as any
    if (!data || typeof data !== 'object') {
      return undefined
    }

    // 检查常见的分页字段
    const paginationFields = {
      page: data.page || data.current_page,
      per_page: data.per_page || data.page_size || data.limit,
      total: data.total || data.total_count,
      total_pages: data.total_pages || data.page_count
    }

    // 过滤undefined值
    const pagination = Object.fromEntries(
      Object.entries(paginationFields).filter(([_, value]) => value !== undefined)
    )

    return Object.keys(pagination).length > 0 ? pagination : undefined
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ResponseHandlingConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取配置
   */
  getConfig(): ResponseHandlingConfig {
    return { ...this.config }
  }
}