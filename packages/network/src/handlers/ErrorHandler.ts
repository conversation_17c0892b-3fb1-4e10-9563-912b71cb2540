/**
 * 统一错误处理器
 * 处理HTTP请求和响应错误，提供标准化的错误格式
 */

import type { AxiosError } from 'axios'
import type { ErrorHandlingConfig, APIError } from '../types'

export class ErrorHandler {
  private config: ErrorHandlingConfig

  constructor(config: ErrorHandlingConfig = {}) {
    this.config = {
      enableLogging: true,
      enableUserFriendlyMessages: true,
      retryAttempts: 0,
      retryDelay: 1000,
      ...config
    }
  }

  /**
   * 处理请求错误
   */
  handleRequestError(error: unknown): APIError {
    if (this.config.enableLogging) {
      console.error('Request Error:', error)
    }

    return this.transformError(error)
  }

  /**
   * 处理响应错误
   */
  handleResponseError(error: AxiosError): APIError {
    if (this.config.enableLogging) {
      console.error('Response Error:', error)
    }

    return this.transformError(error)
  }

  /**
   * 转换错误为标准格式
   */
  private transformError(error: unknown): APIError {
    let apiError: APIError = {
      success: false,
      message: '未知错误',
      code: 'UNKNOWN_ERROR',
      status: 0,
      data: null
    }

    if (this.isAxiosError(error)) {
      apiError = this.handleAxiosError(error)
    } else if (error instanceof Error) {
      apiError = this.handleGenericError(error)
    } else if (typeof error === 'string') {
      apiError.message = error
    }

    // 应用用户友好消息
    if (this.config.enableUserFriendlyMessages) {
      apiError.message = this.getUserFriendlyMessage(apiError)
    }

    return apiError
  }

  /**
   * 处理Axios错误
   */
  private handleAxiosError(error: AxiosError): APIError {
    const apiError: APIError = {
      success: false,
      message: error.message,
      code: 'HTTP_ERROR',
      status: error.response?.status || 0,
      data: null,
      originalError: error
    }

    // 处理响应错误
    if (error.response) {
      apiError.status = error.response.status
      apiError.data = error.response.data

      // 尝试从响应中提取错误信息
      const responseData = error.response.data as any
      if (responseData) {
        // 后端标准错误格式: { code: number, message: string }
        if (typeof responseData.code === 'number' && responseData.message) {
          apiError.message = responseData.message
          apiError.code = `HTTP_${responseData.code}`
        }
        // 兼容格式: { error: "错误描述" }
        else if (responseData.error) {
          apiError.message = responseData.error
        }
        // 兼容格式: { message: "错误描述" }
        else if (responseData.message) {
          apiError.message = responseData.message
        }
      }

      // 根据状态码设置错误类型
      apiError.code = this.getErrorCodeByStatus(error.response.status)
    }
    // 处理请求错误（网络错误等）
    else if (error.request) {
      apiError.code = 'NETWORK_ERROR'
      apiError.message = '网络连接失败'
    }
    // 处理配置错误
    else {
      apiError.code = 'REQUEST_CONFIG_ERROR'
      apiError.message = '请求配置错误'
    }

    return apiError
  }

  /**
   * 处理通用错误
   */
  private handleGenericError(error: Error): APIError {
    let code = 'GENERIC_ERROR'
    let message = error.message

    // 识别特定错误类型
    if (error.message.includes('Failed to fetch')) {
      code = 'FETCH_ERROR'
      message = '网络连接失败'
    } else if (error.message.includes('Network Error')) {
      code = 'NETWORK_ERROR'
      message = '网络错误'
    } else if (error.message.includes('timeout')) {
      code = 'TIMEOUT_ERROR'
      message = '请求超时'
    } else if (error.message.includes('abort')) {
      code = 'ABORT_ERROR'
      message = '请求被取消'
    }

    return {
      success: false,
      message,
      code,
      status: 0,
      data: null,
      originalError: error
    }
  }

  /**
   * 根据HTTP状态码获取错误代码
   */
  private getErrorCodeByStatus(status: number): string {
    switch (status) {
      case 400:
        return 'BAD_REQUEST'
      case 401:
        return 'UNAUTHORIZED'
      case 403:
        return 'FORBIDDEN'
      case 404:
        return 'NOT_FOUND'
      case 409:
        return 'CONFLICT'
      case 422:
        return 'VALIDATION_ERROR'
      case 429:
        return 'TOO_MANY_REQUESTS'
      case 500:
        return 'INTERNAL_SERVER_ERROR'
      case 502:
        return 'BAD_GATEWAY'
      case 503:
        return 'SERVICE_UNAVAILABLE'
      case 504:
        return 'GATEWAY_TIMEOUT'
      default:
        return `HTTP_${status}`
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(error: APIError): string {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误，请检查输入信息',
      401: '认证失败，请重新登录',
      403: '权限不足，无法执行此操作',
      404: '请求的资源不存在',
      409: '资源冲突，请稍后重试',
      422: '数据验证失败，请检查输入信息',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误，请联系管理员',
      502: '网关错误，请稍后重试',
      503: '服务暂时不可用，请稍后重试',
      504: '请求超时，请稍后重试'
    }

    const codeMessages: Record<string, string> = {
      NETWORK_ERROR: '网络连接失败，请检查网络设置',
      TIMEOUT_ERROR: '请求超时，请稍后重试',
      FETCH_ERROR: '网络连接失败，请检查网络设置',
      ABORT_ERROR: '请求被取消',
      UNAUTHORIZED: '登录已过期，请重新登录',
      FORBIDDEN: '权限不足，无法访问此资源',
      NOT_FOUND: '请求的资源不存在',
      VALIDATION_ERROR: '输入信息有误，请检查后重试',
      CONFLICT: '数据冲突，请刷新页面后重试',
      
      // 业务相关错误
      PROJECT_NOT_FOUND: '项目不存在或已被删除',
      PROJECT_ACCESS_DENIED: '无权限访问此项目',
      TEMPLATE_NOT_FOUND: '模板不存在或已被删除',
      INSTANCE_NOT_FOUND: '实例不存在或已被删除',
      USER_NOT_FOUND: '用户不存在',
      DUPLICATE_NAME: '名称已存在，请使用其他名称',
      INVALID_FILE_FORMAT: '文件格式不支持',
      FILE_TOO_LARGE: '文件大小超出限制',
      QUOTA_EXCEEDED: '已达到配额限制',
      OPERATION_NOT_ALLOWED: '当前状态下不允许此操作'
    }

    // 优先使用状态码映射
    if (error.status && statusMessages[error.status]) {
      return statusMessages[error.status]
    }

    // 其次使用错误代码映射
    if (error.code && codeMessages[error.code]) {
      return codeMessages[error.code]
    }

    // 返回原始消息或默认消息
    return error.message || '操作失败，请稍后重试'
  }

  /**
   * 提取错误消息
   */
  extractErrorMessage(error: unknown): string {
    if (this.isAxiosError(error)) {
      const responseData = error.response?.data as any
      if (responseData?.message) {
        return responseData.message
      }
      if (responseData?.error) {
        return responseData.error
      }
    }

    if (error instanceof Error) {
      return error.message
    }

    if (typeof error === 'string') {
      return error
    }

    return '未知错误'
  }

  /**
   * 检查是否为Axios错误
   */
  private isAxiosError(error: unknown): error is AxiosError {
    return error !== null && 
           typeof error === 'object' && 
           'isAxiosError' in error &&
           (error as any).isAxiosError === true
  }

  /**
   * 检查错误是否可重试
   */
  isRetryableError(error: APIError): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'INTERNAL_SERVER_ERROR',
      'BAD_GATEWAY',
      'SERVICE_UNAVAILABLE',
      'GATEWAY_TIMEOUT'
    ]

    const retryableStatuses = [500, 502, 503, 504]

    return retryableCodes.includes(error.code) || 
           (error.status && retryableStatuses.includes(error.status))
  }

  /**
   * 获取重试延迟时间
   */
  getRetryDelay(attempt: number): number {
    return this.config.retryDelay! * Math.pow(2, attempt) // 指数退避
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorHandlingConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取配置
   */
  getConfig(): ErrorHandlingConfig {
    return { ...this.config }
  }

  /**
   * 错误分类
   */
  categorizeError(error: APIError): 'network' | 'auth' | 'validation' | 'business' | 'server' | 'unknown' {
    if (!error.status && (error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT_ERROR')) {
      return 'network'
    }

    if (error.status === 401 || error.code === 'UNAUTHORIZED') {
      return 'auth'
    }

    if (error.status === 400 || error.status === 422 || error.code === 'VALIDATION_ERROR') {
      return 'validation'
    }

    if (error.status === 403 || error.status === 404 || error.status === 409) {
      return 'business'
    }

    if (error.status && error.status >= 500) {
      return 'server'
    }

    return 'unknown'
  }

  /**
   * 添加错误上下文信息
   */
  addErrorContext(error: APIError, context: {
    operation?: string
    resource?: string
    userId?: string
    timestamp?: string
    requestId?: string
  }): APIError {
    return {
      ...error,
      context: {
        ...error.context,
        ...context,
        timestamp: context.timestamp || new Date().toISOString()
      }
    }
  }

  /**
   * 格式化错误用于日志记录
   */
  formatErrorForLogging(error: APIError): string {
    const category = this.categorizeError(error)
    const context = error.context || {}
    
    return JSON.stringify({
      category,
      code: error.code,
      status: error.status,
      message: error.message,
      context,
      timestamp: new Date().toISOString()
    }, null, 2)
  }

  /**
   * 检查是否需要显示错误通知
   */
  shouldShowNotification(error: APIError): boolean {
    const category = this.categorizeError(error)
    
    // 网络错误和服务器错误通常需要显示通知
    if (category === 'network' || category === 'server') {
      return true
    }

    // 认证错误需要显示通知
    if (category === 'auth') {
      return true
    }

    // 业务错误根据具体情况决定
    if (category === 'business') {
      const silentCodes = ['NOT_FOUND'] // 某些业务错误可能不需要显示通知
      return !silentCodes.includes(error.code)
    }

    // 验证错误通常在表单中显示，不需要额外通知
    return category !== 'validation'
  }
}