/**
 * 认证管理器
 * 处理JWT token管理、自动刷新、认证头设置等功能
 */

import type { AuthConfig, TokenInfo } from '../types'

export class AuthManager {
  private config: AuthConfig
  private isRefreshing = false
  private refreshSubscribers: Array<(token: string) => void> = []

  constructor(config: AuthConfig = {}) {
    this.config = {
      tokenKey: 'auth_token',
      refreshTokenKey: 'refresh_token',
      userInfoKey: 'user_info',
      refreshThreshold: 5 * 60 * 1000, // 5分钟
      rememberMeThreshold: 24 * 60 * 60 * 1000, // 24小时
      refreshEndpoint: '/auth/refresh',
      loginRedirect: '/auth/login',
      supportedLanguages: ['zh-CN', 'en-US'],
      defaultLanguage: 'zh-CN',
      ...config
    }
  }

  /**
   * 获取认证头
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {}
    
    const token = this.getToken()
    if (token) {
      // 检查是否需要刷新token
      if (this.shouldRefreshToken() && this.config.enableAutoRefresh) {
        try {
          const newToken = await this.refreshToken()
          headers.Authorization = `Bearer ${newToken}`
        } catch (error) {
          console.error('Token refresh failed:', error)
          headers.Authorization = `Bearer ${token}`
        }
      } else {
        headers.Authorization = `Bearer ${token}`
      }
    }

    return headers
  }

  /**
   * 获取语言头
   */
  getLanguageHeaders(): Record<string, string> {
    if (!this.config.enableI18n) {
      return {}
    }

    const currentLang = this.getCurrentLanguage()
    return {
      'Accept-Language': currentLang
    }
  }

  /**
   * 获取当前token
   */
  getToken(): string | null {
    return localStorage.getItem(this.config.tokenKey!)
  }

  /**
   * 获取刷新token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem(this.config.refreshTokenKey!)
  }

  /**
   * 设置token信息
   */
  setTokenInfo(tokenInfo: TokenInfo): void {
    const { access_token, refresh_token, user, remember_me } = tokenInfo

    localStorage.setItem(this.config.tokenKey!, access_token)
    
    if (refresh_token) {
      localStorage.setItem(this.config.refreshTokenKey!, refresh_token)
    }
    
    if (user) {
      localStorage.setItem(this.config.userInfoKey!, JSON.stringify(user))
    }

    // 如果是记住我模式，设置相应的标识
    if (remember_me) {
      localStorage.setItem('remember_me', 'true')
    }
  }

  /**
   * 清除认证信息
   */
  clearAuthInfo(): void {
    localStorage.removeItem(this.config.tokenKey!)
    localStorage.removeItem(this.config.refreshTokenKey!)
    localStorage.removeItem(this.config.userInfoKey!)
    localStorage.removeItem('remember_me')
    localStorage.removeItem('preferred-locale')
  }

  /**
   * 检查是否需要刷新token
   */
  shouldRefreshToken(error?: any): boolean {
    // 如果传入了错误对象，检查是否是401错误
    if (error) {
      return error.response?.status === 401
    }

    const token = this.getToken()
    if (!token) {
      return false
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const exp = payload.exp * 1000 // 转换为毫秒
      const now = Date.now()
      const timeUntilExpiry = exp - now
      
      // 根据是否为记住我token选择不同的刷新阈值
      let refreshThreshold = this.config.refreshThreshold!
      if (payload.remember_me || localStorage.getItem('remember_me')) {
        refreshThreshold = this.config.rememberMeThreshold!
      }
      
      return timeUntilExpiry < refreshThreshold
    } catch (error) {
      console.error('Failed to parse token:', error)
      return false
    }
  }

  /**
   * 获取token过期时间
   */
  getTokenExpiryTime(): number | null {
    const token = this.getToken()
    if (!token) {
      return null
    }
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp * 1000 // 转换为毫秒
    } catch (error) {
      console.error('Failed to parse token:', error)
      return null
    }
  }

  /**
   * 检查token是否为记住我token
   */
  isRememberMeToken(): boolean {
    const token = this.getToken()
    if (!token) {
      return false
    }
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.remember_me === true || localStorage.getItem('remember_me') === 'true'
    } catch (error) {
      console.error('Failed to parse token:', error)
      return false
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(): Promise<string> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    // 如果正在刷新，等待当前刷新完成
    if (this.isRefreshing) {
      return new Promise((resolve) => {
        this.refreshSubscribers.push(resolve)
      })
    }

    this.isRefreshing = true

    try {
      const response = await fetch(this.config.refreshEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${refreshToken}`
        }
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const data = await response.json()
      
      if (data.data && data.data.access_token) {
        const newToken = data.data.access_token
        
        // 更新本地存储的token信息
        this.setTokenInfo({
          access_token: newToken,
          refresh_token: data.data.refresh_token || refreshToken,
          user: data.data.user,
          remember_me: this.isRememberMeToken()
        })
        
        // 通知所有等待的请求
        this.notifyRefreshSubscribers(newToken)
        
        return newToken
      }
      
      throw new Error('Invalid refresh response')
    } catch (error) {
      console.error('Token refresh failed:', error)
      
      // 刷新失败，清除认证信息并重定向到登录页
      this.handleAuthFailure()
      
      throw error
    } finally {
      this.isRefreshing = false
      this.refreshSubscribers = []
    }
  }

  /**
   * 添加刷新订阅者
   */
  private addRefreshSubscriber(callback: (token: string) => void): void {
    this.refreshSubscribers.push(callback)
  }

  /**
   * 通知所有刷新订阅者
   */
  private notifyRefreshSubscribers(token: string): void {
    this.refreshSubscribers.forEach(callback => callback(token))
    this.refreshSubscribers = []
  }

  /**
   * 处理认证失败
   */
  handleAuthFailure(): void {
    this.clearAuthInfo()
    
    // 避免在已经在登录页面时重复跳转
    if (window.location.pathname !== this.config.loginRedirect) {
      window.location.href = this.config.loginRedirect!
    }
  }

  /**
   * 获取当前语言
   */
  getCurrentLanguage(): string {
    const saved = localStorage.getItem('preferred-locale')
    if (saved && this.config.supportedLanguages!.includes(saved)) {
      return saved
    }
    
    // 尝试从浏览器语言获取
    const browserLang = navigator.language
    const shortLang = browserLang.split('-')[0]
    
    for (const lang of this.config.supportedLanguages!) {
      if (lang.startsWith(shortLang)) {
        return lang
      }
    }
    
    return this.config.defaultLanguage!
  }

  /**
   * 设置当前语言
   */
  setCurrentLanguage(language: string): void {
    if (this.config.supportedLanguages!.includes(language)) {
      localStorage.setItem('preferred-locale', language)
    }
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): any {
    const userInfo = localStorage.getItem(this.config.userInfoKey!)
    if (userInfo) {
      try {
        return JSON.parse(userInfo)
      } catch (error) {
        console.error('Failed to parse user info:', error)
        return null
      }
    }
    return null
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return !!this.getToken()
  }

  /**
   * 获取配置
   */
  getConfig(): AuthConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AuthConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.refreshSubscribers = []
    this.isRefreshing = false
  }
}