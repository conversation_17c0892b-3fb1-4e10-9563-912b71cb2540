<template>
  <div class="network-status">
    <!-- 网络状态指示器 -->
    <div 
      class="status-indicator"
      :class="{
        'online': networkStatus.online,
        'offline': !networkStatus.online,
        'slow': isSlowConnection
      }"
    >
      <div class="status-dot"></div>
      <span class="status-text">
        {{ networkStatus.online ? '在线' : '离线' }}
        <span v-if="networkStatus.online && connectionType" class="connection-type">
          ({{ connectionType }})
        </span>
      </span>
    </div>
    
    <!-- 详细信息面板 -->
    <n-popover
      v-if="showDetails"
      placement="bottom"
      :width="300"
      trigger="hover"
    >
      <template #trigger>
        <n-button 
          size="small" 
          text
          class="details-trigger"
        >
          <template #icon>
            <n-icon><InformationCircleOutline /></n-icon>
          </template>
        </n-button>
      </template>
      
      <div class="network-details">
        <h4>网络状态</h4>
        
        <n-descriptions :column="1" size="small" bordered>
          <n-descriptions-item label="连接状态">
            <n-tag :type="networkStatus.online ? 'success' : 'error'">
              {{ networkStatus.online ? '在线' : '离线' }}
            </n-tag>
          </n-descriptions-item>
          
          <n-descriptions-item label="连接类型" v-if="connectionInfo.type">
            {{ connectionInfo.type }}
          </n-descriptions-item>
          
          <n-descriptions-item label="有效类型" v-if="connectionInfo.effectiveType">
            {{ connectionInfo.effectiveType }}
          </n-descriptions-item>
          
          <n-descriptions-item label="下行速度" v-if="connectionInfo.downlink">
            {{ connectionInfo.downlink }} Mbps
          </n-descriptions-item>
          
          <n-descriptions-item label="往返时间" v-if="connectionInfo.rtt">
            {{ connectionInfo.rtt }} ms
          </n-descriptions-item>
          
          <n-descriptions-item label="省流模式" v-if="connectionInfo.saveData !== undefined">
            <n-tag :type="connectionInfo.saveData ? 'warning' : 'success'">
              {{ connectionInfo.saveData ? '开启' : '关闭' }}
            </n-tag>
          </n-descriptions-item>
        </n-descriptions>
        
        <!-- 网络性能指标 -->
        <div class="performance-metrics" v-if="showPerformance">
          <h5>性能指标</h5>
          
          <div class="metric-item">
            <span class="metric-label">延迟:</span>
            <span class="metric-value" :class="getLatencyClass(metrics.latency)">
              {{ metrics.latency }}ms
            </span>
          </div>
          
          <div class="metric-item">
            <span class="metric-label">带宽:</span>
            <span class="metric-value">
              {{ formatBandwidth(metrics.bandwidth) }}
            </span>
          </div>
          
          <div class="metric-item">
            <span class="metric-label">丢包率:</span>
            <span class="metric-value" :class="getPacketLossClass(metrics.packetLoss)">
              {{ metrics.packetLoss }}%
            </span>
          </div>
        </div>
        
        <!-- 请求统计 -->
        <div class="request-stats" v-if="showStats">
          <h5>请求统计</h5>
          
          <div class="stat-grid">
            <div class="stat-item">
              <div class="stat-number">{{ requestStats.total }}</div>
              <div class="stat-label">总请求</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-number">{{ requestStats.success }}</div>
              <div class="stat-label">成功</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-number">{{ requestStats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-number">{{ requestStats.cached }}</div>
              <div class="stat-label">缓存</div>
            </div>
          </div>
          
          <div class="avg-response-time">
            平均响应时间: {{ formatTime(requestStats.avgResponseTime) }}
          </div>
        </div>
      </div>
    </n-popover>
    
    <!-- 网络质量条 -->
    <div class="quality-bar" v-if="showQualityBar">
      <div class="quality-segments">
        <div 
          v-for="(segment, index) in qualitySegments"
          :key="index"
          class="quality-segment"
          :class="{
            'active': index < activeSegments,
            [`quality-${segment.level}`]: true
          }"
        ></div>
      </div>
      <span class="quality-label">{{ qualityLabel }}</span>
    </div>
    
    <!-- 离线提示 -->
    <n-alert
      v-if="!networkStatus.online && showOfflineAlert"
      title="网络连接已断开"
      description="请检查您的网络连接。在离线状态下，部分功能可能不可用。"
      type="warning"
      show-icon
      :closable="false"
      class="offline-alert"
    />
    
    <!-- 慢速连接提示 -->
    <n-alert
      v-if="isSlowConnection && showSlowAlert"
      title="网络连接较慢"
      description="检测到网络连接速度较慢，建议在更好的网络环境下使用。"
      type="warning"
      show-icon
      closable
      class="slow-alert"
      @close="dismissSlowAlert"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useNetworkManager } from '../composables/useNetworkManager'
import { 
  NPopover, NButton, NIcon, NDescriptions, NDescriptionsItem, NTag, NAlert 
} from 'naive-ui'
import { InformationCircleOutline } from '@vicons/ionicons5'

// Props
interface Props {
  showDetails?: boolean
  showPerformance?: boolean
  showStats?: boolean
  showQualityBar?: boolean
  showOfflineAlert?: boolean
  showSlowAlert?: boolean
  autoHide?: boolean
  hideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: true,
  showPerformance: true,
  showStats: true,
  showQualityBar: true,
  showOfflineAlert: true,
  showSlowAlert: true,
  autoHide: false,
  hideDelay: 5000
})

// Composables
const { networkStatus, stats } = useNetworkManager()

// 本地状态
const connectionInfo = ref({
  type: 'unknown',
  effectiveType: 'unknown',
  downlink: 0,
  rtt: 0,
  saveData: false
})

const metrics = ref({
  latency: 0,
  bandwidth: 0,
  packetLoss: 0
})

const requestStats = computed(() => ({
  total: stats.value.totalRequests,
  success: stats.value.totalRequests - stats.value.failedRequests,
  failed: stats.value.failedRequests,
  cached: stats.value.cachedRequests,
  avgResponseTime: stats.value.averageResponseTime
}))

const slowAlertDismissed = ref(false)

// 计算属性
const connectionType = computed(() => {
  const type = connectionInfo.value.effectiveType
  const typeMap: Record<string, string> = {
    'slow-2g': '2G',
    '2g': '2G',
    '3g': '3G',
    '4g': '4G',
    '5g': '5G',
    'wifi': 'WiFi',
    'ethernet': '以太网'
  }
  return typeMap[type] || type
})

const isSlowConnection = computed(() => {
  const { effectiveType, downlink, rtt } = connectionInfo.value
  return effectiveType === 'slow-2g' || 
         effectiveType === '2g' || 
         downlink < 1.5 || 
         rtt > 400
})

const qualitySegments = computed(() => [
  { level: 'poor' },
  { level: 'fair' },
  { level: 'good' },
  { level: 'excellent' }
])

const activeSegments = computed(() => {
  const { downlink, rtt } = connectionInfo.value
  
  if (!networkStatus.value.online) return 0
  if (downlink >= 10 && rtt <= 100) return 4 // 优秀
  if (downlink >= 5 && rtt <= 200) return 3  // 良好
  if (downlink >= 1.5 && rtt <= 400) return 2 // 一般
  return 1 // 较差
})

const qualityLabel = computed(() => {
  const labels = ['', '较差', '一般', '良好', '优秀']
  return labels[activeSegments.value] || '未知'
})

// 方法
function updateConnectionInfo() {
  const connection = (navigator as Record<string, unknown>).connection || (navigator as Record<string, unknown>).mozConnection || (navigator as Record<string, unknown>).webkitConnection
  
  if (connection) {
    connectionInfo.value = {
      type: connection.type || 'unknown',
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false
    }
  }
}

function measureNetworkPerformance() {
  // 测量延迟
  const startTime = performance.now()
  
  fetch('/api/ping', { 
    method: 'HEAD',
    cache: 'no-cache'
  }).then(() => {
    const endTime = performance.now()
    metrics.value.latency = Math.round(endTime - startTime)
  }).catch(() => {
    metrics.value.latency = -1
  })
  
  // 估算带宽（使用连接信息）
  if (connectionInfo.value.downlink > 0) {
    metrics.value.bandwidth = connectionInfo.value.downlink
  }
  
  // 估算丢包率（基于失败请求比例）
  const total = requestStats.value.total
  const failed = requestStats.value.failed
  if (total > 0) {
    metrics.value.packetLoss = Math.round((failed / total) * 100)
  }
}

function getLatencyClass(latency: number): string {
  if (latency < 0) return 'unknown'
  if (latency <= 100) return 'excellent'
  if (latency <= 200) return 'good'
  if (latency <= 400) return 'fair'
  return 'poor'
}

function getPacketLossClass(loss: number): string {
  if (loss <= 1) return 'excellent'
  if (loss <= 3) return 'good'
  if (loss <= 5) return 'fair'
  return 'poor'
}

function formatBandwidth(bandwidth: number): string {
  if (bandwidth >= 1000) {
    return `${(bandwidth / 1000).toFixed(1)} Gbps`
  }
  return `${bandwidth.toFixed(1)} Mbps`
}

function formatTime(time: number): string {
  if (time < 1000) {
    return `${Math.round(time)}ms`
  }
  return `${(time / 1000).toFixed(2)}s`
}

function dismissSlowAlert() {
  slowAlertDismissed.value = true
}

// 事件监听
let connectionChangeHandler: (() => void) | null = null
let performanceInterval: number | null = null

onMounted(() => {
  // 初始化连接信息
  updateConnectionInfo()
  measureNetworkPerformance()
  
  // 监听连接变化
  connectionChangeHandler = () => {
    updateConnectionInfo()
    measureNetworkPerformance()
    slowAlertDismissed.value = false // 重置慢速连接提示
  }
  
  window.addEventListener('online', connectionChangeHandler)
  window.addEventListener('offline', connectionChangeHandler)
  
  // 监听连接信息变化
  const connection = (navigator as Record<string, unknown>).connection
  if (connection) {
    connection.addEventListener('change', connectionChangeHandler)
  }
  
  // 定期测量网络性能
  if (props.showPerformance) {
    performanceInterval = window.setInterval(() => {
      if (networkStatus.value.online) {
        measureNetworkPerformance()
      }
    }, 30000) // 每30秒测量一次
  }
})

onUnmounted(() => {
  if (connectionChangeHandler) {
    window.removeEventListener('online', connectionChangeHandler)
    window.removeEventListener('offline', connectionChangeHandler)
    
    const connection = (navigator as Record<string, unknown>).connection
    if (connection) {
      connection.removeEventListener('change', connectionChangeHandler)
    }
  }
  
  if (performanceInterval) {
    clearInterval(performanceInterval)
  }
})

// 监听网络状态变化
watch(() => networkStatus.value.online, (online) => {
  if (online) {
    // 网络恢复时重新测量
    setTimeout(() => {
      updateConnectionInfo()
      measureNetworkPerformance()
    }, 1000)
  }
})

// 自动隐藏
watch([
  () => networkStatus.value.online,
  () => isSlowConnection.value
], ([online, slow]) => {
  if (props.autoHide && online && !slow) {
    setTimeout(() => {
      // 可以触发隐藏事件给父组件
    }, props.hideDelay)
  }
})
</script>

<style scoped lang="scss">
.network-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e5e5e5;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
  }
  
  &.online .status-dot {
    background: #18a058;
    box-shadow: 0 0 4px #18a058;
  }
  
  &.offline .status-dot {
    background: #d03050;
    box-shadow: 0 0 4px #d03050;
  }
  
  &.slow .status-dot {
    background: #f0a020;
    box-shadow: 0 0 4px #f0a020;
  }
  
  .status-text {
    color: #333;
    font-weight: 500;
    
    .connection-type {
      color: #999;
      font-weight: normal;
      font-size: 12px;
    }
  }
}

.details-trigger {
  padding: 4px;
  color: #999;
  
  &:hover {
    color: #2080f0;
  }
}

.network-details {
  h4, h5 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }
  
  h5 {
    margin-top: 16px;
    font-size: 13px;
  }
}

.performance-metrics {
  .metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    
    .metric-label {
      font-size: 12px;
      color: #999;
    }
    
    .metric-value {
      font-size: 12px;
      font-weight: 500;
      
      &.excellent { color: #18a058; }
      &.good { color: #2080f0; }
      &.fair { color: #f0a020; }
      &.poor { color: #d03050; }
      &.unknown { color: #999; }
    }
  }
}

.request-stats {
  .stat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .stat-item {
    text-align: center;
    padding: 8px;
    background: #f9f9f9;
    border-radius: 4px;
    
    .stat-number {
      font-size: 16px;
      font-weight: 600;
      color: #2080f0;
    }
    
    .stat-label {
      font-size: 11px;
      color: #999;
      margin-top: 2px;
    }
  }
  
  .avg-response-time {
    font-size: 12px;
    color: #999;
    text-align: center;
    padding: 4px 0;
    border-top: 1px solid #e5e5e5;
  }
}

.quality-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .quality-segments {
    display: flex;
    gap: 2px;
  }
  
  .quality-segment {
    width: 4px;
    height: 16px;
    border-radius: 2px;
    background: #e5e5e5;
    transition: background-color 0.3s ease;
    
    &.active {
      &.quality-poor { background: #d03050; }
      &.quality-fair { background: #f0a020; }
      &.quality-good { background: #2080f0; }
      &.quality-excellent { background: #18a058; }
    }
  }
  
  .quality-label {
    font-size: 12px;
    color: #999;
  }
}

.offline-alert,
.slow-alert {
  margin-top: 8px;
  width: 100%;
}

// 响应式设计
@media (max-width: 768px) {
  .network-status {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .status-indicator {
    justify-content: center;
  }
  
  .quality-bar {
    justify-content: center;
  }
}

// 紧凑模式
.network-status.compact {
  padding: 4px 8px;
  
  .status-indicator {
    .status-text {
      .connection-type {
        display: none;
      }
    }
  }
  
  .quality-bar {
    display: none;
  }
}
</style>