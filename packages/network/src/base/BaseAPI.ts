/**
 * 基础API类
 * 提供通用的CRUD操作模板，减少代码重复
 */

import type { APIResponse, RequestConfig } from '../types'
import type { HttpClient } from '../core/HttpClient'

// 通用分页参数
export interface PaginationParams {
  page?: number
  per_page?: number
  limit?: number
  offset?: number
}

// 通用搜索参数
export interface SearchParams {
  search?: string
  keyword?: string
  q?: string
}

// 通用排序参数
export interface SortParams {
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  order_by?: string
  order?: 'asc' | 'desc'
}

// 通用过滤参数
export interface FilterParams {
  is_active?: boolean
  status?: string
  [key: string]: unknown
}

// 通用列表参数
export interface ListParams extends PaginationParams, SearchParams, SortParams, FilterParams {}

// 通用分页响应
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// 批量操作参数
export interface BatchParams {
  ids: string[]
}

/**
 * 基础API抽象类
 * 提供标准的CRUD操作模板
 */
export abstract class BaseAPI<T = unknown, CreateT = Partial<T>, UpdateT = Partial<T>> {
  protected client: HttpClient
  protected baseEndpoint: string

  constructor(client: HttpClient, baseEndpoint: string) {
    this.client = client
    this.baseEndpoint = baseEndpoint.startsWith('/') ? baseEndpoint : `/${baseEndpoint}`
  }

  /**
   * 获取列表
   */
  async getList(params: ListParams = {}): Promise<APIResponse<PaginatedResponse<T>>> {
    return this.client.get<PaginatedResponse<T>>(this.baseEndpoint, { params })
  }

  /**
   * 获取单个资源
   */
  async getById(id: string | number): Promise<APIResponse<T>> {
    return this.client.get<T>(`${this.baseEndpoint}/${id}`)
  }

  /**
   * 创建资源
   */
  async create(data: CreateT): Promise<APIResponse<T>> {
    return this.client.post<T>(this.baseEndpoint, data)
  }

  /**
   * 更新资源
   */
  async update(id: string | number, data: UpdateT): Promise<APIResponse<T>> {
    return this.client.put<T>(`${this.baseEndpoint}/${id}`, data)
  }

  /**
   * 部分更新资源
   */
  async patch(id: string | number, data: Partial<UpdateT>): Promise<APIResponse<T>> {
    return this.client.patch<T>(`${this.baseEndpoint}/${id}`, data)
  }

  /**
   * 删除资源
   */
  async delete(id: string | number): Promise<APIResponse<null>> {
    return this.client.delete<null>(`${this.baseEndpoint}/${id}`)
  }

  /**
   * 批量删除
   */
  async batchDelete(ids: string[] | number[]): Promise<APIResponse<null>> {
    return this.client.delete<null>(`${this.baseEndpoint}/batch`, {
      data: { ids }
    })
  }

  /**
   * 恢复资源（软删除场景）
   */
  async restore(id: string | number): Promise<APIResponse<T>> {
    return this.client.post<T>(`${this.baseEndpoint}/${id}/restore`)
  }

  /**
   * 获取资源统计信息
   */
  async getStats(id?: string | number): Promise<APIResponse<Record<string, unknown>>> {
    const endpoint = id ? `${this.baseEndpoint}/${id}/stats` : `${this.baseEndpoint}/stats`
    return this.client.get<Record<string, unknown>>(endpoint)
  }

  /**
   * 导出数据
   */
  async export(
    format: 'json' | 'csv' | 'excel' = 'json',
    params: ListParams = {}
  ): Promise<APIResponse<{ download_url: string }>> {
    return this.client.get<{ download_url: string }>(`${this.baseEndpoint}/export`, {
      params: { ...params, format }
    })
  }

  /**
   * 搜索资源
   */
  async search(query: string, params: Omit<ListParams, 'search'> = {}): Promise<APIResponse<PaginatedResponse<T>>> {
    return this.client.get<PaginatedResponse<T>>(`${this.baseEndpoint}/search`, {
      params: { ...params, q: query }
    })
  }

  /**
   * 获取客户端实例（供子类使用）
   */
  protected getClient(): HttpClient {
    return this.client
  }

  /**
   * 获取基础端点（供子类使用）
   */
  protected getBaseEndpoint(): string {
    return this.baseEndpoint
  }

  /**
   * 构建端点URL（供子类使用）
   */
  protected buildEndpoint(path: string): string {
    const normalizedPath = path.startsWith('/') ? path : `/${path}`
    return `${this.baseEndpoint}${normalizedPath}`
  }

  /**
   * 执行自定义请求（供子类使用）
   */
  protected async request<R = unknown>(
    method: 'get' | 'post' | 'put' | 'delete' | 'patch',
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<APIResponse<R>> {
    switch (method) {
      case 'get':
        return this.client.get<R>(endpoint, config)
      case 'post':
        return this.client.post<R>(endpoint, data, config)
      case 'put':
        return this.client.put<R>(endpoint, data, config)
      case 'patch':
        return this.client.patch<R>(endpoint, data, config)
      case 'delete':
        return this.client.delete<R>(endpoint, config)
      default:
        throw new Error(`Unsupported HTTP method: ${method}`)
    }
  }
}

/**
 * 创建API实例的工厂函数
 */
export function createAPI<T, CreateT = Partial<T>, UpdateT = Partial<T>>(
  client: HttpClient,
  endpoint: string
): BaseAPI<T, CreateT, UpdateT> {
  return new (class extends BaseAPI<T, CreateT, UpdateT> {})(client, endpoint)
}