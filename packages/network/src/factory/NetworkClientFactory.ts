/**
 * 网络客户端工厂
 * 提供统一的HTTP客户端实例创建和管理
 */

import { HttpClient } from '../core/HttpClient'
import type { HttpClientConfig } from '../types'

// 默认配置
const DEFAULT_CONFIG: Partial<HttpClientConfig> = {
  timeout: 30000,
  withCredentials: true,
  apiPrefix: '/api',
  defaultHeaders: {
    'Content-Type': 'application/json'
  },
  auth: {
    tokenKey: 'auth_token',
    refreshTokenKey: 'refresh_token',
    userInfoKey: 'user_info',
    refreshThreshold: 5 * 60 * 1000, // 5分钟
    rememberMeThreshold: 24 * 60 * 60 * 1000, // 24小时
    refreshEndpoint: '/auth/refresh',
    loginRedirect: '/auth/login',
    enableAutoRefresh: true,
    enableI18n: true,
    supportedLanguages: ['zh-CN', 'en-US'],
    defaultLanguage: 'zh-CN'
  },
  errorHandling: {
    enableLogging: true,
    enableUserFriendlyMessages: true,
    retryAttempts: 2,
    retryDelay: 1000
  },
  responseHandling: {
    enableLogging: false,
    enableDataTransform: true,
    enableMetadataExtraction: true,
    successCodes: [200, 201, 202, 204],
    dataField: 'data',
    messageField: 'message',
    codeField: 'code',
    successField: 'success'
  }
}

// 客户端实例缓存
const clientInstances = new Map<string, HttpClient>()

/**
 * 网络客户端工厂类
 */
export class NetworkClientFactory {
  /**
   * 创建HTTP客户端
   */
  static createClient(baseURL: string, config: Partial<HttpClientConfig> = {}): HttpClient {
    const fullConfig: HttpClientConfig = {
      ...DEFAULT_CONFIG,
      ...config,
      baseURL,
      auth: {
        ...DEFAULT_CONFIG.auth,
        ...config.auth
      },
      errorHandling: {
        ...DEFAULT_CONFIG.errorHandling,
        ...config.errorHandling
      },
      responseHandling: {
        ...DEFAULT_CONFIG.responseHandling,
        ...config.responseHandling
      }
    }

    return new HttpClient(fullConfig)
  }

  /**
   * 获取或创建客户端实例（单例模式）
   */
  static getInstance(
    key: string, 
    baseURL: string, 
    config: Partial<HttpClientConfig> = {}
  ): HttpClient {
    if (!clientInstances.has(key)) {
      const client = this.createClient(baseURL, config)
      clientInstances.set(key, client)
    }

    return clientInstances.get(key)!
  }

  /**
   * 创建开发环境客户端
   */
  static createDevClient(baseURL: string = 'http://localhost:8080', config: Partial<HttpClientConfig> = {}): HttpClient {
    return NetworkClientFactory.createClient(baseURL, {
      errorHandling: {
        enableLogging: true,
        enableUserFriendlyMessages: true
      },
      responseHandling: {
        enableLogging: true
      },
      ...config
    })
  }

  /**
   * 创建生产环境客户端
   */
  static createProdClient(baseURL: string, config: Partial<HttpClientConfig> = {}): HttpClient {
    return NetworkClientFactory.createClient(baseURL, {
      errorHandling: {
        enableLogging: false,
        enableUserFriendlyMessages: true
      },
      responseHandling: {
        enableLogging: false
      },
      ...config
    })
  }

  /**
   * 创建匿名客户端（无认证）
   */
  static createAnonymousClient(baseURL: string, config: Partial<HttpClientConfig> = {}): HttpClient {
    return NetworkClientFactory.createClient(baseURL, {
      auth: {
        ...DEFAULT_CONFIG.auth,
        enableAutoRefresh: false
      },
      ...config
    })
  }

  /**
   * 创建文件上传客户端
   */
  static createUploadClient(baseURL: string, config: Partial<HttpClientConfig> = {}): HttpClient {
    return NetworkClientFactory.createClient(baseURL, {
      timeout: 300000, // 5分钟超时
      defaultHeaders: {
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
      ...config
    })
  }

  /**
   * 销毁客户端实例
   */
  static destroyInstance(key: string): void {
    const client = clientInstances.get(key)
    if (client) {
      client.dispose()
      clientInstances.delete(key)
    }
  }

  /**
   * 销毁所有客户端实例
   */
  static destroyAllInstances(): void {
    for (const [key, client] of clientInstances) {
      client.dispose()
      clientInstances.delete(key)
    }
  }

  /**
   * 获取默认配置
   */
  static getDefaultConfig(): Partial<HttpClientConfig> {
    return JSON.parse(JSON.stringify(DEFAULT_CONFIG))
  }

  /**
   * 更新默认配置
   */
  static updateDefaultConfig(config: Partial<HttpClientConfig>): void {
    Object.assign(DEFAULT_CONFIG, config)
  }
}

// 便捷方法
export const createHttpClient = NetworkClientFactory.createClient
export const getHttpClient = NetworkClientFactory.getInstance
export const createDevClient = NetworkClientFactory.createDevClient
export const createProdClient = NetworkClientFactory.createProdClient
export const createAnonymousClient = NetworkClientFactory.createAnonymousClient
export const createUploadClient = NetworkClientFactory.createUploadClient