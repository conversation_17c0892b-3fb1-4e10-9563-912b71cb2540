import { useNetworkManager } from '../composables/useNetworkManager'
import type { APIResponse } from '../types'

// 后端API基础配置
const API_CONFIG = {
  BASE_URL: 'http://localhost:3001',
  API_PREFIX: '/api',
  TIMEOUT: 30000,
  WITH_CREDENTIALS: true, // 支持Session Cookie认证
}

// 获取完整的API URL
const getApiUrl = (endpoint: string): string => {
  // 开发环境下使用Vite代理，直接返回相对路径
  // 生产环境使用完整URL
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return `${API_CONFIG.API_PREFIX}${endpoint}`
  }
  return `${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${endpoint}`
}

// 网络管理器实例 - 延迟初始化
let networkManager: ReturnType<typeof useNetworkManager> | null = null

// 获取网络管理器实例
const getNetworkManager = () => {
  if (!networkManager) {
    throw new Error('网络管理器未初始化。请在 Vue 组件中调用 initNetworkManager() 进行初始化。')
  }
  return networkManager
}

// 初始化网络管理器 - 需要在 Vue 组件的 setup() 中调用
export const initNetworkManager = () => {
  if (!networkManager) {
    networkManager = useNetworkManager()
  }
  return networkManager
}

// 请求拦截器 - 添加认证token
const addAuthHeaders = (headers: Record<string, string> = {}): Record<string, string> => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  return headers
}

// 响应拦截器 - 统一处理后端API响应格式
const handleResponse = async <T>(response: Record<string, unknown>): Promise<APIResponse<T>> => {
  try {
    // 如果是从缓存返回的
    if (response.fromCache) {
      return response.data as APIResponse<T>
    }

    const data = response.data
    
    // 后端标准响应格式: { code: number, message: string, data?: {} }
    if (data && typeof data === 'object') {
      // 检查是否是标准的后端响应格式
      if (typeof data.code === 'number' && data.message) {
        const isSuccess = data.code >= 200 && data.code < 300
        return {
          success: isSuccess,
          message: data.message,
          data: data.data as T
        }
      }
      
      // 兼容旧格式: { message: string, data: {} }
      if (data.message && data.data !== undefined) {
        return {
          success: true,
          message: data.message,
          data: data.data as T
        }
      }
      
      // 直接返回数据的情况（如健康检查等）
      if (data.status || data.user || data.templates || data.users || data.token) {
        return {
          success: true,
          message: 'success',
          data: data as T
        }
      }
      
      // 其他对象响应直接包装
      return {
        success: true,
        message: 'success',
        data: data as T
      }
    }
    
    // 非对象响应直接包装
    return {
      success: true,
      message: 'success',
      data: data as T
    }
  } catch (error) {
    console.error('响应处理失败:', error)
    throw new Error('响应数据格式错误')
  }
}

// 错误处理器 - 适配后端错误响应格式
const handleError = (error: unknown): never => {
  console.error('API请求失败:', error)
  
  let message = '请求失败'
  
  // 处理后端返回的错误格式
  if (error.response && error.response.data) {
    const errorData = error.response.data
    
    // 后端标准错误格式: { code: number, message: string }
    if (typeof errorData.code === 'number' && errorData.message) {
      message = errorData.message
    }
    // 兼容格式: { error: "错误描述" }
    else if (errorData.error) {
      message = errorData.error
    }
    // 兼容格式: { message: "错误描述" }
    else if (errorData.message) {
      message = errorData.message
    }
  } else if (error.message) {
    if (error.message.includes('Failed to fetch')) {
      message = '网络连接失败，请检查网络设置'
    } else if (error.message.includes('Network Error')) {
      message = '网络错误，请检查后端服务是否启动'
    } else if (error.message.includes('timeout')) {
      message = '请求超时，请稍后重试'
    } else {
      message = error.message
    }
  }

  // 根据HTTP状态码处理不同错误
  if (error.response) {
    const status = error.response.status
    switch (status) {
      case 400:
        message = `请求参数错误: ${message}`
        break
      case 401:
        message = '认证失败，请重新登录'
        // 清除本地token和用户信息
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_info')
        // 跳转到登录页
        window.location.href = '/auth/login'
        break
      case 403:
        message = '权限不足，无法执行此操作'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 409:
        message = `资源冲突: ${message}`
        break
      case 500:
        message = '服务器内部错误，请联系管理员'
        break
      default:
        message = `HTTP ${status}: ${message}`
    }
  }
  
  // 显示错误消息（需要在使用时导入 Naive UI 的 useMessage）
  console.error(message)
  
  throw new Error(message)
}

// 增强的GET请求
const get = async <T>(url: string, config?: Record<string, unknown>): Promise<APIResponse<T>> => {
  try {
    const response = await getNetworkManager().get(getApiUrl(url), {
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        // 简化headers，避免触发CORS预检请求
        ...addAuthHeaders(config?.headers)
      },
      ...config
    })
    return handleResponse<T>(response)
  } catch (error) {
    return handleError(error)
  }
}

// 增强的POST请求
const post = async <T>(url: string, data?: unknown, config?: Record<string, unknown>): Promise<APIResponse<T>> => {
  try {
    const response = await getNetworkManager().post(getApiUrl(url), data, {
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        // 简化headers，避免触发CORS预检请求
        ...addAuthHeaders(config?.headers)
      },
      ...config
    })
    return handleResponse<T>(response)
  } catch (error) {
    return handleError(error)
  }
}

// 增强的PUT请求
const put = async <T>(url: string, data?: unknown, config?: Record<string, unknown>): Promise<APIResponse<T>> => {
  try {
    const response = await getNetworkManager().put(getApiUrl(url), data, {
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...addAuthHeaders(config?.headers)
      },
      ...config
    })
    return handleResponse<T>(response)
  } catch (error) {
    return handleError(error)
  }
}

// 增强的DELETE请求
const del = async <T>(url: string, config?: Record<string, unknown>): Promise<APIResponse<T>> => {
  try {
    const response = await getNetworkManager().delete(getApiUrl(url), {
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...addAuthHeaders(config?.headers)
      },
      ...config
    })
    return handleResponse<T>(response)
  } catch (error) {
    return handleError(error)
  }
}

// Auth API - 用户认证模块
export const authAPI = {
  // 用户登录
  login: async (credentials: { username: string; password: string }) => {
    return post<{ user: Record<string, unknown>; token?: string }>('/auth/login', credentials)
  },

  // 用户注册
  register: async (userData: { 
    username: string; 
    email: string; 
    password: string; 
    full_name?: string 
  }) => {
    return post<{ user: Record<string, unknown> }>('/auth/register', userData)
  },

  // 用户登出
  logout: async () => {
    return post<{ message: string }>('/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    return get<{ user: Record<string, unknown> }>('/auth/me')
  }
}

// User Management API - 用户管理模块 (仅保留基本的个人资料相关功能)
export const userAPI = {
  // 更新个人资料
  updateProfile: async (userData: {
    full_name?: string;
    email?: string;
    avatar?: string;
  }) => {
    return put<{ user: Record<string, unknown> }>('/user/profile', userData)
  },

  // 修改密码
  changePassword: async (passwordData: {
    current_password: string;
    new_password: string;
  }) => {
    return put<{ message: string }>('/user/password', passwordData)
  }
}

// Project API - 仅保留创建表单时需要的项目列表获取功能
export const projectAPI = {
  getProjects: async (params?: { limit?: number; offset?: number; search?: string; status?: string }) => {
    return get<{ projects: Record<string, unknown>[]; pagination: Record<string, unknown> }>('/projects', { params })
  }
}

// Template API - 仅保留表单创建相关功能
export const templateAPI = {
  createTemplate: async (templateData: {
    project_id: string;
    name: string;
    title: string;
    description?: string;
    keyword?: string;
    version?: string;
    page_config?: Record<string, unknown>;
    form_structure?: Record<string, unknown>;
    component_configs?: Record<string, unknown>;
    validation_rules?: Record<string, unknown>;
    style_config?: Record<string, unknown>;
    permissions?: Record<string, unknown>;
  }) => {
    return post<{ template: Record<string, unknown> }>('/templates', templateData)
  }
}

// 导出基础HTTP方法
export { get, post, put, del }

// 导出网络管理器实例和配置供其他地方使用
export { networkManager, API_CONFIG, getApiUrl }