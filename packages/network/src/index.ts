// 核心HTTP客户端
export { HttpClient } from './core/HttpClient'

// 基础API类
export { BaseAPI, createAPI } from './base/BaseAPI'
export type {
  PaginationParams,
  SearchParams,
  SortParams,
  FilterParams,
  ListParams,
  PaginatedResponse,
  BatchParams
} from './base/BaseAPI'

// 管理组件
export { AuthManager } from './auth/AuthManager'
export { ErrorHandler } from './handlers/ErrorHandler'
export { ResponseHandler } from './handlers/ResponseHandler'

// 工厂类
export { 
  NetworkClientFactory,
  createHttpClient,
  getHttpClient,
  createDevClient,
  createProdClient,
  createAnonymousClient,
  createUploadClient
} from './factory/NetworkClientFactory'

// 组合式函数
export { useNetworkManager, useRequestState } from './composables/useNetworkManager'

// 类型定义 - 完全导出所有类型
export type {
  APIResponse,
  APIError,
  RequestConfig,
  RequestResult,
  HttpClientConfig,
  AuthConfig,
  ErrorHandlingConfig,
  ResponseHandlingConfig,
  TokenInfo,
  NetworkStatus,
  RequestState,
  CacheItem,
  AxiosRequestConfig
} from './types'

// 显式导出APIResponse以确保类型正确
export type { APIResponse as HttpResponse } from './types'