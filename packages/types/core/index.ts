/**
 * 核心类型定义 - 完全类型安全
 */

// 基础类型别名
export type ComponentId = string
export type FieldId = string
export type SectionId = string

// 表单数据类型 - 完全类型安全
export interface FormData {
  readonly [fieldId: string]: FormFieldValue
}

export type FormFieldValue = 
  | string 
  | number 
  | boolean 
  | Date 
  | readonly string[] 
  | readonly FormFieldValue[]
  | FormObjectValue

export interface FormObjectValue {
  readonly [key: string]: FormFieldValue
}

// 组件类型枚举
export enum ComponentType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  SELECT = 'select',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  SWITCH = 'switch',
  SLIDER = 'slider',
  DATE = 'date',
  DATETIME = 'datetime',
  CARD = 'card',
  TITLE = 'title',
  DIVIDER = 'divider'
}

// 编辑器模式枚举
export enum EditorMode {
  EDIT = 'edit',
  PREVIEW = 'preview',
  FILL = 'fill',
  VIEW = 'view',
  PUBLISH = 'publish'
}

// 表单模式枚举
export enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview'
}

// 验证状态枚举
export enum ValidationStatus {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  VALIDATING = 'validating'
}

// 基础组件属性 - 完全类型安全
export interface BaseComponentProps {
  readonly title?: string
  readonly description?: string
  readonly required?: boolean
  readonly disabled?: boolean
  readonly readonly?: boolean
  readonly visible?: boolean
  readonly placeholder?: string
  readonly defaultValue?: FormFieldValue
  readonly value?: FormFieldValue
}

// 验证规则 - 完全类型安全
export interface ValidationRule {
  readonly type: 'required' | 'pattern' | 'min' | 'max' | 'minLength' | 'maxLength' | 'email' | 'url' | 'number' | 'integer' | 'phone' | 'custom'
  readonly message: string
  readonly value?: FormFieldValue
  readonly trigger?: 'blur' | 'change' | 'input' | 'submit'
}

// 验证结果 - 完全类型安全
export interface ValidationResult {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly status: ValidationStatus
}

// 验证状态 - 完全类型安全
export interface ValidationState {
  readonly results: ReadonlyMap<FieldId, ValidationResult>
  readonly isFormValid: boolean
}

// 时间戳接口
export interface WithTimestamp {
  readonly createTime: number
  readonly updateTime: number
}

// ID接口
export interface WithId {
  readonly id: string
}

// 用户信息接口
export interface UserInfo {
  id: string
  username: string
  email: string
  full_name?: string
  avatar_url?: string
  role?: string
  is_active?: boolean
  permissions?: string[]
  created_at?: string
  updated_at?: string
  department?: string
}

// 导出格式类型
export type ExportFormat = 'json' | 'csv' | 'excel' | 'pdf'