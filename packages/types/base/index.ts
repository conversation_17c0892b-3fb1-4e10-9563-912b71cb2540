/**
 * Base component type definitions with generic constraints
 * Replaces any types with specific generic interfaces
 */

// Generic style interface to replace Record<string, any>
export interface ComponentStyle {
  [key: string]: string | number | undefined;
}

// Generic base component props with type constraints
export interface BaseComponentProps<T = unknown> {
  modelValue?: T;
  title?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  visible?: boolean;
  size?: 'small' | 'medium' | 'large';
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  className?: string;
  style?: ComponentStyle;
  validateStatus?: 'success' | 'warning' | 'error' | 'validating';
  errorMessage?: string;
  helpText?: string;
}

// Generic base component emits with type constraints
export interface BaseComponentEmits<T = unknown> {
  'update:modelValue': [value: T];
  change: [value: T];
  blur: [event: FocusEvent];
  focus: [event: FocusEvent];
}

// Generic base component slots
export interface BaseComponentSlots {
  default?: () => unknown;
  prefix?: () => unknown;
  suffix?: () => unknown;
  help?: () => unknown;
}

// Generic base schema interface with type constraints
export interface BaseComponentSchema<T = unknown> {
  type: string;
  title?: string;
  description?: string;
  required?: string[];
  properties?: Record<string, ComponentSchemaProperty>;
  default?: T;
}

// Component schema property with specific types
export interface ComponentSchemaProperty {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  title?: string;
  description?: string;
  default?: unknown;
  enum?: unknown[];
  format?: string;
}

// Generic base component configuration
export interface BaseComponentConfig<T = unknown> {
  code: string;
  name: string;
  label: string;
  icon?: string;
  iconColor?: string;
  description?: string;
  category?: string;
  tags?: string[];
  props?: BaseComponentProps<T>;
  validation?: ComponentValidationConfig<T>;
  layout?: ComponentLayoutConfig;
  metadata?: ComponentMetadata;
}

// Validation configuration with generic constraints
export interface ComponentValidationConfig<T = unknown> {
  required?: boolean;
  rules?: ValidationRule<T>[];
  trigger?: 'blur' | 'change' | 'input' | 'submit';
}

// Layout configuration with specific types
export interface ComponentLayoutConfig {
  span?: number;
  offset?: number;
  order?: number;
  flex?: string | number;
}

// Component metadata with specific types
export interface ComponentMetadata {
  category?: string;
  tags?: string[];
  version?: string;
  author?: string;
  description?: string;
}

// Validation rule with generic constraint
export interface ValidationRule<T = unknown> {
  type: string;
  message: string;
  value?: T;
  pattern?: RegExp;
  trigger?: 'blur' | 'change' | 'input' | 'submit';
  validator?: (value: T) => boolean | string;
}

// Generic form data type with type constraints
export type BaseFormData<T = Record<string, unknown>> = {
  [K in keyof T]: T[K];
}

// Base ID interface
export interface BaseWithId {
  id: string;
}

// Base timestamp interface
export interface BaseWithTimestamp {
  createTime?: number;
  updateTime?: number;
}