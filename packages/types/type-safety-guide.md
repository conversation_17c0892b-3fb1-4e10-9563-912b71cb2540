# TypeScript 类型安全指南

## 概述

本文档定义了 CRF Frontend 项目中 TypeScript 类型系统的设计原则和使用规范，确保类型安全和开发体验。

## 类型系统架构

### 分层结构

```
@crf/types/
├── base/           # 基础类型定义
├── shared/         # 共享类型定义
├── core/           # 核心业务类型
├── components/     # 组件相关类型
├── editor/         # 编辑器相关类型
├── form/           # 表单相关类型
├── validation/     # 验证相关类型
├── application/    # 应用层类型
└── unified/        # 统一导出
```

### 类型命名规范

#### 1. 组件类型
```typescript
// ✅ 推荐命名
export interface CrfTextProps extends BaseComponentProps {
  placeholder?: string
  format?: 'text' | 'email' | 'phone'
}

export type CrfTextSchema = Static<typeof CrfTextConfigSchema>
export type CrfTextConfig = CrfTextSchema
export type CrfTextInstance = InstanceType<typeof CrfText>

// ⚠️ 向后兼容（已弃用）
export type JzTextProps = CrfTextProps
export type JzTextSchema = CrfTextSchema
```

#### 2. Schema 类型
```typescript
// ✅ 推荐
export const CrfTextConfigSchema = createComponentSchema('text', {
  placeholder: Type.Optional(Type.String()),
  format: Type.Optional(Type.Union([
    Type.Literal('text'),
    Type.Literal('email'),
    Type.Literal('phone')
  ]))
})

export type CrfTextSchema = Static<typeof CrfTextConfigSchema>
```

#### 3. 基础类型
```typescript
// 基础组件属性
export interface BaseComponentProps {
  id?: string
  label?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  visible?: boolean
}

// 基础组件配置
export interface BaseComponentSchema {
  type: string
  props: BaseComponentProps
  validation?: BaseValidationRule[]
}

// 基础验证规则
export interface BaseValidationRule {
  type: string
  message?: string
  trigger?: 'blur' | 'change' | 'submit'
}
```

## 类型导出策略

### 1. 统一导出

```typescript
// packages/types/index.ts
export * from './base'
export * from './shared'
export * from './core'
export * from './components'
export * from './editor'
export * from './form'
export * from './validation'
export * from './application'

// 防止命名冲突的别名导出
export {
  ValidationResult as CoreValidationResult,
  FormData as CoreFormData,
  ComponentConfig as CoreComponentConfig
} from './core'
```

### 2. 分类导出

```typescript
// packages/types/components/index.ts
export * from './text'
export * from './input'
export * from './radio'
export * from './textarea'
export * from './base'
export * from './form'
export * from './editor'
```

### 3. 组件特定导出

```typescript
// packages/types/components/text.ts
export interface CrfTextProps extends BaseComponentProps {
  placeholder?: string
  format?: TextFormat
  minLength?: number
  maxLength?: number
}

export type CrfTextSchema = Static<typeof CrfTextConfigSchema>
export type CrfTextConfig = CrfTextSchema
export type CrfTextInstance = ComponentInstance<CrfTextProps>

// 向后兼容
export type JzTextProps = CrfTextProps
export type JzTextSchema = CrfTextSchema
```

## Schema 定义规范

### 1. 使用 TypeBox 定义 Schema

```typescript
import { Type, Static } from '@sinclair/typebox'
import { createComponentSchema } from '@crf/utils'

// ✅ 推荐方式
export const CrfTextConfigSchema = createComponentSchema('text', {
  placeholder: Type.Optional(Type.String()),
  format: Type.Optional(Type.Union([
    Type.Literal('text'),
    Type.Literal('email'),
    Type.Literal('phone')
  ])),
  minLength: Type.Optional(Type.Number({ minimum: 0 })),
  maxLength: Type.Optional(Type.Number({ minimum: 1 }))
})

export type CrfTextSchema = Static<typeof CrfTextConfigSchema>
```

### 2. 扩展基础 Schema

```typescript
// 基础组件 Schema
export const BaseComponentConfigSchema = Type.Object({
  type: Type.String(),
  label: Type.Optional(Type.String()),
  required: Type.Optional(Type.Boolean()),
  disabled: Type.Optional(Type.Boolean()),
  readonly: Type.Optional(Type.Boolean()),
  visible: Type.Optional(Type.Boolean())
})

// 扩展特定组件 Schema
export const CrfTextConfigSchema = Type.Intersect([
  BaseComponentConfigSchema,
  Type.Object({
    type: Type.Literal('text'),
    placeholder: Type.Optional(Type.String()),
    format: Type.Optional(Type.Union([
      Type.Literal('text'),
      Type.Literal('email'),
      Type.Literal('phone')
    ]))
  })
])
```

## 类型安全最佳实践

### 1. 严格类型检查

```typescript
// ✅ 使用严格类型
interface StrictComponentProps {
  readonly type: string
  readonly props: Readonly<BaseComponentProps>
  readonly validation?: readonly BaseValidationRule[]
}

// ❌ 避免 any 类型
interface LooseComponentProps {
  type: any
  props: any
  validation?: any
}
```

### 2. 泛型约束

```typescript
// ✅ 使用泛型约束
interface ComponentInstance<T extends BaseComponentProps = BaseComponentProps> {
  props: T
  validate(): ValidationResult
  getValue(): unknown
}

// 具体组件实例
type CrfTextInstance = ComponentInstance<CrfTextProps>
```

### 3. 联合类型和判别联合

```typescript
// ✅ 使用判别联合
type ComponentConfig = 
  | { type: 'text'; props: CrfTextProps }
  | { type: 'input'; props: CrfInputProps }
  | { type: 'radio'; props: CrfRadioProps }

// 类型守卫
function isCrfTextConfig(config: ComponentConfig): config is Extract<ComponentConfig, { type: 'text' }> {
  return config.type === 'text'
}
```

### 4. 条件类型

```typescript
// ✅ 使用条件类型
type ComponentPropsType<T extends string> = 
  T extends 'text' ? CrfTextProps :
  T extends 'input' ? CrfInputProps :
  T extends 'radio' ? CrfRadioProps :
  never

// 使用示例
type TextProps = ComponentPropsType<'text'> // CrfTextProps
```

## 类型验证

### 1. 运行时类型检查

```typescript
import { Value } from '@sinclair/typebox/value'

// ✅ Schema 验证
function validateComponentConfig<T>(schema: TSchema, value: unknown): value is Static<T> {
  return Value.Check(schema, value)
}

// 使用示例
if (validateComponentConfig(CrfTextConfigSchema, config)) {
  // config 现在是 CrfTextSchema 类型
  console.log(config.placeholder)
}
```

### 2. 类型断言

```typescript
// ✅ 安全的类型断言
function assertComponentType<T extends ComponentConfig>(config: ComponentConfig, type: T['type']): asserts config is T {
  if (config.type !== type) {
    throw new Error(`Expected component type ${type}, got ${config.type}`)
  }
}

// 使用示例
assertComponentType(config, 'text')
// config 现在是 text 类型的组件配置
```

## 向后兼容性

### 1. 类型别名

```typescript
// 新类型定义
export interface CrfTextProps extends BaseComponentProps {
  placeholder?: string
}

// 向后兼容别名
export type JzTextProps = CrfTextProps
export type JzTextSchema = CrfTextSchema
```

### 2. 渐进式迁移

```typescript
// 支持新旧两种类型
type LegacyComponentProps = JzTextProps | JzInputProps | JzRadioProps
type ModernComponentProps = CrfTextProps | CrfInputProps | CrfRadioProps
type ComponentProps = LegacyComponentProps | ModernComponentProps
```

## 开发工具集成

### 1. IDE 支持

- 确保所有类型都有完整的 JSDoc 注释
- 使用 TypeScript 的严格模式
- 配置 ESLint 的 TypeScript 规则

### 2. 类型生成

```typescript
// 自动生成类型定义
export type ComponentTypeMap = {
  text: CrfTextProps
  input: CrfInputProps
  radio: CrfRadioProps
}

export type ComponentType = keyof ComponentTypeMap
export type ComponentPropsOf<T extends ComponentType> = ComponentTypeMap[T]
```

## 检查清单

在定义或更新类型时，请确保：

- [ ] 使用 `Crf` 前缀命名新类型
- [ ] 提供 `Jz` 前缀的向后兼容别名
- [ ] 使用 TypeBox 定义 Schema
- [ ] 扩展基础类型而不是重新定义
- [ ] 添加完整的 JSDoc 注释
- [ ] 使用严格的类型约束
- [ ] 提供类型守卫和验证函数
- [ ] 更新相关的类型导出
- [ ] 添加类型测试用例