/**
 * 验证类型定义 - 完全类型安全
 */

import type { FieldId, FormData, FormFieldValue } from '../core'

// 验证状态 - 完全类型安全
export interface ValidationState {
  readonly results: ReadonlyMap<FieldId, ValidationResult>
  readonly isFormValid: boolean
  readonly pendingValidations: readonly FieldId[]
  readonly validationQueue: readonly ValidationTask[]
}

// 验证结果 - 完全类型安全
export interface ValidationResult {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly status: ValidationStatus
  readonly lastValidated: number
  readonly fieldId: FieldId
}

// 验证状态枚举
export enum ValidationStatus {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  VALIDATING = 'validating',
  PENDING = 'pending'
}

// 验证任务 - 完全类型安全
export interface ValidationTask {
  readonly fieldId: FieldId
  readonly value: FormFieldValue
  readonly rules: readonly ValidationRule[]
  readonly priority: ValidationPriority
  readonly timestamp: number
}

// 验证优先级枚举
export enum ValidationPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4
}

// 验证规则 - 完全类型安全
export interface ValidationRule {
  readonly type: ValidationRuleType
  readonly message: string
  readonly value?: FormFieldValue
  readonly trigger: ValidationTrigger
  readonly priority: ValidationPriority
  readonly customValidator?: CustomValidator
}

// 验证规则类型枚举
export enum ValidationRuleType {
  REQUIRED = 'required',
  PATTERN = 'pattern',
  MIN = 'min',
  MAX = 'max',
  MIN_LENGTH = 'minLength',
  MAX_LENGTH = 'maxLength',
  EMAIL = 'email',
  URL = 'url',
  NUMBER = 'number',
  INTEGER = 'integer',
  PHONE = 'phone',
  CUSTOM = 'custom',
  RANGE = 'range',
  ENUM = 'enum',
  FORMAT = 'format'
}

// 验证触发器枚举
export enum ValidationTrigger {
  BLUR = 'blur',
  CHANGE = 'change',
  INPUT = 'input',
  SUBMIT = 'submit',
  MOUNT = 'mount',
  FOCUS = 'focus'
}

// 自定义验证器 - 完全类型安全
export interface CustomValidator {
  readonly name: string
  readonly validate: (value: FormFieldValue, fieldId: FieldId, formData: FormData) => ValidationResult
  readonly message: string
  readonly async?: boolean
}

// 验证器配置 - 完全类型安全
export interface ValidatorConfig {
  readonly validateOnChange: boolean
  readonly validateOnBlur: boolean
  readonly validateOnSubmit: boolean
  readonly validateOnMount: boolean
  readonly showValidationMessages: boolean
  readonly showWarnings: boolean
  readonly debounceTime: number
  readonly maxConcurrentValidations: number
  readonly customValidators: readonly CustomValidator[]
}

// 验证器结果 - 完全类型安全
export interface ValidatorResult {
  readonly success: boolean
  readonly result?: ValidationResult
  readonly error?: string
  readonly warnings?: readonly string[]
}

// 批量验证结果 - 完全类型安全
export interface BatchValidationResult {
  readonly results: ReadonlyMap<FieldId, ValidationResult>
  readonly isFormValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly summary: ValidationSummary
}

// 验证摘要 - 完全类型安全
export interface ValidationSummary {
  readonly totalFields: number
  readonly validFields: number
  readonly invalidFields: number
  readonly warningFields: number
  readonly pendingFields: number
  readonly errorCount: number
  readonly warningCount: number
}

// 验证器工厂 - 完全类型安全
export interface ValidatorFactory {
  readonly createValidator: (rule: ValidationRule) => FieldValidator
  readonly createCustomValidator: (validator: CustomValidator) => FieldValidator
  readonly createBatchValidator: (rules: readonly ValidationRule[]) => BatchValidator
}

// 字段验证器 - 完全类型安全
export interface FieldValidator {
  readonly validate: (value: FormFieldValue, fieldId: FieldId, formData: FormData) => ValidationResult
  readonly validateAsync?: (value: FormFieldValue, fieldId: FieldId, formData: FormData) => Promise<ValidationResult>
}

// 批量验证器 - 完全类型安全
export interface BatchValidator {
  readonly validate: (data: FormData) => BatchValidationResult
  readonly validateAsync?: (data: FormData) => Promise<BatchValidationResult>
} 