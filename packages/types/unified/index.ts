/**
 * 统一类型导出 - 便于外部使用的统一接口
 * 
 * 这个文件提供了一个统一的类型导出接口，方便外部包使用
 * 处理命名冲突和类型安全
 */

// =============================================================================
// 组件类型映射 - 统一的组件类型映射表
// =============================================================================

import type {
  CrfTextProps,
  CrfTextSchema
} from '../components/text'

import type {
  CrfTextareaProps,
  CrfTextareaSchema
} from '../components/textarea'

/**
 * 组件类型映射表
 * 用于类型安全的组件类型查找
 */
export interface ComponentTypeMap {
  'crf-text': {
    props: CrfTextProps
    schema: CrfTextSchema
  }
  'crf-textarea': {
    props: CrfTextareaProps
    schema: CrfTextareaSchema
  }
}

/**
 * 组件类型联合类型
 */
export type ComponentType = keyof ComponentTypeMap

/**
 * 根据组件类型获取对应的 Props 类型
 */
export type ComponentPropsOf<T extends ComponentType> = ComponentTypeMap[T]['props']

/**
 * 根据组件类型获取对应的 Schema 类型
 */
export type ComponentSchemaOf<T extends ComponentType> = ComponentTypeMap[T]['schema']

/**
 * 所有组件 Props 的联合类型
 */
export type AnyComponentProps = ComponentTypeMap[ComponentType]['props']

/**
 * 所有组件 Schema 的联合类型
 */
export type AnyComponentSchema = ComponentTypeMap[ComponentType]['schema']



// =============================================================================
// 表单相关统一类型
// =============================================================================

import type {
  FormSchema,
  FormData,
  ValidationRule,
  ValidationResult
} from '../form'

/**
 * 统一的表单配置类型
 */
export interface UnifiedFormConfig {
  schema: FormSchema
  data: FormData
  validation: ValidationRule[]
}

/**
 * 统一的表单验证结果类型
 */
export interface UnifiedValidationResult extends ValidationResult {
  componentType?: ComponentType
  fieldPath?: string
}

// =============================================================================
// 编辑器相关统一类型
// =============================================================================

import type {
  EditorState,
  ComponentConfig,
  HistoryEntry
} from '../editor'

/**
 * 统一的编辑器配置类型
 */
export interface UnifiedEditorConfig {
  state: EditorState
  components: ComponentConfig[]
  history: HistoryEntry[]
}

/**
 * 统一的组件配置类型
 */
export interface UnifiedComponentConfig<T extends ComponentType = ComponentType> {
  type: T
  props: ComponentPropsOf<T>
  schema: ComponentSchemaOf<T>
  metadata?: {
    id: string
    label?: string
    description?: string
    version?: string
  }
}

// =============================================================================
// 工具类型
// =============================================================================

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 提取对象中指定键的类型
 */
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P]
}

/**
 * 排除对象中指定键的类型
 */
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>

/**
 * 条件类型 - 如果 T 是 U 的子类型，返回 X，否则返回 Y
 */
export type If<T extends U, U, X, Y> = T extends U ? X : Y

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T

// =============================================================================
// 类型守卫工具
// =============================================================================

/**
 * 检查是否为指定组件类型
 */
export function isComponentType<T extends ComponentType>(
  config: UnifiedComponentConfig,
  type: T
): config is UnifiedComponentConfig<T> {
  return config.type === type
}

/**
 * 检查是否为有效的组件配置
 */
export function isValidComponentConfig(
  value: unknown
): value is UnifiedComponentConfig {
  return (
    typeof value === 'object' &&
    value !== null &&
    'type' in value &&
    'props' in value &&
    'schema' in value
  )
}

/**
 * 检查是否为有效的表单数据
 */
export function isValidFormData(value: unknown): value is FormData {
  return typeof value === 'object' && value !== null
}

// =============================================================================
// 常用类型组合
// =============================================================================

/**
 * 完整的组件定义类型
 */
export interface CompleteComponentDefinition<T extends ComponentType = ComponentType> {
  type: T
  props: ComponentPropsOf<T>
  schema: ComponentSchemaOf<T>
  defaultValue?: unknown
  validation?: ValidationRule[]
  metadata: {
    id: string
    label: string
    description?: string
    version: string
    author?: string
    tags?: string[]
  }
}

/**
 * 组件实例类型
 */
export interface ComponentInstanceType<T extends ComponentType = ComponentType> {
  id: string
  type: T
  props: ComponentPropsOf<T>
  value: unknown
  validation: UnifiedValidationResult[]
  metadata: {
    created: Date
    updated: Date
    version: string
  }
}

/**
 * 表单实例类型
 */
export interface FormInstanceType {
  id: string
  schema: FormSchema
  data: FormData
  components: ComponentInstanceType[]
  validation: UnifiedValidationResult[]
  metadata: {
    created: Date
    updated: Date
    version: string
    title?: string
    description?: string
  }
}