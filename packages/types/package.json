{"name": "@crf/types", "version": "1.0.1", "private": true, "description": "CRF项目类型定义包", "main": "index.ts", "module": "index.ts", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.ts", "require": "./index.ts"}, "./base": {"types": "./base/index.d.ts", "import": "./base/index.ts", "require": "./base/index.ts"}, "./components": {"types": "./components/index.d.ts", "import": "./components/index.ts", "require": "./components/index.ts"}, "./components/text": {"types": "./components/text.d.ts", "import": "./components/text.ts", "require": "./components/text.ts"}, "./core": {"types": "./core/index.d.ts", "import": "./core/index.ts", "require": "./core/index.ts"}, "./editor": {"types": "./editor/index.d.ts", "import": "./editor/index.ts", "require": "./editor/index.ts"}, "./form": {"types": "./form/index.d.ts", "import": "./form/index.ts", "require": "./form/index.ts"}, "./validation": {"types": "./validation/index.d.ts", "import": "./validation/index.ts", "require": "./validation/index.ts"}, "./application": {"types": "./application/index.d.ts", "import": "./application/index.ts", "require": "./application/index.ts"}, "./shared": {"types": "./shared/index.d.ts", "import": "./shared/index.ts", "require": "./shared/index.ts"}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --watch", "stub": "unbuild --stub", "type-check": "echo 'Type checking types package...'", "lint": "echo 'Linting types package...'", "test": "echo 'Testing types package...'", "clean": "rm -rf dist"}, "peerDependencies": {"vue": "^3.5.13"}, "devDependencies": {"@crf/build-config": "workspace:*"}}