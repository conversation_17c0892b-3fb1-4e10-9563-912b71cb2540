/**
 * 共享类型定义
 */

// 错误类别枚举
export enum ErrorCategory {
    NETWORK = 'network',
    VALIDATION = 'validation',
    BUSINESS = 'business',
    SYSTEM = 'system'
}

// 事件类型枚举
export enum EventTypes {
    // 组件事件
    COMPONENT_SELECT = 'component-select',
    COMPONENT_ADD = 'component-add',
    COMPONENT_DELETE = 'component-delete',
    COMPONENT_UPDATE = 'component-update',
    COMPONENT_MOVE = 'component-move',
    COMPONENT_COPY = 'component-copy',

    // 验证事件
    VALIDATION_START = 'validation-start',
    VALIDATION_SUCCESS = 'validation-success',
    VALIDATION_ERROR = 'validation-error',
    VALIDATION_COMPLETE = 'validation-complete',

    // 编辑器事件
    EDITOR_INIT = 'editor-init',
    EDITOR_DESTROY = 'editor-destroy',
    EDITOR_MODE_CHANGE = 'editor-mode-change',
    EDITOR_STATE_CHANGE = 'editor-state-change',

    // 配置事件
    CONFIG_CHANGE = 'config-change',
    CONFIG_SAVE = 'config-save',
    CONFIG_RESET = 'config-reset',
    CONFIG_LOAD = 'config-load',

    // 表单事件
    FORM_SUBMIT = 'form-submit',
    FORM_RESET = 'form-reset',
    FORM_VALIDATE = 'form-validate',
    FORM_CHANGE = 'form-change'
}

// 通用事件接口 - 使用泛型约束替代any
export interface BaseEvent<T = unknown> {
    type: EventTypes | string
    payload: T
    timestamp: number
    source?: string
    target?: string
}

// 事件处理器接口 - 使用泛型约束替代any
export interface EventHandler<T = unknown> {
    (event: BaseEvent<T>): void | Promise<void>
}

// 事件监听器配置
export interface EventListenerConfig {
    once?: boolean
    passive?: boolean
    capture?: boolean
}

// 事件总线接口 - 使用泛型约束
export interface EventBus {
    emit<T = unknown>(eventType: EventTypes | string, payload: T, source?: string): void
    on<T = unknown>(eventType: EventTypes | string, handler: EventHandler<T>, config?: EventListenerConfig): () => void
    off<T = unknown>(eventType: EventTypes | string, handler: EventHandler<T>): void
    once<T = unknown>(eventType: EventTypes | string, handler: EventHandler<T>): void
    clear(): void
    getListeners<T = unknown>(eventType?: EventTypes | string): EventHandler<T>[]
}

// 状态变更接口 - 使用泛型约束替代any
export interface StateChange<T = unknown> {
    type: 'create' | 'update' | 'delete' | 'replace'
    path: string
    oldValue?: T
    newValue: T
    timestamp: number
}

// 操作历史接口 - 使用泛型约束替代any
export interface HistoryEntry<T = unknown> {
    id: string
    type: 'action' | 'state'
    description: string
    data: T
    timestamp: number
    canUndo: boolean
    canRedo: boolean
}

// 缓存接口 - 使用泛型约束替代any
export interface CacheItem<T = unknown> {
    key: string
    value: T
    timestamp: number
    ttl?: number
    tags?: string[]
}

// 缓存管理器接口 - 使用泛型约束
export interface CacheManager {
    get<T = unknown>(key: string): T | undefined
    set<T = unknown>(key: string, value: T, ttl?: number, tags?: string[]): void
    delete(key: string): boolean
    clear(): void
    has(key: string): boolean
    keys(): string[]
    size(): number
    clearByTag(tag: string): void
}

// 性能监控数据
export interface PerformanceMetrics {
    componentRenderTime: number
    schemaParseTime: number
    validationTime: number
    eventProcessTime: number
    memoryUsage: number
    timestamp: number
}

// 错误信息接口 - 使用具体类型替代any
export interface ErrorInfo {
    code: string
    message: string
    stack?: string
    context?: Record<string, unknown>
    timestamp: number
    level: 'info' | 'warn' | 'error' | 'critical'
}

// 调试信息接口 - 使用泛型约束替代any
export interface DebugInfo<T = unknown> {
    component?: string
    action?: string
    data?: T
    timestamp: number
    level: 'trace' | 'debug' | 'info'
}

// 通用回调函数类型 - 使用泛型约束替代any
export type CallbackFunction<T = unknown, R = void> = (data: T) => R | Promise<R>

// 通用配置对象类型 - 使用具体类型替代any
export type ConfigObject = Record<string, unknown>

// 通用键值对类型 - 使用泛型约束替代any
export type KeyValuePair<K = string, V = unknown> = {
    key: K
    value: V
}