/**
 * CrfNumber 组件类型定义
 * 
 * 定义了 CrfNumber 组件的 Props、<PERSON><PERSON><PERSON>、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

// Number component specific props extending base props
export interface CrfNumberProps extends BaseComponentProps<number> {
  placeholder?: string
  min?: number
  max?: number
  step?: number
  stepStrictly?: boolean
  precision?: number
  size?: 'small' | 'medium' | 'large'
  controls?: boolean
  controlsPosition?: 'default' | 'right'
  valueOnClear?: number | null
  unit?: string
  formatter?: (value: number) => string
  parser?: (value: string) => number
  medicalType?: 'general' | 'vital' | 'lab' | 'measurement' | 'dosage' | 'duration'
  fieldCode?: string
}

// Number component specific emits extending base emits
export interface CrfNumberEmits extends BaseComponentEmits<number> {
  'input': [value: number]
}

// Number component configuration
export interface CrfNumberConfig extends BaseComponentConfig<number> {
  type: 'number'
  props: CrfNumberProps
}

// Number component schema
export interface CrfNumberSchema {
  type: 'number' | 'integer'
  title?: string
  description?: string
  default?: number
  minimum?: number
  maximum?: number
  exclusiveMinimum?: number
  exclusiveMaximum?: number
  multipleOf?: number
  format?: 'int32' | 'int64' | 'float' | 'double'
}

// Legacy event interface for backward compatibility
export interface CrfNumberEvents {
  'update:modelValue': (value: number) => void
  'input': (value: number) => void
  'change': (value: number) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
}

// 插槽类型定义
export interface CrfNumberSlots {
  /**
   * 前缀插槽
   */
  prefix?: () => VNode[]
  
  /**
   * 后缀插槽
   */
  suffix?: () => VNode[]
  
  /**
   * 单位插槽
   */
  unit?: () => VNode[]
}

// 默认配置类型
export type CrfNumberDefaultConfig = Required<CrfNumberConfig>

// 组件实例类型
export type CrfNumberInstance = ComponentInstance<{
  props: CrfNumberProps
  slots: CrfNumberSlots
  events: CrfNumberEvents
}>

// Number component validation rule with specific types
export interface CrfNumberValidationRule extends ValidationRule<number> {
  type: 'required' | 'min' | 'max' | 'range' | 'custom'
  min?: number
  max?: number
  step?: number
  precision?: number
}

// Validation result with specific types
export interface CrfNumberValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

// Export for backward compatibility
export type CrfNumberSchemaType = CrfNumberConfig