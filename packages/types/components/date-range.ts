/**
 * CrfDateRange 组件类型定义
 * 
 * 定义了 CrfDateRange 组件的 Props、<PERSON><PERSON><PERSON>、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

import type { SingleDateValue } from './date'
// Date range value type
export type DateRangeValue = [SingleDateValue, SingleDateValue] | null

// Date range component specific props extending base props
export interface CrfDateRangeProps extends BaseComponentProps<DateRangeValue> {
  placeholder?: string | [string, string]
  startPlaceholder?: string
  endPlaceholder?: string
  format?: string
  valueFormat?: string
  type?: 'date' | 'datetime' | 'daterange' | 'datetimerange' | 'month' | 'monthrange' | 'year' | 'yearrange' | 'week' | 'weekrange'
  size?: 'small' | 'medium' | 'large'
  editable?: boolean
  clearable?: boolean
  separator?: string
  rangeSeparator?: string
  popperClass?: string
  unlinkPanels?: boolean
  shortcuts?: Array<{
    text: string
    value: DateRangeValue | (() => DateRangeValue)
  }>
  disabledDate?: (date: Date) => boolean
  cellClassName?: (date: Date) => string
  defaultTime?: [Date, Date] | [string, string]
  defaultValue?: DateRangeValue
  validateEvent?: boolean
  medicalType?: 'general' | 'admission' | 'discharge' | 'treatment' | 'followup' | 'study'
  fieldCode?: string
}

// Date range component specific emits extending base emits
export interface CrfDateRangeEmits extends BaseComponentEmits<DateRangeValue> {
  'calendar-change': [value: DateRangeValue]
  'panel-change': [value: DateRangeValue, mode: string, view: string]
  'visible-change': [visible: boolean]
}

// Date range component configuration
export interface CrfDateRangeConfig extends BaseComponentConfig<DateRangeValue> {
  type: 'date-range'
  props: CrfDateRangeProps
}

// Date range component schema
export interface CrfDateRangeSchema {
  type: 'array'
  title?: string
  description?: string
  items?: {
    type: 'string'
    format: 'date' | 'date-time'
  }
  minItems?: number
  maxItems?: number
}

// Legacy event interface for backward compatibility
export interface CrfDateRangeEvents {
  'update:modelValue': (value: DateRangeValue) => void
  'change': (value: DateRangeValue) => void
  'calendar-change': (value: DateRangeValue) => void
  'panel-change': (value: DateRangeValue, mode: string, view: string) => void
  'visible-change': (visible: boolean) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
}

// 插槽类型定义
export interface CrfDateRangeSlots {
  /**
   * 自定义日期单元格内容
   */
  'date-cell'?: (props: { 
    date: Date
    data: {
      isSelected: boolean
      type: string
      day: string
    }
  }) => VNode[]
  
  /**
   * 自定义范围分隔符
   */
  'range-separator'?: () => VNode[]
}

// 默认配置类型
export type CrfDateRangeDefaultConfig = Required<CrfDateRangeConfig>

// 组件实例类型
export type CrfDateRangeInstance = ComponentInstance<{
  props: CrfDateRangeProps
  slots: CrfDateRangeSlots
  events: CrfDateRangeEvents
}>

// Date range component validation rule with specific types
export interface CrfDateRangeValidationRule extends ValidationRule<DateRangeValue> {
  type: 'required' | 'dateRange' | 'minDays' | 'maxDays' | 'beforeToday' | 'afterToday' | 'custom'
  minDays?: number
  maxDays?: number
  allowFuture?: boolean
  allowPast?: boolean
  startDate?: Date | string
  endDate?: Date | string
}

// Validation result with specific types
export interface CrfDateRangeValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

// Export for backward compatibility
export type CrfDateRangeSchemaType = CrfDateRangeConfig