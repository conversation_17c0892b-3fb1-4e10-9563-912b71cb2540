/**
 * CrfDate 组件类型定义
 * 
 * 定义了 CrfDate 组件的 Props、Schema、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

// Date value type
export type SingleDateValue = Date | string | number | null
export type RangeDateValue = [SingleDateValue, SingleDateValue]
export type DateValue = SingleDateValue | RangeDateValue

// Date component specific props extending base props
export interface CrfDateProps extends BaseComponentProps<DateValue> {
  placeholder?: string
  format?: string
  valueFormat?: string
  type?: 'date' | 'datetime' | 'daterange' | 'datetimerange' | 'month' | 'monthrange' | 'year' | 'yearrange' | 'week' | 'weekrange'
  size?: 'small' | 'medium' | 'large'
  editable?: boolean
  clearable?: boolean
  popperClass?: string
  shortcuts?: Array<{
    text: string
    value: DateValue | (() => DateValue)
  }>
  disabledDate?: (date: Date) => boolean
  cellClassName?: (date: Date) => string
  defaultTime?: Date | string
  defaultValue?: DateValue
  validateEvent?: boolean
  medicalType?: 'general' | 'birth' | 'admission' | 'discharge' | 'diagnosis' | 'treatment'
  fieldCode?: string
}

// Date component specific emits extending base emits
export interface CrfDateEmits extends BaseComponentEmits<DateValue> {
  'calendar-change': [value: DateValue]
  'panel-change': [value: DateValue, mode: string, view: string]
  'visible-change': [visible: boolean]
}

// Date component configuration
export interface CrfDateConfig extends BaseComponentConfig<DateValue> {
  type: 'date'
  props: CrfDateProps
}

// Date component schema
export interface CrfDateSchema {
  type: 'string'
  title?: string
  description?: string
  format?: 'date' | 'date-time'
  pattern?: string
}

// Legacy event interface for backward compatibility
export interface CrfDateEvents {
  'update:modelValue': (value: DateValue) => void
  'change': (value: DateValue) => void
  'calendar-change': (value: DateValue) => void
  'panel-change': (value: DateValue, mode: string, view: string) => void
  'visible-change': (visible: boolean) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
}

// 插槽类型定义
export interface CrfDateSlots {
  /**
   * 自定义日期单元格内容
   */
  'date-cell'?: (props: { 
    date: Date
    data: {
      isSelected: boolean
      type: string
      day: string
    }
  }) => VNode[]
}

// 默认配置类型
export type CrfDateDefaultConfig = Required<CrfDateConfig>

// 组件实例类型
export type CrfDateInstance = ComponentInstance<{
  props: CrfDateProps
  slots: CrfDateSlots
  events: CrfDateEvents
}>

// Date component validation rule with specific types
export interface CrfDateValidationRule extends ValidationRule<DateValue> {
  type: 'required' | 'date' | 'beforeToday' | 'afterToday' | 'dateRange' | 'custom'
  allowFuture?: boolean
  allowPast?: boolean
  minDate?: Date | string
  maxDate?: Date | string
}

// Validation result with specific types
export interface CrfDateValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

// Export for backward compatibility
export type CrfDateSchemaType = CrfDateConfig