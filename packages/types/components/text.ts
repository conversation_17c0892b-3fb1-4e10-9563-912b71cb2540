/**
 * CrfText 组件类型定义 - 使用泛型约束替代any
 * 
 * 定义了 CrfText 组件的 Props、Schema、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule,
} from '@crf/types/base'

// Text component specific props extending base props
export interface CrfTextProps extends BaseComponentProps<string> {
  placeholder?: string;
  format?: 'text' | 'email' | 'phone' | 'url' | 'number';
  minLength?: number;
  maxLength?: number;
  align?: 'left' | 'center' | 'right' | 'justify';
  showCount?: boolean;
  clearable?: boolean;
  autofocus?: boolean;
  prefixIcon?: string;
  suffixIcon?: string;
  customErrorMessage?: string;
  medicalType?: 'general' | 'vital' | 'lab' | 'medication' | 'diagnosis' | 'procedure' | 'allergy' | 'history';
  fieldCode?: string;
}

// Text component specific emits extending base emits
export interface CrfTextEmits extends BaseComponentEmits<string> {
  input: [value: string];
  clear: [];
  keydown: [event: KeyboardEvent];
  keyup: [event: KeyboardEvent];
  keypress: [event: KeyboardEvent];
}

// Text component configuration
export interface CrfTextConfig extends BaseComponentConfig<string> {
  type: 'text';
  props: CrfTextProps;
}

// Text component schema
export interface CrfTextSchema {
  type: 'string';
  title?: string;
  description?: string;
  default?: string;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  format?: 'email' | 'url' | 'phone' | 'number';
  enum?: string[];
}

export interface CrfTextEvents {
  'update:modelValue': (value: string) => void;
  input: (value: string) => void;
  change: (value: string) => void;
  focus: (event: FocusEvent) => void;
  blur: (event: FocusEvent) => void;
  clear: () => void;
  keydown: (event: KeyboardEvent) => void;
  keyup: (event: KeyboardEvent) => void;
  keypress: (event: KeyboardEvent) => void;
}

// 插槽类型定义
export interface CrfTextSlots {
  /**
   * 前缀插槽
   */
  prefix?: () => VNode[]

  /**
   * 后缀插槽
   */
  suffix?: () => VNode[]

  /**
   * 前置元素插槽
   */
  prepend?: () => VNode[]

  /**
   * 后置元素插槽
   */
  append?: () => VNode[]
}

// 默认配置类型
export type CrfTextDefaultConfig = Required<CrfTextConfig>

// 组件实例类型
export type CrfTextInstance = ComponentInstance<{
  props: CrfTextProps
  slots: CrfTextSlots
  events: CrfTextEvents
}>

// Text component validation rule with specific types
export interface CrfTextValidationRule extends ValidationRule<string> {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'email' | 'url' | 'phone' | 'custom';
  minLength?: number;
  maxLength?: number;
  format?: 'text' | 'email' | 'phone' | 'url' | 'number';
}

// Validation result with specific types
export interface CrfTextValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}