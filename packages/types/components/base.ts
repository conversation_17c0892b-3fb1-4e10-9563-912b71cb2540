// 基础组件类型定义
export interface BaseComponentProps {
  id?: string
  className?: string
  style?: Record<string, unknown>
}

export interface BaseComponentSchema {
  type: string
  props?: Record<string, unknown>
  config?: Record<string, unknown>
}

export interface ComponentConfig {
  type: string
  props?: Record<string, unknown>
  children?: ComponentConfig[]
}

export interface ComponentInstance<T = Record<string, unknown>> {
  id: string
  type: string
  props: T
  config: Record<string, unknown>
  mounted: boolean
  visible: boolean
}

export type ComponentSize = 'small' | 'medium' | 'large'
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error'

// 医疗验证相关类型
export interface MedicalValidationStatus {
  /** 是否通过医疗验证 */
  isValid: boolean
  
  /** 验证级别 */
  level: 'info' | 'warning' | 'error' | 'critical'
  
  /** 验证消息 */
  message: string
  
  /** 相关医疗编码 */
  coding?: {
    system: string
    code: string
    display?: string
  }
  
  /** 是否需要医生确认 */
  requiresConfirmation: boolean
}