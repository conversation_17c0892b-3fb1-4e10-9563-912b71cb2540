/**
 * CrfSelect 组件类型定义
 * 
 * 定义了 CrfSelect 组件的 Props、<PERSON><PERSON><PERSON>、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

// Select value type
export type SelectValue = string | number | (string | number)[] | null | undefined

// 选项类型定义
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  description?: string
  group?: string
}

// 选项组类型定义
export interface SelectOptionGroup {
  label: string
  options: SelectOption[]
  disabled?: boolean
}

// Select component specific props extending base props
export interface CrfSelectProps extends BaseComponentProps<string | number | (string | number)[]> {
  options?: SelectOption[]
  optionGroups?: SelectOptionGroup[]
  placeholder?: string
  multiple?: boolean
  multipleLimit?: number
  clearable?: boolean
  filterable?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => Promise<SelectOption[]>
  loading?: boolean
  noDataText?: string
  noMatchText?: string
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
  maxCollapseTags?: number
  allowCreate?: boolean
  reserveKeyword?: boolean
  defaultFirstOption?: boolean
  popperClass?: string
  teleported?: boolean
  medicalType?: 'general' | 'diagnosis' | 'medication' | 'lab' | 'procedure' | 'department'
  fieldCode?: string
}

// Select component specific emits extending base emits
export interface CrfSelectEmits extends BaseComponentEmits<string | number | (string | number)[]> {
  'option-change': [option: SelectOption | SelectOption[]]
  'visible-change': [visible: boolean]
  'remove-tag': [value: string | number]
  'clear': []
  'filter': [query: string]
  'update-options': [options: SelectOption[]]
}

// Select component configuration
export interface CrfSelectConfig extends BaseComponentConfig<string | number | (string | number)[]> {
  type: 'select'
  props: CrfSelectProps
}

// Select component schema
export interface CrfSelectSchema {
  type: 'string' | 'number' | 'array'
  title?: string
  description?: string
  default?: string | number | (string | number)[]
  enum?: (string | number)[]
  enumNames?: string[]
  options?: SelectOption[]
  optionGroups?: SelectOptionGroup[]
  multiple?: boolean
  minItems?: number
  maxItems?: number
}

// Legacy event interface for backward compatibility
export interface CrfSelectEvents {
  'update:modelValue': (value: string | number | (string | number)[]) => void
  'change': (value: string | number | (string | number)[]) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
  'option-change': (option: SelectOption | SelectOption[]) => void
  'visible-change': (visible: boolean) => void
  'remove-tag': (value: string | number) => void
  'clear': () => void
  'filter': (query: string) => void
  'update-options': (options: SelectOption[]) => void
}

// 插槽类型定义
export interface CrfSelectSlots {
  /**
   * 自定义选项内容
   */
  option?: (props: { option: SelectOption; selected: boolean }) => VNode[]
  
  /**
   * 自定义选项组标题
   */
  group?: (props: { group: SelectOptionGroup }) => VNode[]
  
  /**
   * 前缀插槽
   */
  prefix?: () => VNode[]
  
  /**
   * 后缀插槽
   */
  suffix?: () => VNode[]
  
  /**
   * 空数据插槽
   */
  empty?: () => VNode[]
}

// 默认配置类型
export type CrfSelectDefaultConfig = Required<CrfSelectConfig>

// 组件实例类型
export type CrfSelectInstance = ComponentInstance<{
  props: CrfSelectProps
  slots: CrfSelectSlots
  events: CrfSelectEvents
}>

// Select component validation rule with specific types
export interface CrfSelectValidationRule extends ValidationRule<string | number | (string | number)[]> {
  type: 'required' | 'minItems' | 'maxItems' | 'custom'
  minItems?: number
  maxItems?: number
  allowedValues?: (string | number)[]
}

// Validation result with specific types
export interface CrfSelectValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

// Export for backward compatibility
export type CrfSelectSchemaType = CrfSelectConfig