/**
 * CrfTextarea 组件类型定义
 * 
 * 定义了 CrfTextarea 组件的 Props、<PERSON>hem<PERSON>、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule,
} from '@crf/types/base'

// Textarea component specific props extending base props
export interface CrfTextareaProps extends BaseComponentProps<string> {
  placeholder?: string;
  minLength?: number;
  maxLength?: number;
  rows?: number;
  autosize?: boolean | { minRows?: number; maxRows?: number };
  resize?: 'none' | 'both' | 'horizontal' | 'vertical';
  showCount?: boolean;
  autofocus?: boolean;
  customErrorMessage?: string;
  medicalType?: 'general' | 'vital' | 'lab' | 'medication' | 'diagnosis' | 'procedure' | 'allergy' | 'history' | 'symptom' | 'assessment' | 'plan' | 'progress';
  fieldCode?: string;
  dataCollection?: 'required' | 'optional' | 'conditional';
  codingSystem?: string;
  textFormat?: 'plain' | 'structured' | 'soap' | 'narrative';
  template?: string;
  useTemplate?: boolean;
}

// Textarea component specific emits extending base emits
export interface CrfTextareaEmits extends BaseComponentEmits<string> {
  input: [value: string];
  keydown: [event: KeyboardEvent];
  keyup: [event: KeyboardEvent];
  keypress: [event: KeyboardEvent];
  resize: [event: Event];
}

// Textarea component configuration
export interface CrfTextareaConfig extends BaseComponentConfig<string> {
  type: 'textarea';
  props: CrfTextareaProps;
}

// Textarea component schema
export interface CrfTextareaSchema {
  type: 'string';
  title?: string;
  description?: string;
  default?: string;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  format?: 'plain' | 'structured' | 'soap' | 'narrative';
  template?: string;
}

// Legacy event interface for backward compatibility
export interface CrfTextareaEvents {
  'update:modelValue': (value: string) => void;
  input: (value: string) => void;
  change: (value: string) => void;
  focus: (event: FocusEvent) => void;
  blur: (event: FocusEvent) => void;
  keydown: (event: KeyboardEvent) => void;
  keyup: (event: KeyboardEvent) => void;
  keypress: (event: KeyboardEvent) => void;
  resize: (event: Event) => void;
}

// 插槽类型定义
export interface CrfTextareaSlots {
  /**
   * 前缀插槽
   */
  prefix?: () => VNode[]

  /**
   * 后缀插槽
   */
  suffix?: () => VNode[]

  /**
   * 模板提示插槽
   */
  template?: () => VNode[]
}

// 默认配置类型
export type CrfTextareaDefaultConfig = Required<CrfTextareaConfig>

// 组件实例类型
export type CrfTextareaInstance = ComponentInstance<{
  props: CrfTextareaProps
  slots: CrfTextareaSlots
  events: CrfTextareaEvents
}>

// Textarea component validation rule with specific types
export interface CrfTextareaValidationRule extends ValidationRule<string> {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  minLength?: number;
  maxLength?: number;
  format?: 'plain' | 'structured' | 'soap' | 'narrative';
}

// Validation result with specific types
export interface CrfTextareaValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

// Medical text formats for healthcare CRF
export interface MedicalTextFormats {
  soap: {
    subjective?: string;
    objective?: string;
    assessment?: string;
    plan?: string;
  };
  structured: {
    sections: Array<{
      title: string;
      content: string;
    }>;
  };
  narrative: {
    content: string;
    timestamp?: Date;
    author?: string;
  };
}

// Export for backward compatibility
export type CrfTextareaSchemaType = CrfTextareaConfig; 