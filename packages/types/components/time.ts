/**
 * CrfTime 组件类型定义
 */

import type { ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

export type TimeValue = string | null

export interface CrfTimeProps extends BaseComponentProps<TimeValue> {
  placeholder?: string
  format?: string
  size?: 'small' | 'medium' | 'large'
  editable?: boolean
  clearable?: boolean
  medicalType?: 'general' | 'medication' | 'procedure' | 'vital' | 'monitoring'
  fieldCode?: string
}

export interface CrfTimeEmits extends BaseComponentEmits<TimeValue> {
  'visible-change': [visible: boolean]
}

export interface CrfTimeConfig extends BaseComponentConfig<TimeValue> {
  type: 'time'
  props: CrfTimeProps
}

export interface CrfTimeSchema {
  type: 'string'
  title?: string
  description?: string
  format?: 'time'
  pattern?: string
}

export interface CrfTimeEvents {
  'update:modelValue': (value: TimeValue) => void
  'change': (value: TimeValue) => void
  'visible-change': (visible: boolean) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
}

export interface CrfTimeSlots {}

export type CrfTimeDefaultConfig = Required<CrfTimeConfig>

export type CrfTimeInstance = ComponentInstance<{
  props: CrfTimeProps
  slots: CrfTimeSlots
  events: CrfTimeEvents
}>

export interface CrfTimeValidationRule extends ValidationRule<TimeValue> {
  type: 'required' | 'time' | 'custom'
}

export interface CrfTimeValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

export type CrfTimeSchemaType = CrfTimeConfig