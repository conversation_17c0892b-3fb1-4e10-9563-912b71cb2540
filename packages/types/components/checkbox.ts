/**
 * CrfCheckbox 组件类型定义
 * 
 * 定义了 CrfCheckbox 组件的 Props、<PERSON><PERSON><PERSON>、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

// 选项值类型定义 (与 Element Plus 保持一致)
export type CheckboxValueType = string | number | boolean

// 选项类型定义
export interface CheckboxOption {
  value: string | number
  label: string
  disabled?: boolean
  description?: string
}

// Checkbox component specific props extending base props
export interface CrfCheckboxProps extends BaseComponentProps<(string | number)[]> {
  options?: CheckboxOption[]
  direction?: 'horizontal' | 'vertical'
  size?: 'small' | 'medium' | 'large'
  buttonStyle?: boolean
  border?: boolean
  min?: number
  max?: number
  checkAll?: boolean
  medicalType?: 'general' | 'symptom' | 'comorbidity' | 'medication' | 'procedure' | 'allergy'
  fieldCode?: string
}

// Checkbox component specific emits extending base emits
export interface CrfCheckboxEmits extends BaseComponentEmits<(string | number)[]> {
  'option-change': [options: CheckboxOption[]]
  'check-all': [checked: boolean]
  'update-options': [options: CheckboxOption[]]
}

// Checkbox component configuration
export interface CrfCheckboxConfig extends BaseComponentConfig<(string | number)[]> {
  type: 'checkbox'
  props: CrfCheckboxProps
}

// Checkbox component schema
export interface CrfCheckboxSchema {
  type: 'array'
  title?: string
  description?: string
  default?: (string | number)[]
  items?: {
    type: 'string' | 'number'
    enum?: (string | number)[]
    enumNames?: string[]
  }
  minItems?: number
  maxItems?: number
  uniqueItems?: boolean
  options?: CheckboxOption[]
}

export interface CrfCheckboxEvents {
  'update:modelValue': (value: (string | number)[]) => void
  'change': (value: (string | number)[]) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
  'option-change': (options: CheckboxOption[]) => void
  'check-all': (checked: boolean) => void
  'update-options': (options: CheckboxOption[]) => void
}

// 插槽类型定义
export interface CrfCheckboxSlots {
  /**
   * 自定义选项内容
   */
  option?: (props: { option: CheckboxOption; checked: boolean }) => VNode[]

  /**
   * 选项描述插槽
   */
  description?: (props: { option: CheckboxOption }) => VNode[]

  /**
   * 全选按钮插槽
   */
  checkAll?: (props: { checked: boolean; indeterminate: boolean }) => VNode[]
}

// 默认配置类型
export type CrfCheckboxDefaultConfig = Required<CrfCheckboxConfig>

// 组件实例类型
export type CrfCheckboxInstance = ComponentInstance<{
  props: CrfCheckboxProps
  slots: CrfCheckboxSlots
  events: CrfCheckboxEvents
}>

// Checkbox component validation rule with specific types
export interface CrfCheckboxValidationRule extends ValidationRule<(string | number)[]> {
  type: 'required' | 'minItems' | 'maxItems' | 'custom'
  minItems?: number
  maxItems?: number
  allowedValues?: (string | number)[]
}

// Validation result with specific types
export interface CrfCheckboxValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

export type CrfCheckboxSchemaType = CrfCheckboxConfig