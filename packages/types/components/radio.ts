/**
 * CrfRadio 组件类型定义
 * 
 * 定义了 CrfRadio 组件的 Props、<PERSON>hem<PERSON>、Config 等相关类型
 */

import type { VNode, ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule
} from '@crf/types/base'

// 选项值类型定义 (与 Element Plus 保持一致)
export type RadioValueType = string | number | boolean | undefined

// 选项类型定义
export interface RadioOption {
  value: string | number
  label: string
  disabled?: boolean
  description?: string
}

// Radio component specific props extending base props
export interface CrfRadioProps extends BaseComponentProps<string | number> {
  options?: RadioOption[]
  direction?: 'horizontal' | 'vertical'
  size?: 'small' | 'medium' | 'large'
  buttonStyle?: boolean
  border?: boolean
  placeholder?: string
  enableCustomError?: boolean
  medicalType?: 'general' | 'symptom' | 'severity' | 'frequency' | 'response' | 'status'
  fieldCode?: string
}

// Radio component specific emits extending base emits
export interface CrfRadioEmits extends BaseComponentEmits<string | number> {
  'option-change': [option: RadioOption]
  'update-options': [options: RadioOption[]]
}

// Radio component configuration
export interface CrfRadioConfig extends BaseComponentConfig<string | number> {
  type: 'radio'
  props: CrfRadioProps
}

// Radio component schema
export interface CrfRadioSchema {
  type: 'string' | 'number'
  title?: string
  description?: string
  default?: string | number
  enum?: (string | number)[]
  enumNames?: string[]
  options?: RadioOption[]
}

export interface CrfRadioEvents {
  'update:modelValue': (value: string | number) => void
  'change': (value: string | number) => void
  'focus': (event: FocusEvent) => void
  'blur': (event: FocusEvent) => void
  'option-change': (option: RadioOption) => void
  'update-options': (options: RadioOption[]) => void
}

// 插槽类型定义
export interface CrfRadioSlots {
  /**
   * 自定义选项内容
   */
  option?: (props: { option: RadioOption; selected: boolean }) => VNode[]

  /**
   * 选项描述插槽
   */
  description?: (props: { option: RadioOption }) => VNode[]
}

// 默认配置类型
export type CrfRadioDefaultConfig = Required<CrfRadioConfig>

// 组件实例类型
export type CrfRadioInstance = ComponentInstance<{
  props: CrfRadioProps
  slots: CrfRadioSlots
  events: CrfRadioEvents
}>

// Radio component validation rule with specific types
export interface CrfRadioValidationRule extends ValidationRule<string | number> {
  type: 'required' | 'custom'
  allowedValues?: (string | number)[]
}

// Validation result with specific types
export interface CrfRadioValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

export type CrfRadioSchemaType = CrfRadioConfig