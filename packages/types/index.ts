/**
 * CRF Frontend 类型系统统一导出
 * 
 * 分层架构：
 * - base: 基础类型定义
 * - shared: 共享类型定义
 * - core: 核心业务类型
 * - components: 组件相关类型
 * - editor: 编辑器相关类型
 * - form: 表单相关类型
 * - validation: 验证相关类型
 * - application: 应用层类型
 */

// =============================================================================
// 基础类型层 - 最底层的类型定义
// =============================================================================
export * from './base'
export * from './shared'

// =============================================================================
// 核心业务类型层 - 核心业务逻辑类型
// =============================================================================
export type {
  ValidationResult as CoreValidationResult,
  ComponentType as CoreComponentType,
  FormData as CoreFormData
} from './core'

// =============================================================================
// 组件类型层 - 所有组件相关类型
// =============================================================================
// 基础组件类型
export type {
  BaseComponentProps,
  BaseComponentSchema,
  ComponentConfig,
  ComponentSize,
  ComponentVariant,
  ComponentInstance,
  MedicalValidationStatus
} from './components/base'

// 具体组件类型
export * from './components'

// =============================================================================
// 编辑器类型层 - 编辑器功能相关类型
// =============================================================================
export type {
  EditorState,
  ComponentConfig as EditorComponentConfig,
  ComponentProps as EditorComponentProps,
  ComponentValidationConfig,
  ComponentLayoutConfig,
  ComponentMetadata,
  HistoryState,
  HistoryEntry,
  EditorActionResult
} from './editor'

// =============================================================================
// 表单类型层 - 表单系统相关类型
// =============================================================================
export type {
  FormSchema,
  FormProperty,
  FormConfig,
  FormValidationConfig,
  FormLayoutConfig,
  FormMetadata,
  FormActionResult,
  FormData
} from './form'

// =============================================================================
// 验证类型层 - 验证系统相关类型
// =============================================================================
export type {
  ValidationRule,
  ValidationTrigger,
  ValidationState,
  ValidationStatus,
  ValidationResult,
  CustomValidator
} from './validation'

// =============================================================================
// 应用类型层 - 最上层的应用类型
// =============================================================================
export type {
  ValidationResult as ApplicationValidationResult,
  EditorState as ApplicationEditorState,
  BaseBlock,
  PageSchemaFormData,
  BlockSchemaFormData,
  FormSection,
  GlobalConfig,
  FormSchema as ApplicationFormSchema,
  ExportConfig,
  ImportConfig,
  EditorEvent,
  ConfigPanelData
} from './application'

// =============================================================================
// API类型层 - API相关类型定义
// =============================================================================
export * from './src/api'

// =============================================================================
// 统一类型导出 - 便于外部使用的统一接口
// =============================================================================
export * from './unified'