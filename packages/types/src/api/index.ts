/**
 * API相关类型定义
 * 集中管理所有业务API的类型定义
 */

// 基础实体类型
export interface BaseEntity {
  id: string
  created_at: string
  updated_at: string
}

// 用户相关类型
export interface User extends BaseEntity {
  username: string
  email: string
  full_name?: string
  avatar_url?: string
  is_active: boolean
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  full_name?: string
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  full_name?: string
  avatar_url?: string
  is_active?: boolean
}

// 项目相关类型
export interface Project extends BaseEntity {
  name: string
  description?: string
  owner_id: string
  owner?: User
  is_active: boolean
  template_count?: number
  member_count?: number
}

export interface CreateProjectRequest {
  name: string
  description?: string
}

export interface UpdateProjectRequest {
  name?: string
  description?: string
  is_active?: boolean
}

// 项目成员类型
export interface ProjectMember extends BaseEntity {
  project_id: string
  user_id: string
  user: User
  role: 'owner' | 'editor' | 'viewer'
  joined_at: string
}

export interface AddMemberRequest {
  user_id: string
  role: 'editor' | 'viewer'
}

// 模板相关类型
export interface Template extends BaseEntity {
  name: string
  description?: string
  project_id: string
  project?: Project
  schema: Record<string, unknown>
  is_active: boolean
  version: number
  instance_count?: number
}

export interface CreateTemplateRequest {
  name: string
  description?: string
  project_id: string
  schema: Record<string, unknown>
}

export interface UpdateTemplateRequest {
  name?: string
  description?: string
  schema?: Record<string, unknown>
  is_active?: boolean
}

// 实例相关类型
export interface Instance extends BaseEntity {
  template_id: string
  template?: Template
  form_data: Record<string, unknown>
  status: 'draft' | 'submitted' | 'approved' | 'rejected'
  submitted_at?: string
  reviewed_at?: string
  reviewer_id?: string
  reviewer?: User
}

export interface CreateInstanceRequest {
  template_id: string
  form_data?: Record<string, unknown>
}

export interface UpdateInstanceRequest {
  form_data?: Record<string, unknown>
  status?: 'draft' | 'submitted'
}

// 认证相关类型
export interface LoginRequest {
  username: string
  password: string
  remember_me?: boolean
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  user: User
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  full_name?: string
}

export interface RefreshTokenResponse {
  access_token: string
  refresh_token?: string
}

// RBAC相关类型
export interface Role extends BaseEntity {
  name: string
  description?: string
  permissions: Permission[]
  is_system: boolean
}

export interface Permission extends BaseEntity {
  name: string
  resource: string
  action: string
  description?: string
}

export interface UserRole {
  user_id: string
  role_id: string
  assigned_at: string
  assigned_by: string
}

// 历史记录类型
export interface HistoryEntry extends BaseEntity {
  resource_type: string
  resource_id: string
  action: string
  description: string
  user_id: string
  user?: User
  metadata?: Record<string, unknown>
}

// 统计信息类型
export interface ProjectStats {
  template_count: number
  instance_count: number
  member_count: number
  active_templates: number
  recent_activity: Array<{
    type: string
    description: string
    created_at: string
  }>
}

export interface SystemStats {
  total_users: number
  total_projects: number
  total_templates: number
  total_instances: number
  active_users: number
}

// 通用响应类型
export interface ListResponse<T> {
  data: T[]
  pagination: {
    page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// 导出类型
export interface ExportResponse {
  download_url: string
  expires_at: string
  format: 'json' | 'csv' | 'excel'
}

// 文件上传类型
export interface UploadResponse {
  url: string
  filename: string
  size: number
  mime_type: string
}

// 健康检查类型
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  services: Record<string, {
    status: 'up' | 'down'
    response_time?: number
    error?: string
  }>
}