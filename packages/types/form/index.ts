/**
 * 表单类型定义 - 完全类型安全
 */

import type { ComponentId, SectionId, FormFieldValue, WithId, WithTimestamp } from '../core'

// 表单数据类型
export interface FormData {
  [key: string]: FormFieldValue
}

// 验证规则类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  pattern?: string
  validator?: (value: unknown) => boolean | string
}

// 表单Schema - 完全类型安全
export interface FormSchema extends WithId {
  readonly title: string
  readonly description?: string
  readonly version: string
  readonly sections: readonly FormSection[]
  readonly properties: readonly FormProperty[]
  readonly required: readonly string[]
  readonly definitions?: readonly FormDefinition[]
}

// 表单章节 - 完全类型安全
export interface FormSection extends WithId, WithTimestamp {
  readonly name: string
  readonly title: string
  readonly description?: string
  readonly components: readonly ComponentId[]
  readonly level: number
  readonly parentId?: SectionId
  readonly children: readonly FormSection[]
  readonly order: number
  readonly collapsed: boolean
  readonly visible: boolean
}

// 表单属性 - 完全类型安全
export interface FormProperty {
  readonly id: string
  readonly name: string
  readonly type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  readonly title?: string
  readonly description?: string
  readonly default?: FormFieldValue
  readonly enum?: readonly FormFieldValue[]
  readonly format?: string
  readonly minimum?: number
  readonly maximum?: number
  readonly minLength?: number
  readonly maxLength?: number
  readonly pattern?: string
  readonly required: boolean
  readonly componentId: ComponentId
}

// 表单定义 - 完全类型安全
export interface FormDefinition {
  readonly name: string
  readonly type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  readonly properties?: readonly FormProperty[]
  readonly required?: readonly string[]
}

// 表单配置 - 完全类型安全
export interface FormConfig {
  readonly schema: FormSchema
  readonly data: FormData
  readonly validation: FormValidationConfig
  readonly layout: FormLayoutConfig
  readonly metadata: FormMetadata
}

// 表单验证配置 - 完全类型安全
export interface FormValidationConfig {
  readonly validateOnChange: boolean
  readonly validateOnBlur: boolean
  readonly validateOnSubmit: boolean
  readonly showValidationMessages: boolean
  readonly customValidators: readonly CustomValidator[]
}

// 自定义验证器 - 完全类型安全
export interface CustomValidator {
  readonly name: string
  readonly validate: (value: FormFieldValue, fieldId: string, formData: FormData) => ValidationResult
  readonly message: string
}

// 表单布局配置 - 完全类型安全
export interface FormLayoutConfig {
  readonly layout: 'vertical' | 'horizontal' | 'grid'
  readonly columns: number
  readonly labelWidth?: string
  readonly labelPosition: 'left' | 'top' | 'right'
  readonly size: 'small' | 'medium' | 'large'
  readonly disabled: boolean
  readonly readonly: boolean
}

// 表单元数据 - 完全类型安全
export interface FormMetadata {
  readonly author?: string
  readonly createTime: number
  readonly updateTime: number
  readonly version: string
  readonly tags: readonly string[]
  readonly category?: string
  readonly description?: string
}

// 表单操作结果 - 完全类型安全
export interface FormActionResult {
  readonly success: boolean
  readonly data?: FormData
  readonly error?: string
  readonly warnings?: readonly string[]
}

// 表单验证结果 - 完全类型安全
export interface ValidationResult {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly status: 'success' | 'warning' | 'error' | 'validating'
}