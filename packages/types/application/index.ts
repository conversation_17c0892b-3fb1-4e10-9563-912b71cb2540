/**
 * 应用类型定义
 */

import type { BaseWithId, BaseWithTimestamp } from '../base'

// 编辑器状态接口
export interface EditorState {
    currentSelect: BaseBlock
    blockConfig: BaseBlock[]
    pageConfig: PageSchemaFormData
    pageName: string
    configPanelShow: boolean
}

// 基础块接口
export interface BaseBlock extends BaseWithId {
    code?: string
    name?: string
    desc?: string
    icon?: string
    iconColor?: string
    tag?: string
    nested?: boolean
    children?: BaseBlock[][]
    formData?: Partial<BlockSchemaFormData[keyof BlockSchemaFormData]>
    parent?: string
    key?: string
}

// 页面Schema表单数据
export interface PageSchemaFormData {
    title?: string
    description?: string
    [key: string]: unknown
}

// 块Schema表单数据
export interface BlockSchemaFormData {
    [key: string]: unknown
}

// 表单分组定义
export interface FormSection extends BaseWithId, BaseWithTimestamp {
    name: string
    description?: string
    blocks: BaseBlock[]
    collapsed?: boolean
    sort?: number
}

// 全局配置
export interface GlobalConfig {
    title: string
    description?: string
    submitText?: string
    resetText?: string
    showSubmit?: boolean
    showReset?: boolean
    disabled?: boolean
    validateOnRuleChange?: boolean
    hideRequiredAsterisk?: boolean
    showMessage?: boolean
    inlineMessage?: boolean
}

// 表单Schema主结构
export interface FormSchema extends BaseWithId, BaseWithTimestamp {
    version: string
    title: string
    description?: string
    sections: FormSection[]
    globalConfig: GlobalConfig
    author?: string
    tags?: string[]
    status?: 'draft' | 'published' | 'archived'
}

// 验证结果
export interface ValidationResult {
    valid: boolean
    errors: Array<{
        field: string
        message: string
        value: unknown
    }>
}

// 导出配置
export interface ExportConfig {
    format: 'json' | 'yaml' | 'xml'
    includeData?: boolean
    includeConfig?: boolean
    compress?: boolean
}

// 导入配置
export interface ImportConfig {
    format: 'json' | 'yaml' | 'xml'
    merge?: boolean
    overwrite?: boolean
    validate?: boolean
}

// 编辑器事件
export interface EditorEvent {
    type: 'component-select' | 'component-add' | 'component-delete' | 'component-update' | 'section-add' | 'section-delete' | 'schema-update'
    payload: unknown
    timestamp: number
}

// 配置面板数据
export interface ConfigPanelData {
    selectedComponent?: BaseBlock
    selectedSection?: FormSection
    showComponentConfig: boolean
    showPageConfig: boolean
    title: string
}