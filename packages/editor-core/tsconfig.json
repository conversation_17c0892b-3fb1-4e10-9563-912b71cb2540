{"extends": "@crf/tsconfig/vite.json", "compilerOptions": {"baseUrl": ".", "paths": {"@crf/types": ["../types/index.ts"], "@crf/types/*": ["../types/*"], "@crf/utils": ["../utils/index.ts"], "@crf/utils/*": ["../utils/*"], "@crf/constants": ["../constants/index.ts"], "@crf/constants/*": ["../constants/*"], "@crf/components": ["../components/index.ts"], "@crf/components/*": ["../components/*"]}}, "include": ["src/**/*.ts", "src/**/*.vue", "../utils/**/*.ts", "../types/**/*.ts", "../components/**/*.ts", "../components/**/*.vue"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}