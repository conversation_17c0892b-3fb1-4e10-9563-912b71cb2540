{"name": "@crf/editor-core", "version": "1.0.1", "private": true, "description": "Core editor functionality for CRF applications", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}, "./stores": {"import": "./src/stores/index.ts", "types": "./src/stores/index.ts"}, "./composables": {"import": "./src/composables/index.ts", "types": "./src/composables/index.ts"}, "./utils": {"import": "./src/utils/index.ts", "types": "./src/utils/index.ts"}}, "files": ["src", "dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "type-check": "vue-tsc --noEmit"}, "peerDependencies": {"vue": "^3.5.13", "pinia": "^3.0.1"}, "dependencies": {"@crf/types": "workspace:*", "@crf/utils": "workspace:*", "@crf/constants": "workspace:*"}, "devDependencies": {"@crf/build-config": "workspace:*", "@vitejs/plugin-vue": "^5.2.4", "typescript": "^5.8.3", "vite": "^6.2.4", "vue-tsc": "^2.2.8"}}