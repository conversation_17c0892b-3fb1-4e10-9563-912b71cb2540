/**
 * 编辑模式管理
 * 统一管理编辑器的各种状态和模式
 */

import { ref, computed, watch, inject, provide } from 'vue'

// 编辑器模式
export type EditorMode = 'edit' | 'preview' | 'readonly'

// 组件编辑状态
export interface ComponentEditState {
  componentId: string
  disabled: boolean
  readonly: boolean
  mode: EditorMode
  lastModified: number
}

// 全局编辑状态
export interface GlobalEditState {
  mode: EditorMode
  previewFullscreen: boolean
  componentStates: Map<string, ComponentEditState>
  selectedComponentId: string | null
  hoveredComponentId: string | null
  dragMode: boolean
  configPanelVisible: boolean
}

/**
 * 编辑模式管理组合式API
 */
export function useEditMode() {
  // 全局状态
  const globalState = ref<GlobalEditState>({
    mode: 'edit',
    previewFullscreen: false,
    componentStates: new Map(),
    selectedComponentId: null,
    hoveredComponentId: null,
    dragMode: false,
    configPanelVisible: false
  })

  // 计算属性
  const isEditMode = computed(() => globalState.value.mode === 'edit')
  const isPreviewMode = computed(() => globalState.value.mode === 'preview')
  const isReadonlyMode = computed(() => globalState.value.mode === 'readonly')
  const isFullscreenPreview = computed(() => globalState.value.previewFullscreen)

  /**
   * 设置编辑器模式
   */
  function setEditorMode(mode: EditorMode): void {
    const oldMode = globalState.value.mode
    globalState.value.mode = mode

    // 模式切换时的处理
    if (mode === 'preview') {
      // 进入预览模式时，清除选中状态
      globalState.value.selectedComponentId = null
      globalState.value.configPanelVisible = false
      globalState.value.dragMode = false
      
      // 设置所有组件为只读状态
      globalState.value.componentStates.forEach((state) => {
        state.disabled = true
        state.readonly = true
        state.mode = 'preview'
      })
    } else if (mode === 'edit' && oldMode === 'preview') {
      // 从预览模式回到编辑模式，恢复组件状态
      globalState.value.componentStates.forEach((state) => {
        state.disabled = false
        state.readonly = false
        state.mode = 'edit'
      })
    } else if (mode === 'readonly') {
      // 只读模式，禁用所有交互
      globalState.value.selectedComponentId = null
      globalState.value.configPanelVisible = false
      globalState.value.dragMode = false
      
      globalState.value.componentStates.forEach((state) => {
        state.disabled = true
        state.readonly = true
        state.mode = 'readonly'
      })
    }

    console.log(`编辑器模式已切换: ${oldMode} -> ${mode}`)
  }

  /**
   * 切换预览模式
   */
  function togglePreviewMode(): void {
    const newMode = isPreviewMode.value ? 'edit' : 'preview'
    setEditorMode(newMode)
  }

  /**
   * 设置全屏预览
   */
  function setFullscreenPreview(fullscreen: boolean): void {
    globalState.value.previewFullscreen = fullscreen
    
    if (fullscreen) {
      setEditorMode('preview')
      // 添加全屏样式类
      document.body.classList.add('crf-fullscreen-preview')
    } else {
      // 移除全屏样式类
      document.body.classList.remove('crf-fullscreen-preview')
    }
  }

  /**
   * 注册组件状态
   */
  function registerComponent(componentId: string, initialState?: Partial<ComponentEditState>): void {
    const state: ComponentEditState = {
      componentId,
      disabled: globalState.value.mode !== 'edit',
      readonly: globalState.value.mode !== 'edit',
      mode: globalState.value.mode,
      lastModified: Date.now(),
      ...initialState
    }

    globalState.value.componentStates.set(componentId, state)
  }

  /**
   * 注销组件状态
   */
  function unregisterComponent(componentId: string): void {
    globalState.value.componentStates.delete(componentId)
    
    // 如果是当前选中的组件，清除选中状态
    if (globalState.value.selectedComponentId === componentId) {
      globalState.value.selectedComponentId = null
    }
    
    // 如果是当前悬浮的组件，清除悬浮状态
    if (globalState.value.hoveredComponentId === componentId) {
      globalState.value.hoveredComponentId = null
    }
  }

  /**
   * 获取组件状态
   */
  function getComponentState(componentId: string): ComponentEditState | null {
    return globalState.value.componentStates.get(componentId) || null
  }

  /**
   * 更新组件状态
   */
  function updateComponentState(componentId: string, updates: Partial<ComponentEditState>): void {
    const state = globalState.value.componentStates.get(componentId)
    if (state) {
      Object.assign(state, updates, { lastModified: Date.now() })
    }
  }

  /**
   * 检查组件是否禁用
   */
  function isComponentDisabled(componentId: string): boolean {
    const state = getComponentState(componentId)
    return state?.disabled || !isEditMode.value
  }

  /**
   * 检查组件是否只读
   */
  function isComponentReadonly(componentId: string): boolean {
    const state = getComponentState(componentId)
    return state?.readonly || !isEditMode.value
  }

  /**
   * 设置选中组件
   */
  function setSelectedComponent(componentId: string | null): void {
    globalState.value.selectedComponentId = componentId
    
    if (componentId && isEditMode.value) {
      globalState.value.configPanelVisible = true
    }
  }

  /**
   * 设置悬浮组件
   */
  function setHoveredComponent(componentId: string | null): void {
    globalState.value.hoveredComponentId = componentId
  }

  /**
   * 设置拖拽模式
   */
  function setDragMode(enabled: boolean): void {
    globalState.value.dragMode = enabled
  }

  /**
   * 设置配置面板可见性
   */
  function setConfigPanelVisible(visible: boolean): void {
    globalState.value.configPanelVisible = visible
  }

  /**
   * 批量设置组件状态
   */
  function batchUpdateComponentStates(updates: Record<string, Partial<ComponentEditState>>): void {
    Object.entries(updates).forEach(([componentId, update]) => {
      updateComponentState(componentId, update)
    })
  }

  /**
   * 重置所有组件状态
   */
  function resetAllComponentStates(): void {
    globalState.value.componentStates.forEach((state) => {
      state.disabled = !isEditMode.value
      state.readonly = !isEditMode.value
      state.mode = globalState.value.mode
      state.lastModified = Date.now()
    })
  }

  /**
   * 获取所有组件状态
   */
  function getAllComponentStates(): ComponentEditState[] {
    return Array.from(globalState.value.componentStates.values())
  }

  /**
   * 清理组件状态
   */
  function clearComponentStates(): void {
    globalState.value.componentStates.clear()
    globalState.value.selectedComponentId = null
    globalState.value.hoveredComponentId = null
  }

  // 监听模式变化，自动更新组件状态
  watch(() => globalState.value.mode, () => {
    resetAllComponentStates()
  })

  return {
    // 状态
    globalState,
    
    // 计算属性
    isEditMode,
    isPreviewMode,
    isReadonlyMode,
    isFullscreenPreview,
    
    // 模式管理
    setEditorMode,
    togglePreviewMode,
    setFullscreenPreview,
    
    // 组件状态管理
    registerComponent,
    unregisterComponent,
    getComponentState,
    updateComponentState,
    isComponentDisabled,
    isComponentReadonly,
    
    // 交互状态管理
    setSelectedComponent,
    setHoveredComponent,
    setDragMode,
    setConfigPanelVisible,
    
    // 批量操作
    batchUpdateComponentStates,
    resetAllComponentStates,
    getAllComponentStates,
    clearComponentStates
  }
}

/**
 * 组件级别的编辑状态钩子
 */
export function useComponentEditState(componentId: string, initialState?: Partial<ComponentEditState>) {
  // 尝试获取全局编辑状态
  const editMode = inject<ReturnType<typeof useEditMode>>('editMode')
  
  if (!editMode) {
    throw new Error('useComponentEditState must be used within EditModeProvider')
  }

  // 注册组件状态
  editMode.registerComponent(componentId, initialState)

  // 计算属性
  const componentState = computed(() => editMode.getComponentState(componentId))
  const isDisabled = computed(() => editMode.isComponentDisabled(componentId))
  const isReadonly = computed(() => editMode.isComponentReadonly(componentId))
  const isSelected = computed(() => editMode.globalState.value.selectedComponentId === componentId)
  const isHovered = computed(() => editMode.globalState.value.hoveredComponentId === componentId)

  // 更新状态方法
  const updateState = (updates: Partial<ComponentEditState>) => {
    editMode.updateComponentState(componentId, updates)
  }

  // 选中当前组件
  const select = () => {
    editMode.setSelectedComponent(componentId)
  }

  // 清除选中
  const deselect = () => {
    if (isSelected.value) {
      editMode.setSelectedComponent(null)
    }
  }

  // 设置悬浮状态
  const setHovered = (hovered: boolean) => {
    editMode.setHoveredComponent(hovered ? componentId : null)
  }

  // 组件卸载时清理
  const cleanup = () => {
    editMode.unregisterComponent(componentId)
  }

  return {
    // 状态
    componentState,
    
    // 计算属性
    isDisabled,
    isReadonly,
    isSelected,
    isHovered,
    
    // 编辑器状态
    isEditMode: editMode.isEditMode,
    isPreviewMode: editMode.isPreviewMode,
    isReadonlyMode: editMode.isReadonlyMode,
    
    // 方法
    updateState,
    select,
    deselect,
    setHovered,
    cleanup
  }
}

/**
 * 编辑模式Provider
 */
export function provideEditMode() {
  const editMode = useEditMode()
  provide('editMode', editMode)
  return editMode
}

/**
 * 快捷键支持
 */
export function useEditModeShortcuts(editMode: ReturnType<typeof useEditMode>) {
  const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + P: 切换预览模式
    if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
      event.preventDefault()
      editMode.togglePreviewMode()
    }
    
    // F11: 全屏预览
    if (event.key === 'F11') {
      event.preventDefault()
      editMode.setFullscreenPreview(!editMode.isFullscreenPreview.value)
    }
    
    // Esc: 退出全屏或清除选中
    if (event.key === 'Escape') {
      if (editMode.isFullscreenPreview.value) {
        editMode.setFullscreenPreview(false)
      } else if (editMode.globalState.value.selectedComponentId) {
        editMode.setSelectedComponent(null)
      }
    }
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 返回清理函数
  return () => {
    document.removeEventListener('keydown', handleKeydown)
  }
}

// 默认导出
export default useEditMode