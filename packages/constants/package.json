{"name": "@crf/constants", "version": "1.0.1", "description": "CRF 常量定义", "private": true, "main": "index.ts", "module": "index.ts", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.ts", "require": "./index.ts"}}, "scripts": {"build": "unbuild", "dev": "unbuild --watch", "stub": "unbuild --stub", "type-check": "echo 'Type checking constants package...'", "lint": "echo 'Linting constants package...'", "test": "echo 'Testing constants package...'", "clean": "rm -rf dist"}, "files": ["dist"], "devDependencies": {"@crf/build-config": "workspace:*"}}