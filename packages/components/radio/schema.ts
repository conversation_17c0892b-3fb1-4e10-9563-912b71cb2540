/**
 * CrfRadio 组件 Schema 定义
 * 
 * 使用 TypeBox 定义组件的配置 Schema，支持运行时验证和类型推导
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

/**
 * CrfRadio 组件配置 Schema
 */
export const CrfRadioConfigSchema = createComponentSchema({
    // =============================================================================
    // 基础设置
    // =============================================================================
    title: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件标题',
        default: '单选',
        placeholder: '请输入组件标题',
        description: '显示在组件上方的标题文本',
        showInConfig: true,
        configGroup: 'basic',
        helpIcon: true,
        validation: {
            required: true,
            message: '组件标题不能为空'
        }
    })),

    description: Type.Optional(Type.String({
        code: 'config-textarea',
        label: '描述/备注',
        default: '',
        placeholder: '请输入描述或备注信息',
        description: '组件的详细描述或备注信息',
        showInConfig: true,
        configGroup: 'basic'
    })),

    // =============================================================================
    // 选项设置
    // =============================================================================
    options: Type.Optional(Type.Array(Type.Object({
        value: Type.Union([Type.String(), Type.Number()]),
        label: Type.String(),
        disabled: Type.Optional(Type.Boolean()),
        description: Type.Optional(Type.String())
    }), {
        code: 'config-options',
        label: '选项列表',
        default: [
            { value: 'option1', label: '选项一' },
            { value: 'option2', label: '选项二' },
            { value: 'option3', label: '选项三' }
        ],
        description: '单选框的选项列表',
        showInConfig: false,
        configGroup: 'options',
        minItems: 1,
        maxItems: 20
    })),

    // =============================================================================
    // 外观设置
    // =============================================================================
    direction: Type.Optional(Type.Union([
        Type.Literal('horizontal'),
        Type.Literal('vertical')
    ], {
        code: 'config-select',
        label: '布局方式',
        default: 'horizontal',
        description: '选项的排列方向',
        enumNames: ['横向', '纵向'],
        showInConfig: true,
        configGroup: 'appearance'
    })),

    size: Type.Optional(Type.Union([
        Type.Literal('small'),
        Type.Literal('medium'),
        Type.Literal('large')
    ], {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        description: '组件的显示大小',
        enumNames: ['小', '中', '大'],
        showInConfig: false,
        configGroup: 'appearance'
    })),

    buttonStyle: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '按钮样式',
        default: false,
        description: '是否使用按钮样式的单选框',
        showInConfig: true,
        configGroup: 'appearance'
    })),

    border: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '显示边框',
        default: false,
        description: '是否显示选项边框',
        showInConfig: true,
        configGroup: 'appearance'
    })),

    // =============================================================================
    // 验证设置
    // =============================================================================
    required: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '必填',
        default: false,
        description: '是否为必填字段',
        showInConfig: true,
        configGroup: 'validation'
    })),

    enableCustomError: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '自定义错误提示',
        default: false,
        description: '是否启用自定义错误提示信息',
        showInConfig: true,
        configGroup: 'validation'
    })),

    // =============================================================================
    // 医疗相关字段
    // =============================================================================
    medicalType: Type.Optional(Type.Union([
        Type.Literal('general'),
        Type.Literal('symptom'),
        Type.Literal('severity'),
        Type.Literal('frequency'),
        Type.Literal('response'),
        Type.Literal('status')
    ], {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        description: '医疗数据的类型分类',
        enumNames: ['通用', '症状', '严重程度', '频率', '反应', '状态'],
        showInConfig: false,
        configGroup: 'medical'
    })),

    fieldCode: Type.Optional(Type.String({
        code: 'config-input',
        label: '字段编码',
        default: '',
        placeholder: '请输入字段编码',
        description: '用于数据收集和导出的字段编码',
        showInConfig: false,
        configGroup: 'medical'
    }))
})

/**
 * 从 Schema 推导的类型
 */
export type CrfRadioSchema = Static<typeof CrfRadioConfigSchema>

/**
 * 组件配置类型别名
 */
export type CrfRadioConfig = CrfRadioSchema

/**
 * 默认导出
 */
export default CrfRadioConfigSchema