<template>
  <div 
    class="crf-base-container crf-medical-theme"
    :class="{
      'crf-state-disabled': disabled,
      'crf-state-error': validationState.status === 'error'
    }"
    :data-medical-type="props.medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="title-text">{{ props.title || '单选' }}</span>
        <span v-if="props.required" class="required-mark">*</span>
      </div>
      <div v-if="props.description" class="crf-base-description">
        {{ props.description }}
      </div>
    </div>

    <!-- 选项列表 -->
    <div class="crf-base-content" :class="`crf-base-content--${props.direction === 'horizontal' ? 'horizontal' : 'vertical'}`">
      <n-radio-group
        v-model:value="internalValue"
        :size="nativeSize"
        :disabled="shouldDisableInEditMode"
        :name="`radio-group-${Math.random()}`"
        @update:value="handleChange"
      >
        <!-- 可拖拽的选项列表 -->
        <draggable
          v-model="localOptions"
          :disabled="!isInEditorEditMode"
          item-key="value"
          ghost-class="crf-drag-ghost"
          chosen-class="crf-drag-chosen"
          drag-class="crf-drag-active"
          animation="200"
          @start="onDragStart"
          @end="onDragEnd"
          @change="onDragChange"
          class="u-flex u-flex-col u-gap-2"
          :class="{ 'u-flex-row u-flex-wrap': props.direction === 'horizontal' }"
        >
          <template #item="{ element, index }">
            <div
              class="crf-option-wrapper u-transition-colors"
              :class="{ 'draggable': isInEditorEditMode, 'editing': optionEditStates[index] }"
            >
              <!-- 单选框 -->
              <n-radio
                :value="element.value"
                :disabled="element.disabled"
                class="u-flex-1"
              >
                <span v-if="!optionEditStates[index]" class="u-text-sm u-text-primary">
                  {{ element.label }}
                </span>
                <n-input
                  v-else
                  v-model:value="editingOptions[index].label"
                  size="small"
                  class="u-max-w-xs u-ml-2"
                  @blur="saveOptionEdit(index)"
                  @keyup.enter="saveOptionEdit(index)"
                  @keyup.esc="cancelOptionEdit(index)"
                  @click.stop
                />
              </n-radio>
              
              <!-- 编辑操作按钮 -->
              <div v-if="isInEditorEditMode" class="crf-option-actions">
                <button
                  v-if="!optionEditStates[index]"
                  class="crf-option-button u-transition-colors"
                  @click.stop="startOptionEdit(index)"
                  title="编辑选项"
                >
                  <n-icon><PencilOutline /></n-icon>
                </button>
                <button
                  class="crf-option-button u-transition-colors"
                  @click.stop="deleteOption(index)"
                  title="删除选项"
                >
                  <n-icon><TrashOutline /></n-icon>
                </button>
                <button
                  class="crf-option-button u-cursor-move u-transition-colors"
                  title="拖拽排序"
                >
                  <n-icon><SwapVerticalOutline /></n-icon>
                </button>
              </div>
            </div>
          </template>
        </draggable>
      </n-radio-group>
    </div>

    <!-- 编辑模式下的操作按钮 -->
    <div v-if="isInEditorEditMode" class="crf-base-actions">
      <div class="crf-action-buttons">
        <button class="crf-action-button u-transition-colors" @click="addOption">
          <n-icon class="u-mr-1"><AddOutline /></n-icon>
          添加选项
        </button>
      </div>
      <div class="crf-status-indicators">
        <span v-if="props.required" class="crf-status-tag u-bg-brand u-text-white">
          必填
        </span>
      </div>
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject } from 'vue'
import type { CrfRadioProps, RadioOption, RadioValueType } from '@crf/types'
import { NRadio, NRadioGroup, NInput, NIcon } from 'naive-ui'
import { PencilOutline, TrashOutline, AddOutline, SwapVerticalOutline } from '@vicons/ionicons5'
import { EditorMode } from '@crf/types/core'
import draggable from 'vuedraggable'

// 定义组件名称
defineOptions({
  name: 'CrfRadio'
})

const props = withDefaults(defineProps<CrfRadioProps>(), {
  modelValue: '',
  title: '单选',
  description: '',
  options: () => [
    { value: 'option1', label: '选项一' },
    { value: 'option2', label: '选项二' },
    { value: 'option3', label: '选项三' }
  ],
  direction: 'horizontal' as const,
  size: 'medium' as const,
  buttonStyle: false,
  border: false,
  placeholder: '点击选择选项',
  enableCustomError: false,
  medicalType: 'general' as const,
  fieldCode: undefined,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'change': [value: string | number]
  'click': [event: MouseEvent]
  'option-change': [option: RadioOption]
  'validate': [result: { isValid: boolean; errors: string[] }]
  'update-options': [options: RadioOption[]]
}>()

// ===== 验证状态 =====
const validationState = ref({
  status: '',
  message: ''
})

// 验证函数
const validate = async (value: string | number) => {
  const errors: string[] = []
  
  // 必填验证
  if (props.required && (value === undefined || value === null || value === '')) {
    errors.push('请选择一个选项')
  }
  
  // 检查值是否在允许的选项中
  if (value !== undefined && value !== null && value !== '') {
    const validValues = optionsList.value.map(option => option.value)
    if (!validValues.includes(value)) {
      errors.push('选择的值无效')
    }
  }
  
  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0]
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: ''
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: ''
  }
}

// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'medium') {
    return 'medium'
  }
  return props.size
})

// ===== 编辑器状态管理 =====
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const originalValue = ref<string | number>('')

// 计算是否在编辑器编辑模式下且组件被禁用
const isInEditorEditMode = computed(() => {
  const mode = (editStore as any)?.mode
  console.log('编辑器模式检查:', { mode, editStore: !!editStore, isEdit: mode === EditorMode.EDIT })
  return mode === EditorMode.EDIT
})

const shouldDisableInEditMode = computed(() => {
  const editorMode = (editStore as any)?.mode?.value ?? (editStore as any)?.mode
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => (editStore as any)?.mode?.value ?? (editStore as any)?.mode,
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = '' // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)

const isComponentEditable = computed(() => {
  if ((editStore as any)?.mode === 'preview') return true
  if ((editStore as any)?.mode === 'publish') return true
  if ((editStore as any)?.mode === 'edit') return false
  return true
})

// ===== 选项处理 =====
// 本地选项数组，用于拖拽
const localOptions = ref<RadioOption[]>([])

// 同步props.options到localOptions
watch(() => props.options, (newOptions) => {
  if (newOptions && Array.isArray(newOptions)) {
    localOptions.value = [...newOptions]
    console.log('选项列表同步:', localOptions.value)
  }
}, { immediate: true, deep: true })

// 选项列表计算属性（保持向后兼容）
const optionsList = computed(() => {
  return localOptions.value
})

// ===== 选项编辑状态管理 =====
const optionEditStates = ref<boolean[]>([])
const editingOptions = ref<RadioOption[]>([])

// 初始化编辑状态
const initEditStates = () => {
  optionEditStates.value = new Array(localOptions.value.length).fill(false)
  editingOptions.value = localOptions.value.map(option => ({ ...option }))
}

// 监听选项变化，重新初始化编辑状态
watch(localOptions, () => {
  initEditStates()
}, { immediate: true })

// 开始编辑选项
const startOptionEdit = (index: number) => {
  console.log('开始编辑选项:', index)
  optionEditStates.value[index] = true
  editingOptions.value[index] = { ...localOptions.value[index] }
  
  // 确保输入框获得焦点
  setTimeout(() => {
    const inputElement = document.querySelector(`.option-input input`) as HTMLInputElement
    if (inputElement) {
      inputElement.focus()
      inputElement.select()
    }
  }, 0)
}

// 保存选项编辑
const saveOptionEdit = (index: number) => {
  console.log('保存选项编辑:', index, editingOptions.value[index])
  
  // 验证编辑的选项是否有效
  if (!editingOptions.value[index].label || !editingOptions.value[index].label.trim()) {
    console.warn('选项标签不能为空')
    return
  }
  
  // 更新本地选项
  localOptions.value[index] = { ...editingOptions.value[index] }
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  // 结束编辑状态
  optionEditStates.value[index] = false
  
  console.log('选项更新完成:', localOptions.value)
}

// 取消选项编辑
const cancelOptionEdit = (index: number) => {
  editingOptions.value[index] = { ...localOptions.value[index] }
  optionEditStates.value[index] = false
}

// 删除选项
const deleteOption = (index: number) => {
  console.log('删除选项:', index)
  
  // 至少保留一个选项
  if (localOptions.value.length <= 1) {
    console.warn('至少需要保留一个选项')
    return
  }
  
  // 从本地选项中删除
  localOptions.value.splice(index, 1)
  
  // 重新初始化编辑状态
  initEditStates()
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  console.log('选项删除完成:', localOptions.value)
}

// ===== 拖拽功能 =====
const onDragStart = (event: DragEvent) => {
  console.log('开始拖拽:', event)
}

const onDragEnd = (event: DragEvent) => {
  console.log('拖拽结束:', event)
  // 触发选项更新事件
  emit('update-options', localOptions.value)
}

const onDragChange = (event: Record<string, unknown>) => {
  console.log('拖拽变化:', event)
  // 实时更新选项
  emit('update-options', localOptions.value)
}

// 内部值管理
const internalValue = ref<string | number>('')

// 监听 props.modelValue 的变化来更新内部状态
watch(() => props.modelValue, (newValue) => {
  if (newValue !== internalValue.value) {
    internalValue.value = newValue
  }
}, { immediate: true })

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue) => {
  if (isComponentEditable.value) {
    emit('update:modelValue', newValue)
  }
})



// ===== 编辑功能 =====
const startEdit = () => {
  if (props.disabled || props.readonly || !isComponentEditable.value) return
  
  editing.value = true
  originalValue.value = internalValue.value
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  
  if (validationResult.isValid) {
    editing.value = false
  } else {
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }
  
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
  editing.value = false
  clearValidation()
}

// ===== 编辑模式操作按钮处理 =====
const addOption = () => {
  console.log('添加新选项')
  
  // 生成新的选项数据
  const newOptionValue = `option${localOptions.value.length + 1}`
  const newOption = {
    value: newOptionValue,
    label: `选项${localOptions.value.length + 1}`
  }
  
  // 添加到本地选项
  localOptions.value.push(newOption)
  
  // 重新初始化编辑状态
  initEditStates()
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  console.log('新选项添加完成:', localOptions.value)
  
  // 等待DOM更新后，立即进入编辑状态
  setTimeout(() => {
    const newIndex = localOptions.value.length - 1
    startOptionEdit(newIndex)
  }, 100)
}

// ===== 事件处理 =====
const handleChange = (val: RadioValueType) => {
  // el-radio-group 的 @change 事件参数是 RadioValueType, 
  // 但我们组件的 v-model 是 string | number，所以需要过滤掉 boolean/undefined/null 类型。
  const newModelValue = (typeof val === 'string' || typeof val === 'number') ? val : ''
  
  internalValue.value = newModelValue
  emit('update:modelValue', newModelValue)
  emit('change', newModelValue)
  validate(newModelValue)

  // 找到对应的选项并触发选项变化事件
  const selectedOption = optionsList.value.find(option => 
    option.value === newModelValue
  )
  if (selectedOption) {
    emit('option-change', selectedOption)
  }
}



// ===== 监听器 =====
watch(() => props.modelValue, async (newValue) => {
  if (newValue !== undefined) {
    await validate(newValue)
  }
})

// ===== 对外暴露的API =====
defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation
})
</script>

<style lang="scss" scoped>
/* 导入统一样式系统 */
@use '../shared/index.scss';

/* ===== 组件特定样式 ===== */
/* 单选框组件的特殊样式覆盖 */
:deep(.n-radio) {
  margin: 0;
  display: flex;
  align-items: center;
  min-height: var(--min-height-sm);
  
  .n-radio__label {
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    line-height: var(--line-height-normal);
  }
  
  .n-radio__dot {
    border-color: var(--color-border-primary);
  }
  
  &:hover .n-radio__dot {
    border-color: var(--color-primary);
  }
  
  &.n-radio--checked .n-radio__dot {
    border-color: var(--color-primary);
    background-color: var(--color-primary);
  }
  
  &.n-radio--disabled {
    opacity: 0.6;
    
    .n-radio__label {
      color: var(--color-text-disabled);
    }
  }
}

/* 单选框组样式 */
:deep(.n-radio-group) {
  width: 100%;
  
  &.n-radio-group--vertical {
    .n-radio {
      margin-bottom: var(--spacing-2);
    }
  }
  
  &.n-radio-group--horizontal {
    .n-radio {
      margin-right: var(--spacing-4);
      margin-bottom: var(--spacing-2);
    }
  }
}

/* 选项描述样式 */
.option-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  margin-left: var(--spacing-1);
  line-height: var(--line-height-relaxed);
}

/* ===== 选项编辑和拖拽样式 ===== */
.crf-option-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-duration-fast) ease;
  margin-bottom: var(--spacing-2);
  
  &.draggable {
    cursor: move;
    border: 1px solid transparent;
    background-color: var(--color-bg-secondary);
    
    &:hover {
      background-color: var(--color-bg-hover);
      border: 1px dashed var(--color-primary);
    }
  }
  
  &.editing {
    background-color: var(--color-bg-hover);
    border: 1px solid var(--color-primary);
  }
}

/* 拖拽状态样式 */
:deep(.crf-drag-ghost) {
  opacity: 0.5;
  background-color: var(--color-bg-hover) !important;
  border: 2px dashed var(--color-primary) !important;
}

:deep(.crf-drag-chosen) {
  background-color: var(--color-warning-light) !important;
  border: 2px solid var(--color-warning) !important;
}

:deep(.crf-drag-active) {
  opacity: 0.8;
  transform: rotate(2deg);
}

.crf-option-actions {
  display: flex;
  gap: var(--spacing-1);
  opacity: 0;
  transition: opacity var(--transition-duration-fast) ease;
  margin-left: auto;
}

.crf-option-wrapper:hover .crf-option-actions {
  opacity: 1;
}

.crf-option-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-1);
  border: none;
  background: transparent;
  color: var(--color-text-tertiary);
  border-radius: var(--border-radius-xs);
  cursor: pointer;
  min-width: unset;
  height: 24px;
  width: 24px;
  
  &:hover {
    color: var(--color-primary);
    background-color: var(--color-bg-hover);
  }
  
  &.u-cursor-move {
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
  }
}

/* 横向和纵向布局样式 */
.crf-base-content--horizontal {
  .crf-option-wrapper {
    margin-right: var(--spacing-4);
    margin-bottom: var(--spacing-2);
    display: inline-flex;
  }
}

.crf-base-content--vertical {
  .crf-option-wrapper {
    width: 100%;
    display: flex;
  }
}
</style>