import Radio from './radio.vue'
import { withInstallByCode } from '@crf/utils'

// 主要导出（新的命名规范）
export const CrfRadio = withInstallByCode(Radio, 'crf-radio')

// 默认导出
export default CrfRadio

// 导出组件和Schema
export { default as CrfRadioComponent } from './radio.vue'

export { default as CrfRadioSchema } from './schema'
export type { CrfRadioSchema as CrfRadioSchemaType } from './schema'

// 导出类型
export type { CrfRadioProps, CrfRadioConfig, RadioOption } from '@crf/types'