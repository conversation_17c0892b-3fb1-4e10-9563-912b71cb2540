{"extends": "@crf/tsconfig/package-build.json", "compilerOptions": {"baseUrl": ".", "rootDir": ".", "outDir": "dist", "declaration": true, "lib": ["dom", "dom.iterable", "esnext"], "jsx": "preserve", "skipLibCheck": true}, "references": [{"path": "../hooks"}, {"path": "../types"}, {"path": "../utils"}], "include": ["*.ts", "*.vue", "*/**/*.ts", "*/**/*.vue"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts", "**/node_modules/**"]}