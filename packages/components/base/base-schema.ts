import { type Static, Type } from '@sinclair/typebox'

/**
 * 公共基础字段 - 标题
 */
export const baseTitleField = Type.String({
    code: 'config-input',
    label: '标题',
    default: '',
    placeholder: '请输入组件标题',
    description: '组件显示的标题文本',
    minLength: 1,
    maxLength: 100,
    showInConfig: true,
    configGroup: 'basic',
    validation: {
        required: true,
        message: '标题不能为空'
    }
})

/**
 * 公共基础字段 - 描述
 */
export const baseDescriptionField = Type.String({
    code: 'config-textarea',
    label: '描述信息',
    default: '',
    placeholder: '请输入组件描述信息',
    description: '组件的详细描述或说明文本',
    minLength: 0,
    maxLength: 500,
    showInConfig: true,
    configGroup: 'basic'
})

/**
 * 公共基础字段 - 必填标识
 */
export const baseRequiredField = Type.Boolean({
    code: 'config-switch',
    label: '必填项',
    default: false,
    description: '标识该字段是否为必填项',
    showInConfig: true,
    configGroup: 'basic'
})

/**
 * 公共基础字段 - 禁用状态
 */
export const baseDisabledField = Type.Boolean({
    code: 'config-switch',
    label: '禁用状态',
    default: false,
    description: '组件是否处于禁用状态',
    showInConfig: true,
    configGroup: 'state'
})

/**
 * 公共基础字段 - 只读状态
 */
export const baseReadonlyField = Type.Boolean({
    code: 'config-switch',
    label: '只读状态',
    default: false,
    description: '组件是否处于只读状态',
    showInConfig: true,
    configGroup: 'state'
})

/**
 * 公共基础字段 - 显示状态
 */
export const baseVisibleField = Type.Boolean({
    code: 'config-switch',
    label: '显示状态',
    default: true,
    description: '组件是否显示',
    showInConfig: true,
    configGroup: 'state'
})

/**
 * 公共基础字段 - 帮助文本
 */
export const baseHelpTextField = Type.String({
    code: 'config-textarea',
    label: '帮助文本',
    default: '',
    description: '组件的帮助说明文本',
    placeholder: '请输入帮助文本',
    showInConfig: true,
    configGroup: 'medical'
})

/**
 * 医疗专业字段 - 编码系统
 */
export const medicalCodingSystemField = Type.String({
    code: 'config-select',
    label: '编码系统',
    default: 'custom',
    description: '医疗数据编码系统',
    enum: ['icd10', 'icd11', 'snomed', 'loinc', 'custom'],
    enumNames: ['ICD-10', 'ICD-11', 'SNOMED CT', 'LOINC', '自定义'],
    showInConfig: true,
    configGroup: 'medical'
})

/**
 * 医疗专业字段 - 医疗类型
 */
export const medicalTypeField = Type.String({
    code: 'config-select',
    label: '医疗类型',
    default: 'general',
    description: '医疗数据类型分类',
    enum: ['diagnosis', 'medication', 'procedure', 'laboratory', 'vital', 'general'],
    enumNames: ['诊断', '用药', '手术', '检验', '生命体征', '通用'],
    showInConfig: true,
    configGroup: 'medical'
})

/**
 * 医疗专业字段 - 单位
 */
export const medicalUnitField = Type.String({
    code: 'config-input',
    label: '数据单位',
    default: '',
    description: '数据的测量单位',
    placeholder: '如：mg/L, mmHg, 次/分等',
    showInConfig: true,
    configGroup: 'medical'
})

/**
 * 基础组件Schema对象
 */
export const baseComponentSchema = Type.Object({
    title: baseTitleField,
    description: baseDescriptionField,
    required: baseRequiredField,
    disabled: baseDisabledField,
    readonly: baseReadonlyField,
    visible: baseVisibleField,
    helpText: baseHelpTextField
})

/**
 * 基础组件Schema类型
 */
export type BaseComponentSchema = Static<typeof baseComponentSchema>

/**
 * 创建继承基础Schema的工具函数
 * @param additionalProperties 组件特有的属性定义
 * @param defaultTitle 默认标题
 * @returns 完整的组件Schema
 */
export const createComponentSchema = <T extends Record<string, any>>(
    additionalProperties: T,
    defaultTitle: string = '组件'
) => {
    // 覆盖默认标题
    const titleWithDefault = Type.String({
        ...baseTitleField,
        default: defaultTitle
    })

    return Type.Object({
        title: titleWithDefault,
        description: baseDescriptionField,
        required: baseRequiredField,
        disabled: baseDisabledField,
        readonly: baseReadonlyField,
        visible: baseVisibleField,
        helpText: baseHelpTextField,
        ...additionalProperties
    })
}

/**
 * 配置分组定义
 */
export const CONFIG_GROUPS = {
    basic: {
        label: '基础配置',
        order: 1,
        icon: 'material-symbols:settings'
    },
    state: {
        label: '状态配置',
        order: 2,
        icon: 'material-symbols:toggle-on'
    },
    medical: {
        label: '医疗配置',
        order: 3,
        icon: 'material-symbols:medical-services'
    },
    advanced: {
        label: '高级配置',
        order: 4,
        icon: 'material-symbols:tune'
    }
} as const