import { ref, computed, watch, type Ref } from 'vue'
import { useValidation } from '@crf/hooks'
import type { ValidationRule } from '@crf/types'

/**
 * 通用表单配置组件的逻辑复用
 * 解决配置组件重复代码问题
 */
export interface FormConfigData {
  [key: string]: unknown
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  maxLength?: number
  minLength?: number
  showWordLimit?: boolean
  rows?: number
  autosize?: boolean | { minRows?: number; maxRows?: number }
  required?: boolean
  title?: string
  description?: string
  helpText?: string
}

export interface UseFormConfigOptions {
  /**
   * 验证规则
   */
  validationRules?: any[]
  
  /**
   * 验证触发时机
   */
  validationTrigger?: 'change' | 'blur' | 'input' | 'submit'
  
  /**
   * 防抖延迟
   */
  debounceDelay?: number
  
  /**
   * 自定义值转换器
   */
  valueTransformer?: (value: unknown) => unknown
  
  /**
   * 自定义验证器
   */
  customValidator?: (value: unknown) => Promise<boolean | string>
}

export function useFormConfig(
  data: Ref<FormConfigData>,
  options: UseFormConfigOptions = {}
) {
  const {
    validationRules = [],
    validationTrigger = 'change',
    debounceDelay = 300,
    valueTransformer,
    customValidator
  } = options

  // 内部值状态
  const inputValue = ref(data.value)
  const isComposing = ref(false)
  const isDirty = ref(false)

  // 验证Hook
  const {
    validationState,
    validate,
    clearValidation
  } = useValidation({
    fieldId: 'form-config',
    rules: validationRules as ValidationRule[],
    required: data.value.required || false,
    trigger: validationTrigger
  })

  // 计算属性
  const hasError = computed(() => validationState.value.status === 'error')
  const hasWarning = computed(() => validationState.value.status === 'warning')
  const isValid = computed(() => validationState.value.status === 'success')

  // 基础事件处理器
  const handleInput = async (value: unknown) => {
    if (isComposing.value) return

    // 应用值转换器
    const transformedValue = valueTransformer ? valueTransformer(value) : value
    inputValue.value = transformedValue as any
    isDirty.value = true

    // 实时验证
    if (validationTrigger === 'input') {
      await performValidation(transformedValue)
    }

    // 更新原始数据
    updateData('input', transformedValue)
  }

  const handleChange = async (value: unknown) => {
    const transformedValue = valueTransformer ? valueTransformer(value) : value
    inputValue.value = transformedValue as any

    // 变更验证
    if (validationTrigger === 'change') {
      await performValidation(transformedValue)
    }

    updateData('change', transformedValue)
  }

  const handleBlur = async (event: FocusEvent) => {
    isComposing.value = false

    // 失焦验证
    if (validationTrigger === 'blur') {
      await performValidation(inputValue.value)
    }

    updateData('blur', inputValue.value)
  }

  const handleFocus = (event: FocusEvent) => {
    // 清除之前的验证错误（可选）
    if (isDirty.value) {
      clearValidation()
    }

    updateData('focus', inputValue.value)
  }

  const handleCompositionStart = () => {
    isComposing.value = true
  }

  const handleCompositionEnd = (event: CompositionEvent) => {
    isComposing.value = false
    handleInput((event.target as HTMLInputElement).value)
  }

  // 防抖验证
  let validateTimer: NodeJS.Timeout | null = null
  const performValidation = async (value: unknown) => {
    if (validateTimer) {
      clearTimeout(validateTimer)
    }

    validateTimer = setTimeout(async () => {
      // 内置验证
      await validate(value)

      // 自定义验证
      if (customValidator) {
        try {
          const result = await customValidator(value)
          if (typeof result === 'string') {
            // 自定义错误消息
            validationState.value = {
              status: 'error',
              message: result,
              errors: [{ field: '', message: result }]
            }
          }
        } catch (error) {
          validationState.value = {
            status: 'error',
            message: error instanceof Error ? error.message : '验证失败',
            errors: [{ field: '', message: error instanceof Error ? error.message : '验证失败' }]
          }
        }
      }
    }, debounceDelay)
  }

  // 更新数据的抽象方法
  const updateData = (trigger: string, value: unknown) => {
    // 这里可以触发父组件的更新事件
    // 具体实现根据使用场景决定
  }

  // 重置状态
  const reset = () => {
    inputValue.value = data.value
    isDirty.value = false
    clearValidation()
  }

  // 监听外部数据变化
  watch(
    () => data.value,
    (newData) => {
      if (!isDirty.value) {
        inputValue.value = newData
      }
    },
    { deep: true }
  )

  // 监听内部值变化
  watch(
    inputValue,
    (newValue) => {
      if (typeof newValue === 'object' && newValue !== null) {
        Object.assign(data.value, newValue)
      } else {
        data.value = { ...data.value, value: newValue }
      }
    },
    { deep: true }
  )

  return {
    // 状态
    inputValue,
    isComposing,
    isDirty,
    
    // 验证状态
    validationState,
    hasError,
    hasWarning,
    isValid,
    
    // 事件处理器
    handleInput,
    handleChange,
    handleBlur,
    handleFocus,
    handleCompositionStart,
    handleCompositionEnd,
    
    // 方法
    validate: performValidation,
    clearValidation,
    reset
  }
}

/**
 * 针对不同组件类型的特化Hook
 */

// 输入框配置Hook
export function useInputConfig(
  data: Ref<FormConfigData>,
  options: UseFormConfigOptions = {}
) {
  const baseConfig = useFormConfig(data, {
    ...options,
    valueTransformer: options.valueTransformer || ((value) => {
      // 输入框特有的值处理
      if (typeof value === 'string') {
        // 移除首尾空格（可选）
        return value.trim()
      }
      return value
    })
  })

  // 输入框特有的计算属性
  const componentProps = computed(() => ({
    placeholder: data.value.placeholder || '请输入内容',
    disabled: data.value.disabled || false,
    readonly: data.value.readonly || false,
    maxlength: data.value.maxLength,
    'show-word-limit': data.value.showWordLimit || false,
    clearable: true
  }))

  return {
    ...baseConfig,
    componentProps
  }
}

// 文本域配置Hook
export function useTextareaConfig(
  data: Ref<FormConfigData>,
  options: UseFormConfigOptions = {}
) {
  const baseConfig = useFormConfig(data, options)

  // 文本域特有的计算属性
  const componentProps = computed(() => ({
    placeholder: data.value.placeholder || '请输入内容',
    disabled: data.value.disabled || false,
    readonly: data.value.readonly || false,
    maxlength: data.value.maxLength,
    'show-word-limit': data.value.showWordLimit || false,
    rows: data.value.rows || 3,
    autosize: data.value.autosize || false,
    resize: 'vertical'
  }))

  return {
    ...baseConfig,
    componentProps
  }
}

// 选择器配置Hook
export function useSelectConfig(
  data: Ref<FormConfigData>,
  options: UseFormConfigOptions = {}
) {
  const baseConfig = useFormConfig(data, options)

  // 选择器特有的计算属性
  const componentProps = computed(() => ({
    placeholder: data.value.placeholder || '请选择',
    disabled: data.value.disabled || false,
    clearable: true,
    filterable: true,
    multiple: data.value.multiple || false
  }))

  return {
    ...baseConfig,
    componentProps
  }
}

// 数字输入框配置Hook
export function useNumberConfig(
  data: Ref<FormConfigData>,
  options: UseFormConfigOptions = {}
) {
  const baseConfig = useFormConfig(data, {
    ...options,
    valueTransformer: options.valueTransformer || ((value) => {
      // 数字输入框特有的值处理
      const num = Number(value)
      return isNaN(num) ? null : num
    })
  })

  const componentProps = computed(() => ({
    placeholder: data.value.placeholder || '请输入数字',
    disabled: data.value.disabled || false,
    min: data.value.min,
    max: data.value.max,
    step: data.value.step || 1,
    precision: data.value.precision,
    'controls-position': 'right'
  }))

  return {
    ...baseConfig,
    componentProps
  }
}