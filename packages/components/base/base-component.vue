<template>
  <div
    v-show="visible"
    :class="baseClasses"
    :style="baseStyles"
  >
    <!-- 头部区域 -->
    <div v-if="showHeader" class="crf-base__header">
      <slot name="header">
        <!-- 必填标识 -->
        <span v-if="required" title="此字段为必填项" class="crf-base__required">*</span>

        <!-- 标题区域 -->
        <div class="crf-base__title">
          <slot name="title">
            {{ title }}
          </slot>
        </div>
      </slot>
    </div>

    <!-- 描述区域 -->
    <div v-if="showDescription" class="crf-base__description">
      <slot name="description">
        {{ description }}
      </slot>
    </div>

    <!-- 主要内容区域 -->
    <div class="crf-base__content">
      <slot />
    </div>

    <!-- 帮助文本区域 -->
    <div v-if="showHelp" class="crf-base__help">
      <slot name="help">
        {{ helpText }}
      </slot>
    </div>

    <!-- 错误信息区域 -->
    <div v-if="showError" class="crf-base__error">
      <slot name="error">
        {{ errorMessage }}
      </slot>
    </div>

    <!-- 底部区域 -->
    <div v-if="showFooter" class="crf-base__footer">
      <div class="crf-base__footer-left">
        <slot name="footer-left" />
      </div>
      <div class="crf-base__footer-right">
        <slot name="footer-right" />
        <slot name="extra" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, useSlots } from 'vue'
import { useNamespace } from '@crf/hooks'
import { isNumber, isString } from '@crf/utils'
import type { BaseComponentProps } from './types'

defineOptions({
  name: 'CrfBaseComponent'
})

// Props定义
const props = withDefaults(defineProps<BaseComponentProps>(), {
  title: '',
  description: '',
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  errorMessage: '',
  helpText: ''
})

// 命名空间
const ns = useNamespace('base')

// 获取插槽
const slots = useSlots()

// 计算属性：CSS类名
const baseClasses = computed(() => {
  const classes = [ns.b()]

  // 状态变体
  if (props.disabled) {
    classes.push(ns.m('disabled'))
  }

  if (props.readonly) {
    classes.push(ns.m('readonly'))
  }

  if (props.validateStatus) {
    classes.push(ns.m(props.validateStatus))
  }

  return classes
})

// 计算属性：内联样式
const baseStyles = computed(() => {
  const styles: Record<string, any> = {}
  
  if (isString(props.width)) {
    styles.width = props.width
  } else if (isNumber(props.width)) {
    styles.width = `${props.width}px`
  }
  
  if (isString(props.height)) {
    styles.height = props.height
  } else if (isNumber(props.height)) {
    styles.height = `${props.height}px`
  }
  
  return styles
})

// 计算属性：是否显示头部
const showHeader = computed(() => {
  return props.title || props.required || slots.header || slots.title
})

// 计算属性：是否显示描述
const showDescription = computed(() => {
  return props.description || slots.description
})

// 计算属性：是否显示帮助文本
const showHelp = computed(() => {
  return props.helpText || slots.help
})

// 计算属性：是否显示错误信息
const showError = computed(() => {
  return (props.validateStatus === 'error' && props.errorMessage) || slots.error
})

// 计算属性：是否显示底部
const showFooter = computed(() => {
  return slots['footer-left'] || slots['footer-right'] || slots.extra
})

// 公开的方法和属性
defineExpose({
  // 状态属性
  disabled: computed(() => props.disabled),
  readonly: computed(() => props.readonly),
  required: computed(() => props.required),

  // 验证相关
  validateStatus: computed(() => props.validateStatus),
  errorMessage: computed(() => props.errorMessage),

  // DOM引用
  element: computed(() => document.querySelector(`.${ns.b()}`)),

  // 工具方法
  focus: () => {
    const element = document.querySelector(`.${ns.b()}`)
    if (element && element instanceof HTMLElement) {
      element.focus()
    }
  },

  blur: () => {
    const element = document.querySelector(`.${ns.b()}`)
    if (element && element instanceof HTMLElement) {
      element.blur()
    }
  }
})
</script>

<script lang="ts">
// 组件类型导出
export type { BaseComponentProps } from './types'
</script>

<style lang="scss">
// 导入基础样式
@use './styles/base-component.scss';
</style>