import { withInstallByCode } from '@crf/utils'
import BaseComponent from './base-component.vue'

// 导出类型
export type {
    BaseComponentProps,
    BaseComponentSlots,
    BaseComponentEmits,
    BaseComponentConfig
} from './types'

// 导出 Schema
export * from './base-schema'

// 主要导出（标准的 Crf 前缀）
export const CrfBaseComponent = withInstallByCode(BaseComponent, 'crf-base-component')

// 默认导出
export default CrfBaseComponent

// 组件实例类型
export type BaseComponentInstance = InstanceType<typeof BaseComponent>

// 单独导出原始组件
export { BaseComponent } 