import type { VNode } from 'vue'
import type { ComponentStyle } from '@crf/types/base'

/**
 * 基础组件公共Props接口 - 使用泛型约束替代any
 */
export interface BaseComponentProps<T = unknown> {
    // 模型值
    modelValue?: T

    // 基础信息
    title?: string
    description?: string
    required?: boolean

    // 状态控制
    disabled?: boolean
    readonly?: boolean
    visible?: boolean

    // 尺寸控制
    width?: string | number
    height?: string | number
    size?: 'small' | 'medium' | 'large'

    // 样式控制
    theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
    className?: string
    style?: ComponentStyle

    // 表单相关
    validateStatus?: 'success' | 'warning' | 'error' | 'validating'
    errorMessage?: string
    helpText?: string

    // 医疗专业配置
    medicalType?: string
    codingSystem?: string
    unit?: string

    // 标识
    id?: string
    name?: string
}

/**
 * 基础组件插槽类型
 */
export interface BaseComponentSlots {
    // 头部插槽
    header?: () => VNode[]
    title?: () => VNode[]

    // 主要内容插槽
    default?: () => VNode[]

    // 描述插槽
    description?: () => VNode[]

    // 底部插槽
    footer?: () => VNode[]

    // 额外插槽
    extra?: () => VNode[]
}

/**
 * 基础组件事件类型 - 使用泛型约束替代any
 */
export interface BaseComponentEmits<T = unknown> {
    // 值变化事件
    'update:modelValue': [value: T]
    change: [value: T]

    // 基础事件
    click: [event: MouseEvent]
    focus: [event: FocusEvent]
    blur: [event: FocusEvent]

    // 状态变化事件
    'update:required': [required: boolean]
    'update:disabled': [disabled: boolean]
    'update:visible': [visible: boolean]
}

/**
 * 基础组件配置类型
 */
export interface BaseComponentConfig {
    // 显示配置
    showTitle?: boolean
    showDescription?: boolean
    showRequired?: boolean

    // 交互配置
    allowEdit?: boolean
    allowClear?: boolean

    // 验证配置
    validateOnChange?: boolean
    validateOnBlur?: boolean
}