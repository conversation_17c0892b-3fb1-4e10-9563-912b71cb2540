/**
 * 统一颜色系统
 * 用于替换项目中所有硬编码颜色值
 * 基于设计系统和 UnoCSS 兼容的颜色规范
 */

:root {
  /* ===== 主色调系统 ===== */
  --color-primary: #667eea;
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #667eea;  /* 主色调 */
  --color-primary-600: #5a67d8;
  --color-primary-700: #4c51bf;
  --color-primary-800: #434190;
  --color-primary-900: #3730a3;
  --color-primary-950: #1e1b4b;
  
  /* 主色调状态变体 */
  --color-primary-hover: #5a67d8;
  --color-primary-pressed: #4c51bf;
  --color-primary-disabled: #a5b4fc;
  
  /* ===== 功能色系统 ===== */
  /* 成功色 */
  --color-success: #10b981;
  --color-success-50: #ecfdf5;
  --color-success-100: #d1fae5;
  --color-success-200: #a7f3d0;
  --color-success-300: #6ee7b7;
  --color-success-400: #34d399;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  --color-success-800: #065f46;
  --color-success-900: #064e3b;
  
  /* 警告色 */
  --color-warning: #f59e0b;
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  
  /* 错误色 */
  --color-error: #ef4444;
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  
  /* 信息色 */
  --color-info: #3b82f6;
  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;
  
  /* ===== 中性色系统 ===== */
  /* 文本颜色 */
  --color-text-primary: #1f2937;    /* 主要文本 */
  --color-text-secondary: #6b7280;  /* 次要文本 */
  --color-text-tertiary: #9ca3af;   /* 辅助文本 */
  --color-text-disabled: #d1d5db;   /* 禁用文本 */
  --color-text-placeholder: #9ca3af; /* 占位符文本 */
  
  /* 背景颜色 */
  --color-bg-primary: #ffffff;      /* 主要背景 */
  --color-bg-secondary: #f9fafb;    /* 次要背景 */
  --color-bg-tertiary: #f3f4f6;     /* 第三级背景 */
  --color-bg-quaternary: #e5e7eb;   /* 第四级背景 */
  --color-bg-overlay: rgba(0, 0, 0, 0.5); /* 遮罩背景 */
  
  /* 边框颜色 */
  --color-border-primary: #e5e7eb;   /* 主要边框 */
  --color-border-secondary: #d1d5db; /* 次要边框 */
  --color-border-tertiary: #9ca3af;  /* 第三级边框 */
  --color-border-focus: var(--color-primary); /* 聚焦边框 */
  
  /* 分割线颜色 */
  --color-divider: #e5e7eb;
  --color-divider-light: #f3f4f6;
  
  /* ===== 兼容性映射 ===== */
  /* 兼容旧的变量名 */
  --primary-color: var(--color-primary);
  --success-color: var(--color-success);
  --warning-color: var(--color-warning);
  --error-color: var(--color-error);
  --info-color: var(--color-info);
  
  /* Element UI 兼容 */
  --el-color-primary: var(--color-primary);
  --el-color-success: var(--color-success);
  --el-color-warning: var(--color-warning);
  --el-color-danger: var(--color-error);
  --el-color-info: var(--color-info);
  
  /* Naive UI 兼容 */
  --n-color-primary: var(--color-primary);
  --n-color-primary-hover: var(--color-primary-hover);
  --n-color-primary-pressed: var(--color-primary-pressed);
  
  /* JZ 组件系统兼容 */
  --jz-color-primary: var(--color-primary);
  --jz-color-success: var(--color-success);
  --jz-color-warning: var(--color-warning);
  --jz-color-danger: var(--color-error);
  --jz-color-info: var(--color-info);
  
  /* ===== 特殊用途颜色 ===== */
  /* 医疗主题颜色 */
  --color-medical-symptom: #fff2e6;     /* 症状 */
  --color-medical-comorbidity: #fff1f0; /* 合并症 */
  --color-medical-medication: #f6ffed;  /* 药物 */
  --color-medical-procedure: #e6f7ff;   /* 程序 */
  --color-medical-allergy: #f9f0ff;     /* 过敏 */
  
  /* 拖拽状态颜色 */
  --color-drag-ghost: rgba(227, 242, 253, 0.5);
  --color-drag-chosen: #fff3e0;
  --color-drag-border: #2196f3;
  
  /* 表单状态颜色 */
  --color-form-valid: var(--color-success);
  --color-form-invalid: var(--color-error);
  --color-form-pending: var(--color-warning);
}

/* ===== 深色主题支持 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    /* 文本颜色 - 深色主题 */
    --color-text-primary: #f9fafb;
    --color-text-secondary: #d1d5db;
    --color-text-tertiary: #9ca3af;
    --color-text-disabled: #6b7280;
    
    /* 背景颜色 - 深色主题 */
    --color-bg-primary: #111827;
    --color-bg-secondary: #1f2937;
    --color-bg-tertiary: #374151;
    --color-bg-quaternary: #4b5563;
    
    /* 边框颜色 - 深色主题 */
    --color-border-primary: #374151;
    --color-border-secondary: #4b5563;
    --color-border-tertiary: #6b7280;
  }
}

/* ===== 工具类 ===== */
/* 文本颜色工具类 */
.text-primary { color: var(--color-text-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-tertiary { color: var(--color-text-tertiary) !important; }
.text-disabled { color: var(--color-text-disabled) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }
.text-info { color: var(--color-info) !important; }

/* 背景颜色工具类 */
.bg-primary { background-color: var(--color-bg-primary) !important; }
.bg-secondary { background-color: var(--color-bg-secondary) !important; }
.bg-tertiary { background-color: var(--color-bg-tertiary) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-error { background-color: var(--color-error) !important; }
.bg-info { background-color: var(--color-info) !important; }

/* 边框颜色工具类 */
.border-primary { border-color: var(--color-border-primary) !important; }
.border-secondary { border-color: var(--color-border-secondary) !important; }
.border-success { border-color: var(--color-success) !important; }
.border-warning { border-color: var(--color-warning) !important; }
.border-error { border-color: var(--color-error) !important; }
.border-info { border-color: var(--color-info) !important; }

/* ===== 常用硬编码颜色映射 ===== */
/*
硬编码颜色替换映射表：

#667eea → var(--color-primary)
#5a67d8 → var(--color-primary-hover)
#4c51bf → var(--color-primary-pressed)
#409eff → var(--color-info) 或 var(--color-primary)
#f53f3f → var(--color-error)
#ef4444 → var(--color-error)
#10b981 → var(--color-success)
#f59e0b → var(--color-warning)
#3b82f6 → var(--color-info)

#ffffff → var(--color-bg-primary)
#f9fafb → var(--color-bg-secondary)
#f3f4f6 → var(--color-bg-tertiary)
#e5e7eb → var(--color-border-primary)
#d1d5db → var(--color-border-secondary)

#1f2937 → var(--color-text-primary)
#6b7280 → var(--color-text-secondary)
#9ca3af → var(--color-text-tertiary)
#d1d5db → var(--color-text-disabled)

#e8eaec → var(--color-border-primary)
#f0f2f5 → var(--color-bg-tertiary)
#1f2329 → var(--color-text-primary)
#86909c → var(--color-text-secondary)
#4e5969 → var(--color-text-secondary)
#165dff → var(--color-primary)
#606266 → var(--color-text-secondary)
#c0c4cc → var(--color-text-disabled)
*/