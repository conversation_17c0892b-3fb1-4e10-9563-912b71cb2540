/**
 * 工具类库 (Utility Classes)
 * 提供原子级样式类，减少内联样式使用
 * 基于设计系统的一致性原则
 */

/* 导入统一的颜色和间距系统 */
@use './unified-colors.scss';
@use './unified-spacing.scss';

/* ===== 布局工具类 ===== */
/* Display */
.u-block { display: block !important; }
.u-inline { display: inline !important; }
.u-inline-block { display: inline-block !important; }
.u-flex { display: flex !important; }
.u-inline-flex { display: inline-flex !important; }
.u-grid { display: grid !important; }
.u-hidden { display: none !important; }

/* Flexbox */
.u-flex-row { flex-direction: row !important; }
.u-flex-col { flex-direction: column !important; }
.u-flex-wrap { flex-wrap: wrap !important; }
.u-flex-nowrap { flex-wrap: nowrap !important; }

/* Justify Content */
.u-justify-start { justify-content: flex-start !important; }
.u-justify-end { justify-content: flex-end !important; }
.u-justify-center { justify-content: center !important; }
.u-justify-between { justify-content: space-between !important; }
.u-justify-around { justify-content: space-around !important; }
.u-justify-evenly { justify-content: space-evenly !important; }

/* Align Items */
.u-items-start { align-items: flex-start !important; }
.u-items-end { align-items: flex-end !important; }
.u-items-center { align-items: center !important; }
.u-items-baseline { align-items: baseline !important; }
.u-items-stretch { align-items: stretch !important; }

/* Align Self */
.u-self-auto { align-self: auto !important; }
.u-self-start { align-self: flex-start !important; }
.u-self-end { align-self: flex-end !important; }
.u-self-center { align-self: center !important; }
.u-self-stretch { align-self: stretch !important; }

/* Flex Grow/Shrink */
.u-flex-1 { flex: 1 1 0% !important; }
.u-flex-auto { flex: 1 1 auto !important; }
.u-flex-initial { flex: 0 1 auto !important; }
.u-flex-none { flex: none !important; }
.u-grow { flex-grow: 1 !important; }
.u-grow-0 { flex-grow: 0 !important; }
.u-shrink { flex-shrink: 1 !important; }
.u-shrink-0 { flex-shrink: 0 !important; }

/* Position */
.u-static { position: static !important; }
.u-fixed { position: fixed !important; }
.u-absolute { position: absolute !important; }
.u-relative { position: relative !important; }
.u-sticky { position: sticky !important; }

/* ===== 间距工具类 ===== */
/* Margin */
.u-m-0 { margin: 0 !important; }
.u-m-1 { margin: var(--spacing-1) !important; }
.u-m-2 { margin: var(--spacing-2) !important; }
.u-m-3 { margin: var(--spacing-3) !important; }
.u-m-4 { margin: var(--spacing-4) !important; }
.u-m-5 { margin: var(--spacing-5) !important; }
.u-m-6 { margin: var(--spacing-6) !important; }
.u-m-8 { margin: var(--spacing-8) !important; }
.u-m-auto { margin: auto !important; }

/* Margin X (左右) */
.u-mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.u-mx-1 { margin-left: var(--spacing-1) !important; margin-right: var(--spacing-1) !important; }
.u-mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
.u-mx-3 { margin-left: var(--spacing-3) !important; margin-right: var(--spacing-3) !important; }
.u-mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
.u-mx-auto { margin-left: auto !important; margin-right: auto !important; }

/* Margin Y (上下) */
.u-my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.u-my-1 { margin-top: var(--spacing-1) !important; margin-bottom: var(--spacing-1) !important; }
.u-my-2 { margin-top: var(--spacing-2) !important; margin-bottom: var(--spacing-2) !important; }
.u-my-3 { margin-top: var(--spacing-3) !important; margin-bottom: var(--spacing-3) !important; }
.u-my-4 { margin-top: var(--spacing-4) !important; margin-bottom: var(--spacing-4) !important; }

/* Margin Top */
.u-mt-0 { margin-top: 0 !important; }
.u-mt-1 { margin-top: var(--spacing-1) !important; }
.u-mt-2 { margin-top: var(--spacing-2) !important; }
.u-mt-3 { margin-top: var(--spacing-3) !important; }
.u-mt-4 { margin-top: var(--spacing-4) !important; }
.u-mt-6 { margin-top: var(--spacing-6) !important; }
.u-mt-8 { margin-top: var(--spacing-8) !important; }

/* Margin Right */
.u-mr-0 { margin-right: 0 !important; }
.u-mr-1 { margin-right: var(--spacing-1) !important; }
.u-mr-2 { margin-right: var(--spacing-2) !important; }
.u-mr-3 { margin-right: var(--spacing-3) !important; }
.u-mr-4 { margin-right: var(--spacing-4) !important; }

/* Margin Bottom */
.u-mb-0 { margin-bottom: 0 !important; }
.u-mb-1 { margin-bottom: var(--spacing-1) !important; }
.u-mb-2 { margin-bottom: var(--spacing-2) !important; }
.u-mb-3 { margin-bottom: var(--spacing-3) !important; }
.u-mb-4 { margin-bottom: var(--spacing-4) !important; }
.u-mb-6 { margin-bottom: var(--spacing-6) !important; }
.u-mb-8 { margin-bottom: var(--spacing-8) !important; }

/* Margin Left */
.u-ml-0 { margin-left: 0 !important; }
.u-ml-1 { margin-left: var(--spacing-1) !important; }
.u-ml-2 { margin-left: var(--spacing-2) !important; }
.u-ml-3 { margin-left: var(--spacing-3) !important; }
.u-ml-4 { margin-left: var(--spacing-4) !important; }
.u-ml-auto { margin-left: auto !important; }

/* Padding */
.u-p-0 { padding: 0 !important; }
.u-p-1 { padding: var(--spacing-1) !important; }
.u-p-2 { padding: var(--spacing-2) !important; }
.u-p-3 { padding: var(--spacing-3) !important; }
.u-p-4 { padding: var(--spacing-4) !important; }
.u-p-5 { padding: var(--spacing-5) !important; }
.u-p-6 { padding: var(--spacing-6) !important; }
.u-p-8 { padding: var(--spacing-8) !important; }

/* Padding X (左右) */
.u-px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.u-px-1 { padding-left: var(--spacing-1) !important; padding-right: var(--spacing-1) !important; }
.u-px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.u-px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.u-px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.u-px-6 { padding-left: var(--spacing-6) !important; padding-right: var(--spacing-6) !important; }

/* Padding Y (上下) */
.u-py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.u-py-1 { padding-top: var(--spacing-1) !important; padding-bottom: var(--spacing-1) !important; }
.u-py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.u-py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.u-py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }

/* Padding Top */
.u-pt-0 { padding-top: 0 !important; }
.u-pt-1 { padding-top: var(--spacing-1) !important; }
.u-pt-2 { padding-top: var(--spacing-2) !important; }
.u-pt-3 { padding-top: var(--spacing-3) !important; }
.u-pt-4 { padding-top: var(--spacing-4) !important; }

/* Padding Right */
.u-pr-0 { padding-right: 0 !important; }
.u-pr-1 { padding-right: var(--spacing-1) !important; }
.u-pr-2 { padding-right: var(--spacing-2) !important; }
.u-pr-3 { padding-right: var(--spacing-3) !important; }
.u-pr-4 { padding-right: var(--spacing-4) !important; }

/* Padding Bottom */
.u-pb-0 { padding-bottom: 0 !important; }
.u-pb-1 { padding-bottom: var(--spacing-1) !important; }
.u-pb-2 { padding-bottom: var(--spacing-2) !important; }
.u-pb-3 { padding-bottom: var(--spacing-3) !important; }
.u-pb-4 { padding-bottom: var(--spacing-4) !important; }

/* Padding Left */
.u-pl-0 { padding-left: 0 !important; }
.u-pl-1 { padding-left: var(--spacing-1) !important; }
.u-pl-2 { padding-left: var(--spacing-2) !important; }
.u-pl-3 { padding-left: var(--spacing-3) !important; }
.u-pl-4 { padding-left: var(--spacing-4) !important; }

/* Gap (for flexbox and grid) */
.u-gap-0 { gap: 0 !important; }
.u-gap-1 { gap: var(--spacing-1) !important; }
.u-gap-2 { gap: var(--spacing-2) !important; }
.u-gap-3 { gap: var(--spacing-3) !important; }
.u-gap-4 { gap: var(--spacing-4) !important; }
.u-gap-6 { gap: var(--spacing-6) !important; }
.u-gap-8 { gap: var(--spacing-8) !important; }

/* ===== 尺寸工具类 ===== */
/* Width */
.u-w-auto { width: auto !important; }
.u-w-full { width: 100% !important; }
.u-w-screen { width: 100vw !important; }
.u-w-min { width: min-content !important; }
.u-w-max { width: max-content !important; }
.u-w-fit { width: fit-content !important; }

/* 固定宽度 */
.u-w-0 { width: 0 !important; }
.u-w-1 { width: var(--spacing-1) !important; }
.u-w-2 { width: var(--spacing-2) !important; }
.u-w-3 { width: var(--spacing-3) !important; }
.u-w-4 { width: var(--spacing-4) !important; }
.u-w-5 { width: var(--spacing-5) !important; }
.u-w-6 { width: var(--spacing-6) !important; }
.u-w-8 { width: var(--spacing-8) !important; }
.u-w-10 { width: var(--spacing-10) !important; }
.u-w-12 { width: var(--spacing-12) !important; }
.u-w-16 { width: var(--spacing-16) !important; }
.u-w-20 { width: var(--spacing-20) !important; }
.u-w-24 { width: var(--spacing-24) !important; }
.u-w-32 { width: var(--spacing-32) !important; }
.u-w-40 { width: var(--spacing-40) !important; }
.u-w-48 { width: var(--spacing-48) !important; }
.u-w-56 { width: var(--spacing-56) !important; }
.u-w-64 { width: var(--spacing-64) !important; }

/* 百分比宽度 */
.u-w-1\/2 { width: 50% !important; }
.u-w-1\/3 { width: 33.333333% !important; }
.u-w-2\/3 { width: 66.666667% !important; }
.u-w-1\/4 { width: 25% !important; }
.u-w-2\/4 { width: 50% !important; }
.u-w-3\/4 { width: 75% !important; }
.u-w-1\/5 { width: 20% !important; }
.u-w-2\/5 { width: 40% !important; }
.u-w-3\/5 { width: 60% !important; }
.u-w-4\/5 { width: 80% !important; }

/* Height */
.u-h-auto { height: auto !important; }
.u-h-full { height: 100% !important; }
.u-h-screen { height: 100vh !important; }
.u-h-min { height: min-content !important; }
.u-h-max { height: max-content !important; }
.u-h-fit { height: fit-content !important; }

/* 固定高度 */
.u-h-0 { height: 0 !important; }
.u-h-1 { height: var(--spacing-1) !important; }
.u-h-2 { height: var(--spacing-2) !important; }
.u-h-3 { height: var(--spacing-3) !important; }
.u-h-4 { height: var(--spacing-4) !important; }
.u-h-5 { height: var(--spacing-5) !important; }
.u-h-6 { height: var(--spacing-6) !important; }
.u-h-8 { height: var(--spacing-8) !important; }
.u-h-10 { height: var(--spacing-10) !important; }
.u-h-12 { height: var(--spacing-12) !important; }
.u-h-16 { height: var(--spacing-16) !important; }
.u-h-20 { height: var(--spacing-20) !important; }
.u-h-24 { height: var(--spacing-24) !important; }
.u-h-32 { height: var(--spacing-32) !important; }

/* Min/Max Width */
.u-min-w-0 { min-width: 0 !important; }
.u-min-w-full { min-width: 100% !important; }
.u-min-w-min { min-width: min-content !important; }
.u-min-w-max { min-width: max-content !important; }
.u-min-w-fit { min-width: fit-content !important; }

.u-max-w-none { max-width: none !important; }
.u-max-w-xs { max-width: 320px !important; }
.u-max-w-sm { max-width: 384px !important; }
.u-max-w-md { max-width: 448px !important; }
.u-max-w-lg { max-width: 512px !important; }
.u-max-w-xl { max-width: 576px !important; }
.u-max-w-2xl { max-width: 672px !important; }
.u-max-w-3xl { max-width: 768px !important; }
.u-max-w-4xl { max-width: 896px !important; }
.u-max-w-5xl { max-width: 1024px !important; }
.u-max-w-6xl { max-width: 1152px !important; }
.u-max-w-7xl { max-width: 1280px !important; }
.u-max-w-full { max-width: 100% !important; }

/* Min/Max Height */
.u-min-h-0 { min-height: 0 !important; }
.u-min-h-full { min-height: 100% !important; }
.u-min-h-screen { min-height: 100vh !important; }

.u-max-h-full { max-height: 100% !important; }
.u-max-h-screen { max-height: 100vh !important; }

/* ===== 文本工具类 ===== */
/* Font Size */
.u-text-xs { font-size: var(--font-size-xs) !important; line-height: var(--line-height-tight) !important; }
.u-text-sm { font-size: var(--font-size-sm) !important; line-height: var(--line-height-normal) !important; }
.u-text-base { font-size: var(--font-size-base) !important; line-height: var(--line-height-normal) !important; }
.u-text-lg { font-size: var(--font-size-lg) !important; line-height: var(--line-height-relaxed) !important; }
.u-text-xl { font-size: var(--font-size-xl) !important; line-height: var(--line-height-relaxed) !important; }
.u-text-2xl { font-size: var(--font-size-2xl) !important; line-height: var(--line-height-loose) !important; }
.u-text-3xl { font-size: var(--font-size-3xl) !important; line-height: var(--line-height-loose) !important; }

/* Font Weight */
.u-font-thin { font-weight: var(--font-weight-thin) !important; }
.u-font-light { font-weight: var(--font-weight-light) !important; }
.u-font-normal { font-weight: var(--font-weight-normal) !important; }
.u-font-medium { font-weight: var(--font-weight-medium) !important; }
.u-font-semibold { font-weight: var(--font-weight-semibold) !important; }
.u-font-bold { font-weight: var(--font-weight-bold) !important; }
.u-font-extrabold { font-weight: var(--font-weight-extrabold) !important; }

/* Text Align */
.u-text-left { text-align: left !important; }
.u-text-center { text-align: center !important; }
.u-text-right { text-align: right !important; }
.u-text-justify { text-align: justify !important; }

/* Text Color */
.u-text-primary { color: var(--color-text-primary) !important; }
.u-text-secondary { color: var(--color-text-secondary) !important; }
.u-text-tertiary { color: var(--color-text-tertiary) !important; }
.u-text-disabled { color: var(--color-text-disabled) !important; }
.u-text-inverse { color: var(--color-text-inverse) !important; }
.u-text-brand { color: var(--color-primary) !important; }
.u-text-success { color: var(--color-success) !important; }
.u-text-warning { color: var(--color-warning) !important; }
.u-text-error { color: var(--color-error) !important; }
.u-text-info { color: var(--color-info) !important; }

/* Text Decoration */
.u-underline { text-decoration: underline !important; }
.u-line-through { text-decoration: line-through !important; }
.u-no-underline { text-decoration: none !important; }

/* Text Transform */
.u-uppercase { text-transform: uppercase !important; }
.u-lowercase { text-transform: lowercase !important; }
.u-capitalize { text-transform: capitalize !important; }
.u-normal-case { text-transform: none !important; }

/* Text Overflow */
.u-truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.u-text-ellipsis { text-overflow: ellipsis !important; }
.u-text-clip { text-overflow: clip !important; }

/* White Space */
.u-whitespace-normal { white-space: normal !important; }
.u-whitespace-nowrap { white-space: nowrap !important; }
.u-whitespace-pre { white-space: pre !important; }
.u-whitespace-pre-line { white-space: pre-line !important; }
.u-whitespace-pre-wrap { white-space: pre-wrap !important; }

/* Word Break */
.u-break-normal {
  overflow-wrap: normal !important;
  word-break: normal !important;
}

.u-break-words { overflow-wrap: break-word !important; }
.u-break-all { word-break: break-all !important; }

/* Line Height */
.u-leading-none { line-height: 1 !important; }
.u-leading-tight { line-height: var(--line-height-tight) !important; }
.u-leading-snug { line-height: var(--line-height-snug) !important; }
.u-leading-normal { line-height: var(--line-height-normal) !important; }
.u-leading-relaxed { line-height: var(--line-height-relaxed) !important; }
.u-leading-loose { line-height: var(--line-height-loose) !important; }

/* ===== 背景工具类 ===== */
/* Background Color */
.u-bg-transparent { background-color: transparent !important; }
.u-bg-current { background-color: currentColor !important; }
.u-bg-primary { background-color: var(--color-bg-primary) !important; }
.u-bg-secondary { background-color: var(--color-bg-secondary) !important; }
.u-bg-tertiary { background-color: var(--color-bg-tertiary) !important; }
.u-bg-inverse { background-color: var(--color-bg-inverse) !important; }
.u-bg-brand { background-color: var(--color-primary) !important; }
.u-bg-brand-light { background-color: var(--color-primary-50) !important; }
.u-bg-success { background-color: var(--color-success) !important; }
.u-bg-success-light { background-color: var(--color-success-50) !important; }
.u-bg-warning { background-color: var(--color-warning) !important; }
.u-bg-warning-light { background-color: var(--color-warning-50) !important; }
.u-bg-error { background-color: var(--color-error) !important; }
.u-bg-error-light { background-color: var(--color-error-50) !important; }
.u-bg-info { background-color: var(--color-info) !important; }
.u-bg-info-light { background-color: var(--color-info-50) !important; }

/* ===== 边框工具类 ===== */
/* Border Width */
.u-border-0 { border-width: 0 !important; }
.u-border { border-width: 1px !important; }
.u-border-2 { border-width: 2px !important; }
.u-border-4 { border-width: 4px !important; }
.u-border-8 { border-width: 8px !important; }

/* Border Style */
.u-border-solid { border-style: solid !important; }
.u-border-dashed { border-style: dashed !important; }
.u-border-dotted { border-style: dotted !important; }
.u-border-double { border-style: double !important; }
.u-border-none { border-style: none !important; }

/* Border Color */
.u-border-transparent { border-color: transparent !important; }
.u-border-current { border-color: currentColor !important; }
.u-border-primary { border-color: var(--color-border-primary) !important; }
.u-border-secondary { border-color: var(--color-border-secondary) !important; }
.u-border-focus { border-color: var(--color-border-focus) !important; }
.u-border-brand { border-color: var(--color-primary) !important; }
.u-border-success { border-color: var(--color-success) !important; }
.u-border-warning { border-color: var(--color-warning) !important; }
.u-border-error { border-color: var(--color-error) !important; }
.u-border-info { border-color: var(--color-info) !important; }

/* Border Radius */
.u-rounded-none { border-radius: 0 !important; }
.u-rounded-sm { border-radius: var(--radius-sm) !important; }
.u-rounded { border-radius: var(--radius-base) !important; }
.u-rounded-md { border-radius: var(--radius-md) !important; }
.u-rounded-lg { border-radius: var(--radius-lg) !important; }
.u-rounded-xl { border-radius: var(--radius-xl) !important; }
.u-rounded-2xl { border-radius: var(--radius-2xl) !important; }
.u-rounded-3xl { border-radius: var(--radius-3xl) !important; }
.u-rounded-full { border-radius: 9999px !important; }

/* ===== 阴影工具类 ===== */
.u-shadow-none { box-shadow: none !important; }
.u-shadow-sm { box-shadow: var(--shadow-sm) !important; }
.u-shadow { box-shadow: var(--shadow-base) !important; }
.u-shadow-md { box-shadow: var(--shadow-md) !important; }
.u-shadow-lg { box-shadow: var(--shadow-lg) !important; }
.u-shadow-xl { box-shadow: var(--shadow-xl) !important; }
.u-shadow-2xl { box-shadow: var(--shadow-2xl) !important; }
.u-shadow-inner { box-shadow: var(--shadow-inner) !important; }

/* ===== 透明度工具类 ===== */
.u-opacity-0 { opacity: 0 !important; }
.u-opacity-5 { opacity: 0.05 !important; }
.u-opacity-10 { opacity: 0.1 !important; }
.u-opacity-20 { opacity: 0.2 !important; }
.u-opacity-25 { opacity: 0.25 !important; }
.u-opacity-30 { opacity: 0.3 !important; }
.u-opacity-40 { opacity: 0.4 !important; }
.u-opacity-50 { opacity: 0.5 !important; }
.u-opacity-60 { opacity: 0.6 !important; }
.u-opacity-70 { opacity: 0.7 !important; }
.u-opacity-75 { opacity: 0.75 !important; }
.u-opacity-80 { opacity: 0.8 !important; }
.u-opacity-90 { opacity: 0.9 !important; }
.u-opacity-95 { opacity: 0.95 !important; }
.u-opacity-100 { opacity: 1 !important; }

/* ===== 过渡动画工具类 ===== */
.u-transition-none { transition: none !important; }
.u-transition-all { transition: all 150ms ease-in-out !important; }
.u-transition { transition: color 150ms ease-in-out, background-color 150ms ease-in-out, border-color 150ms ease-in-out, text-decoration-color 150ms ease-in-out, fill 150ms ease-in-out, stroke 150ms ease-in-out, opacity 150ms ease-in-out, box-shadow 150ms ease-in-out, transform 150ms ease-in-out !important; }
.u-transition-colors { transition: color 150ms ease-in-out, background-color 150ms ease-in-out, border-color 150ms ease-in-out, text-decoration-color 150ms ease-in-out, fill 150ms ease-in-out, stroke 150ms ease-in-out !important; }
.u-transition-opacity { transition: opacity 150ms ease-in-out !important; }
.u-transition-shadow { transition: box-shadow 150ms ease-in-out !important; }
.u-transition-transform { transition: transform 150ms ease-in-out !important; }

/* Duration */
.u-duration-75 { transition-duration: 75ms !important; }
.u-duration-100 { transition-duration: 100ms !important; }
.u-duration-150 { transition-duration: 150ms !important; }
.u-duration-200 { transition-duration: 200ms !important; }
.u-duration-300 { transition-duration: 300ms !important; }
.u-duration-500 { transition-duration: 500ms !important; }
.u-duration-700 { transition-duration: 700ms !important; }
.u-duration-1000 { transition-duration: 1000ms !important; }

/* Timing Function */
.u-ease-linear { transition-timing-function: linear !important; }
.u-ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1) !important; }
.u-ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important; }
.u-ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; }

/* ===== 变换工具类 ===== */
.u-transform { transform: var(--tw-transform) !important; }
.u-transform-none { transform: none !important; }

/* Scale */
.u-scale-0 { transform: scale(0) !important; }
.u-scale-50 { transform: scale(0.5) !important; }
.u-scale-75 { transform: scale(0.75) !important; }
.u-scale-90 { transform: scale(0.9) !important; }
.u-scale-95 { transform: scale(0.95) !important; }
.u-scale-100 { transform: scale(1) !important; }
.u-scale-105 { transform: scale(1.05) !important; }
.u-scale-110 { transform: scale(1.1) !important; }
.u-scale-125 { transform: scale(1.25) !important; }
.u-scale-150 { transform: scale(1.5) !important; }

/* Rotate */
.u-rotate-0 { transform: rotate(0deg) !important; }
.u-rotate-1 { transform: rotate(1deg) !important; }
.u-rotate-2 { transform: rotate(2deg) !important; }
.u-rotate-3 { transform: rotate(3deg) !important; }
.u-rotate-6 { transform: rotate(6deg) !important; }
.u-rotate-12 { transform: rotate(12deg) !important; }
.u-rotate-45 { transform: rotate(45deg) !important; }
.u-rotate-90 { transform: rotate(90deg) !important; }
.u-rotate-180 { transform: rotate(180deg) !important; }
.u--rotate-180 { transform: rotate(-180deg) !important; }
.u--rotate-90 { transform: rotate(-90deg) !important; }
.u--rotate-45 { transform: rotate(-45deg) !important; }
.u--rotate-12 { transform: rotate(-12deg) !important; }
.u--rotate-6 { transform: rotate(-6deg) !important; }
.u--rotate-3 { transform: rotate(-3deg) !important; }
.u--rotate-2 { transform: rotate(-2deg) !important; }
.u--rotate-1 { transform: rotate(-1deg) !important; }

/* ===== 溢出工具类 ===== */
.u-overflow-auto { overflow: auto !important; }
.u-overflow-hidden { overflow: hidden !important; }
.u-overflow-clip { overflow: clip !important; }
.u-overflow-visible { overflow: visible !important; }
.u-overflow-scroll { overflow: scroll !important; }
.u-overflow-x-auto { overflow-x: auto !important; }
.u-overflow-y-auto { overflow-y: auto !important; }
.u-overflow-x-hidden { overflow-x: hidden !important; }
.u-overflow-y-hidden { overflow-y: hidden !important; }
.u-overflow-x-clip { overflow-x: clip !important; }
.u-overflow-y-clip { overflow-y: clip !important; }
.u-overflow-x-visible { overflow-x: visible !important; }
.u-overflow-y-visible { overflow-y: visible !important; }
.u-overflow-x-scroll { overflow-x: scroll !important; }
.u-overflow-y-scroll { overflow-y: scroll !important; }

/* ===== Z-Index 工具类 ===== */
.u-z-0 { z-index: 0 !important; }
.u-z-10 { z-index: 10 !important; }
.u-z-20 { z-index: 20 !important; }
.u-z-30 { z-index: 30 !important; }
.u-z-40 { z-index: 40 !important; }
.u-z-50 { z-index: 50 !important; }
.u-z-auto { z-index: auto !important; }

/* ===== 光标工具类 ===== */
.u-cursor-auto { cursor: auto !important; }
.u-cursor-default { cursor: default !important; }
.u-cursor-pointer { cursor: pointer !important; }
.u-cursor-wait { cursor: wait !important; }
.u-cursor-text { cursor: text !important; }
.u-cursor-move { cursor: move !important; }
.u-cursor-help { cursor: help !important; }
.u-cursor-not-allowed { cursor: not-allowed !important; }
.u-cursor-none { cursor: none !important; }
.u-cursor-context-menu { cursor: context-menu !important; }
.u-cursor-progress { cursor: progress !important; }
.u-cursor-cell { cursor: cell !important; }
.u-cursor-crosshair { cursor: crosshair !important; }
.u-cursor-vertical-text { cursor: vertical-text !important; }
.u-cursor-alias { cursor: alias !important; }
.u-cursor-copy { cursor: copy !important; }
.u-cursor-no-drop { cursor: no-drop !important; }
.u-cursor-grab { cursor: grab !important; }
.u-cursor-grabbing { cursor: grabbing !important; }
.u-cursor-all-scroll { cursor: all-scroll !important; }
.u-cursor-col-resize { cursor: col-resize !important; }
.u-cursor-row-resize { cursor: row-resize !important; }
.u-cursor-n-resize { cursor: n-resize !important; }
.u-cursor-e-resize { cursor: e-resize !important; }
.u-cursor-s-resize { cursor: s-resize !important; }
.u-cursor-w-resize { cursor: w-resize !important; }
.u-cursor-ne-resize { cursor: ne-resize !important; }
.u-cursor-nw-resize { cursor: nw-resize !important; }
.u-cursor-se-resize { cursor: se-resize !important; }
.u-cursor-sw-resize { cursor: sw-resize !important; }
.u-cursor-ew-resize { cursor: ew-resize !important; }
.u-cursor-ns-resize { cursor: ns-resize !important; }
.u-cursor-nesw-resize { cursor: nesw-resize !important; }
.u-cursor-nwse-resize { cursor: nwse-resize !important; }
.u-cursor-zoom-in { cursor: zoom-in !important; }
.u-cursor-zoom-out { cursor: zoom-out !important; }

/* ===== 用户选择工具类 ===== */
.u-select-none { user-select: none !important; }
.u-select-text { user-select: text !important; }
.u-select-all { user-select: all !important; }
.u-select-auto { user-select: auto !important; }

/* ===== 指针事件工具类 ===== */
.u-pointer-events-none { pointer-events: none !important; }
.u-pointer-events-auto { pointer-events: auto !important; }

/* ===== 可见性工具类 ===== */
.u-visible { visibility: visible !important; }
.u-invisible { visibility: hidden !important; }

/* ===== 响应式工具类 ===== */
@media (max-width: 640px) {
  .sm\:u-hidden { display: none !important; }
  .sm\:u-block { display: block !important; }
  .sm\:u-flex { display: flex !important; }
  .sm\:u-flex-col { flex-direction: column !important; }
  .sm\:u-text-sm { font-size: var(--font-size-sm) !important; }
  .sm\:u-p-2 { padding: var(--spacing-2) !important; }
  .sm\:u-m-2 { margin: var(--spacing-2) !important; }
}

@media (max-width: 768px) {
  .md\:u-hidden { display: none !important; }
  .md\:u-block { display: block !important; }
  .md\:u-flex { display: flex !important; }
  .md\:u-flex-col { flex-direction: column !important; }
  .md\:u-text-base { font-size: var(--font-size-base) !important; }
  .md\:u-p-4 { padding: var(--spacing-4) !important; }
  .md\:u-m-4 { margin: var(--spacing-4) !important; }
}

@media (max-width: 1024px) {
  .lg\:u-hidden { display: none !important; }
  .lg\:u-block { display: block !important; }
  .lg\:u-flex { display: flex !important; }
  .lg\:u-flex-row { flex-direction: row !important; }
  .lg\:u-text-lg { font-size: var(--font-size-lg) !important; }
  .lg\:u-p-6 { padding: var(--spacing-6) !important; }
  .lg\:u-m-6 { margin: var(--spacing-6) !important; }
}

/* ===== 打印样式工具类 ===== */
@media print {
  .print\:u-hidden { display: none !important; }
  .print\:u-block { display: block !important; }
  .print\:u-text-black { color: #000 !important; }
  .print\:u-bg-white { background-color: #fff !important; }
}

/* ===== 深色主题工具类 ===== */
@media (prefers-color-scheme: dark) {
  .dark\:u-text-white { color: #fff !important; }
  .dark\:u-text-gray-100 { color: var(--color-gray-100) !important; }
  .dark\:u-bg-gray-800 { background-color: var(--color-gray-800) !important; }
  .dark\:u-bg-gray-900 { background-color: var(--color-gray-900) !important; }
  .dark\:u-border-gray-700 { border-color: var(--color-gray-700) !important; }
}

/* ===== 组合工具类 ===== */
/* 常用组合 */
.u-center {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.u-center-x {
  display: flex !important;
  justify-content: center !important;
}

.u-center-y {
  display: flex !important;
  align-items: center !important;
}

.u-space-between {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.u-space-around {
  display: flex !important;
  justify-content: space-around !important;
  align-items: center !important;
}

.u-card {
  background-color: var(--color-bg-primary) !important;
  border: 1px solid var(--color-border-primary) !important;
  border-radius: var(--radius-card) !important;
  padding: var(--spacing-4) !important;
  box-shadow: var(--shadow-sm) !important;
}

.u-button-base {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: var(--spacing-2) var(--spacing-4) !important;
  border-radius: var(--radius-button) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  line-height: var(--line-height-normal) !important;
  cursor: pointer !important;
  transition: all 150ms ease-in-out !important;
  border: 1px solid transparent !important;
  text-decoration: none !important;
}

.u-input-base {
  display: block !important;
  width: 100% !important;
  padding: var(--spacing-2) var(--spacing-3) !important;
  border: 1px solid var(--color-border-primary) !important;
  border-radius: var(--radius-input) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-normal) !important;
  background-color: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  transition: border-color 150ms ease-in-out, box-shadow 150ms ease-in-out !important;
}

.u-input-base:focus {
  outline: none !important;
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* ===== 可访问性工具类 ===== */
.u-sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.u-not-sr-only {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

.u-focus-visible:focus-visible {
  outline: 2px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

.u-focus-within:focus-within {
  outline: 2px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

/* ===== 调试工具类 ===== */
.u-debug {
  outline: 2px solid red !important;
  outline-offset: -2px !important;
}

.u-debug * {
  outline: 1px solid blue !important;
  outline-offset: -1px !important;
}

/* ===== 使用示例注释 ===== */
/*
使用示例：

<!-- 布局示例 -->
<div class="u-flex u-items-center u-justify-between u-p-4 u-bg-primary u-rounded-lg">
  <span class="u-text-lg u-font-semibold u-text-white">标题</span>
  <button class="u-button-base u-bg-white u-text-primary u-hover:bg-gray-50">
    操作
  </button>
</div>

<!-- 卡片示例 -->
<div class="u-card u-space-y-4">
  <h3 class="u-text-xl u-font-bold u-text-primary">卡片标题</h3>
  <p class="u-text-secondary u-leading-relaxed">卡片内容描述</p>
  <div class="u-flex u-gap-2">
    <button class="u-button-base u-bg-primary u-text-white">主要操作</button>
    <button class="u-button-base u-border-primary u-text-primary">次要操作</button>
  </div>
</div>

<!-- 表单示例 -->
<form class="u-space-y-4">
  <div>
    <label class="u-block u-text-sm u-font-medium u-text-primary u-mb-1">
      用户名
    </label>
    <input class="u-input-base" type="text" placeholder="请输入用户名" />
  </div>
  <div>
    <label class="u-block u-text-sm u-font-medium u-text-primary u-mb-1">
      密码
    </label>
    <input class="u-input-base" type="password" placeholder="请输入密码" />
  </div>
  <button class="u-button-base u-bg-primary u-text-white u-w-full">
    登录
  </button>
</form>

<!-- 响应式示例 -->
<div class="u-grid u-gap-4 md:u-flex md:u-items-center lg:u-justify-between">
  <div class="u-text-center md:u-text-left">
    <h2 class="u-text-2xl u-font-bold">响应式标题</h2>
  </div>
  <div class="u-hidden md:u-block">
    <button class="u-button-base u-bg-primary u-text-white">
      桌面端按钮
    </button>
  </div>
</div>
*/