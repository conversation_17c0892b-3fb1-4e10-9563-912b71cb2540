// 基础组件样式系统
// 提供统一的组件结构和视觉风格

@use './variables.scss';

// ===== 基础组件容器 =====

.crf-base {
  // 基础布局
  background: var(--crf-base-background, #ffffff);
  padding: var(--crf-base-padding-vertical, 16px) var(--crf-base-padding-horizontal, 16px);
  margin: var(--crf-base-margin-top, 12px) 0 var(--crf-base-margin-bottom, 12px) 0;
  border-radius: var(--crf-base-border-radius, 8px);
  border: 1px solid var(--crf-base-border-color, #e5e7eb);
  box-sizing: border-box;
  position: relative;

  // 过渡动画
  transition: all var(--crf-transition-duration-base, 0.2s) var(--crf-transition-timing-function, ease);

  // 响应式交互
  &:hover {
    border-color: var(--crf-color-primary-light-6, #bfdbfe);
    box-shadow: var(--crf-base-box-shadow, 0 2px 8px rgba(0, 0, 0, 0.1));
  }

  // 聚焦状态
  &:focus-within {
    border-color: var(--crf-color-primary, #3b82f6);
    box-shadow: 0 0 0 2px var(--crf-color-primary-light-8, #dbeafe);
  }
}

// ===== 头部区域 =====

.crf-base__header {
  display: flex;
  align-items: center;
  margin-bottom: var(--crf-base-header-margin-bottom, 8px);
  font-size: var(--crf-base-header-font-size, 16px);
  font-weight: var(--crf-base-header-font-weight, 600);
  color: var(--crf-base-header-color, #374151);
  line-height: var(--crf-base-title-line-height, 1.4);
}

// ===== 标题区域 =====

.crf-base__title {
  font-size: var(--crf-base-title-font-size, 16px);
  font-weight: var(--crf-base-title-font-weight, 600);
  color: var(--crf-base-title-color, #374151);
  line-height: var(--crf-base-title-line-height, 1.4);
  margin: 0;
  padding: 0;

  // 确保标题不会换行
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ===== 必填标识 =====

.crf-base__required {
  color: var(--crf-base-required-color, #ef4444);
  font-size: var(--crf-base-required-font-size, 16px);
  margin-right: var(--crf-base-required-margin-right, 4px);
  line-height: 1;
  vertical-align: middle;

  // 动画效果
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

// ===== 描述区域 =====

.crf-base__description {
  margin-bottom: var(--crf-base-description-margin-bottom, 8px);
  font-size: var(--crf-base-description-font-size, 14px);
  color: var(--crf-base-description-color, #6b7280);
  line-height: var(--crf-base-description-line-height, 1.5);
  min-height: var(--crf-base-description-min-height, auto);

  // 多行文本处理
  word-wrap: break-word;
  word-break: break-all;

  // 空状态处理
  &:empty {
    display: none;
  }
}

// ===== 内容区域 =====

.crf-base__content {
  margin-bottom: var(--crf-base-content-margin-bottom, 8px);

  // 确保内容区域能够正确展示
  &:last-child {
    margin-bottom: 0;
  }
}

// ===== 帮助文本区域 =====

.crf-base__help {
  margin-top: var(--crf-base-help-margin-top, 4px);
  font-size: var(--crf-base-help-font-size, 12px);
  color: var(--crf-base-help-color, #9ca3af);
  line-height: var(--crf-line-height-sm, 1.3);

  // 多行文本处理
  word-wrap: break-word;

  // 空状态处理
  &:empty {
    display: none;
  }
}

// ===== 错误信息区域 =====

.crf-base__error {
  margin-top: var(--crf-base-error-margin-top, 4px);
  font-size: var(--crf-base-error-font-size, 12px);
  color: var(--crf-base-error-color, #ef4444);
  line-height: var(--crf-line-height-sm, 1.3);

  // 错误状态图标
  &::before {
    content: '⚠';
    margin-right: var(--crf-spacing-xs, 4px);
  }

  // 动画效果
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-2px);
  }

  75% {
    transform: translateX(2px);
  }
}

// ===== 底部区域 =====

.crf-base__footer {
  margin-top: var(--crf-base-footer-margin-top, 8px);
  font-size: var(--crf-base-footer-font-size, 14px);
  display: flex;
  justify-content: space-between;
  align-items: center;

  // 空状态处理
  &:empty {
    display: none;
  }
}

.crf-base__footer-left {
  flex: 1;
}

.crf-base__footer-right {
  display: flex;
  align-items: center;
  gap: var(--crf-spacing-md, 8px);
}

// ===== 布局变体 =====

// 水平布局
.crf-base--horizontal {

  .crf-base__header,
  .crf-base__description,
  .crf-base__content,
  .crf-base__help,
  .crf-base__error,
  .crf-base__footer {
    display: flex;
    align-items: center;
    margin-bottom: var(--crf-spacing-xs, 4px);
  }

  .crf-base__header {
    min-width: 120px;
    margin-right: var(--crf-spacing-md, 8px);
  }

  .crf-base__content {
    flex: 1;
  }
}

// ===== 尺寸变体 =====

// 继承自 variables.scss 中的 CSS 变量
.crf-base--small {
  // 变量已在 variables.scss 中定义
}

.crf-base--large {
  // 变量已在 variables.scss 中定义
}

// ===== 主题变体 =====

// 继承自 variables.scss 中的主题定义

// ===== 状态变体 =====

// 禁用状态
.crf-base--disabled {
  // 变量已在 variables.scss 中定义

  // 禁用所有交互
  pointer-events: none;

  // 禁用所有子元素的用户选择
  user-select: none;

  .crf-base__content {
    * {
      cursor: not-allowed !important;
    }
  }
}

// 只读状态
.crf-base--readonly {
  // 变量已在 variables.scss 中定义

  // 保持可选择但不可编辑
  .crf-base__content {

    input,
    textarea,
    select {
      cursor: default;
    }
  }
}

// 验证状态
.crf-base--error {
  // 变量已在 variables.scss 中定义
}

.crf-base--success {
  border-color: var(--crf-color-success);

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 3px;
    background: var(--crf-color-success);
    border-radius: var(--crf-border-radius-base) var(--crf-border-radius-base) 0 0;
  }
}

.crf-base--warning {
  border-color: var(--crf-color-warning);

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 3px;
    background: var(--crf-color-warning);
    border-radius: var(--crf-border-radius-base) var(--crf-border-radius-base) 0 0;
  }
}

// ===== 响应式设计 =====

// 移动端适配
@media (max-width: 768px) {
  .crf-base {
    --crf-base-padding-vertical: var(--crf-spacing-md, 8px);
    --crf-base-padding-horizontal: var(--crf-spacing-md, 8px);
    --crf-base-margin-bottom: var(--crf-spacing-sm, 4px);
  }

  // 强制垂直布局
  .crf-base--horizontal {

    .crf-base__header,
    .crf-base__description,
    .crf-base__content,
    .crf-base__help,
    .crf-base__error,
    .crf-base__footer {
      display: block;
      margin-bottom: var(--crf-spacing-xs, 4px);
    }

    .crf-base__header {
      min-width: auto;
      margin-right: 0;
    }
  }
}

// ===== 打印样式 =====

@media print {
  .crf-base {
    border: 1px solid #000;
    box-shadow: none;
    background: #fff;
    margin-bottom: var(--crf-spacing-sm, 4px);
    page-break-inside: avoid;
  }

  .crf-base__required {
    color: #000;

    &::after {
      content: '(必填)';
      font-size: var(--crf-font-size-xs, 12px);
    }
  }

  .crf-base__error,
  .crf-base__help {
    color: #666;
  }
}

// ===== 辅助功能支持 =====

// 高对比度模式
@media (prefers-contrast: high) {
  .crf-base {
    border-width: 2px;
    --crf-base-border-color: #000;
  }

  .crf-base__title {
    font-weight: var(--crf-font-weight-bold);
  }

  .crf-base__required {
    font-weight: var(--crf-font-weight-bold);
  }
}

// 减少动画
@media (prefers-reduced-motion: reduce) {

  .crf-base,
  .crf-base__required,
  .crf-base__error {
    animation: none;
    transition: none;
  }
}

// ===== 工具类 =====

// 隐藏元素但保持可访问性
.crf-base__sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 清除浮动
.crf-base__clearfix::after {
  content: '';
  display: table;
  clear: both;
}