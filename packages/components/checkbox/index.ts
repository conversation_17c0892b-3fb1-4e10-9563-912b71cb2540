import Checkbox from './checkbox.vue'
import { withInstallByCode } from '@crf/utils'

// 主要导出（新的命名规范）
export const CrfCheckbox = withInstallByCode(Checkbox, 'crf-checkbox')

// 默认导出
export default CrfCheckbox

// 导出组件和Schema
export { default as CrfCheckboxComponent } from './checkbox.vue'

export { default as CrfCheckboxSchema } from './schema'
export type { CrfCheckboxSchema as CrfCheckboxSchemaType } from './schema'

// 导出类型
export type { CrfCheckboxProps, CrfCheckboxConfig, CheckboxOption } from '@crf/types'