<template>
  <div 
    class="crf-base-container"
    :class="{
      'crf-state-disabled': props.disabled,
      'crf-state-readonly': props.readonly,
      'crf-state-required': props.required
    }"
    :data-medical-type="props.medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{ props.title || '多选项' }}</span>
        <span v-if="props.required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="props.description" class="crf-base-description">
        {{ props.description }}
      </div>
    </div>

    <!-- 选项列表 -->
    <div class="crf-base-content">
      <!-- 全选按钮 -->
      <div v-if="props.checkAll" class="u-pb-2 u-border-b u-border-gray-200">
        <n-checkbox
          v-model:checked="checkAllStatus"
          :indeterminate="isIndeterminate"
          :disabled="shouldDisableInEditMode"
          @update:checked="handleCheckAllChange"
        >
          全选
        </n-checkbox>
      </div>
      
      <!-- 选项列表 -->
      <n-checkbox-group
        v-model:value="internalValue"
        :size="nativeSize"
        :disabled="shouldDisableInEditMode"
        @update:value="handleChange"
      >
        <!-- 可拖拽的选项列表 -->
        <draggable
          v-model="localOptions"
          :disabled="!isInEditorEditMode"
          item-key="value"
          ghost-class="crf-drag-ghost"
          chosen-class="crf-drag-chosen"
          drag-class="crf-drag-active"
          animation="200"
          @start="onDragStart"
          @end="onDragEnd"
          @change="onDragChange"
          :class="[
            'u-flex',
            props.direction === 'horizontal' ? 'u-flex-wrap u-gap-4' : 'u-flex-col u-gap-2'
          ]"
        >
          <template #item="{ element, index }">
            <div
              class="crf-option-wrapper u-transition-colors"
              :class="{ 
                'crf-state-draggable': isInEditorEditMode, 
                'crf-state-editing': optionEditStates[index] 
              }"
            >
              <!-- 复选框 -->
              <n-checkbox
                :value="element.value"
                :disabled="element.disabled"
                class="u-flex-1"
              >
                <span v-if="!optionEditStates[index]" class="u-text-sm u-text-primary">{{ element.label }}</span>
                <n-input
                  v-else
                  v-model:value="editingOptions[index].label"
                  size="small"
                  class="u-max-w-xs u-ml-2"
                  @blur="saveOptionEdit(index)"
                  @keyup.enter="saveOptionEdit(index)"
                  @keyup.esc="cancelOptionEdit(index)"
                  @click.stop
                />
              </n-checkbox>
              
              <!-- 编辑操作按钮 -->
              <div v-if="isInEditorEditMode" class="crf-option-actions">
                <button
                  v-if="!optionEditStates[index]"
                  class="crf-option-button u-transition-colors"
                  @click.stop="startOptionEdit(index)"
                  title="编辑选项"
                >
                  <n-icon><PencilOutline /></n-icon>
                </button>
                <button
                  class="crf-option-button u-transition-colors"
                  @click.stop="deleteOption(index)"
                  title="删除选项"
                >
                  <n-icon><TrashOutline /></n-icon>
                </button>
                <button
                  class="crf-option-button crf-drag-handle u-transition-colors"
                  title="拖拽排序"
                >
                  <n-icon><SwapVerticalOutline /></n-icon>
                </button>
              </div>
            </div>
          </template>
        </draggable>
      </n-checkbox-group>
    </div>

    <!-- 编辑模式下的操作按钮 -->
    <div v-if="isInEditorEditMode" class="crf-base-actions">
      <div class="crf-action-buttons">
        <button 
          class="crf-action-button u-transition-colors"
          @click="addOption"
        >
          <n-icon><AddOutline /></n-icon>
          <span class="u-ml-1">添加选项</span>
        </button>
      </div>
      <div class="crf-status-indicators">
        <span v-if="props.required" class="crf-status-tag u-bg-brand u-text-white">必填</span>
      </div>
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject } from 'vue'
import type { CrfCheckboxProps, CheckboxOption, CheckboxValueType } from '@crf/types'
import { NCheckbox, NCheckboxGroup, NInput, NIcon } from 'naive-ui'
import { PencilOutline, TrashOutline, AddOutline, SwapVerticalOutline } from '@vicons/ionicons5'
import { EditorMode } from '@crf/types/core'
import draggable from 'vuedraggable'

// 定义组件名称
defineOptions({
  name: 'CrfCheckbox'
})

const props = withDefaults(defineProps<CrfCheckboxProps>(), {
  modelValue: () => [],
  title: '多选项',
  description: '请选择一个或多个选项',
  options: () => [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
    { value: 'option3', label: '选项3' }
  ],
  direction: 'horizontal' as const,
  size: 'medium' as const,
  buttonStyle: false,
  border: false,
  min: 0,
  max: 0,
  checkAll: false,
  medicalType: 'general' as const,
  fieldCode: undefined,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: (string | number)[]]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'change': [value: (string | number)[]]
  'click': [event: MouseEvent]
  'option-change': [options: CheckboxOption[]]
  'check-all': [checked: CheckboxValueType]
  'validate': [result: { isValid: boolean; errors: string[] }]
  'update-options': [options: CheckboxOption[]]
}>()

// ===== 内部状态 =====
const internalValue = ref<(string | number)[]>([])

// 监听 props.modelValue 的变化来更新内部状态
watch(() => props.modelValue, (newValue) => {
  if (newValue !== internalValue.value) {
    internalValue.value = newValue || []
  }
}, { immediate: true })

// ===== 验证状态 =====
const validationState = ref({
  status: '',
  message: ''
})

// 验证函数
const validate = async (value: (string | number)[]) => {
  const errors: string[] = []

  // 必填验证
  if (props.required && (!value || value.length === 0)) {
    errors.push('请至少选择一个选项')
  }

  // 最少选择数验证
  if (props.min && props.min > 0 && value.length < props.min) {
    errors.push(`至少需要选择${props.min}个选项`)
  }

  // 最多选择数验证
  if (props.max && props.max > 0 && value.length > props.max) {
    errors.push(`最多只能选择${props.max}个选项`)
  }

  // 检查值是否在允许的选项中
  if (value && value.length > 0) {
    const validValues = optionsList.value.map(option => option.value)
    const invalidValues = value.filter(v => !validValues.includes(v))
    if (invalidValues.length > 0) {
      errors.push('选择的值中包含无效选项')
    }
  }

  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0]
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: ''
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: ''
  }
}

// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'medium') {
    return 'medium'
  }
  return props.size
})

// ===== 编辑器状态管理 =====
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const originalValue = ref<(string | number)[]>([])

// 计算是否在编辑器编辑模式下且组件被禁用
const isInEditorEditMode = computed(() => {
  const mode = editStore?.mode
  return mode === EditorMode.EDIT
})

const shouldDisableInEditMode = computed(() => {
  const editorMode = typeof editStore?.mode === 'object' && editStore?.mode !== null
    ? (editStore.mode as { value?: string }).value
    : editStore?.mode
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => {
    return typeof editStore?.mode === 'object' && editStore?.mode !== null
      ? (editStore.mode as { value?: string }).value
      : editStore?.mode
  },
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = [] // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)

const isComponentEditable = computed(() => {
  const mode = typeof editStore?.mode === 'object' && editStore?.mode !== null
    ? (editStore.mode as { value?: string }).value
    : editStore?.mode
  if (mode === 'preview') return true
  if (mode === 'publish') return true
  if (mode === 'edit') return false
  return true
})

// ===== 选项处理 =====
// 本地选项数组，用于拖拽
const localOptions = ref<CheckboxOption[]>([])

// 同步props.options到localOptions
watch(() => props.options, (newOptions) => {
  if (newOptions && Array.isArray(newOptions)) {
    localOptions.value = [...newOptions]
  }
}, { immediate: true, deep: true })

// 选项列表计算属性（保持向后兼容）
const optionsList = computed(() => {
  return localOptions.value
})

// ===== 选项编辑状态管理 =====
const optionEditStates = ref<boolean[]>([])
const editingOptions = ref<CheckboxOption[]>([])

// 初始化编辑状态
const initEditStates = () => {
  optionEditStates.value = new Array(localOptions.value.length).fill(false)
  editingOptions.value = localOptions.value.map(option => ({ ...option }))
}

// 监听选项变化，重新初始化编辑状态
watch(localOptions, () => {
  initEditStates()
}, { immediate: true })

// 开始编辑选项
const startOptionEdit = (index: number) => {
  optionEditStates.value[index] = true
  editingOptions.value[index] = { ...localOptions.value[index] }
  
  // 确保输入框获得焦点
  setTimeout(() => {
    const inputElement = document.querySelector(`.option-input input`) as HTMLInputElement
    if (inputElement) {
      inputElement.focus()
      inputElement.select()
    }
  }, 0)
}

// 保存选项编辑
const saveOptionEdit = (index: number) => {
  // 验证编辑的选项是否有效
  if (!editingOptions.value[index].label || !editingOptions.value[index].label.trim()) {
    return
  }
  
  // 生成value从label
  editingOptions.value[index].value = editingOptions.value[index].label
  
  // 更新本地选项
  localOptions.value[index] = { ...editingOptions.value[index] }
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  // 结束编辑状态
  optionEditStates.value[index] = false
}

// 取消选项编辑
const cancelOptionEdit = (index: number) => {
  editingOptions.value[index] = { ...localOptions.value[index] }
  optionEditStates.value[index] = false
}

// 删除选项
const deleteOption = (index: number) => {
  // 至少保留一个选项
  if (localOptions.value.length <= 1) {
    return
  }
  
  // 从本地选项中删除
  localOptions.value.splice(index, 1)
  
  // 重新初始化编辑状态
  initEditStates()
  
  // 触发更新事件
  emit('update-options', localOptions.value)
}

// ===== 拖拽功能 =====
const onDragStart = () => {
  // 拖拽开始
}

const onDragEnd = () => {
  // 触发选项更新事件
  emit('update-options', localOptions.value)
}

const onDragChange = () => {
  // 实时更新选项
  emit('update-options', localOptions.value)
}

// ===== 编辑模式操作按钮处理 =====
const addOption = () => {
  // 生成新的选项数据
  const newOptionValue = `option${localOptions.value.length + 1}`
  const newOption = {
    value: newOptionValue,
    label: `选项${localOptions.value.length + 1}`
  }
  
  // 添加到本地选项
  localOptions.value.push(newOption)
  
  // 重新初始化编辑状态
  initEditStates()
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  // 等待DOM更新后，立即进入编辑状态
  setTimeout(() => {
    const newIndex = localOptions.value.length - 1
    startOptionEdit(newIndex)
  }, 100)
}

// =============================================================================

// ===== 全选状态计算 =====
const checkAllStatus = computed({
  get: () => {
    return internalValue.value.length === optionsList.value.length
  },
  set: (checked: boolean) => {
    if (checked) {
      internalValue.value = optionsList.value
        .filter(option => !option.disabled)
        .map(option => option.value)
    } else {
      internalValue.value = []
    }
  }
})

// 半选状态
const isIndeterminate = computed(() => {
  const checkedCount = internalValue.value.length
  const totalCount = optionsList.value.filter(option => !option.disabled).length
  return checkedCount > 0 && checkedCount < totalCount
})



// ===== 编辑功能 =====
const startEdit = () => {
  if (props.disabled || props.readonly || !isComponentEditable.value) return
  
  editing.value = true
  originalValue.value = [...internalValue.value]
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  
  if (validationResult.isValid) {
    editing.value = false
  } else {
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }
  
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = [...originalValue.value]
  editing.value = false
  clearValidation()
}

// ===== 事件处理 =====
const handleChange = (val: CheckboxValueType[]) => {
  // el-checkbox-group 的 @change 事件参数是 CheckboxValueType[], 
  // 但我们组件的 v-model 是 (string | number)[]，所以需要过滤掉 boolean 类型。
  const newModelValue = val.filter(v => typeof v !== 'boolean') as (string | number)[]

  emit('update:modelValue', newModelValue)
  emit('change', newModelValue)
  validate(newModelValue)

  // 找到对应的选项并触发选项变化事件
  const selectedOptions = optionsList.value.filter(option => 
    newModelValue.includes(option.value)
  )
  emit('option-change', selectedOptions)
}

const handleCheckAllChange = (val: CheckboxValueType) => {
  const allValues = optionsList.value.map(option => option.value)
  // 全选/取消全选时，直接操作 internalValue，并通过 handleChange 触发事件更新
  const newModelValue = val ? allValues : []
  internalValue.value = newModelValue
  emit('check-all', val)
  // 主动触发一次 change，保持行为一致
  handleChange(newModelValue)
}



// ===== 监听器 =====
watch(() => props.modelValue, async (newValue) => {
  if (newValue !== undefined) {
    await validate(newValue)
  }
})

// ===== 对外暴露的API =====
defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation
})
</script>

<style scoped>
@use '../shared/index.scss';

/* Naive UI 复选框组件样式覆盖 */
:deep(.n-checkbox) {
  font-size: var(--crf-font-size-sm);
  color: var(--crf-text-primary);
  line-height: var(--crf-line-height-base);
}

:deep(.n-checkbox-group) {
  gap: var(--crf-spacing-2);
}

:deep(.n-checkbox .n-checkbox-box) {
  border-color: var(--crf-border-color);
  transition: border-color 0.2s ease;
}

:deep(.n-checkbox:hover .n-checkbox-box) {
  border-color: var(--crf-color-brand) !important;
}

:deep(.n-checkbox:hover .n-checkbox-box__border) {
  border-color: var(--crf-color-brand) !important;
}

:deep(.n-checkbox .n-checkbox-box__border) {
  border-radius: var(--crf-border-radius-sm);
}

:deep(.n-checkbox .n-checkbox-box__border:checked) {
  background-color: var(--crf-color-brand);
  border-color: var(--crf-color-brand);
}

:deep(.n-checkbox .n-checkbox__label) {
  color: var(--crf-text-primary);
  font-size: var(--crf-font-size-sm);
  line-height: var(--crf-line-height-base);
  min-height: var(--crf-min-height-sm);
  padding: var(--crf-spacing-1) 0;
}

/* 选项描述样式 */
.option-description {
  font-size: var(--crf-font-size-xs);
  color: var(--crf-text-secondary);
  margin-left: var(--crf-spacing-1);
}

/* 选项输入框样式 */
:deep(.n-input) {
  font-size: var(--crf-font-size-sm);
}

:deep(.n-input .n-input__input) {
  padding: var(--crf-spacing-1) var(--crf-spacing-2);
  min-height: var(--crf-min-height-xs);
}

/* 边框样式 */
.has-border {
  border: 1px solid var(--crf-border-color);
  border-radius: var(--crf-border-radius-sm);
  padding: var(--crf-spacing-2) var(--crf-spacing-3);
  margin: var(--crf-spacing-1);
}

.has-border:hover {
  border-color: var(--crf-color-brand);
}
</style>