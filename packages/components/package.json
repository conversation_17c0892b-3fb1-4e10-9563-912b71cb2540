{"name": "@crf/components", "version": "1.0.1", "private": true, "description": "基础组件库", "main": "index.ts", "module": "index.ts", "types": "index.ts", "exports": {".": {"types": "./index.ts", "import": "./index.ts", "require": "./index.ts"}, "./text": {"types": "./text/index.ts", "import": "./text/index.ts", "require": "./text/index.ts"}, "./input": {"types": "./input/index.ts", "import": "./input/index.ts", "require": "./input/index.ts"}, "./textarea": {"types": "./textarea/index.ts", "import": "./textarea/index.ts", "require": "./textarea/index.ts"}, "./radio": {"types": "./radio/index.ts", "import": "./radio/index.ts", "require": "./radio/index.ts"}, "./card": {"types": "./card/index.ts", "import": "./card/index.ts", "require": "./card/index.ts"}, "./icon": {"types": "./icon/index.ts", "import": "./icon/index.ts", "require": "./icon/index.ts"}, "./icon-color-picker": {"types": "./icon-color-picker/index.ts", "import": "./icon-color-picker/index.ts", "require": "./icon-color-picker/index.ts"}, "./base": {"types": "./base/index.ts", "import": "./base/index.ts", "require": "./base/index.ts"}, "./icon/iconify/src/iconify.vue": {"import": "./icon/iconify/src/iconify.vue", "types": "./icon/iconify/src/iconify.vue"}, "./shared/index.scss": {"import": "./shared/index.scss", "require": "./shared/index.scss"}}, "files": ["**/*"], "scripts": {"prebuild": "vue-tsc --build --force", "build": "pnpm prebuild && vite build", "stub": "vite build --watch"}, "peerDependencies": {"vue": "^3.5.17"}, "sideEffects": ["*/style/*"], "dependencies": {"@crf/hooks": "workspace:*", "@crf/types": "workspace:*", "@crf/utils": "workspace:*", "@iconify/vue": "^5.0.0", "dayjs": "^1.11.13", "naive-ui": "^2.42.0"}, "devDependencies": {"@crf/build-config": "workspace:*", "@vitejs/plugin-vue": "^5.2.4", "vite": "^6.2.4", "vue": "^3.5.17"}}