<script lang="ts" setup>
import { computed } from 'vue'
import { CrfIcon } from '../icon'

defineOptions({
  name: 'CrfCard'
})

const props = defineProps({
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  info: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: '#000'
  },
  tag: {
    type: String,
    default: ''
  },
  compact: Boolean
})

const compactClass = computed(() => props.compact ? 'compact-mode' : '')
</script>

<template>
  <div :class="[
    'flex items-center gap-1.5 p-1.5 bg-white border border-gray-200 rounded cursor-grab transition-all duration-200 select-none relative',
    'hover:shadow-sm active:cursor-grabbing active:scale-98',
    compactClass
  ]">
    <div :class="[
      'w-8 h-8 flex items-center justify-center rounded text-xl text-white flex-shrink-0',
      compact ? 'w-7 h-7 text-lg' : ''
    ]">
      <crf-icon :color="props.iconColor" :icon="props.icon" />
    </div>
    <div class="flex-1 min-w-0">
      <div :class="[
        'font-medium text-gray-800 mb-0.5 text-sm flex items-center gap-1.5',
        compact ? 'text-sm mb-0.5' : ''
      ]">
        {{ props.title }}
        <span v-if="props.tag" class="medical-tag">
          {{ props.tag }}
        </span>
      </div>
      <div :class="[
        'text-xs text-gray-600 leading-tight overflow-hidden line-clamp-2',
        compact ? 'text-xs' : ''
      ]">
        {{ props.info }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 保留复杂的样式和动画 */
.component-item {
  // 增强拖拽提示
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--border-radius);
    border: 2px dashed transparent;
    transition: all 0.2s ease;
    pointer-events: none;
  }
}

.component-item.dragging {
  opacity: 0.6;
  transform: rotate(5deg);
}

.medical-tag {
  font-size: 0.625rem;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
  background: #e1f5fe;
  color: #0277bd;
  border: 1px solid #b3e5fc;
}

// 紧凑模式的额外调整
.compact-mode {
  padding: 0.25rem 0.375rem;
  gap: 0.25rem;
}

// 添加拖拽动画
@keyframes dragPulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.component-item:hover .drag-indicator {
  animation: dragPulse 2s ease-in-out infinite;
}

/* UnoCSS 工具类扩展 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.scale-98 {
  transform: scale(0.98);
}
</style>