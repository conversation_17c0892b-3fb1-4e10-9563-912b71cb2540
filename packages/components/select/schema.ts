/**
 * CrfSelect 组件 Schema 定义
 * 
 * 使用 TypeBox 定义组件的配置 Schema，支持运行时验证和类型推导
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

/**
 * CrfSelect 组件配置 Schema
 */
export const CrfSelectConfigSchema = createComponentSchema({
    // =============================================================================
    // 基础设置
    // =============================================================================
    title: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件标题',
        default: '下拉选择',
        placeholder: '请输入组件标题',
        description: '显示在组件上方的标题文本',
        showInConfig: true,
        configGroup: 'basic',
        helpIcon: true,
        validation: {
            required: true,
            message: '组件标题不能为空'
        }
    })),

    description: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件描述',
        default: '请选择一个选项',
        placeholder: '请输入组件描述',
        description: '显示在组件下方的描述文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    placeholder: Type.Optional(Type.String({
        code: 'config-input',
        label: '占位符文本',
        default: '请选择',
        placeholder: '请输入占位符文本',
        description: '下拉框为空时显示的提示文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    // =============================================================================
    // 选项设置
    // =============================================================================
    options: Type.Optional(Type.Array(Type.Object({
        value: Type.Union([Type.String(), Type.Number()]),
        label: Type.String(),
        disabled: Type.Optional(Type.Boolean()),
        description: Type.Optional(Type.String()),
        group: Type.Optional(Type.String())
    }), {
        code: 'config-options',
        label: '选项列表',
        default: [
            { value: 'option1', label: '选项1' },
            { value: 'option2', label: '选项2' },
            { value: 'option3', label: '选项3' }
        ],
        description: '下拉框的选项列表',
        showInConfig: true,
        configGroup: 'options',
        minItems: 1,
        maxItems: 50
    })),

    // =============================================================================
    // 功能设置
    // =============================================================================
    multiple: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '多选模式',
        default: false,
        description: '是否允许多选',
        showInConfig: true,
        configGroup: 'feature'
    })),

    clearable: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '可清空',
        default: true,
        description: '是否显示清空按钮',
        showInConfig: true,
        configGroup: 'feature'
    })),

    filterable: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '可搜索',
        default: false,
        description: '是否可以搜索选项',
        showInConfig: true,
        configGroup: 'feature'
    })),

    allowCreate: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '允许创建',
        default: false,
        description: '是否允许用户创建新选项',
        showInConfig: true,
        configGroup: 'feature'
    })),

    // =============================================================================
    // 多选设置
    // =============================================================================
    multipleLimit: Type.Optional(Type.Number({
        code: 'config-number',
        label: '多选限制数量',
        default: 0,
        placeholder: '请输入，0表示不限制',
        minimum: 0,
        maximum: 20,
        description: '多选时的最大选择数量，0表示不限制',
        showInConfig: true,
        configGroup: 'feature',
        showWhen: {
            field: 'multiple',
            value: true
        }
    })),

    collapseTags: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '折叠标签',
        default: false,
        description: '多选时是否折叠标签显示',
        showInConfig: true,
        configGroup: 'feature',
        showWhen: {
            field: 'multiple',
            value: true
        }
    })),

    maxCollapseTags: Type.Optional(Type.Number({
        code: 'config-number',
        label: '最大显示标签数',
        default: 1,
        placeholder: '请输入',
        minimum: 1,
        maximum: 10,
        description: '折叠前最多显示的标签数量',
        showInConfig: true,
        configGroup: 'feature',
        showWhen: {
            field: 'collapseTags',
            value: true
        }
    })),

    // =============================================================================
    // 外观设置
    // =============================================================================
    size: Type.Optional(Type.Union([
        Type.Literal('small'),
        Type.Literal('medium'),
        Type.Literal('large')
    ], {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        description: '组件的显示大小',
        enumNames: ['小', '中', '大'],
        showInConfig: true,
        configGroup: 'appearance'
    })),

    // =============================================================================
    // 验证设置
    // =============================================================================
    required: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '必填项',
        default: false,
        description: '是否为必填字段',
        showInConfig: true,
        configGroup: 'validation'
    })),

    // =============================================================================
    // 文本设置
    // =============================================================================
    noDataText: Type.Optional(Type.String({
        code: 'config-input',
        label: '无数据文本',
        default: '无数据',
        placeholder: '请输入无数据时显示的文本',
        description: '没有选项时显示的文本',
        showInConfig: true,
        configGroup: 'text'
    })),

    noMatchText: Type.Optional(Type.String({
        code: 'config-input',
        label: '无匹配文本',
        default: '无匹配数据',
        placeholder: '请输入无匹配时显示的文本',
        description: '搜索无匹配时显示的文本',
        showInConfig: true,
        configGroup: 'text'
    })),

    // =============================================================================
    // 医疗相关字段
    // =============================================================================
    medicalType: Type.Optional(Type.Union([
        Type.Literal('general'),
        Type.Literal('diagnosis'),
        Type.Literal('medication'),
        Type.Literal('lab'),
        Type.Literal('procedure'),
        Type.Literal('department')
    ], {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        description: '医疗数据的类型分类',
        enumNames: ['通用', '诊断', '用药', '检验', '操作', '科室'],
        showInConfig: true,
        configGroup: 'medical'
    })),

    fieldCode: Type.Optional(Type.String({
        code: 'config-input',
        label: '字段编码',
        default: '',
        placeholder: '请输入字段编码',
        description: '用于数据收集和导出的字段编码',
        showInConfig: true,
        configGroup: 'medical'
    }))
})

/**
 * 从 Schema 推导的类型
 */
export type CrfSelectSchema = Static<typeof CrfSelectConfigSchema>

/**
 * 组件配置类型别名
 */
export type CrfSelectConfig = CrfSelectSchema

/**
 * 默认导出
 */
export default CrfSelectConfigSchema