<template>
  <div 
    class="crf-base-container"
    :class="{
      'crf-state-editing': isInEditorEditMode,
      'crf-state-disabled': shouldDisableInEditMode,
      'crf-state-readonly': readonly,
      'crf-state-required': required
    }"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{ title || '下拉选择' }}</span>
        <span v-if="required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="description" class="crf-base-description">
        {{ description }}
      </div>
    </div>

    <!-- 在编辑模式下显示选项管理界面 -->
    <div v-if="isInEditorEditMode" class="crf-base-content crf-content-vertical">
      <!-- 可拖拽的选项列表 -->
      <div class="u-flex-1 u-min-h-15">
        <draggable
          v-model="localOptions"
          item-key="value"
          ghost-class="crf-drag-ghost"
          chosen-class="crf-drag-chosen"
          drag-class="crf-drag-active"
          animation="200"
          @start="onDragStart"
          @end="onDragEnd"
          @change="onDragChange"
          class="u-flex u-flex-col u-gap-2"
        >
          <template #item="{ element, index }">
            <div
              class="crf-option-wrapper u-transition-colors"
              :class="{ 'crf-state-editing': optionEditStates[index] }"
            >
              <!-- 选项标签显示或编辑 -->
              <div class="u-flex-1">
                <span v-if="!optionEditStates[index]" class="u-text-sm u-text-primary">
                  {{ element.label }}
                </span>
                <n-input
                  v-else
                  v-model:value="editingOptions[index].label"
                  size="small"
                  class="u-max-w-xs u-ml-2"
                  placeholder="输入选项标签"
                  @blur="saveOptionEdit(index)"
                  @keyup.enter="saveOptionEdit(index)"
                  @keyup.esc="cancelOptionEdit(index)"
                  @click.stop
                />
              </div>
              
              <!-- 编辑操作按钮 -->
              <div class="crf-option-actions">
                <button
                  v-if="!optionEditStates[index]"
                  class="crf-option-button u-transition-colors"
                  @click.stop="startOptionEdit(index)"
                >
                  <n-icon><PencilOutline /></n-icon>
                </button>
                <button
                  class="crf-option-button u-transition-colors"
                  @click.stop="deleteOption(index)"
                >
                  <n-icon><TrashOutline /></n-icon>
                </button>
                <button
                  class="crf-option-button u-transition-colors u-cursor-grab active:u-cursor-grabbing"
                  title="拖拽排序"
                >
                  <n-icon><SwapVerticalOutline /></n-icon>
                </button>
              </div>
            </div>
          </template>
        </draggable>
      </div>
      
      <!-- 编辑模式下的操作按钮 -->
      <div class="crf-base-actions">
        <div class="crf-action-buttons">
          <button 
            class="crf-action-button u-transition-colors"
            @click="addOption"
          >
            <n-icon><AddOutline /></n-icon>
            添加选项
          </button>
        </div>
        <div class="crf-status-indicators">
          <span v-if="required" class="crf-status-tag u-bg-brand u-text-white">必填</span>
        </div>
      </div>
    </div>
    
    <!-- 正常显示模式：预览、发布模式 -->
    <div v-else class="crf-base-content">
      <n-select
        ref="selectRef"
        v-model:value="internalValue"
        :placeholder="placeholder || '请选择'"
        :multiple="multiple"
        :max-tag-count="multiple ? (multipleLimit || undefined) : undefined"
        :clearable="clearable"
        :filterable="filterable"
        :size="nativeSize"
        :loading="loading"
        :disabled="shouldDisableInEditMode"
        :options="naiveOptions"
        style="width: 100%"
        @update:value="handleChange"
        @blur="handleSelectBlur"
        @clear="handleClear"
      />
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject, nextTick } from 'vue'
import type { CrfSelectProps, SelectOption } from '@crf/types'
import { NSelect, NInput, NIcon } from 'naive-ui'
import { PencilOutline, TrashOutline, AddOutline, SwapVerticalOutline } from '@vicons/ionicons5'
import draggable from 'vuedraggable'

// 定义组件名称
defineOptions({
  name: 'CrfSelect'
})

const props = withDefaults(defineProps<CrfSelectProps>(), {
  modelValue: undefined,
  title: '下拉选择',
  description: '请选择一个选项',
  placeholder: '请选择',
  options: () => [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
    { value: 'option3', label: '选项3' }
  ],
  multiple: false,
  multipleLimit: 0,
  clearable: true,
  filterable: false,
  remote: false,
  loading: false,
  noDataText: '无数据',
  noMatchText: '无匹配数据',
  collapseTags: false,
  collapseTagsTooltip: true,
  maxCollapseTags: 1,
  allowCreate: false,
  reserveKeyword: true,
  defaultFirstOption: false,
  teleported: true,
  medicalType: 'general' as const,
  fieldCode: undefined,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  size: 'medium' as const,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number | (string | number)[]]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'change': [value: string | number | (string | number)[]]
  'click': [event: MouseEvent]
  'option-change': [option: SelectOption | SelectOption[]]
  'visible-change': [visible: boolean]
  'remove-tag': [value: string | number]
  'clear': []
  'filter': [query: string]
  'validate': [result: { isValid: boolean; errors: string[] }]
  'update-options': [options: SelectOption[]]
}>()

// ===== 验证状态 =====
const validationState = ref({
  status: '',
  message: ''
})

// 验证函数
const validate = async (value: string | number | (string | number)[]) => {
  const errors: string[] = []
  
  // 必填验证
  if (props.required) {
    if (props.multiple) {
      if (!value || (Array.isArray(value) && value.length === 0)) {
        errors.push('请至少选择一个选项')
      }
    } else {
      if (value === undefined || value === null || value === '') {
        errors.push('请选择一个选项')
      }
    }
  }
  
  // 多选数量限制验证
  if (props.multiple && props.multipleLimit && Array.isArray(value)) {
    if (value.length > props.multipleLimit) {
      errors.push(`最多只能选择${props.multipleLimit}个选项`)
    }
  }
  
  // 检查值是否在允许的选项中
  if (value !== undefined && value !== null && value !== '') {
    const validValues = getAllValidValues()
    if (props.multiple && Array.isArray(value)) {
      const invalidValues = value.filter(v => !validValues.includes(v))
      if (invalidValues.length > 0) {
        errors.push('选择的值中包含无效选项')
      }
    } else if (!Array.isArray(value)) {
      if (!validValues.includes(value)) {
        errors.push('选择的值无效')
      }
    }
  }
  
  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0]
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: ''
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: ''
  }
}



// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'medium') {
    return 'medium'
  }
  return props.size
})

// 转换选项格式为 Naive UI 期望的格式
const naiveOptions = computed(() => {
  if (hasOptionGroups.value) {
    return optionGroups.value.map(group => ({
      type: 'group',
      label: group.label,
      key: group.label,
      children: group.options.map(option => ({
        label: option.label,
        value: option.value,
        disabled: option.disabled
      }))
    }))
  } else {
    return optionsList.value.map(option => ({
      label: option.label,
      value: option.value,
      disabled: option.disabled
    }))
  }
})



// ===== 编辑器状态管理 =====
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const selectRef = ref<InstanceType<typeof NSelect>>()
const originalValue = ref<string | number | (string | number)[]>('')

// 计算是否在编辑器编辑模式下且组件被禁用
const isInEditorEditMode = computed(() => {
  const mode = (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined)
  return mode === 'edit'
})

const shouldDisableInEditMode = computed(() => {
  const editorMode = (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined)
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === 'edit' && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined),
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      if (props.multiple) {
        internalValue.value = []
      } else {
        internalValue.value = ''
      }
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)

const isComponentEditable = computed(() => {
  const mode = (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined)
  if (mode === 'preview') return true
  if (mode === 'publish') return true
  if (mode === 'edit') return false
  return true
})

// ===== 选项处理 =====
// 本地选项数组，用于拖拽
const localOptions = ref<SelectOption[]>([])

// 同步props.options到localOptions
watch(() => props.options, (newOptions) => {
  if (newOptions && Array.isArray(newOptions)) {
    localOptions.value = [...newOptions]
  }
}, { immediate: true, deep: true })

// 选项列表计算属性（保持向后兼容）
const optionsList = computed(() => {
  return localOptions.value
})

// ===== 选项编辑状态管理 =====
const optionEditStates = ref<boolean[]>([])
const editingOptions = ref<SelectOption[]>([])

// 初始化编辑状态
const initEditStates = () => {
  optionEditStates.value = new Array(localOptions.value.length).fill(false)
  editingOptions.value = localOptions.value.map(option => ({ ...option }))
}

// 监听选项变化，重新初始化编辑状态
watch(localOptions, () => {
  initEditStates()
}, { immediate: true })

// 开始编辑选项
const startOptionEdit = (index: number) => {
  optionEditStates.value[index] = true
  editingOptions.value[index] = { ...localOptions.value[index] }
  
  // 确保输入框获得焦点
  setTimeout(() => {
    const inputElement = document.querySelector(`.option-input input`) as HTMLInputElement
    if (inputElement) {
      inputElement.focus()
      inputElement.select()
    }
  }, 0)
}

// 保存选项编辑
const saveOptionEdit = (index: number) => {
  // 验证编辑的选项是否有效
  if (!editingOptions.value[index].label || !editingOptions.value[index].label.trim()) {
    return
  }
  
  // 生成value从label
  editingOptions.value[index].value = editingOptions.value[index].label
  
  // 更新本地选项
  localOptions.value[index] = { ...editingOptions.value[index] }
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  // 结束编辑状态
  optionEditStates.value[index] = false
}

// 取消选项编辑
const cancelOptionEdit = (index: number) => {
  editingOptions.value[index] = { ...localOptions.value[index] }
  optionEditStates.value[index] = false
}

// 删除选项
const deleteOption = (index: number) => {
  // 至少保留一个选项
  if (localOptions.value.length <= 1) {
    return
  }
  
  // 从本地选项中删除
  localOptions.value.splice(index, 1)
  
  // 重新初始化编辑状态
  initEditStates()
  
  // 触发更新事件
  emit('update-options', localOptions.value)
}

// ===== 拖拽功能 =====
const onDragStart = () => {
  // 拖拽开始
}

const onDragEnd = () => {
  // 触发选项更新事件
  emit('update-options', localOptions.value)
}

const onDragChange = () => {
  // 实时更新选项
  emit('update-options', localOptions.value)
}

// ===== 编辑模式操作按钮处理 =====
const addOption = () => {
  // 生成新的选项数据
  const newOptionValue = `option${localOptions.value.length + 1}`
  const newOption = {
    value: newOptionValue,
    label: `选项${localOptions.value.length + 1}`
  }
  
  // 添加到本地选项
  localOptions.value.push(newOption)
  
  // 重新初始化编辑状态
  initEditStates()
  
  // 触发更新事件
  emit('update-options', localOptions.value)
  
  // 等待DOM更新后，立即进入编辑状态
  setTimeout(() => {
    const newIndex = localOptions.value.length - 1
    startOptionEdit(newIndex)
  }, 100)
}

const optionGroups = computed(() => {
  return props.optionGroups || []
})

const hasOptionGroups = computed(() => {
  return optionGroups.value && optionGroups.value.length > 0
})

// 获取所有有效值
const getAllValidValues = () => {
  if (hasOptionGroups.value) {
    return optionGroups.value.flatMap(group => 
      group.options.map(option => option.value)
    )
  } else {
    return optionsList.value.map(option => option.value)
  }
}

// 获取所有选项（扁平化）
const getAllOptions = (): SelectOption[] => {
  if (hasOptionGroups.value) {
    return optionGroups.value.flatMap(group => group.options)
  } else {
    return optionsList.value
  }
}

// 内部值管理
const internalValue = ref<string | number | (string | number)[]>(props.multiple ? [] : '')

// 同步 props.modelValue 到 internalValue
watch(() => props.modelValue, (newValue) => {
  // 处理数据清空的情况
  if (newValue === undefined || newValue === null) {
    if (props.multiple) {
      internalValue.value = []
    } else {
      internalValue.value = ''
    }
  } else {
    if (props.multiple) {
      internalValue.value = Array.isArray(newValue) ? newValue : []
    } else {
      internalValue.value = newValue || ''
    }
  }
}, { immediate: true })

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue) => {
  if (isComponentEditable.value) {
    emit('update:modelValue', newValue)
  }
})



// ===== 编辑功能 =====
const startEdit = () => {
  if (props.disabled || props.readonly || !isComponentEditable.value) return
  
  editing.value = true
  originalValue.value = props.multiple ? [...(internalValue.value as (string | number)[])] : internalValue.value
  
  nextTick(() => {
    selectRef.value?.focus()
  })
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  
  if (validationResult.isValid) {
    editing.value = false
  } else {
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }
  
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
  editing.value = false
  clearValidation()
}

// ===== 事件处理 =====
const handleChange = async (value: string | number | (string | number)[]) => {
  await validate(value)
  
  // 找到对应的选项并触发选项变化事件
  if (props.multiple && Array.isArray(value)) {
    const selectedOptions = getAllOptions().filter(option => 
      value.includes(option.value)
    )
    emit('option-change', selectedOptions)
  } else if (!Array.isArray(value)) {
    const selectedOption = getAllOptions().find(option => option.value === value)
    if (selectedOption) {
      emit('option-change', selectedOption)
    }
  }
  
  emit('change', value)
  
  // 单选时自动完成编辑
  if (!props.multiple) {
    setTimeout(() => {
      finishEdit()
    }, 100)
  }
}





const handleClear = () => {
  emit('clear')
}

const handleSelectBlur = (event: FocusEvent) => {
  emit('blur', event)
}



// ===== 监听器 =====
watch(() => props.modelValue, async (newValue) => {
  if (newValue !== undefined) {
    await validate(newValue)
  }
})

// ===== 对外暴露的API =====
defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation,

})
</script>

<style scoped>
@use '../shared/index.scss';

/* Naive UI 组件样式覆盖 */
:deep(.n-select) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border);
  --n-border-radius: var(--crf-border-radius);
  --n-line-height: var(--crf-line-height-base);
  --n-padding-single: var(--crf-spacing-2) var(--crf-spacing-3);
  --n-min-height: var(--crf-size-control-base);
}

:deep(.n-input) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border);
  --n-border-radius: var(--crf-border-radius);
  --n-line-height: var(--crf-line-height-base);
  --n-padding: var(--crf-spacing-1) var(--crf-spacing-2);
  --n-height: var(--crf-size-control-sm);
}

/* 选项描述样式 */
.option-description {
  float: right;
  font-size: var(--crf-font-size-xs);
  color: var(--crf-color-text-secondary);
}
</style>