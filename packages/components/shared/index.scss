/**
 * CRF 统一样式系统入口文件
 * 整合所有样式模块，提供一致的设计系统
 * 
 * 使用方式：
 * 1. 在 main.ts 中导入：import '@crf/components/shared/index.scss'
 * 2. 在组件中使用统一的 CSS 变量和工具类
 * 3. 逐步替换硬编码样式
 */

/* ===== 基础样式系统 ===== */
/* 统一颜色变量 - 替换所有硬编码颜色 */
@use '../base/styles/unified-colors.scss';

/* 统一间距和尺寸系统 - 替换所有硬编码尺寸 */
@use '../base/styles/unified-spacing.scss';

/* 公共组件样式库 - 提供可复用的组件样式 */
@use '../base/styles/common-components.scss';

/* 工具类库 - 提供原子级样式类 */
@use '../base/styles/utility-classes.scss';

/* ===== 全局重置和基础样式 ===== */
/* 现代化的 CSS 重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移除默认的 focus outline，使用自定义样式 */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

a:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* 按钮基础样式重置 */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
}

input::placeholder,
textarea::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 列表样式 */
ul,
ol {
  list-style: none;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

th,
td {
  padding: 0;
  text-align: left;
}

/* ===== 滚动条样式 ===== */
/* Webkit 浏览器滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-secondary);
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-focus);
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-secondary) var(--color-bg-secondary);
}

/* ===== 选择文本样式 ===== */
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

::-moz-selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

/* ===== 兼容性样式 ===== */
/* 兼容 Naive UI 组件 */
.n-button,
.n-input,
.n-select {
  /* 确保 Naive UI 组件使用统一的颜色系统 */
  --n-color-primary: var(--color-primary);
  --n-color-primary-hover: var(--color-primary-500);
  --n-color-primary-pressed: var(--color-primary-700);
  --n-border-radius: var(--radius-base);
}

/* ===== 组件库特定样式 ===== */
/* CRF 组件前缀 */
[class*="crf-"] {
  box-sizing: border-box;
}

/* 医疗表单特定样式 */
.crf-medical-form {
  /* 医疗表单的特殊样式 */
  font-family: var(--font-family-base);
  line-height: var(--line-height-relaxed);
}

.crf-medical-form .required {
  color: var(--color-error);
}

.crf-medical-form .validation-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

/* ===== 动画工具类 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

/* ===== 减少动画偏好支持 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}