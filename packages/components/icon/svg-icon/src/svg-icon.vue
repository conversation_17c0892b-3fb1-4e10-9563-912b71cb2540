<template>
  <div
    :id="randomId"
    class="svg-icon"
    :style="iconStyle"
    :class="{ 'svg-icon--hoverable': tip }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- SVG 内容 -->
    <div v-html="svgContent"></div>
    
    <!-- Tip 提示 -->
    <Transition name="tip-fade">
      <div v-if="showTip && tip" class="svg-icon-tip" :style="tipStyle">
        {{ tip }}
        <div class="svg-icon-tip-arrow"></div>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, type CSSProperties } from 'vue'

// 定义组件 props
const props = withDefaults(defineProps<{
  // 文件名（不包含 .svg 扩展名）
  name: string
  // 大小，默认单位补px
  size?: string | number
  // 颜色
  color?: string
  // 透明度
  opacity?: string | number
  // 提示文本
  tip?: string
  // 提示位置
  tipPlacement?: 'top' | 'bottom' | 'left' | 'right'
}>(), {
  size: 16,
  opacity: 1,
  tipPlacement: 'top'
})

// 定义事件
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// svg 内容
const svgContent = ref<string>('')
const showTip = ref(false)
let tipTimer: number | null = null

// 生成随机字符串
const randomString = (): string => {
  return Math.random().toString(36).substring(2, 15)
}

// 生成随机组件 id
const randomId = `svg-icon-${randomString()}`

// 计算图标样式
const iconStyle = computed(() => {
  const sizeValue = typeof props.size === 'number' ? `${props.size}px` : props.size
  
  return {
    width: sizeValue,
    height: sizeValue,
    display: 'inline-block',
    color: props.color,
    opacity: props.opacity,
    '--icon-color': props.color
  }
})

// 计算提示样式
const tipStyle = computed(() => {
  const placement = props.tipPlacement
  const baseStyle: CSSProperties = {
    position: 'absolute',
    zIndex: 99999,
    backgroundColor: '#333',
    color: '#fff',
    padding: '6px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    whiteSpace: 'nowrap',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
  }

  switch (placement) {
    case 'top':
      baseStyle.bottom = '100%'
      baseStyle.left = '50%'
      baseStyle.transform = 'translateX(-50%)'
      baseStyle.marginBottom = '5px'
      break
    case 'bottom':
      baseStyle.top = '100%'
      baseStyle.left = '50%'
      baseStyle.transform = 'translateX(-50%)'
      baseStyle.marginTop = '5px'
      break
    case 'left':
      baseStyle.right = '100%'
      baseStyle.top = '50%'
      baseStyle.transform = 'translateY(-50%)'
      baseStyle.marginRight = '5px'
      break
    case 'right':
      baseStyle.left = '100%'
      baseStyle.top = '50%'
      baseStyle.transform = 'translateY(-50%)'
      baseStyle.marginLeft = '5px'
      break
  }

  return baseStyle
})

// 加载 SVG 文件
const loadSvg = async (name: string): Promise<void> => {
  try {
    // 动态导入 SVG 文件
    const svgModule = await import(`../assets/${name}.svg?raw`)
    let svgString = svgModule.default

    // 如果指定了颜色，替换 SVG 中的 fill 属性
    if (props.color) {
      // 移除现有的 fill 属性并添加新的
      svgString = svgString.replace(/fill="[^"]*"/g, '')
      svgString = svgString.replace(/<svg/, `<svg fill="${props.color}"`)
      
      // 如果 svg 内部有 path 或其他元素，也需要设置 fill
      svgString = svgString.replace(/<(path|circle|rect|polygon|ellipse)([^>]*?)>/g, 
        (match: string, tag: string, attrs: string) => {
          if (!attrs.includes('fill=')) {
            return `<${tag}${attrs} fill="currentColor">`
          }
          return match
        }
      )
    }

    svgContent.value = svgString
  } catch (error) {
    console.warn(`Failed to load SVG icon: ${name}`, error)
    svgContent.value = ''
  }
}

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}

// 处理鼠标进入事件
const handleMouseEnter = () => {
  if (props.tip) {
    // 清理之前的定时器
    if (tipTimer) {
      clearTimeout(tipTimer)
    }
    
    // 显示提示
    showTip.value = true
  }
}

// 处理鼠标离开事件
const handleMouseLeave = () => {
  if (props.tip) {
    // 清理之前的定时器
    if (tipTimer) {
      clearTimeout(tipTimer)
    }
    
    // 立即隐藏提示
    showTip.value = false
  }
}

// 监听 name 变化，重新加载 SVG
watch(
  () => props.name,
  (newName) => {
    if (newName) {
      loadSvg(newName)
    }
  },
  { immediate: true }
)

// 组件挂载时加载 SVG
onMounted(() => {
  if (props.name) {
    loadSvg(props.name)
  }
})
</script>

<style scoped>
.svg-icon {
  line-height: 1;
  vertical-align: middle;
}

.svg-icon--hoverable {
  cursor: pointer;
  position: relative;
  transition: transform 0.2s ease;
}

.svg-icon--hoverable:hover {
  transform: scale(1.1);
}

.svg-icon--hoverable:active {
  transform: scale(0.95);
}

.svg-icon > div:first-child {
  width: 100%;
  height: 100%;
  display: block;
}

.svg-icon :deep(svg) {
  width: 100%;
  height: 100%;
  display: block;
}

/* 支持通过 CSS 变量控制颜色 */
.svg-icon :deep(svg [fill="currentColor"]) {
  fill: var(--icon-color, currentColor);
}

/* Tip 提示样式 */
.svg-icon-tip {
  pointer-events: none;
  user-select: none;
  z-index: 99999 !important;
  position: absolute !important;
}

.svg-icon-tip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 4px solid transparent;
}

.svg-icon .svg-icon-tip[style*="bottom: 100%"] .svg-icon-tip-arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #333;
}

.svg-icon .svg-icon-tip[style*="top: 100%"] .svg-icon-tip-arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #333;
}

.svg-icon .svg-icon-tip[style*="right: 100%"] .svg-icon-tip-arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #333;
}

.svg-icon .svg-icon-tip[style*="left: 100%"] .svg-icon-tip-arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #333;
}

/* Tip 过渡动画 */
.tip-fade-enter-active,
.tip-fade-leave-active {
  transition: all 0.3s ease;
}

.tip-fade-enter-from,
.tip-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>