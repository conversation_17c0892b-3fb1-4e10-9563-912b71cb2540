import SvgIcon from './src/svg-icon.vue'
import type { App } from 'vue'

// 组件的 props 类型定义
export interface SvgIconProps {
    name: string
    size?: string | number
    color?: string
    opacity?: string | number
    tip?: string
    tipPlacement?: 'top' | 'bottom' | 'left' | 'right'
}

// 支持按需导入
export { SvgIcon }

// 支持全局注册
export default {
    install(app: App) {
        app.component('SvgIcon', SvgIcon)
    }
}

// 导出组件类型
export type SvgIconInstance = InstanceType<typeof SvgIcon> 