# SVG Icon 组件

一个灵活的 Vue 3 SVG 图标组件，支持动态加载、样式定制和最佳实践。

## 特性

- ✨ 动态加载 SVG 文件
- 🎨 支持颜色定制
- 📏 灵活的尺寸设置
- 🎯 TypeScript 支持
- 🚀 响应式设计
- 💡 最佳实践实现

## 安装

```bash
# 按需导入
import { SvgIcon } from './path/to/svg-icon'

# 或全局注册
import SvgIconPlugin from './path/to/svg-icon'
app.use(SvgIconPlugin)
```

## 基础用法

```vue
<template>
  <!-- 基础使用 -->
  <svg-icon name="仓鼠" />
  
  <!-- 设置大小和颜色 -->
  <svg-icon name="仓鼠" :size="40" color="red" />
  
  <!-- 设置透明度 -->
  <svg-icon name="仓鼠" :size="40" color="blue" :opacity="0.8" />
</template>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | `string` | - | SVG 文件名（不包含 .svg 扩展名）**必填** |
| size | `string \| number` | `16` | 图标大小，数字会自动添加 px 单位 |
| color | `string` | - | 图标颜色，支持任何有效的 CSS 颜色值 |
| opacity | `string \| number` | `1` | 图标透明度，取值 0-1 |
| tip | `string` | - | 点击时显示的提示文本 |
| tipPlacement | `'top' \| 'bottom' \| 'left' \| 'right'` | `'top'` | 提示显示位置 |

## 使用示例

### 不同尺寸
```vue
<svg-icon name="仓鼠" :size="16" />
<svg-icon name="仓鼠" :size="24" />
<svg-icon name="仓鼠" :size="32" />
<svg-icon name="仓鼠" size="2rem" />
```

### 不同颜色
```vue
<svg-icon name="仓鼠" color="red" />
<svg-icon name="仓鼠" color="#ff6b35" />
<svg-icon name="仓鼠" color="rgb(255, 107, 53)" />
<svg-icon name="仓鼠" color="currentColor" />
```

### 透明度控制
```vue
<svg-icon name="仓鼠" :opacity="1" />
<svg-icon name="仓鼠" :opacity="0.8" />
<svg-icon name="仓鼠" :opacity="0.5" />
```

### 🆕 Tip 提示功能
```vue
<template>
  <!-- 基础 tip 提示（默认显示在上方） -->
  <svg-icon 
    name="仓鼠" 
    tip="这是一个可爱的仓鼠图标"
    @click="handleClick"
  />
  
  <!-- 不同位置的提示 -->
  <svg-icon name="home" tip="首页" tip-placement="top" />
  <svg-icon name="user" tip="用户" tip-placement="bottom" />
  <svg-icon name="settings" tip="设置" tip-placement="left" />
  <svg-icon name="search" tip="搜索" tip-placement="right" />
</template>

<script setup>
const handleClick = (event) => {
  console.log('SVG图标被点击了', event)
}
</script>
```

**Tip 功能特性：**
- 🖱️ 鼠标悬浮时显示提示文本
- 📍 默认显示在图标上方
- ✅ 支持 4 个方向：top、bottom、left、right
- ✨ 带有淡入淡出动画效果
- ⚡ 鼠标离开时立即隐藏
- 🔍 鼠标悬停放大效果
- 👆 点击缩小反馈效果
- 🏆 最高层级 (z-index: 99999)，不会被其他元素遮挡

## 文件结构

```
svg-icon/
├── src/
│   └── svg-icon.vue     # 主组件文件
├── assets/              # SVG 文件存放目录
│   └── 仓鼠.svg         # 示例 SVG 文件
├── index.ts             # 入口文件
├── types.d.ts           # 类型声明
├── example.vue          # 使用示例
└── README.md            # 说明文档
```

## 技术实现

### 动态导入
组件使用 Vite 的 `?raw` 后缀动态导入 SVG 文件内容：

```typescript
const svgModule = await import(`../assets/${name}.svg?raw`)
```

### 颜色控制
通过正则表达式替换 SVG 内容中的 `fill` 属性来实现颜色控制：

```typescript
svgString = svgString.replace(/fill="[^"]*"/g, '')
svgString = svgString.replace(/<svg/, `<svg fill="${props.color}"`)
```

### 样式计算
使用 Vue 3 的 `computed` 响应式计算图标样式：

```typescript
const iconStyle = computed(() => ({
  width: sizeValue,
  height: sizeValue,
  display: 'inline-block',
  color: props.color,
  opacity: props.opacity
}))
```

## 最佳实践

1. **文件命名**：SVG 文件使用有意义的名称，方便识别
2. **类型安全**：使用 TypeScript 提供完整的类型支持
3. **性能优化**：使用动态导入减少初始包大小
4. **样式隔离**：使用 scoped 样式避免样式污染
5. **错误处理**：提供完善的错误处理和降级方案

## 注意事项

- SVG 文件需要放在 `assets` 目录下
- 文件名不需要包含 `.svg` 扩展名
- 确保 SVG 文件格式正确且可访问
- 颜色控制可能对某些复杂 SVG 无效，建议使用简单的单色图标 