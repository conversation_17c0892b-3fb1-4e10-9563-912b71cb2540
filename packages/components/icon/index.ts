import { withInstallByCode } from '@crf/utils'
import { SvgIcon } from './svg-icon'
import { IconifyIcon } from './iconify'

// 主要导出（标准的 Crf 前缀）
export const CrfSvgIcon = withInstallByCode(SvgIcon, 'crf-svg-icon')
export const CrfIcon = withInstallByCode(IconifyIcon, 'crf-icon')

// 默认导出
export default CrfIcon

// 导出原始组件
export { SvgIcon, IconifyIcon }

// 导出相关类型
export type { SvgIconProps } from './svg-icon'
export type { IconProps, IconifyIconProps } from './iconify'
