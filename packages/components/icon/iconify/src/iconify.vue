<template>
  <div
    :id="randomId"
    class="iconify-icon"
    :style="iconStyle"
    :class="{ 
      'iconify-icon--inline': inline,
      'iconify-icon--hoverable': tip
    }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 在线模式：使用 @iconify/vue 组件 -->
    <Icon
      v-if="!offline && iconName"
      :icon="iconName"
      :width="computedSize"
      :height="computedSize"
      v-bind="{
        ...(color ? { color } : {}),
        ...(rotate !== undefined ? { rotate: typeof rotate === 'string' ? parseInt(rotate, 10) : rotate } : {}),
        ...(horizontalFlip !== undefined ? { 'horizontal-flip': horizontalFlip } : {}),
        ...(verticalFlip !== undefined ? { 'vertical-flip': verticalFlip } : {}),
        ...$attrs
      }"
      @load="handleLoad"
    />
    
    <!-- 离线模式：使用动态导入的图标组件 -->
    <component
      v-else-if="offline && iconComponent"
      :is="iconComponent"
      :style="{ color: color, opacity: opacity, width: computedSize, height: computedSize }"
    />
    
    <!-- 加载中或错误状态 -->
    <div v-else-if="loading" class="iconify-loading">
      <span class="iconify-loading-text">加载中...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-else class="iconify-error">
      <span class="iconify-error-text">图标加载失败</span>
    </div>
    
    <!-- Tip 提示 -->
    <Transition name="tip-fade">
      <div v-if="showTip && tip" class="iconify-tip" :style="tipStyle">
        {{ tip }}
        <div class="iconify-tip-arrow"></div>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick, type CSSProperties } from 'vue'
import { Icon } from '@iconify/vue'

defineOptions({
  name: 'CrfIcon'
})

// 定义组件 props
const props = withDefaults(defineProps<{
  // 图标名称，格式：icon-set:icon-name 或 prefix/icon-name（离线模式）
  icon: string
  // 大小
  size?: string | number
  // 颜色
  color?: string
  // 透明度
  opacity?: string | number
  // 是否内联显示
  inline?: boolean
  // 旋转角度
  rotate?: number | string
  // 水平翻转
  horizontalFlip?: boolean
  // 垂直翻转
  verticalFlip?: boolean
  // 是否使用离线模式
  offline?: boolean
  // 离线模式下的图标前缀
  prefix?: string
  // 提示文本
  tip?: string
  // 提示位置
  tipPlacement?: 'top' | 'bottom' | 'left' | 'right'
}>(), {
  size: '1em',
  opacity: 1,
  inline: false,
  offline: false,
  prefix: 'icon',
  tipPlacement: 'top'
})

// 定义事件
const emit = defineEmits<{
  load: [iconName: string]
  error: [error: unknown]
  click: [event: MouseEvent]
}>()

// 响应式状态
const loading = ref(false)
const iconComponent = ref<Record<string, unknown> | null>(null)
const showTip = ref(false)
let tipTimer: number | null = null

// 生成随机字符串
const randomString = (): string => {
  return Math.random().toString(36).substring(2, 15)
}

// 生成随机组件 id
const randomId = `iconify-${randomString()}`

// 计算图标名称（在线模式）
const iconName = computed(() => {
  if (props.offline) return ''
  return props.icon
})

// 计算尺寸
const computedSize = computed(() => {
  const size = props.size
  if (typeof size === 'number') {
    return `${size}px`
  }
  return size
})

// 计算样式
const iconStyle = computed(() => {
  const style: CSSProperties = {
    width: computedSize.value,
    height: computedSize.value,
    fontSize: computedSize.value,
    opacity: props.opacity
  }
  
  if (props.color) {
    style.color = props.color
  }
  
  return style
})

// 计算提示样式
const tipStyle = computed(() => {
  const placement = props.tipPlacement
  const baseStyle: CSSProperties = {
    position: 'absolute',
    zIndex: 99999,
    backgroundColor: '#333',
    color: '#fff',
    padding: '6px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    whiteSpace: 'nowrap',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
  }

  switch (placement) {
    case 'top':
      baseStyle.bottom = '100%'
      baseStyle.left = '50%'
      baseStyle.transform = 'translateX(-50%)'
      baseStyle.marginBottom = '5px'
      break
    case 'bottom':
      baseStyle.top = '100%'
      baseStyle.left = '50%'
      baseStyle.transform = 'translateX(-50%)'
      baseStyle.marginTop = '5px'
      break
    case 'left':
      baseStyle.right = '100%'
      baseStyle.top = '50%'
      baseStyle.transform = 'translateY(-50%)'
      baseStyle.marginRight = '5px'
      break
    case 'right':
      baseStyle.left = '100%'
      baseStyle.top = '50%'
      baseStyle.transform = 'translateY(-50%)'
      baseStyle.marginLeft = '5px'
      break
  }

  return baseStyle
})

// 加载离线图标
const loadOfflineIcon = async (iconName: string) => {
  try {
    loading.value = true
    
    // 解析图标路径
    const [iconSet, iconNamePart] = iconName.includes(':') 
      ? iconName.split(':')
      : iconName.includes('/') 
        ? iconName.split('/')
        : [props.prefix, iconName]
    
    // 动态导入图标组件
    const iconModule = await import(/* @vite-ignore */ `~icons/${iconSet}/${iconNamePart}`)
    iconComponent.value = iconModule.default
    
    loading.value = false
    emit('load', iconName)
  } catch (error) {
    loading.value = false
    console.warn(`Failed to load offline icon: ${iconName}`, error)
    emit('error', error)
  }
}

// 处理在线图标加载完成事件
const handleLoad = () => {
  emit('load', props.icon)
}

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}

// 处理鼠标进入事件
const handleMouseEnter = () => {
  if (props.tip) {
    // 清理之前的定时器
    if (tipTimer) {
      clearTimeout(tipTimer)
    }
    
    // 显示提示
    showTip.value = true
  }
}

// 处理鼠标离开事件
const handleMouseLeave = () => {
  if (props.tip) {
    // 清理之前的定时器
    if (tipTimer) {
      clearTimeout(tipTimer)
    }
    
    // 立即隐藏提示
    showTip.value = false
  }
}

// 监听图标变化
watch(
  () => props.icon,
  (newIcon) => {
    if (!newIcon) return
    
    if (props.offline) {
      loadOfflineIcon(newIcon)
    }
  },
  { immediate: true }
)

// 监听离线模式变化
watch(
  () => props.offline,
  (isOffline) => {
    if (isOffline && props.icon) {
      nextTick(() => {
        loadOfflineIcon(props.icon)
      })
    }
  }
)

// 组件挂载时处理
onMounted(() => {
  if (props.offline && props.icon) {
    loadOfflineIcon(props.icon)
  }
})
</script>

<style scoped>
.iconify-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
}

.iconify-icon--inline {
  vertical-align: text-bottom;
}

.iconify-icon--hoverable {
  cursor: pointer;
  position: relative;
  transition: transform 0.2s ease;
}

.iconify-icon--hoverable:hover {
  transform: scale(1.1);
}

.iconify-icon--hoverable:active {
  transform: scale(0.95);
}

.iconify-loading,
.iconify-error {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1em;
  min-height: 1em;
  color: #999;
  font-size: 0.8em;
}

.iconify-loading-text,
.iconify-error-text {
  white-space: nowrap;
}

.iconify-error {
  color: #f56c6c;
}

/* 深度选择器，确保图标样式正确 */
.iconify-icon :deep(svg) {
  display: block;
  width: 100%;
  height: 100%;
}

/* Tip 提示样式 */
.iconify-tip {
  pointer-events: none;
  user-select: none;
  z-index: 99999 !important;
  position: absolute !important;
}

.iconify-tip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 4px solid transparent;
}

.iconify-icon .iconify-tip[style*="bottom: 100%"] .iconify-tip-arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #333;
}

.iconify-icon .iconify-tip[style*="top: 100%"] .iconify-tip-arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #333;
}

.iconify-icon .iconify-tip[style*="right: 100%"] .iconify-tip-arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #333;
}

.iconify-icon .iconify-tip[style*="left: 100%"] .iconify-tip-arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #333;
}

/* Tip 过渡动画 */
.tip-fade-enter-active,
.tip-fade-leave-active {
  transition: all 0.3s ease;
}

.tip-fade-enter-from,
.tip-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>