<template>
  <div class="iconify-example-container">
    <h2>Iconify Icon 组件使用示例</h2>
    
    <!-- 简化命名示例 -->
    <div class="example-section">
      <h3>✨ 简化命名（推荐）</h3>
      <div class="icon-row">
        <Icon icon="mdi:home" />
        <Icon icon="mdi:heart" color="red" />
        <Icon icon="ant-design:star-filled" color="orange" :size="24" />
        <Icon icon="heroicons:user-circle" color="blue" :size="32" />
      </div>
      <p class="note">
        现在使用最简洁的 <code>&lt;Icon&gt;</code> 组件名称，简洁易用！
      </p>
    </div>

    <!-- 在线模式示例 -->
    <div class="example-section">
      <h3>在线模式（默认）</h3>
      <div class="icon-row">
        <Icon icon="mdi:home" />
        <Icon icon="mdi:heart" color="red" />
        <Icon icon="ant-design:star-filled" color="orange" :size="24" />
        <Icon icon="heroicons:user-circle" color="blue" :size="32" />
      </div>
    </div>

    <!-- 不同大小 -->
    <div class="example-section">
      <h3>不同大小</h3>
      <div class="icon-row">
        <Icon icon="mdi:favorite" color="red" :size="16" />
        <Icon icon="mdi:favorite" color="red" :size="24" />
        <Icon icon="mdi:favorite" color="red" :size="32" />
        <Icon icon="mdi:favorite" color="red" size="2em" />
      </div>
    </div>

    <!-- 变换效果 -->
    <div class="example-section">
      <h3>变换效果</h3>
      <div class="icon-row">
        <Icon icon="mdi:arrow-right" :size="24" />
        <Icon icon="mdi:arrow-right" :size="24" :rotate="90" />
        <Icon icon="mdi:arrow-right" :size="24" :rotate="180" />
        <Icon icon="mdi:arrow-right" :size="24" :rotate="270" />
      </div>
      <div class="icon-row">
        <Icon icon="mdi:thumb-up" :size="24" />
        <Icon icon="mdi:thumb-up" :size="24" :horizontal-flip="true" />
        <Icon icon="mdi:thumb-up" :size="24" :vertical-flip="true" />
        <Icon icon="mdi:thumb-up" :size="24" :horizontal-flip="true" :vertical-flip="true" />
      </div>
    </div>

    <!-- 离线模式示例（需要安装相应的图标包） -->
    <div class="example-section">
      <h3>离线模式</h3>
      <p class="note">注意：离线模式需要安装 unplugin-icons 和相应的图标包</p>
      <div class="icon-row">
        <Icon icon="carbon/home" :offline="true" :size="24" />
        <Icon icon="carbon/user" :offline="true" :size="24" color="blue" />
        <Icon icon="carbon/settings" :offline="true" :size="24" color="green" />
      </div>
    </div>

    <!-- 内联显示 -->
    <div class="example-section">
      <h3>内联显示</h3>
      <p>
        这是一段文字 
        <Icon icon="mdi:star" color="gold" :inline="true" /> 
        中间包含图标 
        <Icon icon="mdi:heart" color="red" :inline="true" /> 
        的示例。
      </p>
    </div>

    <!-- 透明度 -->
    <div class="example-section">
      <h3>透明度设置</h3>
      <div class="icon-row">
        <Icon icon="mdi:circle" color="blue" :size="32" :opacity="1" />
        <Icon icon="mdi:circle" color="blue" :size="32" :opacity="0.8" />
        <Icon icon="mdi:circle" color="blue" :size="32" :opacity="0.5" />
        <Icon icon="mdi:circle" color="blue" :size="32" :opacity="0.3" />
      </div>
    </div>

    <!-- 事件处理 -->
    <div class="example-section">
      <h3>事件处理</h3>
      <div class="icon-row">
        <Icon 
          icon="mdi:information" 
          :size="32" 
          color="blue"
          @load="handleLoad"
          @error="handleError"
          style="cursor: pointer;"
          title="点击查看加载事件"
        />
      </div>
      <p v-if="loadMessage" class="message">{{ loadMessage }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
// 使用简化的别名
import { Icon } from './index'

const loadMessage = ref('')

const handleLoad = (iconName: string) => {
  loadMessage.value = `图标 ${iconName} 加载成功！`
  setTimeout(() => {
    loadMessage.value = ''
  }, 3000)
}

const handleError = (error: unknown) => {
  const errorMessage = error instanceof Error ? error.message : String(error)
  loadMessage.value = `图标加载失败: ${errorMessage}`
  setTimeout(() => {
    loadMessage.value = ''
  }, 3000)
}
</script>

<style scoped>
.iconify-example-container {
  padding: 20px;
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007acc;
  padding-bottom: 8px;
}

.icon-row {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 15px 0;
  flex-wrap: wrap;
}

.note {
  color: #666;
  font-size: 14px;
  background: #fff3cd;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #ffc107;
}

.message {
  margin-top: 10px;
  padding: 8px 12px;
  background: #d4edda;
  color: #155724;
  border-radius: 4px;
  border-left: 4px solid #28a745;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .iconify-example-container {
    padding: 10px;
  }
  
  .example-section {
    padding: 15px;
  }
  
  .icon-row {
    gap: 10px;
  }
}
</style>