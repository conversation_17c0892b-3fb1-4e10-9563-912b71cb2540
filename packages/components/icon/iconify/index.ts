import IconifyIcon from './src/iconify.vue'
import type { App } from 'vue'

// 组件的 props 类型定义
export interface IconProps {
    icon: string
    size?: string | number
    color?: string
    opacity?: string | number
    inline?: boolean
    rotate?: number | string
    horizontalFlip?: boolean
    verticalFlip?: boolean
    offline?: boolean
    prefix?: string
    tip?: string
    tipPlacement?: 'top' | 'bottom' | 'left' | 'right'
}

// 支持按需导入 - 提供多个别名
export { IconifyIcon as Icon }

// 支持全局注册
export default {
    install(app: App) {
        app.component('Icon', IconifyIcon)
    }
}

// 导出组件类型
export type IconInstance = InstanceType<typeof IconifyIcon>

// 保持原有导出以向后兼容
export { IconifyIcon }

// 类型别名
export type { IconProps as IconifyIconProps } // 向后兼容
export type IconifyIconInstance = InstanceType<typeof IconifyIcon> // 向后兼容 