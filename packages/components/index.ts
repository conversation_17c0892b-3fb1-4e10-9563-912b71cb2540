/**
 * CRF组件库主入口
 * 使用简化的组件注册系统
 */

import type { App } from 'vue'
import { componentRegistry, initializeRegistry } from './registry'
import { COMPONENT_CONFIGURATIONS, getAllComponentConfigurations } from './registry/components-config'

// 导出所有组件
export { CrfBaseComponent } from './base'
export { CrfCard } from './card'
export { CrfIcon, CrfSvgIcon } from './icon'
export { CrfIconColorPicker } from './icon-color-picker'
export { CrfText } from './text'
export { CrfTextarea } from './textarea'
export { CrfRadio } from './radio'
export { CrfCheckbox } from './checkbox'
export { CrfSelect } from './select'
export { CrfNumber } from './number'
export { CrfDateRange } from './date-range'
export { CrfDate } from './date'
export { CrfTimeRange } from './time-range'
export { CrfTime } from './time'

// 导出注册系统
export {
  ComponentRegistry,
  componentRegistry,
  ComponentCategory,
  registerComponent,
  getComponent,
  getComponentMeta,
  searchComponents,
  initializeRegistry
} from './registry'

// 导出类型
export type {
  ComponentMeta,
  ComponentRegistration,
  ComponentSearchOptions,
  ComponentSearchResult,
  ComponentRegistrationOptions
} from './registry'

// 导出组件配置
export { COMPONENT_CONFIGURATIONS, getAllComponentConfigurations } from './registry/components-config'

// 导出Schema
export { default as CrfTextSchema } from './text/schema'
export { default as CrfTextareaSchema } from './textarea/schema'
export { default as CrfRadioSchema } from './radio/schema'
export { default as CrfCheckboxSchema } from './checkbox/schema'
export { default as CrfSelectSchema } from './select/schema'
export { default as CrfNumberSchema } from './number/schema'
export { default as CrfDateRangeSchema } from './date-range/schema'
export { default as CrfDateSchema } from './date/schema'
export { default as CrfTimeRangeSchema } from './time-range/schema'
export { default as CrfTimeSchema } from './time/schema'

// 导出组件类型
export type { IconColorPickerProps, IconColorPickerEmits } from './icon-color-picker'
export type { CrfTextProps, CrfTextConfig } from '@crf/types'
export type { CrfTextareaProps, CrfTextareaConfig } from '@crf/types'
export type { CrfRadioProps, CrfRadioConfig, RadioOption } from '@crf/types'
export type { CrfCheckboxProps, CrfCheckboxConfig, CheckboxOption } from '@crf/types'
export type { CrfSelectProps, CrfSelectConfig, SelectOption, SelectOptionGroup } from '@crf/types'
export type { CrfNumberProps, CrfNumberConfig } from '@crf/types'
export type { CrfDateRangeProps, CrfDateRangeConfig, DateRangeValue } from '@crf/types'
export type { CrfDateProps, CrfDateConfig, DateValue } from '@crf/types'
export type { CrfTimeRangeProps, CrfTimeRangeConfig, TimeRangeValue } from '@crf/types'
export type { CrfTimeProps, CrfTimeConfig, TimeValue } from '@crf/types'

// 编辑器需要的组件配置 - 兼容旧版本
export const COMPONENTS = Object.entries(COMPONENT_CONFIGURATIONS)
  .filter(([_, config]) => config.meta.visible !== false && 'schema' in config && config.schema)
  .map(([code, config]) => ({
    code,
    name: config.meta.name,
    label: config.meta.label,
    icon: config.meta.icon,
    iconColor: config.meta.iconColor,
    component: config.component,
    schema: (config as Record<string, unknown>).schema
  }))

// 获取组件块配置 - 兼容旧版本
export const getComponentBlocks = () => {
  return COMPONENTS.map(comp => ({
    code: comp.code,
    name: comp.name,
    label: comp.label,
    icon: comp.icon,
    iconColor: comp.iconColor,
    component: comp.component
  }))
}

// 导出Schema集合 - 兼容旧版本
export const crfComponentSchemas = Object.fromEntries(
  Object.entries(COMPONENT_CONFIGURATIONS)
    .filter(([_, config]) => 'schema' in config && config.schema)
    .map(([code, config]) => [code, (config as Record<string, unknown>).schema])
)

// 导出schema别名 - 兼容旧版本
export const schema = crfComponentSchemas

// 插件安装函数
const install = (app: App) => {
  // 初始化注册器
  initializeRegistry()
  
  // 注册所有组件
  getAllComponentConfigurations().forEach(([code, config]) => {
    componentRegistry.register(code, config.component, config.meta, config.options)
  })
  
  // 安装所有组件到Vue应用
  componentRegistry.installAll(app)
}

// 导出安装函数
export { install }

// 默认导出 - 用于Vue.use()
export default { install }
