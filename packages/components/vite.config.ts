import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  build: {
    lib: {
      entry: resolve(__dirname, 'index.ts'),
      name: 'CrfComponents',
      fileName: 'index'
    },
    rollupOptions: {
      external: [
        'vue',
        'naive-ui',
        '@crf/types',
        '@crf/utils',
        '@crf/hooks',
        '@crf/constants'
      ],
      output: {
        exports: 'named',
        globals: {
          vue: 'Vue',
          'naive-ui': 'NaiveUI',
          '@crf/types': 'CrfTypes',
          '@crf/utils': 'CrfUtils',
          '@crf/hooks': 'CrfHooks',
          '@crf/constants': 'CrfConstants'
        }
      }
    }
  }
})