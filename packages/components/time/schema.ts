/**
 * CrfTime 组件 Schema 定义
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

export const CrfTimeConfigSchema = createComponentSchema({
    title: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件标题',
        default: '时间',
        placeholder: '请输入组件标题',
        description: '显示在组件上方的标题文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    description: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件描述',
        default: '请选择时间',
        placeholder: '请输入组件描述',
        description: '显示在组件下方的描述文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    placeholder: Type.Optional(Type.String({
        code: 'config-input',
        label: '占位符文本',
        default: '请选择时间',
        showInConfig: true,
        configGroup: 'basic'
    })),

    format: Type.Optional(Type.String({
        code: 'config-input',
        label: '时间格式',
        default: 'HH:mm:ss',
        showInConfig: true,
        configGroup: 'format'
    })),

    size: Type.Optional(Type.Union([
        Type.Literal('small'),
        Type.Literal('medium'),
        Type.Literal('large')
    ], {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        enumNames: ['小', '中', '大'],
        showInConfig: true,
        configGroup: 'appearance'
    })),

    required: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '必填项',
        default: false,
        showInConfig: true,
        configGroup: 'validation'
    })),

    medicalType: Type.Optional(Type.Union([
        Type.Literal('general'),
        Type.Literal('medication'),
        Type.Literal('procedure'),
        Type.Literal('vital'),
        Type.Literal('monitoring')
    ], {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        enumNames: ['通用', '用药时间', '操作时间', '生命体征', '监控时间'],
        showInConfig: true,
        configGroup: 'medical'
    })),

    fieldCode: Type.Optional(Type.String({
        code: 'config-input',
        label: '字段编码',
        default: '',
        showInConfig: true,
        configGroup: 'medical'
    }))
})

export type CrfTimeSchema = Static<typeof CrfTimeConfigSchema>
export type CrfTimeConfig = CrfTimeSchema
export default CrfTimeConfigSchema