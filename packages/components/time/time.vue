<template>
  <div 
    class="crf-base-container"
    :class="{
      'crf-state-editing': editing,
      'crf-state-disabled': shouldDisableInEditMode,
      'crf-state-readonly': readonly,
      'crf-state-required': required
    }"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{ title || '时间选择' }}</span>
        <span v-if="required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="description" class="crf-base-description">
        {{ description }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="crf-base-content">
      <n-time-picker
        ref="timeRef"
        v-model:value="internalValue"
        :placeholder="placeholder || '请选择时间'"
        :format="format || 'HH:mm:ss'"
        :size="nativeSize"
        :clearable="clearable"
        :disabled="shouldDisableInEditMode"
        class="u-w-full"
        @update:value="handleChange"
        @blur="handleTimeBlur"
        @focus="handleTimeFocus"
      />
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject, nextTick } from 'vue'
import type { TimeValue } from '@crf/types'
import { NTimePicker } from 'naive-ui'
import { EditorMode } from '@crf/types/core'

defineOptions({ name: 'CrfTime' })

const props = withDefaults(defineProps<{
  modelValue?: TimeValue
  title?: string
  description?: string
  placeholder?: string
  format?: string
  size?: 'small' | 'default' | 'large'
  editable?: boolean
  clearable?: boolean
  medicalType?: 'general' | 'medication' | 'procedure' | 'vital' | 'monitoring'
  fieldCode?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  visible?: boolean
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
}>(), {
  modelValue: null,
  title: '时间',
  description: '请选择时间',
  placeholder: '请选择时间',
  format: 'HH:mm:ss',
  size: 'default' as const,
  editable: true,
  clearable: true,
  medicalType: 'general' as const,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: TimeValue]
  'change': [value: TimeValue]
  'visible-change': [visible: boolean]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'click': [event: MouseEvent]
  'validate': [result: { isValid: boolean; errors: string[] }]
}>()

const validationState = ref({ status: '', message: '' })

// 内部值管理 - 使用number类型来匹配n-time-picker的期望
const internalValue = ref<number | null>(null)

// 同步 props.modelValue 到 internalValue
watch(() => props.modelValue, (newValue) => {
  if (typeof newValue === 'string' && newValue) {
    const [hours, minutes, seconds] = newValue.split(':').map(Number);
    const date = new Date(1970, 0, 1, hours ?? 0, minutes ?? 0, seconds ?? 0);
    internalValue.value = date.getTime();
  } else {
    internalValue.value = null;
  }
}, { immediate: true });

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue) => {
  if (newValue != null) {
    const date = new Date(newValue);
    const timeString = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    emit('update:modelValue', timeString as TimeValue);
  } else {
    emit('update:modelValue', null as TimeValue);
  }
});

const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const timeRef = ref()
const originalValue = ref<number | null>(null)

const validate = async (value: number | null) => {
  const errors: string[] = []
  if (props.required && value == null) {
    errors.push('请选择时间')
  }
  const isValid = errors.length === 0
  validationState.value = {
    status: isValid ? 'success' : 'error',
    message: errors[0] || ''
  }
  return { isValid, errors }
}

const clearValidation = () => {
  validationState.value = { status: '', message: '' }
}

// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'default') {
    return 'medium'
  }
  return props.size
})


const shouldDisableInEditMode = computed(() => {
  const editorMode = (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined)
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined),
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = null // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)



const startEdit = () => {
  if (props.disabled || props.readonly) return
  editing.value = true
  originalValue.value = internalValue.value
  nextTick(() => timeRef.value?.focus())
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  if (validationResult.isValid) {
    editing.value = false
  }
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
  editing.value = false
  clearValidation()
}

const handleChange = async (value: number | null) => {
  await validate(value);
  if (value != null) {
    const date = new Date(value);
    const timeString = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    emit('change', timeString as TimeValue);
  } else {
    emit('change', null as TimeValue);
  }
}


const handleTimeBlur = async (event: FocusEvent) => {
  await validate(internalValue.value)
  emit('blur', event)
}

const handleTimeFocus = (event: FocusEvent) => emit('focus', event)

watch(() => props.modelValue, async (newValue) => {
  if (newValue && internalValue.value != null) {
    await validate(internalValue.value)
  }
})

defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation,

})
</script>

<style scoped>
@use '../shared/index.scss';

/* Naive UI 组件样式覆盖 */
:deep(.n-time-picker) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border);
  --n-border-radius: var(--crf-border-radius);
  --n-line-height: var(--crf-line-height-base);
  --n-padding: var(--crf-spacing-2) var(--crf-spacing-3);
  --n-height: var(--crf-size-control-base);
}
</style>