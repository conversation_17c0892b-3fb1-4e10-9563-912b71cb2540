/**
 * 核心组件类型定义 - 完全类型安全
 */

import type { ComponentType, FormFieldValue } from '@crf/types/core'
import type { ComponentConfig, ComponentProps, ComponentValidationConfig, ComponentLayoutConfig, ComponentMetadata } from '@crf/types/editor'

// 组件定义 - 完全类型安全
export interface ComponentDefinition<T extends ComponentType = ComponentType> {
  readonly type: T
  readonly code: string
  readonly name: string
  readonly description: string
  readonly icon?: string
  readonly iconColor?: string
  readonly category: string
  readonly tags: readonly string[]
  readonly defaultProps: ComponentProps
  readonly configSchema: readonly ConfigFieldSchema[]
  readonly preview?: string
  readonly component: Record<string, unknown> // Vue组件
  readonly configComponent?: Record<string, unknown> // 配置组件
}

// 配置字段Schema - 完全类型安全
export interface ConfigFieldSchema {
  readonly key: string
  readonly label: string
  readonly type: ConfigFieldType
  readonly defaultValue?: FormFieldValue
  readonly options?: readonly ConfigOption[]
  readonly rules?: readonly ValidationRule[]
  readonly description?: string
  readonly placeholder?: string
  readonly visible?: boolean | ((props: ComponentProps) => boolean)
  readonly disabled?: boolean | ((props: ComponentProps) => boolean)
  readonly group?: string
}

// 配置字段类型枚举
export enum ConfigFieldType {
  INPUT = 'input',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  SWITCH = 'switch',
  NUMBER = 'number',
  COLOR = 'color',
  SLIDER = 'slider',
  DATE = 'date',
  DATETIME = 'datetime',
  FILE = 'file'
}

// 配置选项 - 完全类型安全
export interface ConfigOption {
  readonly label: string
  readonly value: FormFieldValue
  readonly disabled?: boolean
}

// 验证规则 - 完全类型安全
export interface ValidationRule {
  readonly type: 'required' | 'pattern' | 'min' | 'max' | 'minLength' | 'maxLength' | 'email' | 'url' | 'number' | 'integer' | 'phone' | 'custom'
  readonly message: string
  readonly value?: FormFieldValue
  readonly trigger?: 'blur' | 'change' | 'input' | 'submit'
}

// 组件实例 - 完全类型安全
export interface ComponentInstance {
  readonly id: string
  readonly config: ComponentConfig
  readonly validate: () => ValidationResult
  readonly clearValidation: () => void
  readonly reset: () => void
  readonly focus: () => void
  readonly blur: () => void
  readonly updateProps: (props: Partial<ComponentProps>) => void
  readonly updateValidation: (validation: Partial<ComponentValidationConfig>) => void
  readonly updateLayout: (layout: Partial<ComponentLayoutConfig>) => void
  readonly updateMetadata: (metadata: Partial<ComponentMetadata>) => void
}

// 验证结果 - 完全类型安全
export interface ValidationResult {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly status: 'success' | 'warning' | 'error' | 'validating'
}

// 组件注册配置 - 完全类型安全
export interface ComponentRegistration<T extends ComponentType = ComponentType> {
  readonly type: T
  readonly definition: ComponentDefinition<T>
  enabled: boolean
  order: number
}

// 组件分类 - 完全类型安全
export interface ComponentCategory {
  readonly name: string
  readonly label: string
  readonly description?: string
  readonly icon?: string
  order: number
  components: ComponentType[]
}

// 组件搜索选项 - 完全类型安全
export interface ComponentSearchOptions {
  readonly query?: string
  readonly category?: string
  readonly tags?: readonly string[]
  readonly enabled?: boolean
  readonly limit?: number
  readonly offset?: number
}

// 组件搜索结果 - 完全类型安全
export interface ComponentSearchResult {
  readonly components: readonly ComponentDefinition[]
  readonly total: number
  readonly hasMore: boolean
  readonly categories: readonly ComponentCategory[]
}