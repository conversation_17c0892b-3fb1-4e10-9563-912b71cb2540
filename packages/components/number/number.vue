<template>
  <div 
    class="crf-base-container"
    :class="{
      'crf-state-editing': editing,
      'crf-state-disabled': shouldDisableInEditMode,
      'crf-state-readonly': readonly,
      'crf-state-required': required
    }"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{ props.title || '数字输入' }}</span>
        <span v-if="props.required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="props.description" class="crf-base-description">
        {{ props.description }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="crf-base-content">
      <div class="u-flex u-items-center u-gap-2 u-w-full">
        <n-input-number
          ref="numberRef"
          v-model:value="internalValue"
          :placeholder="props.placeholder"
          :min="props.min"
          :max="props.max"
          :step="props.step"
          :precision="props.precision"
          :size="nativeSize"
          :show-button="props.controls"
          :button-placement="nativeControlsPosition"
          :disabled="shouldDisableInEditMode"
          :readonly="false"
          :status="validationState.status === 'error' ? 'error' : undefined"
          class="u-flex-1 u-min-w-0"
          @update:value="handleChange"
          @blur="handleNumberBlur"
          @focus="handleNumberFocus"
        />
        
        <!-- 单位显示 -->
        <span v-if="props.unit" class="u-text-secondary u-text-sm u-font-normal u-whitespace-nowrap u-px-1 u-bg-muted u-rounded u-border">
          {{ props.unit }}
        </span>
      </div>
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject, nextTick } from 'vue'
import type { CrfNumberProps } from '@crf/types'
import { NInputNumber } from 'naive-ui'
import { EditorMode } from '@crf/types/core'

// 定义组件名称
defineOptions({
  name: 'CrfNumber'
})

const props = withDefaults(defineProps<CrfNumberProps>(), {
  modelValue: undefined,
  title: '数字输入',
  description: '请输入数字',
  placeholder: '请输入数字',
  min: undefined,
  max: undefined,
  step: 1,
  stepStrictly: false,
  precision: undefined,
  size: 'medium' as const,
  controls: true,
  controlsPosition: 'right' as const,
  valueOnClear: null,
  unit: '',
  medicalType: 'general' as const,
  fieldCode: undefined,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: number | undefined]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'input': [value: number]
  'change': [value: number | undefined]
  'click': [event: MouseEvent]
  'validate': [result: { isValid: boolean; errors: string[] }]
}>()

// ===== 验证状态 =====
const validationState = ref({
  status: '',
  message: ''
})

// 验证函数
const validate = async (value: number | undefined) => {
  const errors: string[] = []
  
  // 必填验证
  if (props.required && (value === undefined || value === null)) {
    errors.push('请输入数字')
  }
  
  // 数值范围验证
  if (value !== undefined && value !== null) {
    if (props.min !== undefined && value < props.min) {
      errors.push(`数值不能小于${props.min}`)
    }
    
    if (props.max !== undefined && value > props.max) {
      errors.push(`数值不能大于${props.max}`)
    }
    
    // 步长验证
    if (props.stepStrictly && props.step && props.min !== undefined) {
      const diff = value - props.min
      if (diff % props.step !== 0) {
        errors.push(`数值必须是${props.step}的倍数`)
      }
    }
    
    // 精度验证
    if (props.precision !== undefined) {
      const decimal = value.toString().split('.')[1]
      if (decimal && decimal.length > props.precision) {
        errors.push(`小数位数不能超过${props.precision}位`)
      }
    }
  }
  
  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0]
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: ''
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: ''
  }
}

// 映射controlsPosition到Element Plus期望的类型


// 映射size到Element Plus期望的类型


// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'medium') {
    return 'medium'
  }
  return props.size
})

// 映射controlsPosition到Naive UI期望的类型
const nativeControlsPosition = computed(() => {
  return 'right' as const // Naive UI 只支持 'right' 类型
})

// ===== 编辑器状态管理 =====
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const numberRef = ref<InstanceType<typeof NInputNumber>>()
const originalValue = ref<number | undefined>(undefined)

// 计算是否在编辑器编辑模式下且组件被禁用

const shouldDisableInEditMode = computed(() => {
  const editorMode = (editStore as any)?.mode?.value ?? (editStore as any)?.mode
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => (editStore as any)?.mode?.value ?? (editStore as any)?.mode,
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = undefined // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)

const isComponentEditable = computed(() => {
  const mode = (editStore as any)?.mode
  if (mode === 'preview') return true
  if (mode === 'publish') return true
  if (mode === EditorMode.EDIT) return false
  return true
})

// 内部值管理
const internalValue = ref<number | undefined>(undefined)

// 同步 props.modelValue 到 internalValue
watch(() => props.modelValue, (newValue) => {
  // 处理数据清空的情况
  if (newValue === undefined || newValue === null) {
    internalValue.value = undefined
  } else {
    internalValue.value = newValue
  }
}, { immediate: true })

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue) => {
  if (isComponentEditable.value) {
    emit('update:modelValue', newValue)
  }
})

// ===== 编辑功能 =====
const startEdit = () => {
  if (props.disabled || props.readonly || !isComponentEditable.value) return
  
  editing.value = true
  originalValue.value = internalValue.value
  
  nextTick(() => {
    numberRef.value?.focus()
  })
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  
  if (validationResult.isValid) {
    editing.value = false
  } else {
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }
  
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
  editing.value = false
  clearValidation()
}

// ===== 事件处理 =====
const handleChange = async (value: number | null) => {
  const normalizedValue = value === null ? undefined : value
  await validate(normalizedValue)
  emit('change', normalizedValue)
  
  if (normalizedValue !== undefined) {
    emit('input', normalizedValue)
  }
}

const handleNumberBlur = async (event: FocusEvent) => {
  await validate(internalValue.value)
  
  // 延迟关闭编辑状态
  setTimeout(() => {
    finishEdit()
  }, 100)
  
  emit('blur', event)
}

const handleNumberFocus = (event: FocusEvent) => {
  emit('focus', event)
}



// ===== 监听器 =====
watch(() => props.modelValue, async (newValue) => {
  if (newValue !== undefined) {
    await validate(newValue)
  }
})

// ===== 对外暴露的API =====
defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation,

})
</script>

<style scoped>
@use '../shared/index.scss';

/* Naive UI 组件样式覆盖 */
:deep(.n-input-number) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border);
  --n-border-radius: var(--crf-border-radius);
  --n-line-height: var(--crf-line-height-base);
  --n-padding: var(--crf-spacing-2) var(--crf-spacing-3);
  --n-height: var(--crf-size-control-base);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .u-flex {
    flex-direction: column;
    align-items: stretch;
    gap: var(--crf-spacing-2);
  }
  
  .u-whitespace-nowrap {
    align-self: flex-start;
  }
}
</style>