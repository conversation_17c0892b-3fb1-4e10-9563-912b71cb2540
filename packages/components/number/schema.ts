/**
 * CrfNumber 组件 Schema 定义
 * 
 * 使用 TypeBox 定义组件的配置 Schema，支持运行时验证和类型推导
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

/**
 * CrfNumber 组件配置 Schema
 */
export const CrfNumberConfigSchema = createComponentSchema({
    // =============================================================================
    // 基础设置
    // =============================================================================
    title: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件标题',
        default: '数字输入',
        placeholder: '请输入组件标题',
        description: '显示在组件上方的标题文本',
        showInConfig: true,
        configGroup: 'basic',
        helpIcon: true,
        validation: {
            required: true,
            message: '组件标题不能为空'
        }
    })),

    description: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件描述',
        default: '请输入数字',
        placeholder: '请输入组件描述',
        description: '显示在组件下方的描述文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    placeholder: Type.Optional(Type.String({
        code: 'config-input',
        label: '占位符文本',
        default: '请输入数字',
        placeholder: '请输入占位符文本',
        description: '输入框为空时显示的提示文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    // =============================================================================
    // 数值设置
    // =============================================================================
    min: Type.Optional(Type.Number({
        code: 'config-number',
        label: '最小值',
        default: undefined,
        placeholder: '请输入最小值',
        description: '允许输入的最小数值',
        showInConfig: true,
        configGroup: 'validation'
    })),

    max: Type.Optional(Type.Number({
        code: 'config-number',
        label: '最大值',
        default: undefined,
        placeholder: '请输入最大值',
        description: '允许输入的最大数值',
        showInConfig: true,
        configGroup: 'validation'
    })),

    step: Type.Optional(Type.Number({
        code: 'config-number',
        label: '步长',
        default: 1,
        placeholder: '请输入步长',
        minimum: 0.01,
        description: '数值增减的步长',
        showInConfig: true,
        configGroup: 'validation'
    })),

    precision: Type.Optional(Type.Number({
        code: 'config-number',
        label: '小数位数',
        default: undefined,
        placeholder: '请输入小数位数',
        minimum: 0,
        maximum: 10,
        description: '保留的小数位数，不设置表示不限制',
        showInConfig: true,
        configGroup: 'validation'
    })),

    // =============================================================================
    // 功能设置
    // =============================================================================
    stepStrictly: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '严格步长',
        default: false,
        description: '是否只能输入步长的倍数',
        showInConfig: true,
        configGroup: 'feature'
    })),

    controls: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '显示控制按钮',
        default: true,
        description: '是否显示增减按钮',
        showInConfig: true,
        configGroup: 'feature'
    })),

    controlsPosition: Type.Optional(Type.Union([
        Type.Literal('default'),
        Type.Literal('right')
    ], {
        code: 'config-select',
        label: '控制按钮位置',
        default: 'default',
        description: '增减按钮的位置',
        enumNames: ['默认', '右侧'],
        showInConfig: true,
        configGroup: 'feature',
        showWhen: {
            field: 'controls',
            value: true
        }
    })),

    // =============================================================================
    // 外观设置
    // =============================================================================
    size: Type.Optional(Type.Union([
        Type.Literal('small'),
        Type.Literal('medium'),
        Type.Literal('large')
    ], {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        description: '组件的显示大小',
        enumNames: ['小', '中', '大'],
        showInConfig: true,
        configGroup: 'appearance'
    })),

    unit: Type.Optional(Type.String({
        code: 'config-input',
        label: '数值单位',
        default: '',
        placeholder: '如：kg、cm、次等',
        description: '显示在数值后面的单位',
        showInConfig: true,
        configGroup: 'appearance'
    })),

    // =============================================================================
    // 验证设置
    // =============================================================================
    required: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '必填项',
        default: false,
        description: '是否为必填字段',
        showInConfig: true,
        configGroup: 'validation'
    })),

    // =============================================================================
    // 医疗相关字段
    // =============================================================================
    medicalType: Type.Optional(Type.Union([
        Type.Literal('general'),
        Type.Literal('vital'),
        Type.Literal('lab'),
        Type.Literal('measurement'),
        Type.Literal('dosage'),
        Type.Literal('duration')
    ], {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        description: '医疗数据的类型分类',
        enumNames: ['通用', '生命体征', '检验值', '测量值', '药物剂量', '持续时间'],
        showInConfig: true,
        configGroup: 'medical'
    })),

    fieldCode: Type.Optional(Type.String({
        code: 'config-input',
        label: '字段编码',
        default: '',
        placeholder: '请输入字段编码',
        description: '用于数据收集和导出的字段编码',
        showInConfig: true,
        configGroup: 'medical'
    }))
})

/**
 * 从 Schema 推导的类型
 */
export type CrfNumberSchema = Static<typeof CrfNumberConfigSchema>

/**
 * 组件配置类型别名
 */
export type CrfNumberConfig = CrfNumberSchema

/**
 * 默认导出
 */
export default CrfNumberConfigSchema