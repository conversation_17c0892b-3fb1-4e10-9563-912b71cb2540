/**
 * 简化的组件安装器
 */

import { type App, type Component, type Plugin } from 'vue'

export type SFCWithInstall<T> = T & Plugin

/**
 * 为组件添加安装功能
 */
export const withInstall = <T extends Component>(
    main: T,
    name?: string
): SFCWithInstall<T> => {
    const component = main as SFCWithInstall<T>

    component.install = (app: App) => {
        const componentName = name || (main as { name?: string }).name

        if (!componentName) {
            console.warn('[withInstall] 组件名称未定义，跳过安装')
            return
        }

        // 注册组件
        app.component(componentName, main as Component)

        console.log(`[withInstall] 组件已安装: ${componentName}`)
    }

    return component
}

/**
 * 生成组件名称
 */
function generateComponentName(code: string): string {
    return `Crf${code.split('-').map(part =>
        part.charAt(0).toUpperCase() + part.slice(1)
    ).join('')}`
}

/**
 * 为组件添加安装功能 (使用组件代码作为名称)
 */
export const withInstallByCode = <T extends Component>(
    main: T,
    code: string
): SFCWithInstall<T> => {
    const componentName = generateComponentName(code)
    return withInstall(main, componentName)
}