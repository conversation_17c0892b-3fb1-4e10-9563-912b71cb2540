/**
 * 组件配置定义
 * 包含所有组件的元数据和配置信息
 */

import { ComponentCategory } from './types'

// 导入所有组件
import { CrfText } from '../text'
import { CrfTextarea } from '../textarea'
import { CrfRadio } from '../radio'
import { CrfCheckbox } from '../checkbox'
import { CrfSelect } from '../select'
import { CrfNumber } from '../number'
import { CrfDateRange } from '../date-range'
import { CrfDate } from '../date'
import { CrfTimeRange } from '../time-range'
import { CrfTime } from '../time'
// import { CrfMatrix } from '../matrix' // 临时注释，因为文件已删除
import { CrfCard } from '../card'
import { CrfIcon, CrfSvgIcon } from '../icon'
import { CrfBaseComponent } from '../base'

// 导入Schema
import textSchema from '../text/schema'
import textareaSchema from '../textarea/schema'
import radioSchema from '../radio/schema'
import checkboxSchema from '../checkbox/schema'
import selectSchema from '../select/schema'
import numberSchema from '../number/schema'
import dateRangeSchema from '../date-range/schema'
import dateSchema from '../date/schema'
import timeRangeSchema from '../time-range/schema'
import timeSchema from '../time/schema'
// import matrixSchema from '../matrix/schema' // 临时注释，因为文件已删除

// 组件注册配置
export const COMPONENT_CONFIGURATIONS = {
  'crf-text': {
    component: CrfText,
    meta: {
      code: 'crf-text',
      name: 'CrfText',
      label: '文本组件',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:text-fields',
      iconColor: '#2563eb',
      description: '单行文本输入组件，支持多种验证规则和格式化选项',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['text', 'input', 'form', '文本', '输入'],
      tags: ['form', 'input', 'basic'],
      order: 1,
      visible: true,
      props: {
        value: {
          type: 'string',
          default: '',
          required: false,
          description: '输入值'
        },
        placeholder: {
          type: 'string',
          default: '请输入内容',
          required: false,
          description: '占位符文本'
        },
        disabled: {
          type: 'boolean',
          default: false,
          required: false,
          description: '是否禁用'
        },
        readonly: {
          type: 'boolean',
          default: false,
          required: false,
          description: '是否只读'
        },
        maxLength: {
          type: 'number',
          default: undefined,
          required: false,
          description: '最大长度'
        }
      },
      events: {
        'update:value': {
          description: '值变化时触发',
          payload: 'string'
        },
        change: {
          description: '值改变时触发',
          payload: 'string'
        },
        blur: {
          description: '失去焦点时触发'
        },
        focus: {
          description: '获得焦点时触发'
        }
      }
    },
    schema: textSchema,
    options: {
      autoInstall: true,
      order: 1
    }
  },

  'crf-textarea': {
    component: CrfTextarea,
    meta: {
      code: 'crf-textarea',
      name: 'CrfTextarea',
      label: '多行文本',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:notes',
      iconColor: '#10B981',
      description: '多行文本输入组件，支持自动调整高度和字数统计',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['textarea', 'text', 'multiline', '多行', '文本域'],
      tags: ['form', 'input', 'multiline'],
      order: 2,
      visible: true,
      props: {
        value: {
          type: 'string',
          default: '',
          required: false,
          description: '输入值'
        },
        placeholder: {
          type: 'string',
          default: '请输入内容',
          required: false,
          description: '占位符文本'
        },
        disabled: {
          type: 'boolean',
          default: false,
          required: false,
          description: '是否禁用'
        },
        readonly: {
          type: 'boolean',
          default: false,
          required: false,
          description: '是否只读'
        },
        rows: {
          type: 'number',
          default: 3,
          required: false,
          description: '行数'
        },
        maxLength: {
          type: 'number',
          default: undefined,
          required: false,
          description: '最大长度'
        },
        autosize: {
          type: 'boolean',
          default: false,
          required: false,
          description: '是否自动调整高度'
        }
      },
      events: {
        'update:value': {
          description: '值变化时触发',
          payload: 'string'
        },
        change: {
          description: '值改变时触发',
          payload: 'string'
        },
        blur: {
          description: '失去焦点时触发'
        },
        focus: {
          description: '获得焦点时触发'
        }
      }
    },
    schema: textareaSchema,
    options: {
      autoInstall: true,
      order: 2
    }
  },

  'crf-radio': {
    component: CrfRadio,
    meta: {
      code: 'crf-radio',
      name: 'CrfRadio',
      label: '单选框',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:radio-button-checked',
      iconColor: '#3B82F6',
      description: '单选框组件，用于在多个选项中选择一个',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['radio', 'choice', 'selection', '单选', '选择'],
      tags: ['form', 'input', 'choice'],
      order: 3,
      visible: true
    },
    schema: radioSchema,
    options: {
      autoInstall: true,
      order: 3
    }
  },

  'crf-checkbox': {
    component: CrfCheckbox,
    meta: {
      code: 'crf-checkbox',
      name: 'CrfCheckbox',
      label: '复选框',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:check-box',
      iconColor: '#059669',
      description: '复选框组件，用于在多个选项中选择多个',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['checkbox', 'multiple', 'selection', '复选', '多选'],
      tags: ['form', 'input', 'multiple'],
      order: 4,
      visible: true
    },
    schema: checkboxSchema,
    options: {
      autoInstall: true,
      order: 4
    }
  },

  /*
  'crf-matrix': {
    component: CrfMatrix,
    meta: {
      code: 'crf-matrix',
      name: 'CrfMatrix',
      label: '矩阵表格',
      category: ComponentCategory.ADVANCED,
      icon: 'material-symbols:view-module',
      iconColor: '#D946EF',
      description: '矩阵表格组件，用于复杂的数据录入场景',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['matrix', 'table', 'grid', '矩阵', '表格'],
      tags: ['form', 'input', 'advanced', 'table'],
      order: 11,
      visible: true
    },
    schema: matrixSchema,
    options: {
      autoInstall: true,
      order: 11
    }
  },
  */

  'crf-select': {
    component: CrfSelect,
    meta: {
      code: 'crf-select',
      name: 'CrfSelect',
      label: '下拉选择',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:list-alt',
      iconColor: '#7C3AED',
      description: '下拉选择组件，支持单选和多选模式',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['select', 'dropdown', 'choice', '下拉', '选择'],
      tags: ['form', 'input', 'dropdown'],
      order: 5,
      visible: true
    },
    schema: selectSchema,
    options: {
      autoInstall: true,
      order: 5
    }
  },

  'crf-number': {
    component: CrfNumber,
    meta: {
      code: 'crf-number',
      name: 'CrfNumber',
      label: '数字输入',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:pin-number',
      iconColor: '#F59E0B',
      description: '数字输入组件，支持精度控制、步长和范围限制',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['number', 'input', 'numeric', '数字', '输入'],
      tags: ['form', 'input', 'numeric'],
      order: 6,
      visible: true
    },
    schema: numberSchema,
    options: {
      autoInstall: true,
      order: 6
    }
  },
  'crf-date': {
    component: CrfDate,
    meta: {
      code: 'crf-date',
      name: 'CrfDate',
      label: '日期选择',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:calendar-month',
      iconColor: '#DD524C',
      description: '日期选择器',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['date', 'datetime', 'picker', '日期', '选择'],
      tags: ['form', 'input', 'date'],
      order: 7,
      visible: true
    },
    schema: dateSchema,
    options: {
      autoInstall: true,
      order: 7
    }
  },
  'crf-date-range': {
    component: CrfDateRange,
    meta: {
      code: 'crf-date-range',
      name: 'CrfDateRange',
      label: '日期范围',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:date-range',
      iconColor: '#4F46E5',
      description: '日期范围选择器',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['date', 'range', 'picker', '日期', '范围'],
      tags: ['form', 'input', 'date'],
      order: 8,
      visible: true
    },
    schema: dateRangeSchema,
    options: {
      autoInstall: true,
      order: 8
    }
  },
  'crf-time': {
    component: CrfTime,
    meta: {
      code: 'crf-time',
      name: 'CrfTime',
      label: '时间选择',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:schedule',
      iconColor: '#0EA5E9',
      description: '时间选择器',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['time', 'picker', '时间', '选择'],
      tags: ['form', 'input', 'time'],
      order: 9,
      visible: true
    },
    schema: timeSchema,
    options: {
      autoInstall: true,
      order: 9
    }
  },
  'crf-time-range': {
    component: CrfTimeRange,
    meta: {
      code: 'crf-time-range',
      name: 'CrfTimeRange',
      label: '时间范围',
      category: ComponentCategory.INPUT,
      icon: 'material-symbols:timelapse',
      iconColor: '#0284C7',
      description: '时间范围选择器',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['time', 'range', 'picker', '时间', '范围'],
      tags: ['form', 'input', 'time'],
      order: 10,
      visible: true
    },
    schema: timeRangeSchema,
    options: {
      autoInstall: true,
      order: 10
    }
  },

  'crf-card': {
    component: CrfCard,
    meta: {
      code: 'crf-card',
      name: 'CrfCard',
      label: '卡片容器',
      category: ComponentCategory.LAYOUT,
      icon: 'material-symbols:view-agenda-outline',
      iconColor: '#4B5563',
      description: '用于组织和包裹其他组件的卡片容器',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['card', 'container', 'layout', '卡片', '容器'],
      tags: ['layout', 'container'],
      order: 100,
      visible: false // 通常作为布局元素，不在组件列表中直接展示
    },
    options: {
      autoInstall: true,
      order: 100
    }
  },

  'crf-icon': {
    component: CrfIcon,
    meta: {
      code: 'crf-icon',
      name: 'CrfIcon',
      label: '图标',
      category: ComponentCategory.DISPLAY,
      icon: 'material-symbols:emoji-emotions',
      iconColor: '#FBBF24',
      description: 'Iconify 图标库的封装',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['icon', 'iconify', '图标'],
      tags: ['decoration', 'icon'],
      order: 200,
      visible: false
    },
    options: {
      autoInstall: true,
      order: 200
    }
  },

  'crf-svg-icon': {
    component: CrfSvgIcon,
    meta: {
      code: 'crf-svg-icon',
      name: 'CrfSvgIcon',
      label: 'SVG 图标',
      category: ComponentCategory.DISPLAY,
      icon: 'material-symbols:icecream',
      iconColor: '#EC4899',
      description: '本地 SVG 图标的封装',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['icon', 'svg', '图标'],
      tags: ['decoration', 'icon'],
      order: 201,
      visible: false
    },
    options: {
      autoInstall: true,
      order: 201
    }
  },

  'crf-base-component': {
    component: CrfBaseComponent,
    meta: {
      code: 'crf-base-component',
      name: 'CrfBaseComponent',
      label: '基础组件',
      category: ComponentCategory.BASIC,
      icon: 'material-symbols:auto-awesome-mosaic-outline',
      iconColor: '#9CA3AF',
      description: '所有 CRF 组件的基类，提供通用功能',
      version: '1.0.0',
      author: 'CRF Team',
      keywords: ['base', 'internal', '基础', '内部'],
      tags: ['internal'],
      order: 999,
      visible: false
    },
    options: {
      autoInstall: false,
      order: 999
    }
  }
}

// 导出组件配置的键和类型
export type ComponentConfigurationKey = keyof typeof COMPONENT_CONFIGURATIONS
export type ComponentConfiguration = typeof COMPONENT_CONFIGURATIONS[ComponentConfigurationKey]

/**
 * 获取所有组件的配置
 */
export const getAllComponentConfigurations = () => {
  return Object.entries(COMPONENT_CONFIGURATIONS)
}

/**
 * 获取所有可见的组件配置
 */
export const getVisibleComponentConfigurations = () => {
  return Object.entries(COMPONENT_CONFIGURATIONS).filter(
    ([_, config]) => config.meta.visible
  )
}

/**
 * 按类别获取组件配置
 */
export const getComponentConfigurationsByCategory = (category: ComponentCategory) => {
  return Object.entries(COMPONENT_CONFIGURATIONS).filter(
    ([_, config]) => config.meta.category === category
  )
}