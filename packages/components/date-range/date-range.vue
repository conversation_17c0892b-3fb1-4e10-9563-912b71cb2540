<template>
  <div 
    class="crf-base-container"
    :class="{
      'crf-state-editing': editing,
      'crf-state-disabled': shouldDisableInEditMode,
      'crf-state-readonly': readonly,
      'crf-state-required': required
    }"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{ props.title || '日期范围' }}</span>
        <span v-if="props.required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="props.description" class="crf-base-description">
        {{ props.description }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="crf-base-content">
      <n-date-picker
        ref="dateRangeRef"
        v-model:value="vModelCompatibleValue"
        :type="computedType"
        :start-placeholder="props.startPlaceholder || '开始日期'"
        :end-placeholder="props.endPlaceholder || '结束日期'"
        :format="props.format"
        :value-format="props.valueFormat"
        :size="nativeSize"
        :clearable="props.clearable"
        :separator="props.separator || '至'"
        :shortcuts="computedShortcuts"
        :is-date-disabled="transformedDisabledDate"
        :default-time="computedDefaultTime"
        :default-value="computedDefaultValue"
        :disabled="shouldDisableInEditMode"
        class="u-w-full"
        @update:value="handleChange"
        @blur="handleDateRangeBlur"
        @focus="handleDateRangeFocus"
      />
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject } from 'vue'
import type { CrfDateRangeProps, DateRangeValue, SingleDateValue, RangeDateValue } from '@crf/types'
import { NDatePicker } from 'naive-ui'
import { EditorMode } from '@crf/types/core'
import dayjs from 'dayjs'

// 定义组件名称
defineOptions({
  name: 'CrfDateRange'
})

const props = withDefaults(defineProps<CrfDateRangeProps>(), {
  modelValue: null,
  title: '日期范围',
  description: '请选择日期范围',
  startPlaceholder: '开始日期',
  endPlaceholder: '结束日期',
  type: 'date' as const,
  format: 'YYYY-MM-DD',
  valueFormat: 'YYYY-MM-DD',
  size: 'medium' as const,
  editable: true,
  clearable: true,
  separator: '至',
  unlinkPanels: false,
  validateEvent: true,
  medicalType: 'general' as const,
  fieldCode: undefined,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: DateRangeValue]
  'change': [value: DateRangeValue]
  'calendar-change': [value: DateRangeValue]
  'panel-change': [value: DateRangeValue, mode: string, view: string]
  'visible-change': [visible: boolean]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'click': [event: MouseEvent]
  'validate': [result: { isValid: boolean; errors: string[] }]
}>()

// ===== 验证状态 =====
const validationState = ref({
  status: '',
  message: ''
})

// 验证函数
const validate = async (value: DateRangeValue | undefined) => {
  const errors: string[] = []
  
  // 必填验证
  if (props.required && (!value || value[0] == null || value[1] == null)) {
    errors.push('请选择日期范围')
  }
  
  // 日期范围逻辑验证
  if (value && value[0] != null && value[1] != null) {
    const startDate = dayjs(value[0])
    const endDate = dayjs(value[1])
    
    if (startDate.isAfter(endDate)) {
      errors.push('开始日期不能晚于结束日期')
    }
  }
  
  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0]
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: ''
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: ''
  }
}

// ===== 编辑器状态管理 =====
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const dateRangeRef = ref<InstanceType<typeof NDatePicker>>()
const originalValue = ref<DateRangeValue>(null)

// 计算是否在编辑器编辑模式下且组件被禁用

const shouldDisableInEditMode = computed(() => {
  const editorMode = typeof editStore?.mode === 'object' && editStore?.mode !== null
    ? (editStore.mode as { value?: string }).value
    : editStore?.mode
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => {
    return typeof editStore?.mode === 'object' && editStore?.mode !== null
      ? (editStore.mode as { value?: string }).value
      : editStore?.mode
  },
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = null // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)

const isComponentEditable = computed(() => {
  const mode = typeof editStore?.mode === 'object' && editStore?.mode !== null
    ? (editStore.mode as { value?: string }).value
    : editStore?.mode
  if (mode === 'preview') return true
  if (mode === 'publish') return true
  if (mode === EditorMode.EDIT) return false
  return true
})

// 内部值管理
const internalValue = ref<DateRangeValue>(null)

// v-model 兼容层：处理 Naive UI 不接受 null 的问题
const vModelCompatibleValue = computed({
  get() {
    if (internalValue.value == null) return null;
    const convert = (v: SingleDateValue): number | null => {
      if (v == null) return null;
      if (typeof v === 'string') return dayjs(v).valueOf();
      if (typeof v === 'number') return v;
      if (v instanceof Date) return v.getTime();
      return null;
    };
    if (!Array.isArray(internalValue.value)) return null;
    const [start, end] = internalValue.value.map(convert);
    if (start !== null && end !== null) return [start, end] as [number, number];
    return null;
  },
  set(val: [number, number] | null) {
    if (val == null) {
      internalValue.value = null;
      return;
    }
    const convert = (ts: number): SingleDateValue => props.valueFormat ? dayjs(ts).format(props.valueFormat) : new Date(ts);
    internalValue.value = val.map(convert) as RangeDateValue;
  }
})

// 同步 props.modelValue 到 internalValue
watch(() => props.modelValue, (newValue) => {
  internalValue.value = newValue === undefined ? null : newValue
}, { immediate: true })

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue) => {
  if (isComponentEditable.value) {
    emit('update:modelValue', newValue)
  }
})

// 转换disabledDate函数以匹配Naive UI期望的类型
const transformedDisabledDate = computed(() => {
  if (!props.disabledDate) return undefined
  
  return (timestamp: number) => {
    const date = new Date(timestamp)
    return props.disabledDate!(date)
  }
})

// 计算默认值，转换为时间戳数组类型以匹配Naive UI期望
const computedDefaultValue = computed(() => {
  if (!props.defaultValue || !Array.isArray(props.defaultValue) || props.defaultValue.length !== 2) return undefined
  const start = props.defaultValue[0]
  const end = props.defaultValue[1]
  return [
    start instanceof Date ? start.getTime() : new Date(start as string | number).getTime(),
    end instanceof Date ? end.getTime() : new Date(end as string | number).getTime()
  ] as [number, number]
})

// 计算默认时间，转换为字符串数组类型以匹配Naive UI期望
const computedDefaultTime = computed(() => {
  if (!props.defaultTime || !Array.isArray(props.defaultTime) || props.defaultTime.length !== 2) return undefined
  const start = props.defaultTime[0]
  const end = props.defaultTime[1]
  return [
    start instanceof Date ? start.toTimeString().slice(0, 8) : (start as string | number).toString(),
    end instanceof Date ? end.toTimeString().slice(0, 8) : (end as string | number).toString()
  ] as [string, string]
})

// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  // 'default' 不在 'small' | 'medium' | 'large' 类型中，所以使用字符串比较
  if (props.size.toString() === 'default') {
    return 'medium'
  }
  return props.size as 'small' | 'medium' | 'large'
})

// 计算type，确保类型匹配Naive UI期望
const computedType = computed(() => {
  const typeMap: Record<string, 'daterange' | 'datetimerange' | 'monthrange' | 'yearrange'> = {
    'date': 'daterange',
    'datetime': 'datetimerange',
    'month': 'monthrange',
    'year': 'yearrange',
    'week': 'daterange', // Naive UI 不支持 weekrange，使用 daterange 代替
    'daterange': 'daterange',
    'datetimerange': 'datetimerange',
    'monthrange': 'monthrange',
    'yearrange': 'yearrange',
    'weekrange': 'daterange' // Naive UI 不支持 weekrange，使用 daterange 代替
  }
  return typeMap[props.type.toString()] || 'daterange'
})

// 计算快捷选项 - 转换为Naive UI期望的格式
const computedShortcuts = computed(() => {
  if (!props.shortcuts) return {}
  
  const shortcuts: Record<string, [number, number] | (() => [number, number])> = {}
  
  props.shortcuts.forEach(shortcut => {
    shortcuts[shortcut.text] = () => {
      if (typeof shortcut.value === 'function') {
        const result = shortcut.value()
        if (Array.isArray(result) && result.length === 2) {
          return [
            result[0] instanceof Date ? result[0].getTime() : new Date(result[0] as string | number).getTime(),
            result[1] instanceof Date ? result[1].getTime() : new Date(result[1] as string | number).getTime()
          ] as [number, number]
        }
        return [0, 0] as [number, number]
      }
      
      // 处理预设的快捷选项
      const today = dayjs()
      if (shortcut.text === '今天') {
        return [today.valueOf(), today.valueOf()] as [number, number]
      } else if (shortcut.text === '最近一周') {
        return [today.subtract(7, 'day').valueOf(), today.valueOf()] as [number, number]
      } else if (shortcut.text === '最近一月') {
        return [today.subtract(1, 'month').valueOf(), today.valueOf()] as [number, number]
      } else if (shortcut.text === '最近三月') {
        return [today.subtract(3, 'month').valueOf(), today.valueOf()] as [number, number]
      } else if (shortcut.text === '最近一年') {
        return [today.subtract(1, 'year').valueOf(), today.valueOf()] as [number, number]
      } else {
        if (Array.isArray(shortcut.value) && shortcut.value.length === 2) {
          return [
            shortcut.value[0] instanceof Date ? shortcut.value[0].getTime() : new Date(shortcut.value[0] as string | number).getTime(),
            shortcut.value[1] instanceof Date ? shortcut.value[1].getTime() : new Date(shortcut.value[1] as string | number).getTime()
          ] as [number, number]
        }
        return [0, 0] as [number, number]
      }
    }
  })
  
  return shortcuts
})

// ===== 编辑功能 =====
const startEdit = () => {
  if (props.disabled || props.readonly || !isComponentEditable.value) return
  
  editing.value = true
  if (internalValue.value !== null) {
    originalValue.value = internalValue.value
  }
  
  // ElDatePicker 组件不支持 focus 方法
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  
  if (validationResult.isValid) {
    editing.value = false
  } else {
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }
  
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value ?? null
  editing.value = false
  clearValidation()
}

// ===== 事件处理 =====
const handleChange = async (val: [number, number] | null) => {
  let value: DateRangeValue = null;
  if (val !== null && Array.isArray(val) && val.length === 2) {
    const convert = (ts: number): SingleDateValue => props.valueFormat ? dayjs(ts).format(props.valueFormat) : new Date(ts);
    value = val.map(convert) as RangeDateValue;
  }
  internalValue.value = value;
  await validate(value);
  emit('change', value);
}

const handleDateRangeBlur = async (event: FocusEvent) => {
  await validate(internalValue.value)
  emit('blur', event)
}

const handleDateRangeFocus = (event: FocusEvent) => {
  emit('focus', event)
}



// ===== 监听器 =====
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    await validate(newValue)
  }
})

// ===== 对外暴露的API =====
defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation,

})
</script>

<style scoped>
@use '../shared/index.scss';

/* Naive UI 组件样式覆盖 */
:deep(.n-date-picker) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border);
  --n-border-radius: var(--crf-border-radius);
  --n-line-height: var(--crf-line-height-base);
  --n-padding: var(--crf-spacing-2) var(--crf-spacing-3);
  --n-height: var(--crf-size-control-base);
}
</style>