<template>
  <div class="crf-icon-color-picker" @click.stop>
    <n-popover
      v-model:show="popoverVisible"
      placement="bottom-start"
      :style="{ maxWidth: popoverMaxWidth + 'px' }"
      trigger="click"
      @click.stop
    >
      <template #trigger>
        <div 
          class="icon-trigger" 
          :class="{ 'is-disabled': disabled }"
          @click.stop="togglePopover"
        >
          <div class="icon-display">
            <div v-if="modelValue" class="selected-icon" :style="{ color: selectedColor }">
              <n-icon :size="20">
                <component :is="modelValue" />
              </n-icon>
            </div>
            <span v-else class="placeholder">{{ placeholder }}</span>
          </div>
          <n-icon class="arrow-icon" :class="{ 'is-reverse': popoverVisible }">
            <ChevronDownOutline />
          </n-icon>
        </div>
      </template>
      
      <div class="icon-color-picker-content" :style="{ maxHeight: popoverMaxHeight + 'px' }" @click.stop>
        <div class="search-section">
          <n-input
            v-model:value="searchQuery"
            placeholder="搜索图标..."
            size="small"
            clearable
            @click.stop
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </div>
        
        <!-- 颜色选择区域 -->
        <div class="color-section" @click.stop>
          <div class="color-label">选择颜色：</div>
          <div class="color-options">
            <div v-for="(row, rowIndex) in colorOptions" :key="rowIndex" class="color-row">
              <div
                v-for="color in row.colors"
                :key="color.value"
                class="color-item"
                :class="{ 'is-selected': selectedColor === color.value }"
                :style="{ backgroundColor: color.value }"
                @click.stop="selectColor(color.value)"
                :title="color.name"
              >
                <n-icon v-if="selectedColor === color.value" class="check-icon">
                  <CheckmarkOutline />
                </n-icon>
              </div>
            </div>
          </div>
        </div>
        
        <div class="icons-grid" @click.stop>
          <div
            v-for="iconName in filteredIcons"
            :key="iconName"
            class="icon-item"
            :class="{ 'is-selected': modelValue === iconName }"
            @click.stop="selectIcon(iconName)"
          >
            <n-icon :size="18" :style="{ color: selectedColor }">
              <component :is="iconName" />
            </n-icon>
            <span class="icon-name">{{ iconName }}</span>
          </div>
        </div>
        
        <div v-if="filteredIcons.length === 0" class="no-results">
          <div class="empty-state">
            <span>未找到匹配的图标</span>
          </div>
        </div>
      </div>
    </n-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { NIcon, NInput, NPopover } from 'naive-ui'
import { ChevronDownOutline, SearchOutline, CheckmarkOutline } from '@vicons/ionicons5'
// 暂时注释掉图标库，使用自定义图标或者更小的图标集
// import * as ElementPlusIcons from '@element-plus/icons-vue'
import type { IconColorPickerProps, IconColorPickerEmits, ColorRow } from './types'

defineOptions({
  name: 'CrfIconColorPicker',
  components: {
    // 图标集合 - 暂时注释以避免依赖问题
// ...ElementPlusIcons
  }
})

const props = defineProps<IconColorPickerProps>()

const emit = defineEmits<IconColorPickerEmits>()

// Props 默认值
const modelValue = computed(() => props.modelValue || '')
const color = computed(() => props.color || '#3b82f6')
const disabled = computed(() => props.disabled || false)
const placeholder = computed(() => props.placeholder || '选择图标')
const popoverMaxWidth = computed(() => props.popoverMaxWidth || 360)
const popoverMaxHeight = computed(() => props.popoverMaxHeight || 480)

// 状态管理
const popoverVisible = ref(false)
const searchQuery = ref('')
const selectedColor = ref(color.value)
const isSelecting = ref(false)

// 颜色选项 - 重新组织为两行
const colorOptions: ColorRow[] = [
  {
    colors: [
      { name: '蓝色', value: '#3b82f6' },
      { name: '绿色', value: '#10b981' },
      { name: '红色', value: '#ef4444' },
      { name: '黄色', value: '#f59e0b' },
      { name: '紫色', value: '#8b5cf6' },
      { name: '粉色', value: '#ec4899' }
    ]
  },
  {
    colors: [
      { name: '青色', value: '#06b6d4' },
      { name: '橙色', value: '#f97316' },
      { name: '灰色', value: '#6b7280' },
      { name: '深蓝', value: '#1e40af' },
      { name: '深绿', value: '#059669' },
      { name: '深红', value: '#dc2626' }
    ]
  }
]

// Element Plus 图标列表（常用的医疗和表单相关图标）
const commonIcons = [
  // 医疗相关
  'FirstAidKit',
  'Monitor',
  'User',
  'UserFilled',
  'Avatar',
  'Postcard',
  'DocumentCopy',
  'Document',
  'DocumentAdd',
  'DocumentDelete',
  'DocumentRemove',
  'Notebook',
  'Files',
  'Folder',
  'FolderAdd',
  'FolderDelete',
  'FolderOpened',
  
  // 表单相关
  'Edit',
  'EditPen',
  'Reading',
  'List',
  'Grid',
  'Menu',
  'Operation',
  'Setting',
  'Tools',
  'Key',
  'Lock',
  'Unlock',
  'View',
  'Hide',
  'Search',
  'Filter',
  'Sort',
  
  // 状态相关
  'Check',
  'Close',
  'Plus',
  'Minus',
  'Warning',
  'WarningFilled',
  'CircleCheck',
  'CircleCheckFilled',
  'CircleClose',
  'CircleCloseFilled',
  'InfoFilled',
  'QuestionFilled',
  'SuccessFilled',
  
  // 操作相关
  'Download',
  'Upload',
  'Share',
  'Document',
  'CopyDocument',
  'Delete',
  'Refresh',
  'Back',
  'Right',
  'Top',
  'Bottom',
  'ArrowLeft',
  'ArrowRight',
  'ArrowUp',
  'ArrowDown',
  
  // 数据相关
  'DataLine',
  'DataBoard',
  'Histogram',
  'PieChart',
  'TrendCharts',
  'DataAnalysis',
  'SetUp',
  'Switch',
  'SwitchButton',
  
  // 其他常用
  'Star',
  'StarFilled',
  'Bell',
  'BellFilled',
  'Message',
  'MessageBox',
  'ChatDotRound',
  'ChatLineRound',
  'Location',
  'LocationFilled',
  'Clock',
  'Tickets',
  'Cpu',
  'Platform',
  'House',
  'HouseFilled',
  'OfficeBuilding',
  'Connection',
  'Link',
  'ScaleToOriginal',
  'FullScreen',
  'Aim',
  'Compass',
  'Position'
]

// 过滤图标
const filteredIcons = computed(() => {
  if (!searchQuery.value) {
    return commonIcons
  }
  
  const query = searchQuery.value.toLowerCase()
  return commonIcons.filter(iconName => 
    iconName.toLowerCase().includes(query)
  )
})

// 切换弹窗
const togglePopover = (event: Event) => {
  if (disabled.value || isSelecting.value) return
  
  // 阻止事件冒泡和默认行为
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()
  
  popoverVisible.value = !popoverVisible.value
}

// 选择颜色
const selectColor = (color: string) => {
  selectedColor.value = color
  emit('update:color', color)
  emit('color-change', color)
}

// 选择图标
const selectIcon = async (iconName: string) => {
  if (isSelecting.value) return
  
  isSelecting.value = true
  emit('update:modelValue', iconName)
  emit('icon-change', iconName)
  
  // 使用nextTick确保状态更新完成
  await nextTick()
  
  // 延迟关闭弹窗，给用户一个视觉反馈的时间
  setTimeout(() => {
    if (popoverVisible.value) {
      popoverVisible.value = false
      searchQuery.value = ''
    }
    isSelecting.value = false
  }, 150)
}

// 监听颜色变化
watch(color, (newColor) => {
  if (newColor) {
    selectedColor.value = newColor
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.crf-icon-color-picker {
  .icon-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    background: #ffffff;
    min-height: 32px;
    
    &:hover:not(.is-disabled) {
      border-color: #c0c4cc;
    }
    
    &.is-disabled {
      cursor: not-allowed;
      background-color: #f5f7fa;
      color: #c0c4cc;
      
      .arrow-icon {
        color: #c0c4cc;
      }
    }
    
    .icon-display {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      
      .selected-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }
      
      .placeholder {
        color: #a8abb2;
        font-size: 14px;
      }
    }
    
    .arrow-icon {
      color: #a8abb2;
      transition: transform 0.2s;
      
      &.is-reverse {
        transform: rotate(180deg);
      }
    }
  }
}
</style>

<style lang="scss">
.crf-icon-color-picker-popover {
  padding: 12px !important;
  overflow: hidden !important;
  z-index: 9999 !important;
  
  .icon-color-picker-content {
    overflow: hidden;
    
    .search-section {
      margin-bottom: 12px;
    }
    
    .color-section {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .color-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .color-options {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .color-row {
          display: flex;
          gap: 8px;
          justify-content: flex-start;
          flex-wrap: nowrap;
          
          .color-item {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid transparent;
            flex-shrink: 0;
            
            &:hover {
              transform: scale(1.1);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            
            &.is-selected {
              border-color: #ffffff;
              box-shadow: 0 0 0 2px #409eff;
              transform: scale(1.1);
            }
            
            .check-icon {
              color: #ffffff;
              font-size: 12px;
              font-weight: bold;
            }
          }
        }
      }
    }
    
    .icons-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      max-height: 280px;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
      
      .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px 4px;
        border: 1px solid transparent;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        background: #ffffff;
        
        &:hover {
          background: #f5f7fa;
          border-color: #e4e7ed;
        }
        
        &.is-selected {
          background: #ecf5ff;
          border-color: #409eff;
        }
        
        .icon-name {
          font-size: 10px;
          color: #606266;
          text-align: center;
          margin-top: 4px;
          line-height: 1.2;
          word-break: break-all;
          max-width: 100%;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        &.is-selected .icon-name {
          color: #409eff;
        }
      }
    }
    
    .no-results {
      text-align: center;
      padding: 20px;
      color: #909399;
    }
  }
}

// 全局样式，确保在对话框中的图标选择器正确显示
.el-dialog .crf-icon-color-picker-popover {
  z-index: 9999 !important;
}

// 确保弹窗不会被对话框遮罩层遮挡
.el-overlay .crf-icon-color-picker-popover {
  z-index: 9999 !important;
}
</style>