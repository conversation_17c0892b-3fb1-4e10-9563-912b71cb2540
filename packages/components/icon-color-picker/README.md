# CrfIconColorPicker 图标颜色选择器

一个功能完整的图标和颜色选择器组件，支持从 Element Plus 图标库中选择图标并自定义颜色。

## 功能特性

- 🎨 **颜色选择**: 提供12种预设颜色，支持两行布局
- 🔍 **图标搜索**: 支持实时搜索图标名称
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎯 **易于使用**: 简洁的API设计
- ⚡ **高性能**: 优化的渲染和事件处理
- 🎭 **可定制**: 支持多种配置选项

## 基本用法

```vue
<template>
  <CrfIconColorPicker 
    v-model="selectedIcon" 
    v-model:color="selectedColor"
    placeholder="选择图标"
  />
</template>

<script setup>
import { ref } from 'vue'
import { CrfIconColorPicker } from '@crf/components'

const selectedIcon = ref('Document')
const selectedColor = ref('#3b82f6')
</script>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `''` | 当前选中的图标名称 |
| `color` | `string` | `'#3b82f6'` | 当前选中的颜色 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `placeholder` | `string` | `'选择图标'` | 占位符文本 |
| `popoverMaxWidth` | `number` | `360` | 弹窗的最大宽度 |
| `popoverMaxHeight` | `number` | `480` | 弹窗的最大高度 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: string)` | 图标变化时触发 |
| `update:color` | `(value: string)` | 颜色变化时触发 |
| `icon-change` | `(value: string)` | 图标选择时触发 |
| `color-change` | `(value: string)` | 颜色选择时触发 |

### 预设颜色

组件提供以下12种预设颜色：

**第一行:**
- 蓝色: `#3b82f6`
- 绿色: `#10b981`
- 红色: `#ef4444`
- 黄色: `#f59e0b`
- 紫色: `#8b5cf6`
- 粉色: `#ec4899`

**第二行:**
- 青色: `#06b6d4`
- 橙色: `#f97316`
- 灰色: `#6b7280`
- 深蓝: `#1e40af`
- 深绿: `#059669`
- 深红: `#dc2626`

### 可用图标

组件包含100+个常用图标，涵盖：
- 医疗相关图标
- 表单操作图标
- 状态指示图标
- 数据展示图标
- 通用功能图标

## 高级用法

### 自定义弹窗尺寸

```vue
<CrfIconColorPicker 
  v-model="icon" 
  v-model:color="color"
  :popover-max-width="400"
  :popover-max-height="500"
/>
```

### 禁用状态

```vue
<CrfIconColorPicker 
  v-model="icon" 
  v-model:color="color"
  disabled
/>
```

### 监听事件

```vue
<CrfIconColorPicker 
  v-model="icon" 
  v-model:color="color"
  @icon-change="handleIconChange"
  @color-change="handleColorChange"
/>
```

## 样式定制

组件使用 CSS 变量，可以通过覆盖样式来自定义外观：

```css
.crf-icon-color-picker {
  --trigger-border-color: #e5e7eb;
  --trigger-hover-border-color: #d1d5db;
  --trigger-focus-border-color: #3b82f6;
}
```

## 注意事项

1. 组件依赖 Element Plus 图标库
2. 弹窗使用 `teleported` 模式，确保在对话框中正确显示
3. 支持键盘导航和无障碍访问
4. 自动处理事件冒泡，避免意外关闭

## 更新日志

### v1.0.0
- 初始版本发布
- 支持图标和颜色选择
- 提供完整的 TypeScript 类型支持
- 优化的用户体验和性能