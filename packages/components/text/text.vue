<template>
  <div 
    class="crf-base-container"
    :class="{
      'crf-state-disabled': disabled,
      'crf-state-readonly': readonly,
      'crf-state-required': required
    }"
    :data-medical-type="medicalType"
    @click="handleClick"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{ title || '文本输入' }}</span>
        <span v-if="required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="description" class="crf-base-description">
        {{ description || '文本输入组件' }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="crf-base-content">
      <n-input
        ref="inputRef"
        v-model:value="internalValue"
        :placeholder="placeholder || ''"
        :maxlength="maxLength || undefined"
        :minlength="minLength || undefined"
        :show-count="showCount"
        :disabled="shouldDisableInEditMode"
        :readonly="readonlyProp || false"
        :clearable="clearable"
        :size="nativeSize"
        :status="validationState.status === 'error' ? 'error' : undefined"
        @blur="handleInputBlur"
        @change="handleChange"
        @input="handleInput"
        @keyup.enter="finishEdit"
        @keyup.esc="cancelEdit"
      />
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject, onMounted, nextTick, toRefs } from 'vue'
import { NInput } from 'naive-ui'


// 定义组件名称
defineOptions({
  name: 'CrfText'
})

const props = withDefaults(defineProps<{
  modelValue?: string
  title?: string
  description?: string
  placeholder?: string
  format?: 'text' | 'email' | 'phone' | 'url' | 'number'
  minLength?: number
  maxLength?: number
  align?: 'left' | 'center' | 'right' | 'justify'
  showCount?: boolean
  clearable?: boolean
  autofocus?: boolean
  prefixIcon?: string
  suffixIcon?: string
  customErrorMessage?: string
  medicalType?: 'general' | 'vital' | 'lab' | 'medication' | 'diagnosis' | 'procedure' | 'allergy' | 'history'
  fieldCode?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  visible?: boolean
  size?: 'small' | 'medium' | 'large'
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
}>(), {
  modelValue: '',
  title: '文本输入',
  description: '请输入文本内容',
  placeholder: '请输入内容',
  format: 'text' as const,
  align: 'left' as const,
  showCount: false,
  clearable: false,
  autofocus: false,
  medicalType: 'general' as const,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  size: 'medium' as const,
  theme: 'default' as const
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'input': [value: string]
  'change': [value: string]
  'click': [event: MouseEvent]
  'validate': [result: { isValid: boolean; errors: string[] }]
}>()

// 解构props以便在模板中直接使用
const {
  title,
  description,
  placeholder,
  maxLength,
  minLength,
  showCount,
  required,
  readonly: readonlyProp
} = toRefs(props)

// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'medium') {
    return 'medium'
  }
  return props.size
})

// ===== 使用新的Hook系统 =====

// 简化的验证状态
const validationState = ref({
  status: '',
  message: ''
})

// 验证函数
const validate = async (value: string) => {
  const errors: string[] = []
  
  // 必填验证
  if (props.required && !value?.trim()) {
    errors.push('此字段为必填项')
  }
  
  // 长度验证
  if (props.minLength && value.length < props.minLength) {
    errors.push(`最少需要输入${props.minLength}个字符`)
  }
  
  if (props.maxLength && value.length > props.maxLength) {
    errors.push(`最多只能输入${props.maxLength}个字符`)
  }
  
  // 格式验证
  if (props.format && value) {
    switch (props.format) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          errors.push('请输入有效的邮箱地址')
        }
        break
      case 'phone':
        const phoneRegex = /^1[3-9]\d{9}$/
        if (!phoneRegex.test(value)) {
          errors.push('请输入有效的手机号码')
        }
        break
      case 'url':
        try {
          new URL(value)
        } catch {
          errors.push('请输入有效的网址')
        }
        break
      case 'number':
        if (isNaN(Number(value))) {
          errors.push('请输入有效的数字')
        }
        break
    }
  }
  
  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0] || '验证失败'
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: ''
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: ''
  }
}

// ===== 编辑器状态管理 =====

// 注入编辑器状态
const editStore = inject('editStore', null) as Record<string, unknown> | null

// 编辑状态
const editing = ref(false)
const inputRef = ref<InstanceType<typeof NInput>>()
const originalValue = ref('')



// 在编辑器编辑模式下，组件应该被禁用以防止用户输入
const shouldDisableInEditMode = computed(() => {
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  const mode = (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined)
  return mode === 'edit'
})

// 组件实际是否可编辑（只有在非编辑器编辑模式下或预览模式下才可编辑）
const isComponentEditable = computed(() => {
  const editorMode = (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined)
  // 预览模式下可以编辑
  if (editorMode === 'preview') return true
  // 发布模式下可以编辑
  if (editorMode === 'publish') return true
  // 编辑模式下不可编辑
  if (editorMode === 'edit') return false
  // 没有编辑器状态时可以编辑
  return true
})

// 内部值管理
const internalValue = ref('')

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => (editStore?.mode as { value: string } | undefined)?.value ?? (editStore?.mode as string | undefined),
  (newMode, oldMode) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = '' // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  }
)

// ===== 事件处理 =====

const handleInput = async (value: string) => {
  // 默认不进行实时验证
  emit('input', value)
}

const handleChange = async (value: string) => {
  // 默认在变更时进行验证
  await validate(value)
  emit('change', value)
}

const handleClick = (event: MouseEvent) => {
  // 如果组件不可编辑，则不处理点击事件
  if (!isComponentEditable.value) return
  
  // 触发点击事件
  emit('click', event)
  
  // 如果不在编辑状态，则进入编辑状态
  if (!editing.value) {
    startEdit()
  }
}





const handleInputBlur = async (event: FocusEvent) => {
  // 失焦验证
  await validate(internalValue.value)
  
  // 延迟关闭编辑状态，避免点击其他地方时立即关闭
  setTimeout(() => {
    finishEdit()
  }, 100)
  
  emit('blur', event)
}

// ===== 编辑功能 =====

const startEdit = () => {
  // 如果组件不可编辑，则不允许开始编辑
  if (!isComponentEditable.value) return
  
  // 记录原始值
  originalValue.value = internalValue.value
  // 进入编辑状态
  editing.value = true
  // 聚焦输入框
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  
  if (validationResult.isValid) {
    editing.value = false
  } else {
    // 验证失败，保持编辑状态
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }
  
  // 向父组件发送验证结果（转换为期望的格式）
  emit('validate', {
    isValid: validationResult.isValid,
    errors: validationResult.errors.map(e => e || '验证失败')
  })
  
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
  editing.value = false
  clearValidation()
}



// 在组件挂载时进行初始验证
onMounted(async () => {
  if (props.modelValue) {
    await validate(props.modelValue)
  }
})

// ===== 监听器 =====

// 监听配置变化（防止过度触发）
watch(() => props, (newProps, oldProps) => {
  // 只在真正有变化时更新
  if (JSON.stringify(newProps) !== JSON.stringify(oldProps)) {
    console.log('👀 Props配置变化:', newProps)
    // updateConfig(newProps as Record<string, unknown>)
  }
}, { deep: true })

// 监听modelValue变化进行同步和验证
watch(() => props.modelValue, async (newValue) => {
  // 同步到内部值，处理数据清空的情况
  if (newValue === undefined || newValue === null) {
    internalValue.value = ''
  } else {
    internalValue.value = newValue
  }
  
  // 进行验证
  if (newValue !== undefined) {
    await validate(newValue)
  }
}, { immediate: true })

// 监听内部值变化，向外发送更新事件
watch(internalValue, (newValue) => {
  if (isComponentEditable.value) {
    emit('update:modelValue', newValue)
  }
})

// ===== 对外暴露的API =====

defineExpose({
  // 基础状态
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),

  // 编辑控制
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  
  // 验证状态
  getValidationState: () => validationState.value,
  validate,
  clearValidation
})
</script>

<style scoped>
@use '../shared/index.scss';

/* Naive UI 输入框样式覆盖 */
:deep(.n-input) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border-primary);
  --n-border-radius: var(--crf-border-radius-sm);
  --n-height: var(--crf-input-height-md);
  --n-padding-left: var(--crf-spacing-sm);
  --n-padding-right: var(--crf-spacing-sm);
  
  width: 100%;
}

:deep(.n-input:hover) {
  --n-border-hover: 1px solid var(--crf-color-border-hover);
}

:deep(.n-input:focus-within) {
  --n-border-focus: 1px solid var(--crf-color-primary);
  --n-box-shadow-focus: 0 0 0 2px var(--crf-color-primary-light);
}

:deep(.n-input.n-input--disabled) {
  --n-color-disabled: var(--crf-color-bg-disabled);
  --n-text-color-disabled: var(--crf-color-text-disabled);
  --n-border-disabled: 1px solid var(--crf-color-border-disabled);
}

:deep(.n-input.n-input--error) {
  --n-border: 1px solid var(--crf-color-error);
  --n-border-focus: 1px solid var(--crf-color-error);
  --n-box-shadow-focus: 0 0 0 2px var(--crf-color-error-light);
}

/* 字符计数样式 */
:deep(.n-input__count) {
  color: var(--crf-color-text-secondary);
  font-size: var(--crf-font-size-xs);
}

/* 清除按钮样式 */
:deep(.n-input__clear) {
  color: var(--crf-color-text-secondary);
}

:deep(.n-input__clear:hover) {
  color: var(--crf-color-text-primary);
}
</style>