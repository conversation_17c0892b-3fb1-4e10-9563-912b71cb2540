import Text from './text.vue'
import { withInstallByCode } from '@crf/utils'

// 主要导出（新的命名规范）
export const CrfText = withInstallByCode(Text, 'crf-text')

// 默认导出
export default CrfText

// 导出组件和Schema
export { default as CrfTextComponent } from './text.vue'

export { default as CrfTextSchema } from './schema'
export type { CrfTextSchema as CrfTextSchemaType } from './schema'

// 导出类型
export type { CrfTextProps, CrfTextConfig } from '@crf/types'