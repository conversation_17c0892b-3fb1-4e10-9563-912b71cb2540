/**
 * CrfText 组件 Schema 定义
 * 
 * 使用 TypeBox 定义组件的配置 Schema，支持运行时验证和类型推导
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

/**
 * CrfText 组件配置 Schema
 */
export const CrfTextConfigSchema = createComponentSchema({
    // =============================================================================
    // 基础设置
    // =============================================================================
    title: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件标题',
        default: '文本输入',
        placeholder: '请输入组件标题',
        description: '显示在组件上方的标题文本',
        showInConfig: true,
        configGroup: 'basic',
        helpIcon: true,
        validation: {
            required: true,
            message: '组件标题不能为空'
        }
    })),

    description: Type.Optional(Type.String({
        code: 'config-input',
        label: '组件描述',
        default: '请输入文本内容',
        placeholder: '请输入组件描述',
        description: '显示在组件下方的描述文本',
        showInConfig: true,
        configGroup: 'basic'
    })),

    placeholder: Type.Optional(Type.String({
        code: 'config-input',
        label: '占位符文本',
        default: '请输入内容',
        placeholder: '请输入内容',
        description: '当输入框为空时显示的提示文本',
        showInConfig: true,
        configGroup: 'basic',
        helpIcon: true
    })),

    // =============================================================================
    // 格式和验证设置
    // =============================================================================
    format: Type.Optional(Type.Union([
        Type.Literal('text'),
        Type.Literal('email'),
        Type.Literal('phone'),
        Type.Literal('url'),
        Type.Literal('number')
    ], {
        code: 'config-select',
        label: '文本格式',
        default: 'text',
        description: '输入内容的格式验证',
        enumNames: ['普通文本', '邮箱', '手机号', '网址', '数字'],
        showInConfig: true,
        configGroup: 'validation'
    })),

    minLength: Type.Optional(Type.Number({
        code: 'config-number',
        label: '最少字符数',
        default: 0,
        placeholder: '请输入',
        minimum: 0,
        maximum: 1000,
        description: '输入内容的最少字符数',
        showInConfig: true,
        configGroup: 'validation'
    })),

    maxLength: Type.Optional(Type.Number({
        code: 'config-number',
        label: '最多字符数',
        default: 200,
        placeholder: '请输入',
        minimum: 1,
        maximum: 5000,
        description: '输入内容的最多字符数',
        showInConfig: true,
        configGroup: 'validation'
    })),

    // =============================================================================
    // 外观设置
    // =============================================================================
    align: Type.Optional(Type.Union([
        Type.Literal('left'),
        Type.Literal('center'),
        Type.Literal('right'),
        Type.Literal('justify')
    ], {
        code: 'config-select',
        label: '文本对齐',
        default: 'left',
        description: '文本对齐方式',
        enumNames: ['左对齐', '居中', '右对齐', '两端对齐'],
        showInConfig: true,
        configGroup: 'appearance'
    })),

    size: Type.Optional(Type.Union([
        Type.Literal('small'),
        Type.Literal('medium'),
        Type.Literal('large')
    ], {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        description: '组件的显示大小',
        enumNames: ['小', '中', '大'],
        showInConfig: true,
        configGroup: 'appearance'
    })),

    // =============================================================================
    // 功能设置
    // =============================================================================
    required: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '必填项',
        default: false,
        description: '是否为必填字段',
        showInConfig: true,
        configGroup: 'validation'
    })),

    showCount: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '显示字符计数',
        default: false,
        description: '是否显示当前字符数和最大字符数',
        showInConfig: true,
        configGroup: 'feature'
    })),

    clearable: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '可清空',
        default: false,
        description: '是否显示清空按钮',
        showInConfig: true,
        configGroup: 'feature'
    })),

    autofocus: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '自动获取焦点',
        default: false,
        description: '页面加载时是否自动获取焦点',
        showInConfig: true,
        configGroup: 'feature'
    })),

    // =============================================================================
    // 图标设置
    // =============================================================================
    prefixIcon: Type.Optional(Type.String({
        code: 'config-icon',
        label: '前缀图标',
        default: '',
        description: '输入框前缀图标',
        showInConfig: true,
        configGroup: 'icon'
    })),

    suffixIcon: Type.Optional(Type.String({
        code: 'config-icon',
        label: '后缀图标',
        default: '',
        description: '输入框后缀图标',
        showInConfig: true,
        configGroup: 'icon'
    })),

    // =============================================================================
    // 自定义错误设置
    // =============================================================================
    enableCustomError: Type.Optional(Type.Boolean({
        code: 'config-switch',
        label: '自定义错误提示',
        default: false,
        description: '是否启用自定义错误提示信息',
        showInConfig: true,
        configGroup: 'validation',
        helpIcon: true
    })),

    customErrorMessage: Type.Optional(Type.String({
        code: 'config-input',
        label: '错误提示内容',
        default: '',
        placeholder: '请输入自定义错误信息',
        description: '当验证失败时显示的自定义错误信息',
        showInConfig: true,
        configGroup: 'validation',
        showWhen: {
            field: 'enableCustomError',
            value: true
        }
    })),

    // =============================================================================
    // 医疗相关字段
    // =============================================================================
    medicalType: Type.Optional(Type.Union([
        Type.Literal('general'),
        Type.Literal('vital'),
        Type.Literal('lab'),
        Type.Literal('medication'),
        Type.Literal('diagnosis'),
        Type.Literal('procedure'),
        Type.Literal('allergy'),
        Type.Literal('history')
    ], {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        description: '医疗数据的类型分类',
        enumNames: ['通用', '生命体征', '检验', '用药', '诊断', '操作', '过敏', '病史'],
        showInConfig: true,
        configGroup: 'medical'
    })),

    fieldCode: Type.Optional(Type.String({
        code: 'config-input',
        label: '字段编码',
        default: '',
        placeholder: '请输入字段编码',
        description: '用于数据收集和导出的字段编码',
        showInConfig: true,
        configGroup: 'medical'
    }))
})

/**
 * 从 Schema 推导的类型
 */
export type CrfTextSchema = Static<typeof CrfTextConfigSchema>

/**
 * 组件配置类型别名
 */
export type CrfTextConfig = CrfTextSchema

/**
 * 默认导出
 */
export default CrfTextConfigSchema