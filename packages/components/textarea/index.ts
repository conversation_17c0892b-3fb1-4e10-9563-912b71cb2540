import Textarea from './textarea.vue'
import { withInstallByCode } from '@crf/utils'

// 主要导出（新的命名规范）
export const CrfTextarea = withInstallByCode(Textarea, 'crf-textarea')

// 默认导出
export default CrfTextarea

// 导出组件和Schema
export { default as CrfTextareaComponent } from './textarea.vue'

export { default as CrfTextareaSchema } from './schema'
export type { CrfTextareaSchema as CrfTextareaSchemaType } from './schema'

// 导出类型
export type { CrfTextareaProps, CrfTextareaConfig } from '@crf/types'