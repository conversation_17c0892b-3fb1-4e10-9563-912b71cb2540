/**
 * 主题工具函数
 */

import { ref, computed } from 'vue'
import type { ThemeName, Theme, ThemeContext, ThemeOptions } from './types'
import { themes } from './config'

// 全局主题状态
const currentThemeName = ref<ThemeName>('default')

/**
 * 主题管理 Composable
 */
export function useTheme() {
  // 计算当前主题
  const currentTheme = computed<Theme>(() => themes[currentThemeName.value])
  
  // 主题上下文
  const themeContext = computed<ThemeContext>(() => ({
    currentTheme: currentThemeName.value,
    theme: currentTheme.value,
    isDark: currentThemeName.value === 'dark',
    isMedical: currentThemeName.value === 'medical'
  }))

  /**
   * 切换主题
   */
  function setTheme(options: ThemeOptions | ThemeName): void {
    const config = typeof options === 'string' ? { theme: options } : options
    
    currentThemeName.value = config.theme
    
    // 持久化存储
    if (config.persistInStorage !== false) {
      localStorage.setItem('crf-theme', config.theme)
    }
    
    // 应用到文档
    if (config.applyToDocument !== false) {
      applyThemeToDocument(config.theme)
    }
    
    console.log(`主题已切换到: ${config.theme}`)
  }

  /**
   * 从存储中加载主题
   */
  function loadThemeFromStorage(): void {
    const savedTheme = localStorage.getItem('crf-theme') as ThemeName
    if (savedTheme && themes[savedTheme]) {
      setTheme({ theme: savedTheme, applyToDocument: true })
    }
  }

  /**
   * 应用主题到文档
   */
  function applyThemeToDocument(themeName: ThemeName): void {
    const theme = themes[themeName]
    const root = document.documentElement
    
    // 移除所有主题类
    Object.keys(themes).forEach(name => {
      root.classList.remove(`theme-${name}`)
    })
    
    // 添加当前主题类
    root.classList.add(`theme-${themeName}`)
    
    // 设置 CSS 变量
    const setColorVariables = (prefix: string, colors: Record<string, unknown>) => {
      Object.entries(colors).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          Object.entries(value as Record<string, unknown>).forEach(([shade, color]) => {
            root.style.setProperty(`--${prefix}-${key}-${shade}`, color as string)
          })
        } else {
          root.style.setProperty(`--${prefix}-${key}`, value as string)
        }
      })
    }
    
    setColorVariables('color', theme.colors)
  }

  /**
   * 获取颜色值
   */
  function getColor(colorPath: string): string {
    const paths = colorPath.split('.')
    let value: any = currentTheme.value.colors
    
    for (const path of paths) {
      value = value?.[path]
    }
    
    return (value as string) || ''
  }

  /**
   * 获取组件主题
   */
  function getComponentTheme(componentName: string): Record<string, unknown> {
    const components = currentTheme.value.components as Record<string, unknown> | undefined
    return components?.[componentName] as Record<string, unknown> || {} as Record<string, unknown>
  }

  /**
   * 检查是否为深色主题
   */
  function isDarkTheme(): boolean {
    return currentThemeName.value === 'dark'
  }

  /**
   * 检查是否为医疗主题
   */
  function isMedicalTheme(): boolean {
    return currentThemeName.value === 'medical'
  }

  /**
   * 切换深色模式
   */
  function toggleDarkMode(): void {
    const newTheme = isDarkTheme() ? 'default' : 'dark'
    setTheme(newTheme)
  }

  /**
   * 切换医疗主题
   */
  function toggleMedicalTheme(): void {
    const newTheme = isMedicalTheme() ? 'default' : 'medical'
    setTheme(newTheme)
  }

  return {
    // 状态
    currentThemeName,
    currentTheme,
    themeContext,
    
    // 方法
    setTheme,
    loadThemeFromStorage,
    applyThemeToDocument,
    getColor,
    getComponentTheme,
    isDarkTheme,
    isMedicalTheme,
    toggleDarkMode,
    toggleMedicalTheme
  }
}

/**
 * 颜色工具函数
 */
export const colorUtils = {
  /**
   * 十六进制转 RGB
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },

  /**
   * RGB 转十六进制
   */
  rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  },

  /**
   * 添加透明度
   */
  addOpacity(color: string, opacity: number): string {
    const rgb = this.hexToRgb(color)
    if (!rgb) return color
    
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`
  },

  /**
   * 调整亮度
   */
  adjustBrightness(color: string, percent: number): string {
    const rgb = this.hexToRgb(color)
    if (!rgb) return color
    
    const adjust = (value: number) => {
      const adjusted = value + (value * percent / 100)
      return Math.max(0, Math.min(255, Math.round(adjusted)))
    }
    
    return this.rgbToHex(adjust(rgb.r), adjust(rgb.g), adjust(rgb.b))
  }
}

/**
 * 响应式设计工具
 */
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

/**
 * 媒体查询工具
 */
export function useMediaQuery(query: string) {
  const matches = ref(false)
  
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia(query)
    matches.value = mediaQuery.matches
    
    const listener = (e: MediaQueryListEvent) => {
      matches.value = e.matches
    }
    
    mediaQuery.addEventListener('change', listener)
    
    // 返回清理函数
    return {
      matches,
      cleanup: () => mediaQuery.removeEventListener('change', listener)
    }
  }
  
  return { matches, cleanup: () => {} }
}

/**
 * 预设媒体查询
 */
export function useBreakpoints() {
  const sm = useMediaQuery(`(min-width: ${breakpoints.sm})`)
  const md = useMediaQuery(`(min-width: ${breakpoints.md})`)
  const lg = useMediaQuery(`(min-width: ${breakpoints.lg})`)
  const xl = useMediaQuery(`(min-width: ${breakpoints.xl})`)
  const xl2 = useMediaQuery(`(min-width: ${breakpoints['2xl']})`)
  
  return {
    sm: sm.matches,
    md: md.matches,
    lg: lg.matches,
    xl: xl.matches,
    '2xl': xl2.matches,
    cleanup: () => {
      sm.cleanup()
      md.cleanup()
      lg.cleanup()
      xl.cleanup()
      xl2.cleanup()
    }
  }
}