/**
 * 主题系统类型定义
 */

import type { Theme, ThemeName } from './config'

// 主题上下文
export interface ThemeContext {
  currentTheme: ThemeName
  theme: Theme
  isDark: boolean
  isMedical: boolean
}

// 主题切换选项
export interface ThemeOptions {
  theme: ThemeName
  persistInStorage?: boolean
  applyToDocument?: boolean
}

// 组件主题配置
export interface ComponentTheme {
  background?: string
  borderColor?: string
  textColor?: string
  hoverBackground?: string
  hoverBorderColor?: string
  hoverTextColor?: string
  activeBackground?: string
  activeBorderColor?: string
  activeTextColor?: string
  focusBackground?: string
  focusBorderColor?: string
  focusTextColor?: string
  focusBoxShadow?: string
  disabledBackground?: string
  disabledBorderColor?: string
  disabledTextColor?: string
}

// 颜色调色板
export interface ColorPalette {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string
  600: string
  700: string
  800: string
  900: string
  950: string
}

// 主题配置
export interface ThemeConfig {
  name: string
  colors: {
    primary: ColorPalette
    secondary: ColorPalette
    success: ColorPalette
    warning: ColorPalette
    error: ColorPalette
    gray: ColorPalette
  }
  components: {
    input: ComponentTheme
    button: {
      primary: ComponentTheme
      secondary?: ComponentTheme
      success?: ComponentTheme
      warning?: ComponentTheme
      error?: ComponentTheme
    }
    [key: string]: unknown
  }
}

export type { Theme, ThemeName }