{"extends": "@crf/tsconfig/package-build.json", "compilerOptions": {"baseUrl": ".", "declaration": true, "declarationMap": true, "outDir": "dist", "paths": {"@crf/types": ["../types/index.ts"], "@crf/types/*": ["../types/*"], "@crf/utils": ["../utils/index.ts"], "@crf/utils/*": ["../utils/*"], "@crf/hooks": ["../hooks/index.ts"], "@crf/hooks/*": ["../hooks/*"]}}, "references": [{"path": "../types"}, {"path": "../utils"}, {"path": "../constants"}], "include": ["src/**/*.ts"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}