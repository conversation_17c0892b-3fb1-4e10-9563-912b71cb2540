{"name": "@crf/composables", "version": "1.0.1", "description": "Advanced Vue composables for CRF applications", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}, "./drag": {"import": "./src/useDragEnhanced.ts", "types": "./src/useDragEnhanced.ts"}, "./history": {"import": "./src/useHistoryManager.ts", "types": "./src/useHistoryManager.ts"}, "./performance": {"import": "./src/usePerformance.ts", "types": "./src/usePerformance.ts"}}, "files": ["src", "dist"], "scripts": {"build": "unbuild", "stub": "unbuild --stub", "dev": "unbuild --stub --watch", "type-check": "vue-tsc --noEmit", "lint": "echo 'Linting composables package...'", "test": "echo 'Testing composables package...'", "clean": "rm -rf dist"}, "peerDependencies": {"vue": "^3.5.13"}, "dependencies": {"@crf/types": "workspace:*", "@crf/utils": "workspace:*", "@crf/constants": "workspace:*", "@vueuse/core": "^13.2.0"}, "devDependencies": {"@crf/build-config": "workspace:*", "@types/node": "^22.15.19", "typescript": "^5.8.3", "vite": "^6.2.4", "vue-tsc": "^2.2.8"}}