import { computed, type Ref } from 'vue'

export interface OptionItem {
  value: unknown
  label: string
  disabled?: boolean
  icon?: string
  color?: string
  description?: string
}

export interface OptionConfig {
  key?: string
  value?: unknown
  options?: OptionItem[]
  enum?: unknown[]
  enumNames?: string[]
  default?: unknown
  formData?: {
    value?: unknown
  }
}

/**
 * 选项解析组合式函数
 * 统一处理各种选项格式，避免重复代码
 */
export function useOptionsParser(config: Ref<OptionConfig>) {
  /**
   * 解析选项数据
   */
  const options = computed(() => {
    const configValue = config.value
    if (!configValue) return []

    // 如果直接提供了options数组
    if (configValue.options && Array.isArray(configValue.options)) {
      return configValue.options.map(item => ({
        value: item.value,
        label: item.label || item.value,
        disabled: item.disabled || false,
        icon: item.icon,
        color: item.color,
        description: item.description
      }))
    }

    // 如果提供了enum
    if (configValue.enum && Array.isArray(configValue.enum)) {
      return configValue.enum.map((value: unknown, index: number) => ({
        value,
        label: configValue.enumNames?.[index] || value,
        disabled: false
      }))
    }

    return []
  })

  /**
   * 获取当前选中的值
   */
  const currentValue = computed(() => {
    const configValue = config.value
    if (!configValue) return undefined

    // 优先从formData中获取值
    if (configValue.formData?.value !== undefined) {
      return configValue.formData.value
    }

    // 其次从value中获取
    if (configValue.value !== undefined) {
      return configValue.value
    }

    // 最后使用默认值
    return configValue.default
  })

  /**
   * 获取当前选中项的标签
   */
  const currentLabel = computed(() => {
    const value = currentValue.value
    if (value === undefined || value === null) return ''

    const option = options.value.find((item: OptionItem) => item.value === value)
    return option?.label || value
  })

  /**
   * 获取当前选中项的完整信息
   */
  const currentOption = computed(() => {
    const value = currentValue.value
    if (value === undefined || value === null) return null

    return options.value.find((item: OptionItem) => item.value === value) || null
  })

  /**
   * 检查某个值是否被选中
   */
  const isSelected = (value: unknown) => {
    return currentValue.value === value
  }

  /**
   * 检查某个值是否被禁用
   */
  const isDisabled = (value: unknown) => {
    const option = options.value.find((item: OptionItem) => item.value === value)
    return option?.disabled || false
  }

  /**
   * 获取有效的选项（未禁用的）
   */
  const enabledOptions = computed(() => {
    return options.value.filter((item: OptionItem) => !item.disabled)
  })

  /**
   * 获取禁用的选项
   */
  const disabledOptions = computed(() => {
    return options.value.filter((item: OptionItem) => item.disabled)
  })

  /**
   * 根据值获取选项
   */
  const getOptionByValue = (value: unknown): OptionItem | null => {
    return options.value.find((item: OptionItem) => item.value === value) || null
  }

  /**
   * 根据标签获取选项
   */
  const getOptionByLabel = (label: string): OptionItem | null => {
    return options.value.find((item: OptionItem) => item.label === label) || null
  }

  /**
   * 搜索选项
   */
  const searchOptions = (keyword: string): OptionItem[] => {
    if (!keyword) return options.value

    const lowerKeyword = keyword.toLowerCase()
    return options.value.filter((item: OptionItem) => 
      item.label.toLowerCase().includes(lowerKeyword) ||
      String(item.value).toLowerCase().includes(lowerKeyword) ||
      (item.description && item.description.toLowerCase().includes(lowerKeyword))
    )
  }

  /**
   * 获取选项的显示文本
   */
  const getDisplayText = (option: OptionItem): string => {
    return option.label || String(option.value)
  }

  /**
   * 验证值是否在有效选项中
   */
  const isValidValue = (value: unknown): boolean => {
    return options.value.some((item: OptionItem) => item.value === value)
  }

  /**
   * 获取第一个有效选项的值
   */
  const getFirstValidValue = (): unknown => {
    const firstEnabled = enabledOptions.value[0]
    return firstEnabled?.value
  }

  /**
   * 格式化选项用于显示
   */
  const formatOptionsForDisplay = () => {
    return options.value.map((option: OptionItem) => ({
      ...option,
      displayText: getDisplayText(option),
      isSelected: isSelected(option.value),
      isDisabled: option.disabled || false
    }))
  }

  return {
    options,
    currentValue,
    currentLabel,
    currentOption,
    enabledOptions,
    disabledOptions,
    isSelected,
    isDisabled,
    getOptionByValue,
    getOptionByLabel,
    searchOptions,
    getDisplayText,
    isValidValue,
    getFirstValidValue,
    formatOptionsForDisplay
  }
}