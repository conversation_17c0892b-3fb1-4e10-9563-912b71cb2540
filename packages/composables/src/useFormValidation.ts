import { ref, computed } from 'vue'

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: string | RegExp
  min?: number
  max?: number
  customValidator?: (value: unknown) => string | null
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

/**
 * 表单验证组合式函数
 * 统一的验证逻辑，避免重复代码
 */
export function useFormValidation() {
  const errors = ref<string[]>([])
  const isValid = computed(() => errors.value.length === 0)

  /**
   * 验证单个字段
   */
  const validateField = (value: unknown, rules: ValidationRule): ValidationResult => {
    const fieldErrors: string[] = []

    // 必填验证
    if (rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
      fieldErrors.push('此字段为必填项')
    }

    // 如果值为空且不是必填，跳过后续验证
    if (!value && !rules.required) {
      return { isValid: true, errors: [] }
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        fieldErrors.push(`最少需要输入${rules.minLength}个字符`)
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        fieldErrors.push(`最多只能输入${rules.maxLength}个字符`)
      }
    }

    // 数值范围验证
    if (typeof value === 'number') {
      if (rules.min !== undefined && value < rules.min) {
        fieldErrors.push(`值不能小于${rules.min}`)
      }
      if (rules.max !== undefined && value > rules.max) {
        fieldErrors.push(`值不能大于${rules.max}`)
      }
    }

    // 正则表达式验证
    if (rules.pattern && typeof value === 'string') {
      const regex = typeof rules.pattern === 'string' ? new RegExp(rules.pattern) : rules.pattern
      if (!regex.test(value)) {
        fieldErrors.push('格式不正确')
      }
    }

    // 自定义验证器
    if (rules.customValidator) {
      const customError = rules.customValidator(value)
      if (customError) {
        fieldErrors.push(customError)
      }
    }

    return {
      isValid: fieldErrors.length === 0,
      errors: fieldErrors
    }
  }

  /**
   * 常用验证规则
   */
  const commonValidators = {
    email: (value: string): string | null => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(value) ? null : '请输入有效的邮箱地址'
    },
    phone: (value: string): string | null => {
      const phoneRegex = /^1[3-9]\d{9}$/
      return phoneRegex.test(value) ? null : '请输入有效的手机号码'
    },
    url: (value: string): string | null => {
      try {
        new URL(value)
        return null
      } catch {
        return '请输入有效的网址'
      }
    },
    idCard: (value: string): string | null => {
      const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/
      return idCardRegex.test(value) ? null : '请输入有效的身份证号码'
    },
    number: (value: string): string | null => {
      const numberRegex = /^-?\d+(\.\d+)?$/
      return numberRegex.test(value) ? null : '请输入有效的数字'
    },
    integer: (value: string): string | null => {
      const integerRegex = /^-?\d+$/
      return integerRegex.test(value) ? null : '请输入有效的整数'
    },
    positiveNumber: (value: string): string | null => {
      const num = parseFloat(value)
      return !isNaN(num) && num > 0 ? null : '请输入大于0的数字'
    }
  }

  /**
   * 批量验证
   */
  const validateFields = (fields: Array<{ value: unknown; rules: ValidationRule; name?: string }>) => {
    const allErrors: Record<string, string[]> = {}
    let hasErrors = false

    fields.forEach((field, index) => {
      const result = validateField(field.value, field.rules)
      if (!result.isValid) {
        const fieldName = field.name || `field_${index}`
        allErrors[fieldName] = result.errors
        hasErrors = true
      }
    })

    return {
      isValid: !hasErrors,
      errors: allErrors
    }
  }

  /**
   * 设置错误信息
   */
  const setErrors = (newErrors: string[]) => {
    errors.value = newErrors
  }

  /**
   * 清除错误信息
   */
  const clearErrors = () => {
    errors.value = []
  }

  /**
   * 添加错误信息
   */
  const addError = (error: string) => {
    errors.value.push(error)
  }

  return {
    errors,
    isValid,
    validateField,
    validateFields,
    commonValidators,
    setErrors,
    clearErrors,
    addError
  }
}