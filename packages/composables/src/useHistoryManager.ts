/**
 * 高级历史记录管理器
 * 支持撤销/重做、历史压缩、分支历史等功能
 */

import { ref, shallowRef, computed } from 'vue'

// 简单的深拷贝实现
function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T
  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  return obj
}

// 简单的错误处理
function useErrorHandler() {
  return {
    handleError: (error: unknown) => {
      console.error('History Manager Error:', error)
    }
  }
}

// 历史记录条目
export interface HistoryEntry<T = unknown> {
  id: string
  timestamp: number
  action: string
  description: string
  data: T
  metadata?: {
    user?: string
    component?: string
    size?: number
    duration?: number
    compressedCount?: number
    originalIds?: string[]
  }
}

// 历史记录配置
export interface HistoryConfig {
  maxSize: number
  autoCompress: boolean
  compressAfter: number
  enableBranching: boolean
  trackMetadata: boolean
}

// 历史记录统计
export interface HistoryStats {
  totalEntries: number
  memoryUsage: number
  oldestEntry?: Date
  newestEntry?: Date
  mostFrequentAction: string
  compressionRatio: number
}

/**
 * 历史记录管理器组合式API
 */
export function useHistoryManager<T = unknown>(config: Partial<HistoryConfig> = {}) {
  const { handleError } = useErrorHandler()
  
  // 默认配置
  const defaultConfig: HistoryConfig = {
    maxSize: 50,
    autoCompress: true,
    compressAfter: 20,
    enableBranching: false,
    trackMetadata: true
  }
  
  const finalConfig = { ...defaultConfig, ...config }
  
  // 历史记录状态
  const past = shallowRef<HistoryEntry<T>[]>([])
  const future = shallowRef<HistoryEntry<T>[]>([])
  const current = shallowRef<HistoryEntry<T> | null>(null)
  const isProcessing = ref(false)
  
  // 历史记录分支（如果启用）
  const branches = shallowRef<Map<string, HistoryEntry<T>[]>>(new Map())
  const currentBranch = ref('main')
  
  // 压缩相关
  const compressedEntries = shallowRef<Set<string>>(new Set())
  
  // 计算属性
  const canUndo = computed(() => past.value.length > 0)
  const canRedo = computed(() => future.value.length > 0)
  const historySize = computed(() => past.value.length + future.value.length + (current.value ? 1 : 0))
  
  const historyStats = computed((): HistoryStats => {
    const allEntries = [...past.value, ...(current.value ? [current.value] : []), ...future.value]
    
    if (allEntries.length === 0) {
      return {
        totalEntries: 0,
        memoryUsage: 0,
        mostFrequentAction: '',
        compressionRatio: 0
      }
    }
    
    // 计算内存使用
    const memoryUsage = allEntries.reduce((total, entry) => {
      return total + (entry.metadata?.size || JSON.stringify(entry).length)
    }, 0)
    
    // 统计最频繁的操作
    const actionCounts = new Map<string, number>()
    allEntries.forEach(entry => {
      actionCounts.set(entry.action, (actionCounts.get(entry.action) || 0) + 1)
    })
    
    const mostFrequentAction = Array.from(actionCounts.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || ''
    
    return {
      totalEntries: allEntries.length,
      memoryUsage,
      oldestEntry: new Date(Math.min(...allEntries.map(e => e.timestamp))),
      newestEntry: new Date(Math.max(...allEntries.map(e => e.timestamp))),
      mostFrequentAction,
      compressionRatio: compressedEntries.value.size / allEntries.length
    }
  })
  
  // 工具函数
  function generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
  
  function calculateDataSize(data: unknown): number {
    try {
      return JSON.stringify(data).length
    } catch {
      return 0
    }
  }
  
  /**
   * 创建历史记录条目
   */
  function createEntry(
    action: string,
    description: string,
    data: T,
    metadata?: HistoryEntry<T>['metadata']
  ): HistoryEntry<T> {
    const entry: HistoryEntry<T> = {
      id: generateId(),
      timestamp: Date.now(),
      action,
      description,
      data: deepClone(data)
    }
    
    if (finalConfig.trackMetadata) {
      entry.metadata = {
        size: calculateDataSize(data),
        duration: 0, // 将在实际使用时计算
        ...metadata
      }
    }
    
    return entry
  }
  
  /**
   * 推送新的历史记录
   */
  function push(
    action: string,
    description: string,
    data: T,
    metadata?: HistoryEntry<T>['metadata']
  ): void {
    if (isProcessing.value) return
    
    try {
      isProcessing.value = true
      
      const entry = createEntry(action, description, data, metadata)
      
      // 如果有当前条目，移到past
      if (current.value) {
        past.value.push(current.value)
      }
      
      // 清空future（新操作会创建新分支）
      future.value = []
      
      // 设置新的当前条目
      current.value = entry
      
      // 检查是否需要压缩
      if (finalConfig.autoCompress && past.value.length >= finalConfig.compressAfter) {
        compressHistory()
      }
      
      // 限制历史大小
      if (past.value.length > finalConfig.maxSize) {
        const removed = past.value.shift()
        if (removed) {
          compressedEntries.value.delete(removed.id)
        }
      }
      
      console.log(`历史记录已添加: ${description}`, {
        action,
        total: historySize.value,
        canUndo: canUndo.value
      })
      
    } catch (error) {
      handleError(error)
    } finally {
      isProcessing.value = false
    }
  }
  
  /**
   * 撤销操作
   */
  function undo(): HistoryEntry<T> | null {
    if (!canUndo.value || isProcessing.value) return null
    
    try {
      isProcessing.value = true
      
      const currentEntry = current.value
      const previousEntry = past.value.pop()!
      
      // 将当前条目移到future
      if (currentEntry) {
        future.value.push(currentEntry)
      }
      
      // 设置新的当前条目
      current.value = previousEntry
      
      console.log(`撤销操作: ${previousEntry.description}`, {
        timestamp: new Date(previousEntry.timestamp),
        canRedo: canRedo.value
      })
      
      return previousEntry
      
    } catch (error) {
      handleError(error)
      return null
    } finally {
      isProcessing.value = false
    }
  }
  
  /**
   * 重做操作
   */
  function redo(): HistoryEntry<T> | null {
    if (!canRedo.value || isProcessing.value) return null
    
    try {
      isProcessing.value = true
      
      const currentEntry = current.value
      const nextEntry = future.value.pop()!
      
      // 将当前条目移到past
      if (currentEntry) {
        past.value.push(currentEntry)
      }
      
      // 设置新的当前条目
      current.value = nextEntry
      
      console.log(`重做操作: ${nextEntry.description}`, {
        timestamp: new Date(nextEntry.timestamp),
        canUndo: canUndo.value
      })
      
      return nextEntry
      
    } catch (error) {
      handleError(error)
      return null
    } finally {
      isProcessing.value = false
    }
  }
  
  /**
   * 跳转到特定历史记录
   */
  function jumpTo(entryId: string): boolean {
    const allEntries = [...past.value, ...(current.value ? [current.value] : []), ...future.value]
    const targetEntry = allEntries.find(e => e.id === entryId)
    
    if (!targetEntry) return false
    
    try {
      isProcessing.value = true
      
      // 重新组织历史记录
      const targetIndex = allEntries.indexOf(targetEntry)
      const newPast = allEntries.slice(0, targetIndex)
      const newFuture = allEntries.slice(targetIndex + 1)
      
      past.value = newPast
      current.value = targetEntry
      future.value = newFuture
      
      console.log(`跳转到历史记录: ${targetEntry.description}`)
      return true
      
    } catch (error) {
      handleError(error)
      return false
    } finally {
      isProcessing.value = false
    }
  }
  
  /**
   * 历史记录压缩
   */
  function compressHistory(): void {
    if (past.value.length < 2) return
    
    try {
      // 压缩连续的相同操作
      const compressed: HistoryEntry<T>[] = []
      let currentGroup: HistoryEntry<T>[] = []
      
      past.value.forEach(entry => {
        if (currentGroup.length === 0 || (currentGroup[0] && currentGroup[0].action === entry.action)) {
          currentGroup.push(entry)
        } else {
          // 处理当前组
          if (currentGroup.length > 1 && currentGroup[0] && shouldCompress(currentGroup[0].action)) {
            const compressedEntry = compressGroup(currentGroup)
            compressed.push(compressedEntry)
            currentGroup.forEach(e => compressedEntries.value.add(e.id))
          } else {
            compressed.push(...currentGroup)
          }
          
          currentGroup = [entry]
        }
      })
      
      // 处理最后一组
      if (currentGroup.length > 1 && currentGroup[0] && shouldCompress(currentGroup[0].action)) {
        const compressedEntry = compressGroup(currentGroup)
        compressed.push(compressedEntry)
        currentGroup.forEach(e => compressedEntries.value.add(e.id))
      } else {
        compressed.push(...currentGroup)
      }
      
      past.value = compressed
      
    } catch (error) {
      handleError(error)
    }
  }
  
  /**
   * 判断是否应该压缩特定操作
   */
  function shouldCompress(action: string): boolean {
    const compressibleActions = ['update_form_data', 'update_component_props', 'drag_move']
    return compressibleActions.includes(action)
  }
  
  /**
   * 压缩一组相同操作
   */
  function compressGroup(group: HistoryEntry<T>[]): HistoryEntry<T> {
    const first = group[0]
    const last = group[group.length - 1]
    
    if (!first || !last) {
      throw new Error('Invalid group for compression')
    }
    
    return {
      id: generateId(),
      timestamp: first.timestamp,
      action: `${first.action}_batch`,
      description: `${first.description} (${group.length}次操作)`,
      data: last.data, // 使用最后的状态
      metadata: {
        ...first.metadata,
        compressedCount: group.length,
        originalIds: group.map(e => e.id)
      }
    }
  }
  
  /**
   * 创建历史分支
   */
  function createBranch(branchName: string): boolean {
    if (!finalConfig.enableBranching) return false
    
    try {
      const currentHistory = [...past.value, ...(current.value ? [current.value] : [])]
      branches.value.set(branchName, currentHistory)
      currentBranch.value = branchName
      
      console.log(`创建历史分支: ${branchName}`)
      return true
      
    } catch (error) {
      handleError(error)
      return false
    }
  }
  
  /**
   * 切换历史分支
   */
  function switchBranch(branchName: string): boolean {
    if (!finalConfig.enableBranching || !branches.value.has(branchName)) {
      return false
    }
    
    try {
      // 保存当前分支
      const currentHistory = [...past.value, ...(current.value ? [current.value] : [])]
      branches.value.set(currentBranch.value, currentHistory)
      
      // 切换到新分支
      const newHistory = branches.value.get(branchName)!
      past.value = newHistory.slice(0, -1)
      current.value = newHistory[newHistory.length - 1] || null
      future.value = []
      
      currentBranch.value = branchName
      
      console.log(`切换到历史分支: ${branchName}`)
      return true
      
    } catch (error) {
      handleError(error)
      return false
    }
  }
  
  /**
   * 获取历史记录列表
   */
  function getHistory(): HistoryEntry<T>[] {
    return [...past.value, ...(current.value ? [current.value] : []), ...future.value]
  }
  
  /**
   * 搜索历史记录
   */
  function searchHistory(query: string): HistoryEntry<T>[] {
    const allEntries = getHistory()
    const lowerQuery = query.toLowerCase()
    
    return allEntries.filter(entry => 
      entry.description.toLowerCase().includes(lowerQuery) ||
      entry.action.toLowerCase().includes(lowerQuery)
    )
  }
  
  /**
   * 清空历史记录
   */
  function clear(): void {
    past.value = []
    future.value = []
    current.value = null
    compressedEntries.value.clear()
    
    if (finalConfig.enableBranching) {
      branches.value.clear()
      currentBranch.value = 'main'
    }
    
    console.log('历史记录已清空')
  }
  
  /**
   * 导出历史记录
   */
  function exportHistory(): string {
    return JSON.stringify({
      config: finalConfig,
      history: getHistory(),
      branches: finalConfig.enableBranching ? Object.fromEntries(branches.value) : {},
      currentBranch: currentBranch.value,
      stats: historyStats.value
    }, null, 2)
  }
  
  /**
   * 导入历史记录
   */
  function importHistory(data: string): boolean {
    try {
      const parsed = JSON.parse(data)
      
      past.value = parsed.history.slice(0, -1)
      current.value = parsed.history[parsed.history.length - 1] || null
      future.value = []
      
      if (finalConfig.enableBranching && parsed.branches) {
        branches.value = new Map(Object.entries(parsed.branches))
        currentBranch.value = parsed.currentBranch || 'main'
      }
      
      console.log('历史记录导入成功')
      return true
      
    } catch (error) {
      handleError(error)
      return false
    }
  }
  
  return {
    // 状态
    past,
    future,
    current,
    isProcessing,
    
    // 计算属性
    canUndo,
    canRedo,
    historySize,
    historyStats,
    
    // 分支相关
    branches,
    currentBranch,
    
    // 核心操作
    push,
    undo,
    redo,
    jumpTo,
    
    // 高级功能
    compressHistory,
    createBranch,
    switchBranch,
    getHistory,
    searchHistory,
    
    // 管理
    clear,
    exportHistory,
    importHistory,
    
    // 配置
    config: finalConfig
  }
}