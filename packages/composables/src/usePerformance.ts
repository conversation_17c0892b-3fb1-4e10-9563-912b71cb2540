import { ref, computed, watch, onMounted, onUnmounted, nextTick, type Ref } from 'vue'
import { debounce, throttle, type DebouncedFunc } from 'lodash-es'

/**
 * 性能监控组合式API
 */
export function usePerformanceMonitor() {
  const metrics = ref({
    fps: 0,
    memory: {
      used: 0,
      total: 0,
      limit: 0
    },
    timing: {
      domReady: 0,
      loadComplete: 0,
      firstPaint: 0,
      firstContentfulPaint: 0
    }
  })
  
  const isSupported = computed(() => {
    return typeof performance !== 'undefined' && 'memory' in performance
  })
  
  let frameCount = 0
  let lastTime = performance.now()
  let animationId: number
  
  /**
   * FPS 监控
   */
  const measureFPS = () => {
    const now = performance.now()
    frameCount++
    
    if (now >= lastTime + 1000) {
      metrics.value.fps = Math.round((frameCount * 1000) / (now - lastTime))
      frameCount = 0
      lastTime = now
    }
    
    animationId = requestAnimationFrame(measureFPS)
  }
  
  /**
   * 内存使用监控
   */
  const measureMemory = () => {
    if (!isSupported.value) return
    
    const memory = (performance as Record<string, unknown>).memory
    if (memory) {
      metrics.value.memory = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      }
    }
  }
  
  /**
   * 获取性能时间
   */
  const measureTiming = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')
    
    if (navigation) {
      // 使用 startTime 作为基准时间，因为 navigationStart 已被弃用
      const baseTime = navigation.startTime
      metrics.value.timing = {
        domReady: Math.round(navigation.domContentLoadedEventEnd - baseTime),
        loadComplete: Math.round(navigation.loadEventEnd - baseTime),
        firstPaint: paint.find(entry => entry.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
      }
    }
  }
  
  let memoryInterval: ReturnType<typeof setInterval> | null = null
  
  /**
   * 开始监控
   */
  const startMonitoring = () => {
    measureFPS()
    measureTiming()
    
    memoryInterval = setInterval(measureMemory, 5000)
  }
  
  /**
   * 停止监控
   */
  const stopMonitoring = () => {
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
    if (memoryInterval) {
      clearInterval(memoryInterval)
      memoryInterval = null
    }
  }
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    stopMonitoring()
  })
  
  /**
   * 性能警告检查
   */
  const checkPerformanceWarnings = computed(() => {
    const warnings: string[] = []
    
    if (metrics.value.fps < 30) {
      warnings.push('FPS过低，可能影响用户体验')
    }
    
    if (metrics.value.memory.used > 100) {
      warnings.push('内存使用过高，可能导致页面卡顿')
    }
    
    if (metrics.value.timing.domReady > 3000) {
      warnings.push('DOM加载时间过长')
    }
    
    return warnings
  })
  
  return {
    metrics,
    isSupported,
    startMonitoring,
    stopMonitoring,
    measureMemory,
    checkPerformanceWarnings
  }
}

/**
 * 虚拟滚动组合式API
 */
export function useVirtualScroll<T>(
  items: Ref<T[]>,
  itemHeight: number = 50,
  containerHeight: number = 400,
  buffer: number = 5
) {
  const scrollTop = ref(0)
  const containerRef = ref<HTMLElement>()
  
  // 可见范围计算
  const visibleRange = computed(() => {
    const start = Math.max(0, Math.floor(scrollTop.value / itemHeight) - buffer)
    const end = Math.min(
      items.value.length,
      Math.ceil((scrollTop.value + containerHeight) / itemHeight) + buffer
    )
    return { start, end }
  })
  
  // 可见项目
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      top: (start + index) * itemHeight
    }))
  })
  
  // 总高度
  const totalHeight = computed(() => items.value.length * itemHeight)
  
  // 滚动处理
  const handleScroll: DebouncedFunc<(event: Event) => void> = throttle((event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }, 16) // 约60fps
  
  onMounted(() => {
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
    }
  })
  
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
  })
  
  return {
    containerRef,
    visibleItems,
    totalHeight,
    scrollTop,
    handleScroll
  }
}

/**
 * 防抖搜索组合式API
 */
export function useDebouncedSearch<T>(
  items: Ref<T[]>,
  searchFields: (keyof T)[],
  delay: number = 300
) {
  const searchQuery = ref('')
  const isSearching = ref(false)
  
  // 搜索结果
  const searchResults = ref<T[]>([])
  
  // 防抖搜索函数
  const debouncedSearch: DebouncedFunc<(query: string) => Promise<void>> = debounce(async (query: string) => {
    isSearching.value = true
    
    try {
      if (!query.trim()) {
        searchResults.value = items.value
      } else {
        const lowercaseQuery = query.toLowerCase()
        searchResults.value = items.value.filter(item => {
          return searchFields.some(field => {
            const value = item[field]
            return String(value).toLowerCase().includes(lowercaseQuery)
          })
        })
      }
    } finally {
      isSearching.value = false
    }
  }, delay)
  
  // 监听搜索查询变化
  watch(searchQuery, (newQuery) => {
    debouncedSearch(newQuery)
  }, { immediate: true })
  
  // 清空搜索
  const clearSearch = () => {
    searchQuery.value = ''
  }
  
  return {
    searchQuery,
    searchResults,
    isSearching,
    clearSearch
  }
}

/**
 * 组件懒加载组合式API
 */
export function useLazyComponent() {
  const isVisible = ref(false)
  const elementRef = ref<HTMLElement>()
  
  onMounted(() => {
    if (!elementRef.value) return
    
    // 使用 Intersection Observer 监听元素可见性
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (entry && entry.isIntersecting) {
          isVisible.value = true
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    )
    
    observer.observe(elementRef.value)
    
    onUnmounted(() => {
      observer.disconnect()
    })
  })
  
  return {
    isVisible,
    elementRef
  }
}

/**
 * 批量操作优化组合式API
 */
export function useBatchUpdate<T>(
  updateFn: (items: T[]) => void,
  delay: number = 16
) {
  const pendingUpdates: Ref<T[]> = ref([])
  const isUpdating = ref(false)
  
  // 批量更新处理器
  const flushUpdates = throttle(async () => {
    if (pendingUpdates.value.length === 0 || isUpdating.value) return
    
    isUpdating.value = true
    
    try {
      await nextTick()
      updateFn([...pendingUpdates.value] as T[])
      pendingUpdates.value = []
    } finally {
      isUpdating.value = false
    }
  }, delay)
  
  // 添加更新项
  const addUpdate = (item: T) => {
    (pendingUpdates.value as T[]).push(item)
    flushUpdates()
  }
  
  // 批量添加更新项
  const addBatchUpdate = (items: T[]) => {
    (pendingUpdates.value as T[]).push(...items)
    flushUpdates()
  }
  
  // 强制执行更新
  const forceFlush = async () => {
    flushUpdates.cancel()
    if (pendingUpdates.value.length > 0) {
      isUpdating.value = true
      try {
        await nextTick()
        updateFn([...pendingUpdates.value] as T[])
        pendingUpdates.value = []
      } finally {
        isUpdating.value = false
      }
    }
  }
  
  return {
    addUpdate,
    addBatchUpdate,
    forceFlush,
    isUpdating,
    pendingCount: computed(() => pendingUpdates.value.length)
  }
}

/**
 * 图片懒加载组合式API
 */
export function useLazyImage(src: string, placeholder?: string) {
  const imageRef = ref<HTMLImageElement>()
  const isLoaded = ref(false)
  const isError = ref(false)
  const currentSrc = ref(placeholder || '')
  
  onMounted(() => {
    if (!imageRef.value || !src) return
    
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (entry && entry.isIntersecting) {
          loadImage()
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )
    
    observer.observe(imageRef.value)
    
    onUnmounted(() => {
      observer.disconnect()
    })
  })
  
  const loadImage = () => {
    const img = new Image()
    
    img.onload = () => {
      currentSrc.value = src
      isLoaded.value = true
      isError.value = false
    }
    
    img.onerror = () => {
      isError.value = true
      isLoaded.value = false
    }
    
    img.src = src
  }
  
  return {
    imageRef,
    currentSrc,
    isLoaded,
    isError
  }
}