# CRF 编辑器后端 API 文档

## 自动保存 API

### 保存数据

**POST** `/api/autosave`

**请求体：**
```json
{
  "resource_type": "crf_template",
  "resource_id": "demo-template-id", 
  "data": {
    "sections": [...],
    "pageConfig": {...},
    "formData": {...},
    "timestamp": 1641234567890
  }
}
```

**响应：**
```json
{
  "message": "数据保存成功",
  "saved_at": "2023-01-01T12:00:00Z"
}
```

### 获取保存的数据

**GET** `/api/autosave/{resource_type}/{resource_id}`

**响应：**
```json
{
  "data": {
    "sections": [...],
    "pageConfig": {...}, 
    "formData": {...},
    "timestamp": 1641234567890
  },
  "saved_at": "2023-01-01T12:00:00Z"
}
```

### 删除保存的数据

**DELETE** `/api/autosave/{resource_type}/{resource_id}`

**响应：**
```json
{
  "message": "数据删除成功"
}
```

## 数据库表结构建议

### auto_saves 表

```sql
CREATE TABLE auto_saves (
    id SERIAL PRIMARY KEY,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(255) NOT NULL,
    save_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(resource_type, resource_id)
);

-- 索引
CREATE INDEX idx_auto_saves_resource ON auto_saves(resource_type, resource_id);
CREATE INDEX idx_auto_saves_updated_at ON auto_saves(updated_at);
```

## 环境配置

开发环境 API 地址: `http://localhost:8080/api`
生产环境 API 地址: `https://your-api-domain.com/api`

## 错误处理

API 错误响应格式：
```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2023-01-01T12:00:00Z"
}
```

常见错误码：
- `VALIDATION_ERROR`: 数据验证失败
- `NOT_FOUND`: 资源不存在
- `SERVER_ERROR`: 服务器内部错误
- `UNAUTHORIZED`: 未授权访问