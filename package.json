{"name": "crf-frontend", "version": "1.0.0", "private": true, "packageManager": "pnpm@9.15.4", "description": "基于Vue3、TypeScript、Unocss 的现代化医疗专业领域 CRF 表单制作工具", "scripts": {"dev": "pnpm -C apps/crf-editor dev", "build": "turbo run build", "build:packages": "turbo run build --filter='!./apps/**'", "build:apps": "turbo run build --filter='./apps/**'", "dev:packages": "turbo run dev --filter='!./apps/**'", "type-check": "turbo run type-check", "type-check:packages": "turbo run type-check --filter='!./apps/**'", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "test": "turbo run test", "test:ci": "turbo run test:ci", "clean": "turbo run clean && rm -rf node_modules/.cache && rm -rf .turbo", "clean:dist": "turbo run clean:dist", "postinstall": "turbo run build:packages", "changeset": "changeset", "changeset:version": "changeset version", "changeset:publish": "changeset publish", "changeset:status": "changeset status", "api:mock": "node mock-api-server.js", "dev:full": "concurrently \"pnpm run api:mock\" \"pnpm run dev\"", "migrate:analyze": "node scripts/migrate-to-unocss.js", "migrate:analyze:editor": "node scripts/migrate-to-unocss.js apps/crf-editor/src", "migrate:analyze:components": "node scripts/migrate-to-unocss.js packages/components", "migrate:message": "node scripts/migrate-message-imports.js", "migrate:message:check": "node scripts/migrate-message-imports.js --dry-run", "unocss:dev": "turbo run unocss:dev", "unocss:build": "turbo run unocss:build", "unocss:inspect": "unocss --inspect", "style:check": "turbo run unocss:build && echo 'UnoCSS build completed successfully'", "docs:migration": "echo 'Opening migration documentation...' && start docs/development/UNOCSS_MIGRATION_GUIDE.md", "docs:message": "echo 'Opening message usage guide...' && start docs/MESSAGE_USAGE_GUIDE.md", "quality:check": "node scripts/check-code-quality.js", "quality:report": "npm run quality:check && echo '代码质量报告已生成'", "quality:fix": "echo '自动修复功能开发中...' && npm run quality:check"}, "devDependencies": {"@changesets/cli": "^2.29.5", "@crf/eslint-config": "workspace:*", "@crf/tsconfig": "workspace:*", "@sinclair/typebox": "^0.34.33", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/test-utils": "^2.4.6", "concurrently": "^8.2.2", "cors": "^2.8.5", "express": "^4.21.2", "sass": "^1.89.0", "turbo": "^2.5.5", "typescript": "^5.8.3", "unbuild": "^3.5.0", "unocss": "^66.1.2", "vite": "^6.2.4", "vitest": "^3.1.3", "vue-tsc": "^2.2.8"}, "engines": {"node": ">=18.0.0", "pnpm": ">=9.0.0"}}