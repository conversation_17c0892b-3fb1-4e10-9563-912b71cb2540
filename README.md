# CRF 临床研究表单管理系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/Node.js-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![PNPM Version](https://img.shields.io/badge/PNPM-%3E%3D9.0.0-f69220)](https://pnpm.io/)
[![Go Version](https://img.shields.io/badge/Go-%3E%3D1.19-00ADD8)](https://golang.org/)

基于 **Vue 3 + Go** 的现代化医疗专业领域 CRF（临床研究表单）管理系统，提供可视化表单设计、数据收集、权限管理等全流程解决方案。

## ✨ 核心特性

### 🎨 表单设计与管理
- **可视化设计器** - 拖拽式表单构建，所见即所得
- **丰富组件库** - 基础、选择、布局、医疗专业等多类组件
- **智能配置** - 组件属性、验证规则、样式等全方位配置
- **版本管理** - 表单版本追踪、数据迁移策略
- **实时预览** - 编辑过程中实时预览表单效果

### 🔐 权限与安全
- **RBAC权限系统** - 基于角色的访问控制
  - Administrator (系统管理员)：用户管理、权限分配、系统配置
  - Researcher (研究者)：设计表单、创建研究项目
  - Data Entry Personnel (数据录入员)：填写CRF表单
  - Reviewer (审阅者)：审核数据准确性和完整性
- **JWT身份认证** - 无状态认证与刷新机制
- **匿名用户支持** - 公开表单访问功能

### 📊 数据管理
- **数据分析服务** - 表单数据统计分析
- **多格式导出** - CSV/JSON/Excel格式数据导出
- **版本感知数据** - 跨版本数据统一视图
- **自动保存** - 智能草稿保存与冲突解决

### 🌟 现代化架构
- **响应式设计** - 多端适配，移动端友好
- **高性能** - 基于 Vue3 Composition API
- **类型安全** - 全面 TypeScript 支持
- **微服务架构** - 前后端分离，API优先

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.5+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建**: Vite 6.0+ + Turbo
- **样式**: UnoCSS + SCSS
- **状态管理**: Pinia
- **UI组件**: Naive UI + 自定义组件库
- **包管理**: pnpm (workspace)

### 后端技术栈
- **语言**: Go 1.19+
- **框架**: Gin + GORM
- **数据库**: PostgreSQL
- **存储**: MinIO 对象存储
- **缓存**: Redis
- **认证**: JWT

### 项目结构

```
crf-frontend/
├── apps/                           # 应用层
│   └── crf-editor/                 # CRF编辑器应用
│       ├── src/
│       │   ├── components/         # 业务组件
│       │   │   ├── edit/          # 表单编辑器组件
│       │   │   ├── form/          # 表单渲染组件
│       │   │   ├── admin/         # 管理功能组件
│       │   │   └── profile/       # 用户资料组件
│       │   ├── pages/             # 页面组件
│       │   ├── stores/            # Pinia状态管理
│       │   ├── composables/       # 组合式函数
│       │   └── utils/             # 工具函数
│       └── ...
├── backend/                        # 后端服务
│   ├── internal/
│   │   ├── handlers/              # HTTP处理器
│   │   ├── services/              # 业务逻辑层
│   │   ├── models/                # 数据模型
│   │   └── middleware/            # 中间件
│   └── ...
├── packages/                       # 共享包
│   ├── components/                # 组件库
│   ├── constants/                 # 常量定义
│   └── utils/                     # 工具函数
└── internal/                       # 内部工具包
```

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **pnpm** >= 9.0.0
- **Go** >= 1.19 (后端开发)
- **PostgreSQL** >= 13 (数据库)
- **Redis** >= 6.0 (缓存)

### 安装与启动

```bash
# 克隆项目
git clone <repository-url>
cd crf-frontend

# 安装前端依赖
pnpm install

# 启动开发服务器
pnpm dev

# 启动后端服务 (另一个终端)
cd backend/
make dev

# 同时启动前后端 (包含Mock API)
pnpm dev:full
```

### 数据库设置

```bash
# 进入后端目录
cd backend/

# 运行数据库迁移
make migrate

# 或手动执行SQL
psql "$(DATABASE_URL)" -f database/schema.sql
```

## 📖 功能模块

### 🏠 工作台
- 项目概览统计
- 最近项目访问
- 快速操作入口
- 项目搜索与筛选

### 📋 表单管理
- 拖拽式表单设计器
- 实时预览与配置
- 表单版本管理
- 发布与分享

### 📊 数据管理
- 表单数据收集
- 数据统计分析
- 多格式数据导出
- 数据版本兼容

### 👥 用户与权限
- 用户账户管理
- 角色权限配置
- 个人资料设置
- 头像上传编辑

### 🔧 系统管理
- 系统配置管理
- 操作日志审计
- 性能监控
- 数据备份

## 🛠️ 开发指南

### 常用命令

```bash
# 前端开发
pnpm dev                    # 启动开发服务器
pnpm build                  # 构建生产版本
pnpm type-check            # TypeScript类型检查
pnpm lint                  # ESLint检查
pnpm test                  # 运行测试

# 后端开发
cd backend/
make dev                   # 开发模式运行
make build                 # 构建生产版本
make test                  # 运行测试
make lint                  # 代码检查

# 系统检查
scripts/system-check.sh    # 完整系统检查
scripts/final-verification.sh  # 最终验证
```

### 开发最佳实践

#### 前端开发规范
- 使用 **Composition API** 编写所有组件
- 采用 **PascalCase** 命名Vue组件文件
- 使用 **kebab-case** 命名页面组件
- 优先使用 **Composables** 封装业务逻辑
- 保持组件代码在300行以内

#### 代码风格规范
- 遵循 **Vue 3 官方风格指南**
- 使用 **ESLint + Prettier** 格式化代码
- **TypeScript 严格模式**
- 变量和函数名使用英文，注释使用中文

#### 提交规范
```bash
# 提交格式: type(scope): description
feat(auth): 添加JWT认证功能
fix(form): 修复表单验证问题
docs(readme): 更新安装说明
style(layout): 调整页面布局样式
```

### 组件开发

#### 创建新组件
```bash
# 1. 在相应目录创建组件
packages/components/your-component/

# 2. 编写组件实现
YourComponent.vue
index.ts
types.ts

# 3. 注册到组件映射
apps/crf-editor/src/components/config/block.ts

# 4. 更新导出
packages/components/index.ts
```

#### 组件配置Schema
```typescript
interface ComponentConfigSchema {
  code: ComponentType           // 组件类型标识
  name: string                 // 组件显示名称
  description: string          // 组件描述信息
  icon: string                // 组件图标
  category: string            // 组件类别
  defaultProps: object        // 默认属性
  configSchema: ConfigField[] // 配置项定义
}
```

### 状态管理

#### Store 设计
```typescript
// 使用 Pinia 定义 Store
export const useExampleStore = defineStore('example', () => {
  // 状态
  const state = ref({})
  
  // 计算属性
  const computed = computed(() => {})
  
  // 操作方法
  const actions = {
    async fetchData() {}
  }
  
  return { state, computed, ...actions }
})
```

#### Composables 模式
```typescript
// 业务逻辑封装
export function useFormManagement() {
  const formList = useFormList()           // 数据管理
  const formOperations = useFormOperations() // CRUD操作
  const formSelection = useFormSelection()  // 选择状态
  const formFilters = useFormFilters()     // 过滤搜索
  
  return {
    ...formList,
    ...formOperations, 
    ...formSelection,
    ...formFilters
  }
}
```

## 🧪 测试

### 运行测试
```bash
# 前端测试
pnpm test              # 单元测试
pnpm test:ci          # CI环境测试

# 后端测试
cd backend/
make test             # 运行所有测试
make test-coverage    # 测试覆盖率

# 功能测试
scripts/test-save-functionality.sh  # 保存功能测试
```

## 📦 部署

### 构建生产版本
```bash
# 构建前端
pnpm build

# 构建后端
cd backend/
make build

# Docker部署
cd docker/
docker-compose -f docker-compose.prod.yml up -d
```

### 环境配置
```bash
# 前端环境变量
VITE_API_BASE_URL=https://api.example.com
VITE_APP_NAME=CRF系统

# 后端环境变量  
DATABASE_URL=postgres://user:pass@localhost/crf
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
MINIO_ENDPOINT=*************:9000
```

## 🔍 系统监控

### 健康检查
```bash
# 系统完整性检查
./scripts/system-check.sh

# 服务状态检查
curl http://localhost:8080/health

# 数据库连接检查  
make test-db-connection
```

### 性能优化
- **前端**：虚拟滚动、懒加载、代码分割
- **后端**：Redis缓存、数据库索引、连接池
- **网络**：CDN加速、Gzip压缩、HTTP/2

## 🤝 贡献指南

### 开发流程
1. **Fork** 项目到个人仓库
2. **创建功能分支** `git checkout -b feature/your-feature`
3. **提交代码** 遵循提交规范
4. **推送分支** `git push origin feature/your-feature`
5. **创建 Pull Request**
6. **代码审查** 通过后由维护者合并

### 分支管理
- `main` - 主分支，生产环境
- `develop` - 开发分支，集成测试
- `feature/*` - 功能开发分支
- `hotfix/*` - 紧急修复分支

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🆘 支持与帮助

- 📖 [开发文档](docs/)
- 🐛 [问题反馈](https://github.com/your-repo/issues)
- 💬 [讨论区](https://github.com/your-repo/discussions)
- 📧 联系邮箱：<EMAIL>

---

**CRF 临床研究表单管理系统** - 让医疗研究表单设计更简单、更专业、更高效！🏥✨