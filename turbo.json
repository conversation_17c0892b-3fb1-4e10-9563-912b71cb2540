{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**"], "cache": true}, "build:packages": {"dependsOn": [], "outputs": ["dist/**"], "cache": true}, "build:apps": {"dependsOn": ["build:packages"], "outputs": ["dist/**", "build/**"], "cache": true}, "dev": {"dependsOn": ["build:packages"], "cache": false, "persistent": true}, "dev:packages": {"cache": false, "persistent": true}, "type-check": {"dependsOn": ["^build"], "outputs": [], "cache": true}, "type-check:packages": {"dependsOn": ["build:packages"], "outputs": [], "cache": true}, "lint": {"outputs": [], "cache": true}, "lint:fix": {"outputs": [], "cache": false}, "test": {"dependsOn": ["build:packages"], "outputs": ["coverage/**"], "cache": true}, "test:ci": {"dependsOn": ["build:packages"], "outputs": ["coverage/**"], "cache": true}, "clean": {"cache": false}, "clean:dist": {"cache": false}, "unocss:build": {"outputs": ["**/*.css"], "cache": true}, "unocss:dev": {"cache": false, "persistent": true}}, "globalDependencies": ["package.json", "pnpm-workspace.yaml", "tsconfig.json", "vitest.config.mts", "uno.config.ts"], "remoteCache": {"enabled": false}}