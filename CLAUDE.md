# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 数据库设计约束 / Database Design Constraints
采用 turbo 加速项目构建

**重要：数据库设计必须严格遵循以下约束**

### 1. 外键约束策略
- **不使用物理外键**：所有表之间的关联关系通过逻辑维护，不在数据库层面创建物理外键约束
- **逻辑外键**：在代码层面维护数据一致性，通过应用逻辑确保引用完整性
- **原因**：提高系统灵活性，避免数据库锁定，便于数据迁移和分库分表

### 2. 软删除策略
- **统一软删除**：所有数据表必须支持软删除，不进行物理删除
- **删除字段**：使用`deleted_at`字段标记删除状态（GORM标准）
- **查询过滤**：所有查询默认过滤已删除记录
- **数据恢复**：保留数据恢复能力，支持审计和追溯

### 3. 数据完整性保障
- **应用层验证**：在业务逻辑层进行数据完整性检查
- **事务管理**：使用数据库事务确保多表操作的原子性
- **级联操作**：通过代码实现级联删除和更新逻辑

### 4. 字段设计规范
- **主键**：统一使用UUID作为主键，避免自增ID
- **时间戳**：所有表包含`created_at`、`updated_at`、`deleted_at`字段
- **审计字段**：重要操作表增加`created_by`、`updated_by`字段
- **状态字段**：使用枚举类型或状态码，避免布尔值

### Database Design Constraints

**Important: Database design must strictly follow these constraints**

### 1. Foreign Key Strategy
- **No Physical Foreign Keys**: All table relationships maintained through logical references, no physical foreign key constraints at database level
- **Logical Foreign Keys**: Maintain data consistency at application level through business logic
- **Reason**: Improve system flexibility, avoid database locking, facilitate data migration and sharding

### 2. Soft Delete Strategy
- **Unified Soft Delete**: All data tables must support soft delete, no physical deletion
- **Delete Field**: Use `deleted_at` field to mark deletion status (GORM standard)
- **Query Filtering**: All queries filter deleted records by default
- **Data Recovery**: Retain data recovery capability, support auditing and traceability

### 3. Data Integrity Assurance
- **Application Layer Validation**: Perform data integrity checks at business logic layer
- **Transaction Management**: Use database transactions to ensure atomicity of multi-table operations
- **Cascade Operations**: Implement cascade delete and update logic through code

### 4. Field Design Standards
- **Primary Key**: Uniformly use UUID as primary key, avoid auto-increment ID
- **Timestamps**: All tables include `created_at`, `updated_at`, `deleted_at` fields
- **Audit Fields**: Important operation tables add `created_by`, `updated_by` fields
- **Status Fields**: Use enum types or status codes, avoid boolean values

## 语言设置 / Language Settings

**默认语言：中文**
- 所有对话、回答和交互都使用中文
- 代码注释和文档也优先使用中文
- 变量名和函数名保持英文（符合编程规范）

**Default Language: Chinese**
- All conversations, responses and interactions should be in Chinese
- Code comments and documentation should also prioritize Chinese
- Variable names and function names remain in English (following programming conventions)

## Project Overview

This is a monorepo containing a Clinical Research Form (CRF) management system with:
- **Frontend**: Vue 3 + TypeScript medical form editor (apps/crf-editor)
- **Backend**: Go + Gin + GORM + PostgreSQL API server (backend/)
- **Architecture**: Monorepo with pnpm workspaces for frontend packages

## Common Development Commands

### Backend (Go)
```bash
# Navigate to backend directory first
cd backend/

# Development
make dev                    # Run in development mode
make build                  # Build for production
make run                    # Build and run
make test                   # Run tests
make test-coverage          # Run tests with coverage
make lint                   # Lint code
make fmt                    # Format code

# Database
make migrate                # Run database migrations
psql "$(DATABASE_URL)" -f database/schema.sql  # Manual migration

# Tools
make install-tools          # Install development tools
make dev-watch             # Auto-reload development server

# Scripts (from root directory)
../scripts/start.sh         # Start backend server
../scripts/test_compile.sh  # Test compilation
../scripts/test-save-functionality.sh  # Test save functionality
```

### Frontend (Vue 3 + TypeScript)
```bash
# Root level commands
pnpm dev                   # Start development server
pnpm build                 # Build all packages and apps
pnpm test                  # Run tests
pnpm lint                  # Lint all packages
pnpm clean                 # Clean build artifacts

# Package-specific commands
pnpm -C apps/crf-editor dev      # Run editor app only
pnpm -C apps/crf-editor build    # Build editor app only
pnpm -r --filter='!./apps/**' build  # Build packages only
```

## Architecture & Code Organization

### Backend Architecture (Go)
```
backend/
├── cmd/                   # Application entry points
├── internal/
│   ├── app/              # Application initialization
│   ├── config/           # Configuration management
│   ├── database/         # Database connection & utilities
│   ├── handlers/         # HTTP request handlers (REST endpoints)
│   ├── middleware/       # HTTP middleware (auth, logging, etc.)
│   ├── models/           # GORM database models
│   ├── router/           # Route definitions
│   ├── server/           # HTTP server setup
│   └── services/         # Business logic layer
├── database/             # SQL schemas and migrations
└── docs/                 # API documentation
```

**Key Backend Patterns:**
- **Layered Architecture**: handlers → services → models
- **Dependency Injection**: Services passed to handlers in app.go
- **JWT Authentication**: middleware.JWTAuth for protected routes
- **GORM Models**: All models use UUID primary keys, soft deletes
- **JSON Response**: Consistent response format via internal/response
- **Version-Aware Data**: Sophisticated versioning system for form evolution

### Frontend Architecture (Vue 3 + TypeScript)
```
apps/crf-editor/
├── src/
│   ├── components/
│   │   ├── edit/          # Form editor components
│   │   ├── preview/       # Form preview components
│   │   ├── form/          # Form rendering components
│   │   ├── version/       # Version management
│   │   └── instances/     # Form instance management
│   ├── composables/       # Vue composition functions
│   ├── stores/           # Pinia state management
│   ├── pages/            # Route components
│   ├── router/           # Vue Router setup
│   └── utils/            # Utility functions
packages/                  # Shared workspace packages
```

**Key Frontend Patterns:**
- **Composition API**: All components use `<script setup>`
- **Pinia Stores**: Centralized state management
- **Composables**: Reusable logic (useEditor, useAutoSave, etc.)
- **Component Architecture**: Edit/Preview/Form separation
- **Drag & Drop**: Vue.Draggable for form building
- **Auto-save**: Sophisticated auto-save with conflict resolution

## Key Development Areas

### RBAC权限管理系统
完整的基于角色的访问控制系统:
- **四种预定义角色**: 
  - Administrator (系统管理): 负责用户管理、权限分配、系统配置
  - Researcher (研究者): 负责设计表单、创建研究项目
  - Data Entry Personnel (数据录入员): 负责填写CRF表单
  - Reviewer (审阅者): 负责审核数据的准确性和完整性
- **权限控制**: 细粒度的资源和操作权限控制
- **用户管理**: 完整的用户创建、编辑、激活/停用功能
- **角色管理**: 动态角色配置和权限分配
- **API集成**: 完整的前后端权限验证
- **前端集成**: 用户管理功能集成在用户菜单下拉框中，位于"退出登录"上方

### 头像上传系统
集成MinIO对象存储的头像管理:
- **MinIO配置**: 
  - Endpoint: 47.113.207.13:9000
  - Access Key: minio_rRkfbm  
  - Secret Key: minio_BcMiih
  - Bucket: avatars
- **头像编辑器**: 支持裁剪、旋转、缩放的高级图像编辑功能
- **多尺寸预览**: 自动生成80x80、64x64、40x40等多种尺寸
- **文件验证**: 类型检查、大小限制(2MB)
- **个人资料集成**: 移除了账号统计功能，专注于头像和基本信息管理

### Data Management System
The system includes a comprehensive data management solution:
- **DataAnalysisService**: Form data analysis and statistics
- **DataExportService**: Export to CSV/JSON/Excel formats
- **VersionAwareDataService**: Handles schema evolution across form versions
- **API Endpoints**: `/api/data/templates/:id/*` for all data operations

### Form Version Evolution
Critical feature for research forms that change over time:
- **Version Tracking**: Each form version is stored with schema snapshot
- **Field Evolution**: Tracks when fields are added/modified/removed
- **Unified Data View**: Merges data from different versions for export
- **Migration Planning**: Suggests data migration strategies

### Authentication & Security
- **JWT Tokens**: Stateless authentication with refresh mechanism
- **Anonymous Users**: Public form access without registration
- **Role-Based Access**: Admin/user roles with different permissions
- **Session Management**: Redis-based session storage

### Form Building System
- **Schema-Based**: JSON schema defines form structure
- **Component Registry**: Extensible component system
- **Validation**: Client and server-side validation
- **Auto-save**: Automatic draft saving with conflict resolution

## Database Schema

### Core Tables
- `users` - User accounts with JWT authentication
- `projects` - Optional project grouping for templates
- `crf_templates` - Form templates with JSON schema
- `crf_versions` - Published versions of templates
- `crf_instances` - User-filled form instances
- `auto_saves` - Auto-saved drafts
- `operation_history` - Audit trail

### Data Types
- **UUIDs**: All primary keys use UUID v4
- **JSONB**: PostgreSQL JSONB for flexible schema storage
- **Timestamps**: All models include created_at/updated_at
- **Soft Deletes**: GORM soft delete for data retention

## Testing & Quality

### Backend Testing
```bash
cd backend/
make test                  # Run all tests
make test-coverage         # Generate coverage report
make lint                  # Run golangci-lint
make security              # Run gosec security scan
```

### Frontend Testing
```bash
pnpm test                  # Run Vitest tests
pnpm test:ci               # Run tests with coverage
pnpm type-check            # TypeScript type checking
pnpm lint                  # ESLint + Prettier
```

## Important Implementation Notes

### Version Management
- When forms are re-edited, new versions are created automatically
- The VersionAwareDataService ensures exported data includes all fields from all versions
- Missing fields in older versions are marked as null with metadata

### Anonymous User System
- Public forms accessible via `/form/fill/:id` without authentication
- Anonymous users can create/update/submit instances
- No registration required for form filling

### Auto-save Behavior
- Frontend auto-saves every 30 seconds with 2-second debounce
- Backend stores auto-save data with TTL cleanup
- Conflict resolution when multiple users edit same form

### Performance Considerations
- Redis caching for frequently accessed data
- Pagination for large datasets (max 100 items per page)
- JSONB indexing for form data queries
- Lazy loading for large component trees

## Configuration Files

### Backend Configuration
- `backend/config.yaml` - Main configuration
- `backend/.env` - Environment variables
- `backend/database/schema.sql` - Database schema

### Frontend Configuration
- `apps/crf-editor/vite.config.ts` - Vite build configuration
- `pnpm-workspace.yaml` - Workspace configuration
- `tsconfig.json` - TypeScript configuration

## Development Workflow

1. **Database Setup**: Run migrations with `make migrate`
2. **Backend Development**: Use `make dev` for auto-reload
3. **Frontend Development**: Use `pnpm dev` for hot-reload
4. **Full Stack Testing**: Use `pnpm dev:full` to run both
5. **Production Build**: Use `pnpm build` + `make build`
6. **System Check**: Use `scripts/system-check.sh` for integrity verification

## Common Pitfalls

- **UUID Types**: Always use `uuid.UUID` type in Go, not string
- **JSONB Queries**: Use proper JSONB operators in GORM queries
- **Authentication**: Remember to add JWT middleware to protected routes
- **Version Conflicts**: Handle form schema evolution carefully
- **Auto-save**: Don't forget to clear auto-save data after successful saves
- **CORS**: Backend has CORS configured for development
- **软删除**: 确保所有删除操作都使用软删除模式
- **权限检查**: 所有管理操作都需要相应的权限中间件保护

## API Integration

The system provides comprehensive REST APIs documented in `backend/docs/API.md`:
- Authentication: `/api/auth/*`
- Template Management: `/api/templates/*` 
- Instance Management: `/api/instances/*`
- Data Management: `/api/data/*`
- User Management: `/api/users/*`
- Role Management: `/api/roles/*`
- Permission Management: `/api/permissions/*`
- User Profile: `/api/users/:id/profile`, `/api/users/:id/avatar`

All APIs use consistent JSON response format with proper HTTP status codes and JWT authentication where required.

## 系统状态

**当前状态**: ✅ 系统完全可用
- 前后端服务正常运行
- RBAC权限系统完整实现
- MinIO头像上传功能已配置
- 所有TypeScript编译错误已修复
- 系统完整性检查通过
- 多语言支持完整实现（中文/英文）
- API接口已适配后端i18n支持
- 示例代码已清理完毕

**主要功能**:
- ✅ 用户登录注册
- ✅ JWT身份认证
- ✅ RBAC权限控制
- ✅ 用户管理界面
- ✅ 角色管理界面  
- ✅ 个人资料管理
- ✅ MinIO头像上传
- ✅ 表单编辑器
- ✅ 表单填写功能
- ✅ 完整的多语言支持
- ✅ 后端国际化API

**多语言功能**:
- ✅ 支持中文（zh-CN）和英文（en-US）
- ✅ 前端完整的i18n配置文件
- ✅ 语言切换器组件
- ✅ API请求头自动包含Accept-Language
- ✅ 后端返回对应语言的错误信息
- ✅ 组件、页面、错误信息的完整翻译

**快速检查**: 运行 `scripts/system-check.sh` 检查系统状态

## 多语言支持使用指南

### 基本使用

在Vue组件中使用多语言翻译：

```vue
<template>
  <div>
    <h1>{{ $t('components.auth.login.title') }}</h1>
    <button>{{ $t('common.buttons.save') }}</button>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/composables/useI18n'

const { t: $t, currentLocale, setLocale } = useI18n()

// 切换语言
const switchLanguage = () => {
  setLocale(currentLocale.value === 'zh-CN' ? 'en-US' : 'zh-CN')
}
</script>
```

### 语言文件结构

```
src/locales/
├── zh-CN/
│   ├── common.json      # 通用翻译
│   ├── components.json  # 组件翻译
│   ├── pages.json       # 页面翻译
│   └── errors.json      # 错误信息翻译
└── en-US/
    ├── common.json
    ├── components.json
    ├── pages.json
    └── errors.json
```

### 语言切换器组件

```vue
<template>
  <LanguageSwitcher 
    mode="dropdown" 
    :show-label="true" 
    :show-native="true" 
  />
</template>

<script setup lang="ts">
import LanguageSwitcher from '@/components/i18n/LanguageSwitcher.vue'
</script>
```

### API请求语言支持

前端API请求会自动包含当前语言的Accept-Language头：

```typescript
// API请求会自动包含
headers: {
  'Accept-Language': 'zh-CN'  // 或 'en-US'
}
```

后端会根据Accept-Language头返回对应语言的错误信息和响应内容。

## Code Standards & Naming Conventions

### Vue 3 Frontend Naming Standards

**CRITICAL: These naming conventions MUST be followed strictly to maintain code consistency.**

#### File Naming Rules

1. **Vue Components**: Use `PascalCase` (每个单词首字母大写)
   ```
   ✅ CORRECT:
   - ConfigBase.vue
   - EditRender.vue
   - FormRenderer.vue
   - VersionManager.vue
   - UserManagement.vue
   
   ❌ INCORRECT:
   - config-base.vue
   - edit-render.vue
   - form-renderer.vue
   - version-manager.vue
   - user-management.vue
   ```

2. **Page Components**: Use `kebab-case` (小写字母用连字符分隔)
   ```
   ✅ CORRECT:
   - index.vue
   - login.vue
   - register.vue
   - api-test.vue
   - user-profile.vue
   
   ❌ INCORRECT:
   - Index.vue
   - Login.vue
   - Register.vue
   - ApiTest.vue
   - UserProfile.vue
   ```

3. **Folder Names**: Use `kebab-case` (小写字母用连字符分隔)
   ```
   ✅ CORRECT:
   - components/
   - edit/
   - config/
   - form-fill/
   - icon-selector/
   
   ❌ INCORRECT:
   - Components/
   - Edit/
   - Config/
   - FormFill/
   - IconSelector/
   ```

4. **TypeScript/JavaScript Files**: Use `kebab-case` or `camelCase`
   ```
   ✅ CORRECT:
   - useFormConfig.ts
   - editor-store.ts
   - user-service.ts
   - api-client.ts
   
   ❌ INCORRECT:
   - UseFormConfig.ts
   - EditorStore.ts
   - UserService.ts
   - ApiClient.ts
   ```

#### Import Statement Standards

When importing components, ALWAYS use the exact file name:

```typescript
// ✅ CORRECT Examples:
import ConfigBase from '@/components/config/ConfigBase.vue'
import EditRender from '@/components/edit/render/EditRender.vue'
import FormRenderer from '@/components/form/FormRenderer.vue'
import VersionManager from '@/components/version/VersionManager.vue'
import UserManagement from '@/components/admin/UserManagement.vue'

// ❌ INCORRECT Examples:
import ConfigBase from '@/components/config/config-base.vue'
import EditRender from '@/components/edit/render/edit-render.vue'
import FormRenderer from '@/components/form/form-renderer.vue'
import VersionManager from '@/components/version/version-manager.vue'
import UserManagement from '@/components/admin/user-management.vue'
```

#### Component Registration

When registering components globally or locally:

```typescript
// ✅ CORRECT: Component names should be PascalCase
app.component('ConfigBase', ConfigBase)
app.component('EditRender', EditRender)
app.component('FormRenderer', FormRenderer)

// In templates, use kebab-case
<template>
  <config-base />
  <edit-render />
  <form-renderer />
</template>
```

#### File Structure Standards

```
apps/crf-editor/src/
├── components/              # Vue components (PascalCase files)
│   ├── config/
│   │   ├── ConfigBase.vue
│   │   ├── ConfigInput.vue
│   │   └── index.ts
│   ├── edit/
│   │   ├── block/
│   │   │   ├── EditBlock.vue
│   │   │   └── EditBlockDrag.vue
│   │   ├── render/
│   │   │   ├── EditRender.vue
│   │   │   └── EditRenderDrag.vue
│   │   └── config/
│   │       ├── EditConfig.vue
│   │       └── EditConfigBlock.vue
│   ├── form/
│   │   ├── FormField.vue
│   │   └── FormRenderer.vue
│   └── version/
│       ├── VersionManager.vue
│       ├── VersionCompare.vue
│       └── VersionDetails.vue
├── pages/                   # Page components (kebab-case files)
│   ├── auth/
│   │   ├── login.vue
│   │   └── register.vue
│   ├── admin/
│   │   ├── index.vue
│   │   ├── users.vue
│   │   └── roles.vue
│   └── settings/
│       ├── index.vue
│       └── api-test.vue
├── composables/            # Composable functions (camelCase)
│   ├── useEditor.ts
│   ├── useAutoSave.ts
│   └── useVersionManagement.ts
├── stores/                 # Pinia stores (kebab-case)
│   ├── editor-store.ts
│   ├── user-store.ts
│   └── permission-store.ts
└── utils/                  # Utility functions (kebab-case)
    ├── component-registry.ts
    ├── template-version-control.ts
    └── error-handler.ts
```

### Development Rules

1. **NEVER** create files with inconsistent naming
2. **ALWAYS** check existing file structure before adding new files
3. **ALWAYS** update import statements when renaming files
4. **ALWAYS** run `pnpm build` to verify no import errors after changes
5. **NEVER** mix PascalCase and kebab-case in the same directory type

### Code Review Checklist

Before committing code, ensure:
- [ ] All Vue component files use PascalCase
- [ ] All page files use kebab-case
- [ ] All folder names use kebab-case
- [ ] All import statements reference correct file names
- [ ] Build process completes without module resolution errors
- [ ] No TypeScript errors related to missing imports

### Enforcement

These standards are enforced to:
- Maintain consistency across the codebase
- Prevent module resolution errors
- Improve code readability and maintainability
- Follow Vue 3 official style guide recommendations
- Ensure proper IDE support and IntelliSense

**Violation of these standards will result in build failures and should be corrected immediately.**

## Vue 3 Composables Architecture

### Modern Composables Pattern

The project extensively uses Vue 3's Composition API with a sophisticated composables system for business logic separation and reusability:

#### Forms Management Composables System

The forms management exemplifies best-practice composables architecture:

```typescript
// Main orchestration composable
useFormsPage() {
  // Integrates multiple focused composables
  const formList = useFormList()           // Data management
  const formOperations = useFormOperations() // CRUD operations  
  const formSelection = useFormSelection() // Selection state
  const formFilters = useFormFilters()     // Filtering/search
  
  // Returns unified interface for components
  return { ...formList, ...formOperations, ...formSelection, ...formFilters }
}
```

#### Key Composables Patterns:

1. **Data Management (`useFormList`)**:
   - Handles form data fetching, caching, and state management
   - Manages different form states (published, unpublished, deleted)
   - Provides reactive form lists with automatic updates

2. **Operations (`useFormOperations`)**:
   - Encapsulates CRUD operations (create, duplicate, delete)
   - Handles operation states (loading, success, error)
   - Manages optimistic updates and rollback logic

3. **Selection Management (`useFormSelection`)**:
   - Controls form selection state across grid/table views
   - Handles batch operations and selection persistence
   - Provides selection utilities (select all, clear, etc.)

4. **Filtering & Search (`useFormFilters`)**:
   - Manages active tab state and form filtering
   - Handles search functionality with debouncing
   - Provides computed filtered form lists

#### Composables Best Practices:

- **Single Responsibility**: Each composable has a focused purpose
- **Reactive State**: Use `ref()` and `reactive()` for state management
- **Computed Properties**: Derive state with `computed()` for performance
- **Event Handling**: Centralize event logic within appropriate composables
- **Error Handling**: Consistent error handling patterns across composables
- **Type Safety**: Full TypeScript support with proper interfaces

### Component Communication Patterns

#### Event Parameter Handling

**Critical Pattern**: Always ensure proper event parameter passing:

```vue
<!-- ✅ CORRECT: FormGridView event handling -->
<FormCard
  @command="(cmd, form) => $emit('command', cmd, form)"
  @select="(formId, checked) => $emit('select', formId, checked)"
/>

<!-- ❌ INCORRECT: Event parameter loss -->
<FormCard
  @command="$emit('command', $event)"
  @select="$emit('select', $event)"
/>
```

#### Dropdown Command Pattern

For dropdown actions, ensure form object is properly passed:

```typescript
// FormCard.vue - Dropdown implementation
<n-dropdown
  :options="dropdownOptions" 
  @select="(cmd, option) => $emit('command', cmd, form)"
/>

// Parent component - Command handling
const handleCommand = async (command: string, form: FormItem) => {
  switch (command) {
    case 'duplicate':
      await formOperations.handleDuplicateForm(form, ...)
      break
    case 'delete':
      await formOperations.handleDeleteForm(form, ...)
      break
  }
}
```

## Recent Frontend Architecture Improvements

### Vue 3 Component Refactoring Success Story

**Major Achievement**: Successfully refactored monolithic `/pages/forms/index.vue` (1572 lines) into modular architecture:

#### Before Refactoring:
- Single massive component with mixed concerns
- All UI, business logic, and state management in one file
- Difficult to maintain, test, and extend
- Poor separation of concerns

#### After Refactoring (93% code reduction):
- **Main Component**: 100 lines (orchestration only)
- **9 Extracted Components**: Focused, reusable UI components
- **5 Composables**: Separated business logic
- **3 Constants Files**: Configuration and table definitions

#### Extracted Components:
1. `StatisticsCards.vue` - Form statistics display
2. `FormListToolbar.vue` - Search, filter, and action controls  
3. `FormGridView.vue` - Grid layout for form cards
4. `FormTableView.vue` - Table layout for form data
5. `FormCard.vue` - Individual form card with actions
6. `CreateFormDialog.vue` - Form creation modal
7. `LoadingSkeletonGrid.vue` - Loading state UI
8. `LoadingIndicator.vue` - Operation feedback
9. `EmptyState.vue` - No data state

#### Business Logic Composables:
1. `useFormsPage.ts` - Main orchestration
2. `useFormList.ts` - Data management
3. `useFormOperations.ts` - CRUD operations
4. `useFormSelection.ts` - Selection handling
5. `useFormFilters.ts` - Filtering and search

### Refactoring Guidelines

When encountering large components (>600 lines), apply this systematic approach:

1. **Identify Concerns**: Separate UI, business logic, and state
2. **Extract Components**: Create focused, single-purpose components
3. **Create Composables**: Extract reusable business logic
4. **Maintain Interfaces**: Ensure proper event handling and prop passing
5. **Test Integration**: Verify all functionality remains intact

## Enhanced Development Commands

### Frontend Commands (Updated)

```bash
# Development
pnpm dev                    # Start development server
pnpm dev:prod              # Start in production mode  
pnpm dev:test              # Start in test mode
pnpm dev:full              # Start with mock API server

# Building
pnpm build                 # Build all packages and apps
pnpm build:packages        # Build packages only
pnpm build:apps           # Build apps only
pnpm build:dev            # Build in development mode
pnpm build:prod           # Build in production mode

# Type Checking & Quality
pnpm type-check           # TypeScript type checking
pnpm type-check:packages  # Type check packages only
pnpm lint                 # Lint all packages
pnpm lint:fix            # Fix linting issues

# Testing
pnpm test                 # Run Vitest tests
pnpm test:ci             # Run tests with coverage

# UnoCSS & Styling
pnpm unocss:dev          # Watch UnoCSS changes
pnpm unocss:build        # Build UnoCSS
pnpm unocss:inspect      # Inspect UnoCSS classes
pnpm style:check         # Check UnoCSS build

# Code Quality
pnpm quality:check       # Run code quality analysis
pnpm quality:report      # Generate quality report

# Utilities
pnpm clean               # Clean build artifacts
pnpm clean:dist         # Clean distribution files
```

### Backend Commands (Enhanced)

```bash
# Development & Building
make dev                 # Run in development mode with hot reload
make dev-watch          # Auto-reload development server (requires air)
make build-local        # Build for current platform
make run                # Build and run application

# Testing & Quality
make test               # Run all tests
make test-coverage      # Run tests with coverage report
make lint              # Run golangci-lint
make fmt               # Format code with go fmt
make security          # Run gosec security analysis

# Database Operations
make migrate           # Run database migrations
psql "$(DATABASE_URL)" -f database/schema.sql  # Manual migration

# Tools & Setup
make install-tools     # Install development tools (air, golangci-lint, gosec)
make deps             # Download and tidy dependencies
make clean            # Clean build artifacts

# Docker Operations
make docker-build     # Build Docker image
make docker-run       # Run with Docker
```

### System Verification

```bash
# System Health Check
scripts/system-check.sh          # Complete system integrity check
scripts/final-verification.sh    # Final verification script
scripts/test-save-functionality.sh  # Test save functionality
```

## Advanced Development Patterns

### Form Card Component Architecture

The `FormCard.vue` component demonstrates advanced Vue 3 patterns:

#### Fixed Height Layout Pattern
```scss
.form-card {
  display: flex;
  flex-direction: column;
  height: 220px;  // Fixed height for consistency
  
  .form-card-body {
    flex: 1;  // Take available space
  }
  
  .form-actions {
    margin-top: auto;  // Push to bottom
  }
}
```

#### Enhanced Checkbox Interaction
```vue
<div class="form-checkbox-container" @click.stop>
  <n-checkbox
    :checked="isSelected"
    @update:checked="handleCheckboxClick" 
    class="form-card-checkbox"
  />
</div>

<script setup>
const handleCheckboxClick = (checked: boolean) => {
  emit('select', props.form.id, checked)
}
</script>
```

### Error Handling Patterns

#### Dropdown Command Error Prevention
```typescript
// Always validate form object before operations
const handleCommand = async (command: string, form: FormItem) => {
  if (!form || !form.id) {
    console.error('Invalid form object:', form)
    return
  }
  
  switch (command) {
    case 'duplicate':
      await handleDuplicateForm(form)
      break
    case 'delete':
      await handleDeleteForm(form)
      break
  }
}
```

#### Event Parameter Validation
```typescript
// FormGridView.vue - Ensure parameters are correctly passed
<FormCard
  @command="(cmd, form) => {
    if (!cmd || !form) {
      console.error('Invalid command parameters:', { cmd, form })
      return
    }
    $emit('command', cmd, form)
  }"
/>
```

## Troubleshooting & Common Issues

### Build & Runtime Errors

1. **TypeScript Compilation Errors**: Run `pnpm type-check` to identify issues
2. **Missing Import Errors**: Verify file naming conventions (PascalCase vs kebab-case)
3. **Event Parameter Issues**: Ensure proper parameter passing in component chains
4. **State Management**: Check composable integration and reactive state updates

### Performance Optimization

1. **Large Form Lists**: Use virtual scrolling for 100+ items
2. **Auto-save**: Implement debouncing (2-second delay, 30-second interval)
3. **Component Loading**: Use lazy loading for non-critical components
4. **State Updates**: Batch state updates to prevent excessive re-renders

### Development Best Practices

1. **Component Size**: Keep components under 300 lines, extract when larger
2. **Composable Design**: Single responsibility, focused purpose
3. **Event Handling**: Always validate event parameters
4. **Error Boundaries**: Implement proper error handling in composables
5. **Type Safety**: Use TypeScript interfaces for all component props and events

## System Integration Notes

### API Integration Patterns
- All APIs follow consistent JSON response format
- JWT authentication for protected routes
- Proper error handling with user-friendly messages
- Request debouncing for search and auto-save features

### State Management Architecture
- Pinia stores for global state (user, permissions, editor)
- Composables for component-level business logic
- Reactive state management with Vue 3's reactivity system
- Proper cleanup and memory management

This enhanced architecture ensures maintainable, scalable, and performant Vue 3 applications with clear separation of concerns and robust error handling.