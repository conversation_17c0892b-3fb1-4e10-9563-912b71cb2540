{"$schema": "https://json.schemastore.org/tsconfig", "display": "Package Build", "extends": "./base.json", "compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "bundler", "allowImportingTsExtensions": false, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": false, "outDir": "dist", "strict": true, "exactOptionalPropertyTypes": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "composite": true, "incremental": true, "lib": ["es2020", "dom"]}, "include": ["src/**/*", "index.ts", "*.ts"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts", "**/*.d.ts", "**/*.js", "**/*.map"]}