{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"target": "es2018", "module": "esnext", "moduleResolution": "node", "strict": true, "noUnusedLocals": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "removeComments": false, "preserveSymlinks": true, "skipLibCheck": true, "declaration": true, "declarationMap": false, "sourceMap": false, "composite": true, "incremental": true, "baseUrl": ".", "paths": {}}, "exclude": ["node_modules"]}