import { defineBuildConfig } from 'unbuild'

export interface BuildOptions {
    /**
     * 入口文件
     */
    entries?: string[]
    /**
     * 输出目录
     */
    outDir?: string
    /**
     * 是否生成声明文件
     */
    declaration?: boolean
    /**
     * 是否清理输出目录
     */
    clean?: boolean
    /**
     * 是否生成 CJS 格式
     */
    emitCJS?: boolean
    /**
     * 外部依赖
     */
    externals?: string[]
    /**
     * 是否在警告时失败
     */
    failOnWarn?: boolean
    /**
     * 是否替换依赖
     */
    replace?: Record<string, string>
}

/**
 * 默认的外部依赖
 */
const DEFAULT_EXTERNALS = [
    // Vue 核心
    'vue',
    '@vue/shared',
    '@vue/runtime-core',
    '@vue/runtime-dom',

    // UI 框架
    'naive-ui',

    // 状态管理
    'pinia',

    // 图标
    '@iconify/vue',

    // 工具库
    'nanoid',
    'lodash-es',

    // 类型相关
    '@sinclair/typebox'
]

/**
 * 创建标准的构建配置
 */
export function createBuildConfig(options: BuildOptions = {}) {
    const {
        entries = ['./index'],
        outDir = 'dist',
        declaration = true,
        clean = true,
        emitCJS = true,
        externals = [],
        failOnWarn = false,
        replace = {}
    } = options

    return defineBuildConfig({
        entries,
        outDir,
        declaration,
        clean,
        rollup: {
            emitCJS,
            inlineDependencies: false,
            esbuild: {
                target: 'es2020',
                minify: false,
                sourcemap: false
            }
        },
        sourcemap: false,
        externals: [
            ...DEFAULT_EXTERNALS,
            ...externals
        ],
        failOnWarn,
        replace
    })
}

/**
 * 组件包的构建配置
 */
export function createComponentBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            '@crf/utils',
            '@crf/hooks',
            '@crf/constants',
            ...(options.externals || [])
        ]
    })
}

/**
 * 工具包的构建配置
 */
export function createUtilsBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            ...(options.externals || [])
        ]
    })
}

/**
 * 类型包的构建配置
 */
export function createTypesBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        emitCJS: false, // 类型包不需要 CJS
        externals: [
            ...(options.externals || [])
        ]
    })
}

/**
 * 主题包的构建配置
 */
export function createThemeBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            '@crf/constants',
            '@unocss/core',
            'sass',
            ...(options.externals || [])
        ]
    })
}

/**
 * 编辑器核心包的构建配置
 */
export function createEditorCoreBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            '@crf/utils',
            '@crf/constants',
            ...(options.externals || [])
        ]
    })
}

/**
 * 组合式函数包的构建配置
 */
export function createComposablesBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            '@crf/utils',
            '@crf/hooks',
            ...(options.externals || [])
        ]
    })
}

/**
 * 常量包的构建配置
 */
export function createConstantsBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            ...(options.externals || [])
        ]
    })
}

/**
 * Hooks包的构建配置
 */
export function createHooksBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            '@crf/utils',
            ...(options.externals || [])
        ]
    })
}

/**
 * 网络包的构建配置
 */
export function createNetworkBuildConfig(options: BuildOptions = {}) {
    return createBuildConfig({
        ...options,
        externals: [
            '@crf/types',
            '@crf/utils',
            '@crf/constants',
            '@crf/composables',
            ...(options.externals || [])
        ]
    })
}

// 导出默认配置
export default createBuildConfig