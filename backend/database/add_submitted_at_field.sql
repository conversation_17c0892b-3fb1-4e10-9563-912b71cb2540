-- =================================================================
-- 数据库迁移脚本：添加submitted_at字段到crf_instances表
-- 执行日期：2025-07-10
-- 描述：修复表单实例提交功能所需的数据库字段
-- =================================================================

-- 1. 添加submitted_at字段到crf_instances表
DO $$
BEGIN
    -- 检查并添加submitted_at字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'crf_instances' AND column_name = 'submitted_at'
    ) THEN
        ALTER TABLE crf_instances 
        ADD COLUMN submitted_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added submitted_at column to crf_instances';
    ELSE
        RAISE NOTICE 'submitted_at column already exists in crf_instances';
    END IF;
END
$$;

-- 2. 添加索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_crf_instances_submitted_at ON crf_instances(submitted_at);

-- 3. 更新现有已提交的实例（如果status为'submitted'但submitted_at为空）
UPDATE crf_instances 
SET submitted_at = updated_at 
WHERE status = 'submitted' AND submitted_at IS NULL;

-- 4. 验证字段是否添加成功
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'crf_instances' 
AND column_name = 'submitted_at';

-- 5. 显示当前crf_instances表的所有列（用于验证）
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'crf_instances' 
ORDER BY ordinal_position;

-- 6. 显示统计信息
SELECT 
    COUNT(*) as total_instances,
    COUNT(submitted_at) as instances_with_submitted_at,
    COUNT(*) - COUNT(submitted_at) as instances_without_submitted_at
FROM crf_instances;

SELECT 'Migration completed! The submitted_at field has been added to crf_instances table.' as result;