-- ================================================================
-- 修复admin用户权限的SQL脚本
-- 适用于WSL环境手工执行
-- ================================================================

-- 1. 首先查看当前状态
SELECT '=== 当前admin用户信息 ===' as info;
SELECT id, username, email, created_at FROM users WHERE username = 'admin';

SELECT '=== 当前系统角色 ===' as info;
SELECT id, name, display_name, is_system FROM roles WHERE is_system = true ORDER BY name;

SELECT '=== admin用户当前角色分配 ===' as info;
SELECT 
    u.username,
    r.name as role_name,
    r.display_name,
    ur.project_id,
    ur.assigned_at
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'admin';

-- 2. 删除admin用户现有的所有角色分配
SELECT '=== 清理现有角色分配 ===' as info;
DELETE FROM user_roles 
WHERE user_id = (SELECT id FROM users WHERE username = 'admin');

-- 3. 为admin用户分配super_admin角色
SELECT '=== 分配超级管理员角色 ===' as info;
INSERT INTO user_roles (
    id,
    user_id, 
    role_id, 
    project_id, 
    assigned_by, 
    assigned_at,
    created_at,
    updated_at
) 
SELECT 
    uuid_generate_v4(),
    u.id as user_id,
    r.id as role_id,
    NULL as project_id, -- 全局角色
    u.id as assigned_by, -- 自己分配
    CURRENT_TIMESTAMP as assigned_at,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at
FROM users u, roles r 
WHERE u.username = 'admin' 
  AND r.name = 'super_admin';

-- 4. 清除admin用户的权限缓存
SELECT '=== 清除权限缓存 ===' as info;
DELETE FROM user_permission_cache 
WHERE user_id = (SELECT id FROM users WHERE username = 'admin');

-- 5. 验证修复结果
SELECT '=== 修复结果验证 ===' as info;
SELECT 
    u.username,
    u.email,
    r.name as role_name,
    r.display_name as role_display_name,
    ur.project_id,
    ur.assigned_at,
    CASE 
        WHEN ur.project_id IS NULL THEN '全局权限'
        ELSE '项目权限'
    END as scope
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'admin';

-- 6. 检查admin用户应该拥有的部分关键权限
SELECT '=== 权限检查 (应该返回多条记录) ===' as info;
SELECT DISTINCT
    p.resource,
    p.action,
    p.scope,
    p.description
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin'
  AND p.resource IN ('user', 'role', 'template', 'instance')
ORDER BY p.resource, p.action, p.scope;

SELECT '=== 修复完成! ===' as info;
SELECT 'admin用户现在应该拥有超级管理员权限，可以访问系统管理页面' as message;