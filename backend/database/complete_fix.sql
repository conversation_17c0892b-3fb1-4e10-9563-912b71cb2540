-- ================================================================
-- 完整修复admin用户权限问题
-- ================================================================

-- 1. 清除所有权限缓存
DELETE FROM user_permission_cache;

-- 2. 确认并重新分配admin用户的super_admin角色
DELETE FROM user_roles WHERE user_id = (SELECT id FROM users WHERE username = 'admin');

INSERT INTO user_roles (
    id,
    user_id, 
    role_id, 
    project_id, 
    assigned_by, 
    assigned_at,
    created_at,
    updated_at
) 
SELECT 
    uuid_generate_v4(),
    u.id,
    r.id,
    NULL, -- 全局角色
    u.id, -- 自己分配
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM users u, roles r 
WHERE u.username = 'admin' 
  AND r.name = 'super_admin';

-- 3. 验证admin用户的权限
SELECT '=== admin用户角色验证 ===' as step;
SELECT 
    u.username,
    r.name as role_name,
    r.display_name,
    COUNT(p.id) as permission_count
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin'
  AND ur.deleted_at IS NULL
  AND rp.deleted_at IS NULL
  AND p.deleted_at IS NULL
GROUP BY u.username, r.name, r.display_name;

-- 4. 检查关键template权限
SELECT '=== template权限检查 ===' as step;
SELECT 
    u.username,
    p.resource,
    p.action,
    p.scope,
    p.description
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin'
  AND p.resource = 'template'
  AND ur.deleted_at IS NULL
  AND rp.deleted_at IS NULL
  AND p.deleted_at IS NULL
ORDER BY p.action, p.scope;

-- 5. 检查用户管理权限
SELECT '=== 用户管理权限检查 ===' as step;
SELECT 
    u.username,
    p.resource,
    p.action,
    p.scope,
    p.description
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin'
  AND p.resource IN ('user', 'role')
  AND ur.deleted_at IS NULL
  AND rp.deleted_at IS NULL
  AND p.deleted_at IS NULL
ORDER BY p.resource, p.action, p.scope;

-- 6. 测试权限检查函数
SELECT '=== 权限函数测试 ===' as step;
SELECT 
    u.username,
    u.id as user_id,
    check_user_permission(u.id, 'template', 'read', NULL) as can_read_template,
    check_user_permission(u.id, 'template', 'create', NULL) as can_create_template,
    check_user_permission(u.id, 'user', 'read', NULL) as can_read_user,
    check_user_permission(u.id, 'role', 'read', NULL) as can_read_role
FROM users u
WHERE u.username = 'admin';

SELECT '=== 修复完成 ===' as final_result;