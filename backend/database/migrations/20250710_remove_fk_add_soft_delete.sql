-- ================================================================
-- 数据库迁移脚本: 移除外键约束 + 添加逻辑删除
-- ================================================================
-- 迁移版本: 20250710_remove_fk_add_soft_delete
-- 创建时间: 2025-07-10
-- 描述: 移除所有外键约束，为所有表添加逻辑删除字段，提升系统灵活性和数据统计能力
-- ================================================================

-- 开始事务
BEGIN;

-- ================================================================
-- 第一步：移除所有外键约束
-- ================================================================

-- 记录开始时间
INSERT INTO system_settings (key, value, description) VALUES
    ('migration_20250710_start', '"' || CURRENT_TIMESTAMP || '"', '迁移开始时间')
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- 1. 移除 user_sessions 表的外键约束
ALTER TABLE user_sessions DROP CONSTRAINT IF EXISTS user_sessions_user_id_fkey;

-- 2. 移除 projects 表的外键约束
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_created_by_fkey;

-- 3. 移除 crf_templates 表的外键约束
ALTER TABLE crf_templates DROP CONSTRAINT IF EXISTS crf_templates_project_id_fkey;
ALTER TABLE crf_templates DROP CONSTRAINT IF EXISTS crf_templates_published_by_fkey;
ALTER TABLE crf_templates DROP CONSTRAINT IF EXISTS crf_templates_created_by_fkey;
ALTER TABLE crf_templates DROP CONSTRAINT IF EXISTS crf_templates_updated_by_fkey;

-- 4. 移除 crf_versions 表的外键约束
ALTER TABLE crf_versions DROP CONSTRAINT IF EXISTS crf_versions_template_id_fkey;
ALTER TABLE crf_versions DROP CONSTRAINT IF EXISTS crf_versions_published_by_fkey;
ALTER TABLE crf_versions DROP CONSTRAINT IF EXISTS crf_versions_created_by_fkey;

-- 5. 移除 crf_instances 表的外键约束
ALTER TABLE crf_instances DROP CONSTRAINT IF EXISTS crf_instances_template_id_fkey;
ALTER TABLE crf_instances DROP CONSTRAINT IF EXISTS crf_instances_locked_by_fkey;
ALTER TABLE crf_instances DROP CONSTRAINT IF EXISTS crf_instances_reviewed_by_fkey;
ALTER TABLE crf_instances DROP CONSTRAINT IF EXISTS crf_instances_created_by_fkey;
ALTER TABLE crf_instances DROP CONSTRAINT IF EXISTS crf_instances_updated_by_fkey;

-- 6. 移除 auto_saves 表的外键约束
ALTER TABLE auto_saves DROP CONSTRAINT IF EXISTS auto_saves_user_id_fkey;

-- 7. 移除 operation_history 表的外键约束
ALTER TABLE operation_history DROP CONSTRAINT IF EXISTS operation_history_user_id_fkey;
-- 这里也可以写作：
-- ALTER TABLE operation_history DROP CONSTRAINT IF EXISTS fk_operation_histories_user;

-- 8. 移除 attachments 表的外键约束
ALTER TABLE attachments DROP CONSTRAINT IF EXISTS attachments_uploaded_by_fkey;

-- 9. 移除 todos 表的外键约束
ALTER TABLE todos DROP CONSTRAINT IF EXISTS todos_created_by_fkey;
ALTER TABLE todos DROP CONSTRAINT IF EXISTS todos_assigned_to_fkey;
ALTER TABLE todos DROP CONSTRAINT IF EXISTS todos_project_id_fkey;

-- 10. 移除 todo_comments 表的外键约束
ALTER TABLE todo_comments DROP CONSTRAINT IF EXISTS todo_comments_todo_id_fkey;
ALTER TABLE todo_comments DROP CONSTRAINT IF EXISTS todo_comments_created_by_fkey;

-- ================================================================
-- 第二步：为所有表添加逻辑删除字段
-- ================================================================

-- 1. users 表添加逻辑删除
ALTER TABLE users ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 2. user_sessions 表添加逻辑删除
ALTER TABLE user_sessions ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE user_sessions ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 3. projects 表添加逻辑删除
ALTER TABLE projects ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 4. crf_templates 表添加逻辑删除
ALTER TABLE crf_templates ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE crf_templates ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 5. crf_versions 表添加逻辑删除
ALTER TABLE crf_versions ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE crf_versions ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 6. crf_instances 表添加逻辑删除
ALTER TABLE crf_instances ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE crf_instances ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 7. auto_saves 表添加逻辑删除
ALTER TABLE auto_saves ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE auto_saves ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 8. operation_history 表添加逻辑删除（操作历史通常不删除，但为了一致性添加）
ALTER TABLE operation_history ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE operation_history ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 9. attachments 表添加逻辑删除
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE attachments ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 10. system_settings 表添加逻辑删除
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 11. todos 表添加逻辑删除
ALTER TABLE todos ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 12. todo_comments 表添加逻辑删除
ALTER TABLE todo_comments ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE todo_comments ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 13. migrations 表添加逻辑删除（迁移记录通常不删除，但为了一致性添加）
ALTER TABLE migrations ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE migrations ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- ================================================================
-- 第三步：创建逻辑删除相关的索引
-- ================================================================

-- 为所有表的逻辑删除字段创建索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_users_deleted ON users(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_deleted ON user_sessions(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_projects_deleted ON projects(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_deleted ON crf_templates(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_crf_versions_deleted ON crf_versions(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_crf_instances_deleted ON crf_instances(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_auto_saves_deleted ON auto_saves(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_operation_history_deleted ON operation_history(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_attachments_deleted ON attachments(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_system_settings_deleted ON system_settings(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_todos_deleted ON todos(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_todo_comments_deleted ON todo_comments(is_deleted, deleted_at);
CREATE INDEX IF NOT EXISTS idx_migrations_deleted ON migrations(is_deleted, deleted_at);

-- ================================================================
-- 第四步：创建逻辑删除相关的函数和触发器
-- ================================================================

-- 创建逻辑删除函数
CREATE OR REPLACE FUNCTION trigger_soft_delete()
RETURNS TRIGGER AS $$
BEGIN
    -- 当is_deleted设置为true时，自动设置deleted_at时间戳
    IF NEW.is_deleted = TRUE AND OLD.is_deleted = FALSE THEN
        NEW.deleted_at = CURRENT_TIMESTAMP;
    END IF;
    
    -- 当is_deleted设置为false时，清除deleted_at时间戳
    IF NEW.is_deleted = FALSE AND OLD.is_deleted = TRUE THEN
        NEW.deleted_at = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为所有表创建逻辑删除触发器
CREATE TRIGGER soft_delete_users BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_user_sessions BEFORE UPDATE ON user_sessions 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_projects BEFORE UPDATE ON projects 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_crf_templates BEFORE UPDATE ON crf_templates 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_crf_versions BEFORE UPDATE ON crf_versions 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_crf_instances BEFORE UPDATE ON crf_instances 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_auto_saves BEFORE UPDATE ON auto_saves 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_operation_history BEFORE UPDATE ON operation_history 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_attachments BEFORE UPDATE ON attachments 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_system_settings BEFORE UPDATE ON system_settings 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_todos BEFORE UPDATE ON todos 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_todo_comments BEFORE UPDATE ON todo_comments 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();
CREATE TRIGGER soft_delete_migrations BEFORE UPDATE ON migrations 
    FOR EACH ROW EXECUTE FUNCTION trigger_soft_delete();

-- ================================================================
-- 第五步：创建数据统计和查询的视图
-- ================================================================

-- 创建活跃用户视图（排除已逻辑删除的用户）
CREATE OR REPLACE VIEW active_users AS
SELECT * FROM users WHERE is_deleted = FALSE;

-- 创建活跃项目视图
CREATE OR REPLACE VIEW active_projects AS
SELECT * FROM projects WHERE is_deleted = FALSE;

-- 创建活跃模板视图
CREATE OR REPLACE VIEW active_crf_templates AS
SELECT * FROM crf_templates WHERE is_deleted = FALSE;

-- 创建活跃实例视图
CREATE OR REPLACE VIEW active_crf_instances AS
SELECT * FROM crf_instances WHERE is_deleted = FALSE;

-- 创建活跃任务视图
CREATE OR REPLACE VIEW active_todos AS
SELECT * FROM todos WHERE is_deleted = FALSE;

-- ================================================================
-- 第六步：创建匿名用户记录（解决外键约束问题）
-- ================================================================

-- 创建系统匿名用户（用于操作历史等记录）
INSERT INTO users (
    id,
    username,
    email,
    password_hash,
    full_name,
    role,
    is_active,
    is_deleted
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    'system_anonymous',
    '<EMAIL>',
    'no_password_required',
    '系统匿名用户',
    'guest',
    true,
    false
) ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    is_active = EXCLUDED.is_active,
    is_deleted = EXCLUDED.is_deleted,
    updated_at = CURRENT_TIMESTAMP;

-- 创建默认项目（确保有一个默认项目供模板使用）
INSERT INTO projects (
    id,
    name,
    description,
    created_by,
    status,
    is_deleted
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    'default_project',
    '系统默认项目，用于承载无特定项目归属的模板',
    '00000000-0000-0000-0000-000000000001',
    'active',
    false
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    created_by = EXCLUDED.created_by,
    status = EXCLUDED.status,
    is_deleted = EXCLUDED.is_deleted,
    updated_at = CURRENT_TIMESTAMP;

-- ================================================================
-- 第七步：更新系统设置
-- ================================================================

-- 记录迁移完成时间和相关配置
INSERT INTO system_settings (key, value, description) VALUES
    ('soft_delete_enabled', 'true', '是否启用逻辑删除功能'),
    ('foreign_key_constraints_removed', 'true', '外键约束已移除，由程序逻辑维护数据完整性'),
    ('data_statistics_enabled', 'true', '启用数据统计功能，包括已删除数据的统计'),
    ('anonymous_user_id', '"00000000-0000-0000-0000-000000000001"', '系统匿名用户ID'),
    ('default_project_id', '"00000000-0000-0000-0000-000000000001"', '系统默认项目ID'),
    ('migration_20250710_completed', '"' || CURRENT_TIMESTAMP || '"', '迁移完成时间')
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- ================================================================
-- 第八步：创建数据统计和清理的存储过程
-- ================================================================

-- 创建数据统计函数
CREATE OR REPLACE FUNCTION get_table_statistics()
RETURNS TABLE(
    table_name TEXT,
    total_records BIGINT,
    active_records BIGINT,
    deleted_records BIGINT,
    deletion_rate NUMERIC(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'users'::TEXT,
        (SELECT COUNT(*) FROM users),
        (SELECT COUNT(*) FROM users WHERE is_deleted = FALSE),
        (SELECT COUNT(*) FROM users WHERE is_deleted = TRUE),
        CASE 
            WHEN (SELECT COUNT(*) FROM users) > 0 
            THEN ROUND(((SELECT COUNT(*) FROM users WHERE is_deleted = TRUE)::NUMERIC / (SELECT COUNT(*) FROM users)::NUMERIC) * 100, 2)
            ELSE 0::NUMERIC(5,2)
        END
    UNION ALL
    SELECT 
        'projects'::TEXT,
        (SELECT COUNT(*) FROM projects),
        (SELECT COUNT(*) FROM projects WHERE is_deleted = FALSE),
        (SELECT COUNT(*) FROM projects WHERE is_deleted = TRUE),
        CASE 
            WHEN (SELECT COUNT(*) FROM projects) > 0 
            THEN ROUND(((SELECT COUNT(*) FROM projects WHERE is_deleted = TRUE)::NUMERIC / (SELECT COUNT(*) FROM projects)::NUMERIC) * 100, 2)
            ELSE 0::NUMERIC(5,2)
        END
    UNION ALL
    SELECT 
        'crf_templates'::TEXT,
        (SELECT COUNT(*) FROM crf_templates),
        (SELECT COUNT(*) FROM crf_templates WHERE is_deleted = FALSE),
        (SELECT COUNT(*) FROM crf_templates WHERE is_deleted = TRUE),
        CASE 
            WHEN (SELECT COUNT(*) FROM crf_templates) > 0 
            THEN ROUND(((SELECT COUNT(*) FROM crf_templates WHERE is_deleted = TRUE)::NUMERIC / (SELECT COUNT(*) FROM crf_templates)::NUMERIC) * 100, 2)
            ELSE 0::NUMERIC(5,2)
        END
    UNION ALL
    SELECT 
        'crf_instances'::TEXT,
        (SELECT COUNT(*) FROM crf_instances),
        (SELECT COUNT(*) FROM crf_instances WHERE is_deleted = FALSE),
        (SELECT COUNT(*) FROM crf_instances WHERE is_deleted = TRUE),
        CASE 
            WHEN (SELECT COUNT(*) FROM crf_instances) > 0 
            THEN ROUND(((SELECT COUNT(*) FROM crf_instances WHERE is_deleted = TRUE)::NUMERIC / (SELECT COUNT(*) FROM crf_instances)::NUMERIC) * 100, 2)
            ELSE 0::NUMERIC(5,2)
        END
    UNION ALL
    SELECT 
        'todos'::TEXT,
        (SELECT COUNT(*) FROM todos),
        (SELECT COUNT(*) FROM todos WHERE is_deleted = FALSE),
        (SELECT COUNT(*) FROM todos WHERE is_deleted = TRUE),
        CASE 
            WHEN (SELECT COUNT(*) FROM todos) > 0 
            THEN ROUND(((SELECT COUNT(*) FROM todos WHERE is_deleted = TRUE)::NUMERIC / (SELECT COUNT(*) FROM todos)::NUMERIC) * 100, 2)
            ELSE 0::NUMERIC(5,2)
        END;
END;
$$ LANGUAGE plpgsql;

-- 创建清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_data(days_threshold INTEGER DEFAULT 30)
RETURNS TABLE(
    table_name TEXT,
    deleted_count BIGINT
) AS $$
DECLARE
    threshold_date TIMESTAMP WITH TIME ZONE;
BEGIN
    threshold_date := CURRENT_TIMESTAMP - (days_threshold || ' days')::INTERVAL;
    
    -- 清理过期的自动保存记录
    DELETE FROM auto_saves 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_deleted = TRUE 
    AND deleted_at < threshold_date;
    
    RETURN QUERY SELECT 'auto_saves'::TEXT, ROW_COUNT::BIGINT;
    
    -- 清理过期的用户会话
    DELETE FROM user_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_deleted = TRUE 
    AND deleted_at < threshold_date;
    
    RETURN QUERY SELECT 'user_sessions'::TEXT, ROW_COUNT::BIGINT;
    
    -- 可以根据需要添加更多表的清理逻辑
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- 第九步：记录迁移信息
-- ================================================================

-- 在migrations表中记录这次迁移
INSERT INTO migrations (version, name, applied, applied_at) VALUES
    ('20250710_remove_fk_add_soft_delete', 'Remove foreign key constraints and add soft delete capability', true, CURRENT_TIMESTAMP)
ON CONFLICT (version) DO UPDATE SET
    applied = true,
    applied_at = CURRENT_TIMESTAMP;

-- 提交事务
COMMIT;

-- ================================================================
-- 迁移完成信息输出
-- ================================================================

SELECT 'Database Migration 20250710 Completed Successfully!' as message;
SELECT 'Foreign key constraints removed: All tables now use programmatic integrity' as fk_status;
SELECT 'Soft delete fields added: deleted_at and is_deleted columns added to all tables' as soft_delete_status;
SELECT 'Indexes created: Performance optimized for soft delete queries' as index_status;
SELECT 'Views created: Active record views available for all main tables' as view_status;
SELECT 'System users created: Anonymous user and default project established' as system_data_status;
SELECT 'Statistics functions: get_table_statistics() and cleanup_expired_data() available' as functions_status;

-- 显示迁移统计信息
SELECT * FROM get_table_statistics();