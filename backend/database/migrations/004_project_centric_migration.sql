-- ================================================================
-- 项目中心化架构迁移脚本
-- Migration Script for Project-Centric Architecture
-- Version: 1.0.0
-- Created: 2025-07-29
-- Description: 迁移现有数据到新的项目中心化架构
-- ================================================================

-- 开始事务
BEGIN;

-- ================================================================
-- 第一步: 检查当前数据状态
-- ================================================================

-- 检查是否已经执行过迁移
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'project_members'
    ) THEN
        RAISE NOTICE '检测到项目成员表已存在，检查是否需要数据迁移...';
    ELSE
        RAISE EXCEPTION '错误：项目成员表不存在，请先运行schema更新脚本';
    END IF;
END $$;

-- 统计当前数据
DO $$
DECLARE
    template_count INTEGER;
    user_count INTEGER;
    project_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO template_count FROM crf_templates WHERE is_deleted = false;
    SELECT COUNT(*) INTO user_count FROM users WHERE is_active = true;
    SELECT COUNT(*) INTO project_count FROM projects WHERE is_deleted = false;
    
    RAISE NOTICE '当前数据统计：';
    RAISE NOTICE '- 活跃用户数: %', user_count;
    RAISE NOTICE '- 有效模板数: %', template_count;
    RAISE NOTICE '- 现有项目数: %', project_count;
END $$;

-- ================================================================
-- 第二步: 为没有项目的模板创建默认项目
-- ================================================================

-- 为每个模板创建者创建默认项目（如果还没有项目的话）
INSERT INTO projects (
    id,
    name, 
    description, 
    created_by, 
    status,
    template_count,
    instance_count,
    member_count,
    completion_rate,
    is_public,
    allow_anonymous,
    created_at,
    updated_at
)
SELECT DISTINCT
    uuid_generate_v4() as id,
    CASE 
        WHEN u.full_name IS NOT NULL AND u.full_name != '' 
        THEN u.full_name || ' 的默认项目'
        ELSE u.username || ' 的默认项目'
    END as name,
    '系统自动创建的默认项目，用于管理现有模板' as description,
    t.created_by,
    'active' as status,
    0 as template_count,  -- 将在后续步骤中更新
    0 as instance_count,  -- 将在后续步骤中更新
    1 as member_count,    -- 创建者作为拥有者
    0.0 as completion_rate,
    false as is_public,
    false as allow_anonymous,
    COALESCE(MIN(t.created_at), CURRENT_TIMESTAMP) as created_at,
    CURRENT_TIMESTAMP as updated_at
FROM crf_templates t
JOIN users u ON t.created_by = u.id
LEFT JOIN projects p ON p.created_by = t.created_by AND p.is_deleted = false
WHERE t.is_deleted = false 
  AND t.project_id IS NULL
  AND p.id IS NULL  -- 只为还没有项目的用户创建默认项目
GROUP BY t.created_by, u.full_name, u.username;

-- 记录创建的默认项目数量
DO $$
DECLARE
    created_projects INTEGER;
BEGIN
    GET DIAGNOSTICS created_projects = ROW_COUNT;
    RAISE NOTICE '创建了 % 个默认项目', created_projects;
END $$;

-- ================================================================
-- 第三步: 将没有项目关联的模板关联到默认项目
-- ================================================================

-- 更新模板的项目关联
UPDATE crf_templates 
SET project_id = (
    SELECT p.id 
    FROM projects p 
    WHERE p.created_by = crf_templates.created_by 
      AND p.is_deleted = false
      AND p.name LIKE '%默认项目'
    LIMIT 1
)
WHERE project_id IS NULL 
  AND is_deleted = false;

-- 记录更新的模板数量
DO $$
DECLARE
    updated_templates INTEGER;
BEGIN
    GET DIAGNOSTICS updated_templates = ROW_COUNT;
    RAISE NOTICE '更新了 % 个模板的项目关联', updated_templates;
END $$;

-- ================================================================
-- 第四步: 为项目创建者添加拥有者权限
-- ================================================================

-- 为所有项目的创建者添加拥有者权限记录
INSERT INTO project_members (
    id,
    project_id,
    user_id,
    role,
    permissions,
    status,
    joined_at,
    created_at,
    updated_at
)
SELECT 
    uuid_generate_v4() as id,
    p.id as project_id,
    p.created_by as user_id,
    'owner' as role,
    '{
        "templates": {"read": true, "create": true, "update": true, "delete": true, "publish": true},
        "instances": {"read": true, "create": true, "update": true, "delete": true, "submit": true},
        "members": {"read": true, "invite": true, "manage": true},
        "analytics": {"read": true, "export": true},
        "project": {"read": true, "update": true, "delete": true}
    }'::jsonb as permissions,
    'active' as status,
    p.created_at as joined_at,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at
FROM projects p
LEFT JOIN project_members pm ON pm.project_id = p.id AND pm.user_id = p.created_by
WHERE p.is_deleted = false 
  AND pm.id IS NULL;  -- 只为还没有成员记录的项目创建

-- 记录创建的成员权限数量
DO $$
DECLARE
    created_members INTEGER;
BEGIN
    GET DIAGNOSTICS created_members = ROW_COUNT;
    RAISE NOTICE '创建了 % 个项目成员权限记录', created_members;
END $$;

-- ================================================================
-- 第五步: 更新项目统计信息
-- ================================================================

-- 更新项目的模板数量统计
UPDATE projects 
SET template_count = (
    SELECT COUNT(*) 
    FROM crf_templates t 
    WHERE t.project_id = projects.id 
      AND t.is_deleted = false
)
WHERE is_deleted = false;

-- 更新项目的实例数量统计
UPDATE projects 
SET instance_count = (
    SELECT COUNT(*) 
    FROM crf_instances i 
    JOIN crf_templates t ON i.template_id = t.id
    WHERE t.project_id = projects.id 
      AND i.is_deleted = false
      AND t.is_deleted = false
)
WHERE is_deleted = false;

-- 更新项目的成员数量统计
UPDATE projects 
SET member_count = (
    SELECT COUNT(*) 
    FROM project_members pm 
    WHERE pm.project_id = projects.id 
      AND pm.status = 'active'
)
WHERE is_deleted = false;

-- 更新项目完成率（基于实例完成情况）
UPDATE projects 
SET completion_rate = COALESCE((
    SELECT AVG(i.completion_percentage)
    FROM crf_instances i 
    JOIN crf_templates t ON i.template_id = t.id
    WHERE t.project_id = projects.id 
      AND i.is_deleted = false
      AND t.is_deleted = false
), 0.0)
WHERE is_deleted = false;

DO $$
BEGIN
    RAISE NOTICE '更新了项目统计信息';
END $$;

-- ================================================================
-- 第六步: 创建项目活动日志
-- ================================================================

-- 为迁移创建活动日志记录
INSERT INTO project_activities (
    id,
    project_id,
    user_id,
    activity_type,
    activity_description,
    resource_type,
    resource_id,
    resource_name,
    activity_data,
    created_at
)
SELECT 
    uuid_generate_v4() as id,
    p.id as project_id,
    p.created_by as user_id,
    'project_created' as activity_type,
    '系统迁移：创建默认项目' as activity_description,
    'project' as resource_type,
    p.id as resource_id,
    p.name as resource_name,
    '{"migration": true, "version": "1.0.0"}'::jsonb as activity_data,
    p.created_at
FROM projects p
WHERE p.name LIKE '%默认项目'
  AND p.is_deleted = false;

DO $$
BEGIN
    RAISE NOTICE '创建了项目活动日志记录';
END $$;

-- ================================================================
-- 第七步: 数据完整性检查
-- ================================================================

-- 检查是否有模板没有关联到项目
DO $$
DECLARE
    orphan_templates INTEGER;
BEGIN
    SELECT COUNT(*) INTO orphan_templates 
    FROM crf_templates 
    WHERE project_id IS NULL AND is_deleted = false;
    
    IF orphan_templates > 0 THEN
        RAISE WARNING '发现 % 个模板没有关联到项目', orphan_templates;
    ELSE
        RAISE NOTICE '✓ 所有模板都已正确关联到项目';
    END IF;
END $$;

-- 检查是否有项目没有拥有者
DO $$
DECLARE
    projects_without_owner INTEGER;
BEGIN
    SELECT COUNT(*) INTO projects_without_owner
    FROM projects p
    LEFT JOIN project_members pm ON pm.project_id = p.id AND pm.role = 'owner' AND pm.status = 'active'
    WHERE p.is_deleted = false AND pm.id IS NULL;
    
    IF projects_without_owner > 0 THEN
        RAISE WARNING '发现 % 个项目没有拥有者', projects_without_owner;
    ELSE
        RAISE NOTICE '✓ 所有项目都有拥有者';
    END IF;
END $$;

-- 检查项目统计信息是否正确
DO $$
DECLARE
    incorrect_stats INTEGER;
BEGIN
    SELECT COUNT(*) INTO incorrect_stats
    FROM projects p
    WHERE p.is_deleted = false
      AND (
          p.template_count != (
              SELECT COUNT(*) FROM crf_templates t 
              WHERE t.project_id = p.id AND t.is_deleted = false
          )
          OR p.member_count != (
              SELECT COUNT(*) FROM project_members pm 
              WHERE pm.project_id = p.id AND pm.status = 'active'
          )
      );
    
    IF incorrect_stats > 0 THEN
        RAISE WARNING '发现 % 个项目的统计信息不正确', incorrect_stats;
    ELSE
        RAISE NOTICE '✓ 所有项目统计信息正确';
    END IF;
END $$;

-- ================================================================
-- 第八步: 更新系统设置
-- ================================================================

-- 更新系统设置，标记迁移完成
INSERT INTO system_settings (key, value, description, created_at, updated_at) VALUES
    ('project_migration_completed', 'true'::jsonb, '项目中心化架构迁移是否完成', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('project_migration_version', '"1.0.0"'::jsonb, '项目迁移脚本版本', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('project_migration_date', to_jsonb(CURRENT_TIMESTAMP::text), '项目迁移完成时间', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- 记录迁移完成到migrations表
INSERT INTO migrations (version, name, applied, applied_at, created_at) VALUES
    ('project_migration_1.0.0', 'Project-Centric Architecture Migration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (version) DO UPDATE SET 
    applied = true,
    applied_at = CURRENT_TIMESTAMP;

-- ================================================================
-- 第九步: 生成迁移报告
-- ================================================================

DO $$
DECLARE
    final_project_count INTEGER;
    final_template_count INTEGER;
    final_member_count INTEGER;
    final_activity_count INTEGER;
BEGIN
    -- 统计最终数据
    SELECT COUNT(*) INTO final_project_count FROM projects WHERE is_deleted = false;
    SELECT COUNT(*) INTO final_template_count FROM crf_templates WHERE is_deleted = false AND project_id IS NOT NULL;
    SELECT COUNT(*) INTO final_member_count FROM project_members WHERE status = 'active';
    SELECT COUNT(*) INTO final_activity_count FROM project_activities;
    
    RAISE NOTICE '';
    RAISE NOTICE '========================================';
    RAISE NOTICE '项目中心化架构迁移完成报告';
    RAISE NOTICE '========================================';
    RAISE NOTICE '迁移时间: %', CURRENT_TIMESTAMP;
    RAISE NOTICE '迁移版本: 1.0.0';
    RAISE NOTICE '';
    RAISE NOTICE '迁移后数据统计：';
    RAISE NOTICE '- 项目总数: %', final_project_count;
    RAISE NOTICE '- 关联模板数: %', final_template_count;
    RAISE NOTICE '- 项目成员数: %', final_member_count;
    RAISE NOTICE '- 活动记录数: %', final_activity_count;
    RAISE NOTICE '';
    RAISE NOTICE '✓ 迁移成功完成！';
    RAISE NOTICE '========================================';
END $$;

-- 提交事务
COMMIT;

-- ================================================================
-- 迁移后验证脚本（可选执行）
-- ================================================================

-- 验证脚本，可以单独运行来检查迁移结果
-- SELECT 'MIGRATION VERIFICATION' as verification_type;

-- 检查项目分布
-- SELECT 
--     u.username,
--     u.full_name,
--     COUNT(p.id) as project_count,
--     COUNT(t.id) as template_count
-- FROM users u
-- LEFT JOIN projects p ON p.created_by = u.id AND p.is_deleted = false
-- LEFT JOIN crf_templates t ON t.project_id = p.id AND t.is_deleted = false
-- WHERE u.is_active = true
-- GROUP BY u.id, u.username, u.full_name
-- ORDER BY project_count DESC, template_count DESC;

-- 检查项目成员权限
-- SELECT 
--     p.name as project_name,
--     u.username,
--     pm.role,
--     pm.status,
--     pm.joined_at
-- FROM projects p
-- JOIN project_members pm ON pm.project_id = p.id
-- JOIN users u ON u.id = pm.user_id
-- WHERE p.is_deleted = false
-- ORDER BY p.name, pm.role;

-- 检查孤立数据
-- SELECT 
--     'Templates without project' as issue_type,
--     COUNT(*) as count
-- FROM crf_templates 
-- WHERE project_id IS NULL AND is_deleted = false
-- UNION ALL
-- SELECT 
--     'Projects without owner' as issue_type,
--     COUNT(*) as count
-- FROM projects p
-- LEFT JOIN project_members pm ON pm.project_id = p.id AND pm.role = 'owner' AND pm.status = 'active'
-- WHERE p.is_deleted = false AND pm.id IS NULL;

-- ================================================================
-- 回滚脚本（紧急情况使用）
-- ================================================================

-- ROLLBACK SCRIPT - 仅在紧急情况下使用
-- 警告：此脚本将删除所有迁移数据，请谨慎使用！

/*
BEGIN;

-- 删除自动创建的项目活动记录
DELETE FROM project_activities 
WHERE activity_data::text LIKE '%"migration": true%';

-- 删除项目成员记录
DELETE FROM project_members 
WHERE project_id IN (
    SELECT id FROM projects WHERE name LIKE '%默认项目'
);

-- 将模板的项目关联设为NULL
UPDATE crf_templates 
SET project_id = NULL 
WHERE project_id IN (
    SELECT id FROM projects WHERE name LIKE '%默认项目'
);

-- 删除自动创建的默认项目
DELETE FROM projects 
WHERE name LIKE '%默认项目';

-- 删除迁移相关的系统设置
DELETE FROM system_settings 
WHERE key IN ('project_migration_completed', 'project_migration_version', 'project_migration_date');

-- 删除迁移记录
DELETE FROM migrations 
WHERE version = 'project_migration_1.0.0';

RAISE NOTICE '迁移已回滚，请检查数据完整性';

COMMIT;
*/