-- RBAC系统数据库迁移脚本
-- Migration: 003_add_rbac_system
-- Description: 添加基于角色的访问控制(RBAC)系统，支持逻辑删除，无物理外键
-- Version: 1.0.0
-- Date: 2025-07-11

-- ================================================================
-- 启用UUID扩展（如果未启用）
-- ================================================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================================================
-- RBAC系统核心表
-- ================================================================

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT false,  -- 是否为系统内置角色
    is_active BOOLEAN DEFAULT true,   -- 角色是否激活
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL  -- 逻辑删除
);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource VARCHAR(50) NOT NULL,      -- 资源类型：template, instance, user, project, system
    action VARCHAR(50) NOT NULL,        -- 操作：create, read, update, delete, publish, approve, export
    scope VARCHAR(50) DEFAULT 'global', -- 权限范围：global, project, own
    description TEXT,
    is_system BOOLEAN DEFAULT true,     -- 是否为系统权限
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,  -- 逻辑删除
    
    UNIQUE(resource, action, scope)
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID NOT NULL,        -- 关联roles表，无物理外键
    permission_id UUID NOT NULL,  -- 关联permissions表，无物理外键
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,  -- 逻辑删除
    
    CONSTRAINT unique_role_permission UNIQUE(role_id, permission_id)
);

-- 用户角色关联表（支持项目级角色）
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,       -- 关联users表，无物理外键
    role_id UUID NOT NULL,       -- 关联roles表，无物理外键
    project_id UUID,             -- 关联projects表，无物理外键，NULL表示全局角色
    
    assigned_by UUID,            -- 分配者user_id，无物理外键
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE, -- 角色过期时间，NULL表示永不过期
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,  -- 逻辑删除
    
    -- 使用函数创建唯一约束，处理NULL值
    CONSTRAINT unique_user_role_project UNIQUE(user_id, role_id, project_id)
);

-- 权限检查缓存表（可选，用于提高权限检查性能）
CREATE TABLE IF NOT EXISTS user_permission_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,       -- 关联users表，无物理外键
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    project_id UUID,             -- 关联projects表，无物理外键
    has_permission BOOLEAN NOT NULL,
    
    cache_expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '1 hour'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,  -- 逻辑删除
    
    -- 使用函数创建唯一约束，处理NULL值
    CONSTRAINT unique_user_permission_cache UNIQUE(user_id, resource, action, project_id)
);

-- ================================================================
-- 创建索引优化查询性能
-- ================================================================

-- 角色表索引
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(is_active);
CREATE INDEX IF NOT EXISTS idx_roles_system ON roles(is_system);
CREATE INDEX IF NOT EXISTS idx_roles_deleted ON roles(deleted_at);

-- 权限表索引
CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);
CREATE INDEX IF NOT EXISTS idx_permissions_scope ON permissions(scope);
CREATE INDEX IF NOT EXISTS idx_permissions_deleted ON permissions(deleted_at);

-- 角色权限关联表索引
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_deleted ON role_permissions(deleted_at);

-- 用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_roles_user ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_project ON user_roles(project_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_expires ON user_roles(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_roles_deleted ON user_roles(deleted_at);

-- 权限缓存表索引
CREATE INDEX IF NOT EXISTS idx_user_permission_cache_user ON user_permission_cache(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permission_cache_expires ON user_permission_cache(cache_expires_at);
CREATE INDEX IF NOT EXISTS idx_user_permission_cache_deleted ON user_permission_cache(deleted_at);

-- ================================================================
-- 插入系统默认权限
-- ================================================================

INSERT INTO permissions (resource, action, scope, description, is_system) VALUES
-- 用户管理权限
('user', 'create', 'global', '创建用户', true),
('user', 'read', 'global', '查看所有用户', true),
('user', 'read', 'own', '查看自己的信息', true),
('user', 'update', 'global', '更新任意用户信息', true),
('user', 'update', 'own', '更新自己的信息', true),
('user', 'delete', 'global', '删除用户', true),
('user', 'assign_role', 'global', '分配角色给用户', true),
('user', 'assign_role', 'project', '在项目内分配角色', true),

-- 角色管理权限
('role', 'create', 'global', '创建角色', true),
('role', 'read', 'global', '查看角色', true),
('role', 'update', 'global', '更新角色', true),
('role', 'delete', 'global', '删除角色', true),
('role', 'assign_permission', 'global', '为角色分配权限', true),

-- 项目管理权限
('project', 'create', 'global', '创建项目', true),
('project', 'read', 'global', '查看所有项目', true),
('project', 'read', 'project', '查看所在项目', true),
('project', 'update', 'global', '更新任意项目', true),
('project', 'update', 'project', '更新所在项目', true),
('project', 'update', 'own', '更新自己创建的项目', true),
('project', 'delete', 'global', '删除任意项目', true),
('project', 'delete', 'own', '删除自己创建的项目', true),

-- 模板管理权限
('template', 'create', 'global', '创建任意模板', true),
('template', 'create', 'project', '在项目内创建模板', true),
('template', 'read', 'global', '查看所有模板', true),
('template', 'read', 'project', '查看项目内模板', true),
('template', 'read', 'own', '查看自己的模板', true),
('template', 'update', 'global', '更新任意模板', true),
('template', 'update', 'project', '更新项目内模板', true),
('template', 'update', 'own', '更新自己的模板', true),
('template', 'delete', 'global', '删除任意模板', true),
('template', 'delete', 'project', '删除项目内模板', true),
('template', 'delete', 'own', '删除自己的模板', true),
('template', 'publish', 'global', '发布任意模板', true),
('template', 'publish', 'project', '发布项目内模板', true),
('template', 'publish', 'own', '发布自己的模板', true),

-- 实例管理权限
('instance', 'create', 'global', '创建任意实例', true),
('instance', 'create', 'project', '在项目内创建实例', true),
('instance', 'read', 'global', '查看所有实例', true),
('instance', 'read', 'project', '查看项目内实例', true),
('instance', 'read', 'own', '查看自己的实例', true),
('instance', 'update', 'global', '更新任意实例', true),
('instance', 'update', 'project', '更新项目内实例', true),
('instance', 'update', 'own', '更新自己的实例', true),
('instance', 'delete', 'global', '删除任意实例', true),
('instance', 'delete', 'project', '删除项目内实例', true),
('instance', 'delete', 'own', '删除自己的实例', true),
('instance', 'submit', 'global', '提交任意实例', true),
('instance', 'submit', 'project', '提交项目内实例', true),
('instance', 'submit', 'own', '提交自己的实例', true),
('instance', 'approve', 'global', '审批任意实例', true),
('instance', 'approve', 'project', '审批项目内实例', true),
('instance', 'lock', 'global', '锁定任意实例', true),
('instance', 'lock', 'project', '锁定项目内实例', true),

-- 数据管理权限
('data', 'read', 'global', '查看所有数据', true),
('data', 'read', 'project', '查看项目数据', true),
('data', 'export', 'global', '导出所有数据', true),
('data', 'export', 'project', '导出项目数据', true),
('data', 'analyze', 'global', '分析所有数据', true),
('data', 'analyze', 'project', '分析项目数据', true),

-- 系统管理权限
('system', 'config', 'global', '系统配置管理', true),
('system', 'monitor', 'global', '系统监控', true),
('system', 'backup', 'global', '系统备份', true),
('system', 'audit', 'global', '审计日志查看', true)

ON CONFLICT (resource, action, scope) DO NOTHING;

-- ================================================================
-- 插入系统默认角色
-- ================================================================

INSERT INTO roles (name, display_name, description, is_system, is_active) VALUES
('super_admin', '超级管理员', '拥有系统所有权限的超级管理员', true, true),
('admin', '系统管理员', '负责用户管理、权限分配、系统配置等', true, true),
('researcher', '研究者', '负责设计表单、创建研究项目等', true, true),
('data_entry', '数据录入员', '负责填写CRF表单', true, true),
('reviewer', '审阅者', '负责审核数据的准确性和完整性', true, true),
('viewer', '查看者', '只能查看数据，无修改权限', true, true)

ON CONFLICT (name) DO NOTHING;

-- ================================================================
-- 为系统角色分配权限
-- ================================================================

-- 超级管理员：拥有所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p 
WHERE r.name = 'super_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 系统管理员：用户、角色、系统管理权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p 
WHERE r.name = 'admin'
AND p.resource IN ('user', 'role', 'system') 
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 研究者：项目、模板管理权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p 
WHERE r.name = 'researcher'
AND (
    (p.resource = 'project' AND p.action IN ('create', 'read', 'update')) OR
    (p.resource = 'template' AND p.action IN ('create', 'read', 'update', 'publish')) OR
    (p.resource = 'instance' AND p.action IN ('read')) OR
    (p.resource = 'data' AND p.action IN ('read', 'analyze')) OR
    (p.resource = 'user' AND p.action = 'read' AND p.scope = 'own')
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 数据录入员：实例填写权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p 
WHERE r.name = 'data_entry'
AND (
    (p.resource = 'instance' AND p.action IN ('create', 'read', 'update', 'submit') AND p.scope IN ('project', 'own')) OR
    (p.resource = 'template' AND p.action = 'read') OR
    (p.resource = 'project' AND p.action = 'read' AND p.scope = 'project') OR
    (p.resource = 'user' AND p.action IN ('read', 'update') AND p.scope = 'own')
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 审阅者：数据审核权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p 
WHERE r.name = 'reviewer'
AND (
    (p.resource = 'instance' AND p.action IN ('read', 'approve', 'lock') AND p.scope IN ('project', 'global')) OR
    (p.resource = 'data' AND p.action IN ('read', 'analyze') AND p.scope IN ('project', 'global')) OR
    (p.resource = 'template' AND p.action = 'read') OR
    (p.resource = 'project' AND p.action = 'read') OR
    (p.resource = 'user' AND p.action IN ('read', 'update') AND p.scope = 'own')
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 查看者：只读权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p 
WHERE r.name = 'viewer'
AND (
    (p.resource IN ('template', 'instance', 'data', 'project') AND p.action = 'read' AND p.scope IN ('project', 'own')) OR
    (p.resource = 'user' AND p.action IN ('read', 'update') AND p.scope = 'own')
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- ================================================================
-- 创建权限检查函数
-- ================================================================

-- 检查用户是否具有指定权限的函数
CREATE OR REPLACE FUNCTION check_user_permission(
    p_user_id UUID,
    p_resource VARCHAR(50),
    p_action VARCHAR(50),
    p_project_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_has_permission BOOLEAN := FALSE;
    v_cached_result BOOLEAN;
    v_cache_expires TIMESTAMP WITH TIME ZONE;
BEGIN
    -- 检查缓存
    SELECT has_permission, cache_expires_at INTO v_cached_result, v_cache_expires
    FROM user_permission_cache 
    WHERE user_id = p_user_id 
        AND resource = p_resource 
        AND action = p_action 
        AND (project_id = p_project_id OR (project_id IS NULL AND p_project_id IS NULL))
        AND deleted_at IS NULL
        AND cache_expires_at > CURRENT_TIMESTAMP;
    
    IF FOUND THEN
        RETURN v_cached_result;
    END IF;
    
    -- 检查全局权限
    SELECT TRUE INTO v_has_permission
    FROM user_roles ur
    JOIN role_permissions rp ON ur.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.id
    WHERE ur.user_id = p_user_id
        AND ur.deleted_at IS NULL
        AND rp.deleted_at IS NULL
        AND p.deleted_at IS NULL
        AND p.resource = p_resource
        AND p.action = p_action
        AND p.scope = 'global'
        AND (ur.expires_at IS NULL OR ur.expires_at > CURRENT_TIMESTAMP)
        AND ur.project_id IS NULL
    LIMIT 1;
    
    -- 如果没有全局权限，检查项目权限
    IF NOT FOUND AND p_project_id IS NOT NULL THEN
        SELECT TRUE INTO v_has_permission
        FROM user_roles ur
        JOIN role_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = p_user_id
            AND ur.deleted_at IS NULL
            AND rp.deleted_at IS NULL
            AND p.deleted_at IS NULL
            AND p.resource = p_resource
            AND p.action = p_action
            AND p.scope IN ('project', 'own')
            AND (ur.expires_at IS NULL OR ur.expires_at > CURRENT_TIMESTAMP)
            AND ur.project_id = p_project_id
        LIMIT 1;
    END IF;
    
    -- 如果仍然没有权限，检查own权限
    IF NOT FOUND THEN
        SELECT TRUE INTO v_has_permission
        FROM user_roles ur
        JOIN role_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = p_user_id
            AND ur.deleted_at IS NULL
            AND rp.deleted_at IS NULL
            AND p.deleted_at IS NULL
            AND p.resource = p_resource
            AND p.action = p_action
            AND p.scope = 'own'
            AND (ur.expires_at IS NULL OR ur.expires_at > CURRENT_TIMESTAMP)
        LIMIT 1;
    END IF;
    
    v_has_permission := COALESCE(v_has_permission, FALSE);
    
    -- 缓存结果
    INSERT INTO user_permission_cache (user_id, resource, action, project_id, has_permission)
    VALUES (p_user_id, p_resource, p_action, p_project_id, v_has_permission)
    ON CONFLICT (user_id, resource, action, project_id) 
    DO UPDATE SET 
        has_permission = EXCLUDED.has_permission,
        cache_expires_at = CURRENT_TIMESTAMP + INTERVAL '1 hour';
    
    RETURN v_has_permission;
END;
$$ LANGUAGE plpgsql;

-- 清理过期缓存的函数
CREATE OR REPLACE FUNCTION cleanup_expired_permission_cache() RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM user_permission_cache 
    WHERE cache_expires_at <= CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 清理指定用户权限缓存的函数
CREATE OR REPLACE FUNCTION clear_user_permission_cache(p_user_id UUID) RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM user_permission_cache 
    WHERE user_id = p_user_id;
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- 迁移完成
-- ================================================================

-- 输出迁移完成信息
DO $$
BEGIN
    RAISE NOTICE 'RBAC系统迁移完成';
    RAISE NOTICE '已创建表: roles, permissions, role_permissions, user_roles, user_permission_cache';
    RAISE NOTICE '已插入6个系统角色和50+个权限';
    RAISE NOTICE '权限检查函数已创建完成';
END $$;