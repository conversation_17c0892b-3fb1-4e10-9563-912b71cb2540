-- 为admin用户分配超级管理员角色

-- 首先检查admin用户是否存在
SELECT 'Checking admin user...' as status;
SELECT id, username, email FROM users WHERE username = 'admin';

-- 检查super_admin角色是否存在
SELECT 'Checking super_admin role...' as status;
SELECT id, name, display_name FROM roles WHERE name = 'super_admin';

-- 分配超级管理员角色给admin用户
INSERT INTO user_roles (
    id,
    user_id, 
    role_id, 
    project_id, 
    assigned_by, 
    assigned_at,
    created_at,
    updated_at
) 
SELECT 
    uuid_generate_v4(),
    u.id,
    r.id,
    NULL, -- 全局角色
    u.id, -- 自己分配
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM users u, roles r 
WHERE u.username = 'admin' 
  AND r.name = 'super_admin'
  AND NOT EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = u.id 
      AND ur.role_id = r.id 
      AND ur.project_id IS NULL
  );

-- 清除权限缓存
DELETE FROM user_permission_cache 
WHERE user_id IN (SELECT id FROM users WHERE username = 'admin');

-- 验证分配结果
SELECT 'Assignment result:' as status;
SELECT 
    u.username,
    u.email,
    r.name as role_name,
    r.display_name as role_display_name,
    ur.assigned_at
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'admin';

SELECT 'Done!' as status;