-- 为操作历史表添加软删除字段
-- 这个迁移脚本会安全地添加deleted_at和is_deleted字段

-- 检查并添加 deleted_at 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'operation_history' 
        AND column_name = 'deleted_at'
    ) THEN
        ALTER TABLE operation_history 
        ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
        
        RAISE NOTICE '已为 operation_history 表添加 deleted_at 字段';
    ELSE
        RAISE NOTICE 'operation_history 表的 deleted_at 字段已存在';
    END IF;
END $$;

-- 检查并添加 is_deleted 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'operation_history' 
        AND column_name = 'is_deleted'
    ) THEN
        ALTER TABLE operation_history 
        ADD COLUMN is_deleted BOOLEAN DEFAULT false NOT NULL;
        
        RAISE NOTICE '已为 operation_history 表添加 is_deleted 字段';
    ELSE
        RAISE NOTICE 'operation_history 表的 is_deleted 字段已存在';
    END IF;
END $$;

-- 为新字段创建索引
CREATE INDEX IF NOT EXISTS idx_operation_history_deleted_at 
ON operation_history(deleted_at);

CREATE INDEX IF NOT EXISTS idx_operation_history_is_deleted 
ON operation_history(is_deleted);

-- 检查是否需要创建 operation_histories 表（如果后端代码期望这个表名）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'operation_histories'
    ) THEN
        -- 创建 operation_histories 表，结构与 operation_history 相同，但包含所有需要的字段
        CREATE TABLE operation_histories (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance', 'project')),
            resource_id UUID NOT NULL,
            
            -- 操作信息
            action VARCHAR(50) NOT NULL,
            description TEXT,
            
            -- 操作数据
            before_data JSONB,
            after_data JSONB,
            
            -- 客户端信息
            client_info JSONB,
            
            -- 软删除字段
            deleted_at TIMESTAMP WITH TIME ZONE,
            is_deleted BOOLEAN DEFAULT false NOT NULL,
            
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引
        CREATE INDEX idx_operation_histories_user_resource ON operation_histories(user_id, resource_type, resource_id);
        CREATE INDEX idx_operation_histories_action ON operation_histories(action);
        CREATE INDEX idx_operation_histories_created_at ON operation_histories(created_at);
        CREATE INDEX idx_operation_histories_deleted_at ON operation_histories(deleted_at);
        CREATE INDEX idx_operation_histories_is_deleted ON operation_histories(is_deleted);
        
        RAISE NOTICE '已创建 operation_histories 表';
    ELSE
        -- 检查并添加缺失的字段到 operation_histories 表
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'operation_histories' 
            AND column_name = 'deleted_at'
        ) THEN
            ALTER TABLE operation_histories 
            ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
            
            RAISE NOTICE '已为 operation_histories 表添加 deleted_at 字段';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'operation_histories' 
            AND column_name = 'is_deleted'
        ) THEN
            ALTER TABLE operation_histories 
            ADD COLUMN is_deleted BOOLEAN DEFAULT false NOT NULL;
            
            RAISE NOTICE '已为 operation_histories 表添加 is_deleted 字段';
        END IF;
        
        -- 创建索引（如果不存在）
        CREATE INDEX IF NOT EXISTS idx_operation_histories_deleted_at ON operation_histories(deleted_at);
        CREATE INDEX IF NOT EXISTS idx_operation_histories_is_deleted ON operation_histories(is_deleted);
        
        RAISE NOTICE 'operation_histories 表已存在，已检查并添加缺失字段';
    END IF;
END $$;

-- 记录迁移
INSERT INTO migrations (version, name, applied, applied_at) VALUES
    ('001', 'Add deleted_at and is_deleted to operation_history and operation_histories', true, CURRENT_TIMESTAMP)
ON CONFLICT (version) DO UPDATE SET 
    applied = true,
    applied_at = CURRENT_TIMESTAMP;

SELECT '操作历史表软删除字段迁移完成!' as message;