-- 给admin用户分配超级管理员角色

-- 查找admin用户ID
DO $$
DECLARE
    admin_user_id UUID;
    super_admin_role_id UUID;
BEGIN
    -- 获取admin用户ID
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin';
    
    IF admin_user_id IS NULL THEN
        RAISE NOTICE 'Admin user not found';
        RETURN;
    END IF;
    
    -- 获取super_admin角色ID
    SELECT id INTO super_admin_role_id FROM roles WHERE name = 'super_admin';
    
    IF super_admin_role_id IS NULL THEN
        RAISE NOTICE 'Super admin role not found';
        RETURN;
    END IF;
    
    -- 删除现有的角色分配（如果有）
    DELETE FROM user_roles WHERE user_id = admin_user_id;
    
    -- 分配超级管理员角色
    INSERT INTO user_roles (
        user_id, 
        role_id, 
        project_id, 
        assigned_by, 
        assigned_at,
        created_at,
        updated_at
    ) VALUES (
        admin_user_id,
        super_admin_role_id,
        NULL, -- 全局角色
        admin_user_id, -- 自己分配
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    );
    
    -- 清除权限缓存
    DELETE FROM user_permission_cache WHERE user_id = admin_user_id;
    
    RAISE NOTICE 'Successfully assigned super_admin role to admin user';
    RAISE NOTICE 'Admin user ID: %', admin_user_id;
    RAISE NOTICE 'Super admin role ID: %', super_admin_role_id;
END $$;

-- 验证分配结果
SELECT 
    u.username,
    u.email,
    r.name as role_name,
    r.display_name as role_display_name,
    ur.assigned_at
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'admin';