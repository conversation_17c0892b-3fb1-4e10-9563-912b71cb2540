-- 添加缺失的发布相关字段到crf_templates表
ALTER TABLE crf_templates 
ADD COLUMN IF NOT EXISTS published_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS published_by UUID REFERENCES users(id) ON DELETE SET NULL;

-- 添加索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_at ON crf_templates(published_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_by ON crf_templates(published_by);

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'crf_templates' 
AND column_name IN ('published_at', 'published_by')
ORDER BY column_name;