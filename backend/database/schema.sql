-- CRF (Case Report Form) Web Editor Database Schema
-- 医疗CRF表单编辑器数据库架构
-- Version: 2.0.0
-- Updated: 2025-07-08

-- 创建数据库扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================================================
-- 用户管理相关表
-- ================================================================

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'editor', 'viewer', 'user')),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    
    -- 多种登录方式支持
    phone VARCHAR(20) UNIQUE,
    wechat_open_id VARCHAR(100) UNIQUE,
    wechat_union_id VARCHAR(100) UNIQUE,
    login_type VARCHAR(20) DEFAULT 'password' CHECK (login_type IN ('password', 'sms', 'wechat')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    
    -- 会话信息
    ip_address INET,
    user_agent TEXT,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 项目管理相关表
-- ================================================================

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'archived')),

    -- 项目类型和分类
    project_type VARCHAR(50) DEFAULT 'clinical_trial' CHECK (project_type IN ('clinical_trial', 'observational_study', 'registry', 'survey', 'other')),
    category VARCHAR(100),

    -- 项目设置
    settings JSONB DEFAULT '{}',

    -- 项目统计字段（冗余存储，提高查询性能）
    template_count INTEGER DEFAULT 0,
    instance_count INTEGER DEFAULT 0,
    member_count INTEGER DEFAULT 1,

    -- 项目时间管理
    start_date DATE,
    end_date DATE,

    -- 审计字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- 索引优化
    CONSTRAINT projects_name_unique UNIQUE (name, created_by)
);

-- ================================================================
-- CRF模板管理相关表
-- ================================================================

-- CRF表单模板表
CREATE TABLE IF NOT EXISTS crf_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    keyword VARCHAR(100),
    version VARCHAR(20) DEFAULT '1.0.0',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- 模板类型：区分标准模板和自定义表单
    template_type VARCHAR(20) DEFAULT 'template' CHECK (template_type IN ('template', 'custom_form')),
    
    -- 模板来源：系统预置、用户创建、导入等
    source_type VARCHAR(20) DEFAULT 'user_created' CHECK (source_type IN ('system_preset', 'user_created', 'imported', 'copied')),
    
    -- 是否为公共模板（可被其他用户使用）
    is_public BOOLEAN DEFAULT false,
    
    -- 统一的模板数据存储
    template_data JSONB NOT NULL DEFAULT '{}',
    
    -- 权限设置
    permissions JSONB DEFAULT '{}',
    
    -- 标签，便于分类和搜索
    tags VARCHAR(500),
    
    -- 使用统计
    usage_count INTEGER DEFAULT 0,
    
    -- 发布相关字段
    published_at TIMESTAMP WITH TIME ZONE,
    published_by UUID REFERENCES users(id) ON DELETE SET NULL,
    
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(project_id, name, version)
);

-- CRF表单版本表
CREATE TABLE IF NOT EXISTS crf_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES crf_templates(id) ON DELETE CASCADE,
    version VARCHAR(20) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    
    -- 版本快照数据
    snapshot_data JSONB NOT NULL,
    
    -- 变更日志
    change_log TEXT,
    
    -- 版本状态
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- 发布信息
    published_at TIMESTAMP WITH TIME ZONE,
    published_by UUID REFERENCES users(id) ON DELETE SET NULL,
    
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(template_id, version)
);

-- ================================================================
-- CRF实例管理相关表
-- ================================================================

-- CRF表单实例表（用户填写的表单）
CREATE TABLE IF NOT EXISTS crf_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES crf_templates(id) ON DELETE CASCADE,
    template_version VARCHAR(20) NOT NULL,
    
    -- 实例信息
    instance_name VARCHAR(255),
    subject_id VARCHAR(100), -- 受试者ID
    visit_id VARCHAR(100),   -- 访问ID
    
    -- 表单数据
    form_data JSONB NOT NULL DEFAULT '{}',
    
    -- 验证结果
    validation_results JSONB DEFAULT '{}',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'completed', 'locked', 'rejected')),
    
    -- 完成度
    completion_percentage DECIMAL(5,2) DEFAULT 0.0,
    
    -- 锁定信息
    locked_by UUID REFERENCES users(id) ON DELETE SET NULL,
    locked_at TIMESTAMP WITH TIME ZONE,
    
    -- 审核信息
    reviewed_by UUID REFERENCES users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_comment TEXT,
    
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 系统功能相关表
-- ================================================================

-- 自动保存记录表
CREATE TABLE IF NOT EXISTS auto_saves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance')),
    resource_id UUID NOT NULL,
    
    -- 自动保存的数据
    save_data JSONB NOT NULL,
    
    -- 保存时间
    saved_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 过期时间（用于清理旧数据）
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days')
);

-- 操作历史记录表
CREATE TABLE IF NOT EXISTS operation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance', 'project')),
    resource_id UUID NOT NULL,
    
    -- 操作信息
    action VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- 操作数据
    before_data JSONB,
    after_data JSONB,
    
    -- 客户端信息
    client_info JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 文件附件表
CREATE TABLE IF NOT EXISTS attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance', 'project')),
    resource_id UUID NOT NULL,
    
    -- 文件信息
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    
    -- 上传信息
    uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 元数据
    metadata JSONB DEFAULT '{}'
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 任务管理相关表
-- ================================================================

-- ================================================================
-- 数据库迁移跟踪表
-- ================================================================

-- 数据库迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    applied BOOLEAN DEFAULT false,
    applied_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 创建索引以提高查询性能
-- ================================================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_wechat_open_id ON users(wechat_open_id);
CREATE INDEX IF NOT EXISTS idx_users_wechat_union_id ON users(wechat_union_id);
CREATE INDEX IF NOT EXISTS idx_users_login_type ON users(login_type);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- 用户会话表索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 项目表索引
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON projects(updated_at);

-- CRF模板表索引
CREATE INDEX IF NOT EXISTS idx_crf_templates_project_id ON crf_templates(project_id);
CREATE INDEX IF NOT EXISTS idx_crf_templates_status ON crf_templates(status);
CREATE INDEX IF NOT EXISTS idx_crf_templates_template_type ON crf_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_crf_templates_source_type ON crf_templates(source_type);
CREATE INDEX IF NOT EXISTS idx_crf_templates_is_public ON crf_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_crf_templates_created_by ON crf_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_crf_templates_updated_by ON crf_templates(updated_by);
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_at ON crf_templates(published_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_by ON crf_templates(published_by);
CREATE INDEX IF NOT EXISTS idx_crf_templates_updated_at ON crf_templates(updated_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_usage_count ON crf_templates(usage_count);

-- 为模板数据JSON字段创建GIN索引以支持高效查询
CREATE INDEX IF NOT EXISTS idx_crf_templates_template_data ON crf_templates USING GIN (template_data);
CREATE INDEX IF NOT EXISTS idx_crf_templates_permissions ON crf_templates USING GIN (permissions);
CREATE INDEX IF NOT EXISTS idx_crf_templates_tags ON crf_templates USING GIN (to_tsvector('english', tags));

-- CRF版本表索引
CREATE INDEX IF NOT EXISTS idx_crf_versions_template_id ON crf_versions(template_id);
CREATE INDEX IF NOT EXISTS idx_crf_versions_status ON crf_versions(status);
CREATE INDEX IF NOT EXISTS idx_crf_versions_published_at ON crf_versions(published_at);
CREATE INDEX IF NOT EXISTS idx_crf_versions_published_by ON crf_versions(published_by);
CREATE INDEX IF NOT EXISTS idx_crf_versions_created_by ON crf_versions(created_by);

-- CRF实例表索引
CREATE INDEX IF NOT EXISTS idx_crf_instances_template_id ON crf_instances(template_id);
CREATE INDEX IF NOT EXISTS idx_crf_instances_status ON crf_instances(status);
CREATE INDEX IF NOT EXISTS idx_crf_instances_subject_id ON crf_instances(subject_id);
CREATE INDEX IF NOT EXISTS idx_crf_instances_visit_id ON crf_instances(visit_id);
CREATE INDEX IF NOT EXISTS idx_crf_instances_created_by ON crf_instances(created_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_updated_by ON crf_instances(updated_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_locked_by ON crf_instances(locked_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_reviewed_by ON crf_instances(reviewed_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_completion_percentage ON crf_instances(completion_percentage);
CREATE INDEX IF NOT EXISTS idx_crf_instances_updated_at ON crf_instances(updated_at);

-- 为实例数据JSON字段创建GIN索引
CREATE INDEX IF NOT EXISTS idx_crf_instances_form_data ON crf_instances USING GIN (form_data);
CREATE INDEX IF NOT EXISTS idx_crf_instances_validation_results ON crf_instances USING GIN (validation_results);

-- 自动保存表索引
CREATE INDEX IF NOT EXISTS idx_auto_saves_user_resource ON auto_saves(user_id, resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_auto_saves_expires_at ON auto_saves(expires_at);
CREATE INDEX IF NOT EXISTS idx_auto_saves_saved_at ON auto_saves(saved_at);

-- 操作历史表索引
CREATE INDEX IF NOT EXISTS idx_operation_history_user_resource ON operation_history(user_id, resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_operation_history_action ON operation_history(action);
CREATE INDEX IF NOT EXISTS idx_operation_history_created_at ON operation_history(created_at);

-- 附件表索引
CREATE INDEX IF NOT EXISTS idx_attachments_resource ON attachments(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_by ON attachments(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_at ON attachments(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_attachments_file_type ON attachments(file_type);

-- 系统设置表索引
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key);

-- 迁移表索引
CREATE INDEX IF NOT EXISTS idx_migrations_version ON migrations(version);
CREATE INDEX IF NOT EXISTS idx_migrations_applied ON migrations(applied);
CREATE INDEX IF NOT EXISTS idx_migrations_applied_at ON migrations(applied_at);

-- ================================================================
-- 创建更新时间的触发器函数
-- ================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crf_templates_updated_at BEFORE UPDATE ON crf_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crf_instances_updated_at BEFORE UPDATE ON crf_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================================================
-- 插入默认系统设置
-- ================================================================

INSERT INTO system_settings (key, value, description) VALUES
    ('app_name', '"CRF Web Editor"', '应用程序名称'),
    ('app_version', '"2.0.0"', '应用程序版本'),
    ('auto_save_interval', '30', '自动保存间隔（秒）'),
    ('max_file_size', '10485760', '最大文件上传大小（字节）'),
    ('session_timeout', '7200', '会话超时时间（秒）'),
    ('enable_audit_log', 'true', '是否启用审计日志'),
    ('backup_retention_days', '30', '备份保留天数'),
    ('template_types_enabled', 'true', '是否启用模板类型区分功能'),
    ('multi_login_enabled', 'true', '是否启用多种登录方式'),
    ('sms_provider', '"aliyun"', '短信服务提供商'),
    ('wechat_app_id', '""', '微信应用ID'),
    ('template_import_max_size', '10485760', '模板导入文件最大大小（字节）'),
    ('public_template_enabled', 'true', '是否允许创建公共模板'),
    ('auto_save_enabled', 'true', '是否启用自动保存功能'),
    ('operation_history_enabled', 'true', '是否启用操作历史记录'),
    ('attachment_enabled', 'true', '是否启用文件附件功能'),
    ('notification_enabled', 'true', '是否启用通知功能')
ON CONFLICT (key) DO NOTHING;

-- ================================================================
-- 创建默认用户和示例数据
-- ================================================================

-- 创建默认管理员用户（密码：admin123）
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
    ('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewqLmkjnHnL1K2/2', '系统管理员', 'admin'),
    ('editor', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewqLmkjnHnL1K2/2', '表单编辑员', 'editor'),
    ('viewer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewqLmkjnHnL1K2/2', '表单查看员', 'viewer')
ON CONFLICT (username) DO NOTHING;

-- 创建示例项目
INSERT INTO projects (name, description, created_by, status) VALUES
    ('示例CRF项目', '这是一个示例的CRF项目，用于演示系统功能', 
     (SELECT id FROM users WHERE username = 'admin' LIMIT 1), 'active'),
    ('临床试验项目A', '用于临床试验A的CRF表单管理', 
     (SELECT id FROM users WHERE username = 'admin' LIMIT 1), 'active'),
    ('药物安全性评估项目', '专门用于药物安全性评估的表单系统', 
     (SELECT id FROM users WHERE username = 'editor' LIMIT 1), 'draft')
ON CONFLICT DO NOTHING;

-- 创建示例模板
INSERT INTO crf_templates (
    name, title, description, keyword, version, status,
    template_type, source_type, is_public, tags, usage_count,
    project_id, created_by, template_data
) VALUES
    -- 系统预置标准模板
    (
        'patient_baseline_template',
        '患者基线信息采集模板',
        '用于收集患者基本信息、病史、体征等基线数据的标准化模板',
        'baseline,patient,demographics',
        '1.0.0',
        'published',
        'template',
        'system_preset',
        true,
        '患者信息,基线数据,标准模板',
        0,
        (SELECT id FROM projects WHERE name = '示例CRF项目' LIMIT 1),
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
        '{
            "pageConfig": {
                "theme": "medical",
                "layout": "vertical",
                "language": "zh-CN",
                "title": "患者基线信息采集",
                "showProgress": true,
                "allowSave": true
            },
            "formStructure": {
                "sections": [
                    {
                        "id": "demographics",
                        "title": "人口学信息",
                        "components": [
                            {"id": "subject_id", "type": "input", "label": "受试者编号", "required": true},
                            {"id": "age", "type": "number", "label": "年龄", "required": true},
                            {"id": "gender", "type": "radio", "label": "性别", "required": true, "options": [{"label": "男", "value": "male"}, {"label": "女", "value": "female"}]}
                        ]
                    },
                    {
                        "id": "medical_history",
                        "title": "病史信息",
                        "components": [
                            {"id": "primary_diagnosis", "type": "textarea", "label": "主要诊断", "required": true},
                            {"id": "medical_history", "type": "textarea", "label": "既往病史"},
                            {"id": "allergies", "type": "checkbox", "label": "过敏史", "options": [{"label": "药物过敏", "value": "drug"}, {"label": "食物过敏", "value": "food"}, {"label": "无过敏史", "value": "none"}]}
                        ]
                    }
                ]
            },
            "styleConfig": {
                "primaryColor": "#1976d2",
                "secondaryColor": "#42a5f5"
            }
        }'
    ),
    -- 用户创建的自定义表单
    (
        'adverse_event_form',
        '不良事件报告表',
        '专门用于记录临床试验中发生的不良事件',
        'adverse,event,safety',
        '1.0.0',
        'draft',
        'custom_form',
        'user_created',
        false,
        '不良事件,安全性,临床试验',
        0,
        (SELECT id FROM projects WHERE name = '示例CRF项目' LIMIT 1),
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
        '{
            "pageConfig": {
                "theme": "medical",
                "layout": "vertical",
                "title": "不良事件报告",
                "showProgress": false
            },
            "formStructure": {
                "sections": [
                    {
                        "id": "event_info",
                        "title": "事件信息",
                        "components": [
                            {"id": "event_date", "type": "date", "label": "事件发生日期", "required": true},
                            {"id": "event_description", "type": "textarea", "label": "事件描述", "required": true},
                            {"id": "severity", "type": "select", "label": "严重程度", "required": true, "options": [{"label": "轻度", "value": "mild"}, {"label": "中度", "value": "moderate"}, {"label": "重度", "value": "severe"}]}
                        ]
                    }
                ]
            }
        }'
    ),
    -- 导入的模板示例
    (
        'lab_results_template',
        '实验室检查结果模板',
        '从外部系统导入的实验室检查标准模板',
        'laboratory,lab,results',
        '2.1.0',
        'published',
        'template',
        'imported',
        true,
        '实验室检查,化验结果,导入模板',
        5,
        (SELECT id FROM projects WHERE name = '示例CRF项目' LIMIT 1),
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
        '{
            "pageConfig": {
                "theme": "medical",
                "layout": "two-column",
                "title": "实验室检查结果"
            },
            "formStructure": {
                "sections": [
                    {
                        "id": "blood_test",
                        "title": "血液检查",
                        "components": [
                            {"id": "wbc", "type": "number", "label": "白细胞计数", "unit": "×10⁹/L"},
                            {"id": "rbc", "type": "number", "label": "红细胞计数", "unit": "×10¹²/L"},
                            {"id": "hemoglobin", "type": "number", "label": "血红蛋白", "unit": "g/L"}
                        ]
                    }
                ]
            }
        }'
    )
ON CONFLICT (project_id, name, version) DO NOTHING;

-- 创建示例版本记录
INSERT INTO crf_versions (
    template_id, version, title, description, snapshot_data, status, created_by
) VALUES
    (
        (SELECT id FROM crf_templates WHERE name = 'patient_baseline_template' LIMIT 1),
        '1.0.0',
        '患者基线信息采集模板',
        '初始版本，包含基本的人口学信息和病史收集',
        '{"version": "1.0.0", "snapshot": "Initial version"}',
        'published',
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1)
    )
ON CONFLICT (template_id, version) DO NOTHING;

-- 记录初始数据库版本
INSERT INTO migrations (version, name, applied, applied_at) VALUES
    ('2.0.0', 'Initial schema with full CRF system', true, CURRENT_TIMESTAMP)
ON CONFLICT (version) DO NOTHING;

-- ================================================================
-- 数据库架构说明
-- ================================================================

/*
数据库架构说明 (Database Schema Documentation)

1. 用户管理 (User Management):
   - users: 用户基本信息，支持多种登录方式
   - user_sessions: 用户会话管理

2. 项目管理 (Project Management):
   - projects: 项目信息和设置

3. CRF模板管理 (CRF Template Management):
   - crf_templates: CRF表单模板主表
   - crf_versions: 模板版本管理

4. CRF实例管理 (CRF Instance Management):
   - crf_instances: 填写的表单实例

5. 系统功能 (System Features):
   - auto_saves: 自动保存记录
   - operation_history: 操作历史审计
   - attachments: 文件附件管理
   - system_settings: 系统配置

6. 系统管理 (System Management):
   - migrations: 数据库迁移追踪

主要特性 (Key Features):
- UUID主键确保全局唯一性
- JSONB字段支持灵活的数据存储
- 完整的索引策略提升查询性能
- 外键约束确保数据完整性
- 触发器自动更新时间戳
- 多种登录方式支持
- 版本控制系统
- 审计日志记录
- 自动保存功能
*/

-- ================================================================
-- 架构版本信息
-- ================================================================

INSERT INTO system_settings (key, value, description) VALUES
    ('schema_version', '"2.0.0"', '数据库架构版本'),
    ('schema_updated_at', '"' || CURRENT_TIMESTAMP || '"', '架构最后更新时间'),
    ('schema_description', '"Complete CRF Web Editor Database Schema with full feature support"', '架构描述')
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- ================================================================
-- 项目统计触发器和函数
-- ================================================================

-- 更新项目统计的函数
CREATE OR REPLACE FUNCTION update_project_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新模板数量
    UPDATE projects
    SET template_count = (
        SELECT COUNT(*)
        FROM crf_templates
        WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
    )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);

    -- 更新实例数量
    UPDATE projects
    SET instance_count = (
        SELECT COUNT(*)
        FROM crf_instances ci
        JOIN crf_templates ct ON ci.template_id = ct.id
        WHERE ct.project_id = COALESCE(NEW.project_id, OLD.project_id)
    )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 模板变更时更新项目统计
CREATE TRIGGER trigger_update_project_stats_on_template
    AFTER INSERT OR UPDATE OR DELETE ON crf_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_project_stats();

-- 实例变更时更新项目统计
CREATE TRIGGER trigger_update_project_stats_on_instance
    AFTER INSERT OR UPDATE OR DELETE ON crf_instances
    FOR EACH ROW
    EXECUTE FUNCTION update_project_stats();

-- 项目查询优化索引
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(project_type);
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_dates ON projects(start_date, end_date);

-- 模板项目关联索引优化
CREATE INDEX IF NOT EXISTS idx_crf_templates_project_status ON crf_templates(project_id, status);

-- 实例统计查询优化视图
CREATE OR REPLACE VIEW project_statistics AS
SELECT
    p.id,
    p.name,
    p.description,
    p.status,
    p.project_type,
    p.category,
    p.start_date,
    p.end_date,
    p.created_at,
    p.updated_at,
    u.username as creator_name,
    u.full_name as creator_full_name,

    -- 统计数据
    COALESCE(template_stats.template_count, 0) as template_count,
    COALESCE(template_stats.published_template_count, 0) as published_template_count,
    COALESCE(instance_stats.instance_count, 0) as instance_count,
    COALESCE(instance_stats.completed_instance_count, 0) as completed_instance_count,
    COALESCE(instance_stats.draft_instance_count, 0) as draft_instance_count

FROM projects p
LEFT JOIN users u ON p.created_by = u.id
LEFT JOIN (
    SELECT
        project_id,
        COUNT(*) as template_count,
        COUNT(CASE WHEN status = 'published' THEN 1 END) as published_template_count
    FROM crf_templates
    GROUP BY project_id
) template_stats ON p.id = template_stats.project_id
LEFT JOIN (
    SELECT
        ct.project_id,
        COUNT(ci.id) as instance_count,
        COUNT(CASE WHEN ci.status = 'completed' THEN 1 END) as completed_instance_count,
        COUNT(CASE WHEN ci.status = 'draft' THEN 1 END) as draft_instance_count
    FROM crf_instances ci
    JOIN crf_templates ct ON ci.template_id = ct.id
    GROUP BY ct.project_id
) instance_stats ON p.id = instance_stats.project_id;

-- 输出完成信息
SELECT 'CRF Web Editor Database Schema v2.1.0 创建完成!' as message;
SELECT 'Schema includes: Users, Projects, Templates, Instances, System Features, Tasks, and Migrations' as features;
SELECT 'Total tables created: 13, with enhanced project management' as table_count;
SELECT 'Ready for production use with comprehensive indexing and project statistics' as status;