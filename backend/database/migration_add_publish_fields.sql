-- =================================================================
-- 数据库迁移脚本：添加发布相关字段到crf_templates表
-- 执行日期：2025-07-09
-- 描述：修复发布模板功能所需的数据库字段
-- =================================================================

-- 1. 添加缺失的发布相关字段到crf_templates表
DO $$
BEGIN
    -- 检查并添加published_at字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'crf_templates' AND column_name = 'published_at'
    ) THEN
        ALTER TABLE crf_templates 
        ADD COLUMN published_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added published_at column to crf_templates';
    ELSE
        RAISE NOTICE 'published_at column already exists in crf_templates';
    END IF;

    -- 检查并添加published_by字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'crf_templates' AND column_name = 'published_by'
    ) THEN
        ALTER TABLE crf_templates 
        ADD COLUMN published_by UUID REFERENCES users(id) ON DELETE SET NULL;
        RAISE NOTICE 'Added published_by column to crf_templates';
    ELSE
        RAISE NOTICE 'published_by column already exists in crf_templates';
    END IF;
END
$$;

-- 2. 添加索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_at ON crf_templates(published_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_by ON crf_templates(published_by);

-- 3. 验证字段是否添加成功
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'crf_templates' 
AND column_name IN ('published_at', 'published_by')
ORDER BY column_name;

-- 4. 显示当前crf_templates表的所有列（用于验证）
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'crf_templates' 
ORDER BY ordinal_position;

RAISE NOTICE '迁移完成！现在可以使用发布模板功能了。';