-- ================================================================
-- 诊断admin用户权限问题的详细SQL脚本
-- ================================================================

-- 1. 确认admin用户角色分配是否成功
SELECT '=== 1. admin用户角色分配检查 ===' as step;
SELECT 
    u.id as user_id,
    u.username,
    r.id as role_id,
    r.name as role_name,
    r.display_name,
    ur.created_at as role_assigned_at
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'admin';

-- 2. 检查super_admin角色拥有的权限
SELECT '=== 2. super_admin角色权限检查 ===' as step;
SELECT 
    r.name as role_name,
    p.resource,
    p.action,
    p.scope,
    p.description,
    COUNT(*) OVER() as total_permissions
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'super_admin'
ORDER BY p.resource, p.action, p.scope;

-- 3. 检查admin用户实际能获取到的权限
SELECT '=== 3. admin用户实际权限检查 ===' as step;
SELECT 
    u.username,
    p.resource,
    p.action,
    p.scope,
    p.description
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin'
  AND ur.deleted_at IS NULL
  AND rp.deleted_at IS NULL
  AND p.deleted_at IS NULL
  AND (ur.expires_at IS NULL OR ur.expires_at > CURRENT_TIMESTAMP)
ORDER BY p.resource, p.action, p.scope;

-- 4. 特别检查template相关权限（表单列表需要这个）
SELECT '=== 4. template权限专项检查 ===' as step;
SELECT 
    u.username,
    p.resource,
    p.action,
    p.scope,
    'admin用户是否有template读取权限' as check_purpose
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin'
  AND p.resource = 'template'
  AND p.action = 'read'
  AND ur.deleted_at IS NULL
  AND rp.deleted_at IS NULL
  AND p.deleted_at IS NULL;

-- 5. 检查权限缓存表
SELECT '=== 5. 权限缓存检查 ===' as step;
SELECT 
    upc.user_id,
    upc.resource,
    upc.action,
    upc.has_permission,
    upc.cache_expires_at,
    CASE 
        WHEN upc.cache_expires_at > CURRENT_TIMESTAMP THEN '缓存有效'
        ELSE '缓存已过期'
    END as cache_status
FROM user_permission_cache upc
JOIN users u ON upc.user_id = u.id
WHERE u.username = 'admin'
ORDER BY upc.resource, upc.action;

-- 6. 测试权限检查函数
SELECT '=== 6. 权限函数测试 ===' as step;
SELECT 
    u.username,
    check_user_permission(u.id, 'template', 'read', NULL) as can_read_template_global,
    check_user_permission(u.id, 'template', 'create', NULL) as can_create_template,
    check_user_permission(u.id, 'user', 'read', NULL) as can_read_user
FROM users u
WHERE u.username = 'admin';

-- 7. 检查所有表的deleted_at字段状态
SELECT '=== 7. 数据完整性检查 ===' as step;
SELECT 
    'users' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records
FROM users
WHERE username = 'admin'
UNION ALL
SELECT 
    'user_roles' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records
FROM user_roles ur
JOIN users u ON ur.user_id = u.id
WHERE u.username = 'admin'
UNION ALL
SELECT 
    'roles' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records
FROM roles
WHERE name = 'super_admin';

SELECT '=== 诊断完成 ===' as final_step;