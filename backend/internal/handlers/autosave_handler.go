package handlers

import (
	"crf-backend/internal/models"
	"crf-backend/internal/services"
	"encoding/json"
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type AutoSaveHandler struct {
	autoSaveService *services.AutoSaveService
	logger          *logrus.Logger
}

func NewAutoSaveHandler(autoSaveService *services.AutoSaveService, logger *logrus.Logger) *AutoSaveHandler {
	return &AutoSaveHandler{
		autoSaveService: autoSaveService,
		logger:          logger,
	}
}

type SaveDataRequest struct {
	ResourceType string          `json:"resource_type" binding:"required"`
	ResourceID   string          `json:"resource_id" binding:"required"`
	Data         json.RawMessage `json:"data" binding:"required"`
}

func (h *AutoSaveHandler) SaveData(c *gin.Context) {
	var req SaveDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user
	session := sessions.Default(c)
	userIDStr := session.Get("user_id").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	resourceID, err := uuid.Parse(req.ResourceID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
		return
	}

	if err := h.autoSaveService.SaveData(userID, resourceID, req.ResourceType, req.Data); err != nil {
		h.logger.WithError(err).Error("Failed to save auto save data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Data saved successfully"})
}

// Development version without authentication
func (h *AutoSaveHandler) SaveDataDev(c *gin.Context) {
	var req SaveDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Ensure development user exists
	userID := h.ensureDevUser()

	resourceID, err := uuid.Parse(req.ResourceID)
	if err != nil {
		// For development, if it's not a valid UUID, use the string as is
		resourceID = uuid.New()
	}

	// Map frontend resource types to database enum values
	resourceType := h.mapResourceType(req.ResourceType)

	if err := h.autoSaveService.SaveData(userID, resourceID, resourceType, req.Data); err != nil {
		h.logger.WithError(err).Error("Failed to save auto save data (dev)")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Data saved successfully"})
}

func (h *AutoSaveHandler) GetSavedData(c *gin.Context) {
	resourceType := c.Param("resource_type")
	resourceIDStr := c.Param("resource_id")

	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
		return
	}

	// Get current user
	session := sessions.Default(c)
	userIDStr := session.Get("user_id").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	autoSave, err := h.autoSaveService.GetSavedData(userID, resourceID, resourceType)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get auto save data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get saved data"})
		return
	}

	if autoSave == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No saved data found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": autoSave})
}

// Development version without authentication
func (h *AutoSaveHandler) GetSavedDataDev(c *gin.Context) {
	resourceType := c.Param("resource_type")
	resourceIDStr := c.Param("resource_id")

	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
		return
	}

	// Use a default user ID for development
	userID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	autoSave, err := h.autoSaveService.GetSavedData(userID, resourceID, resourceType)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get auto save data (dev)")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get saved data"})
		return
	}

	if autoSave == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No saved data found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": autoSave})
}

func (h *AutoSaveHandler) DeleteSavedData(c *gin.Context) {
	resourceType := c.Param("resource_type")
	resourceIDStr := c.Param("resource_id")

	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
		return
	}

	// Get current user
	session := sessions.Default(c)
	userIDStr := session.Get("user_id").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if err := h.autoSaveService.DeleteSavedData(userID, resourceID, resourceType); err != nil {
		h.logger.WithError(err).Error("Failed to delete auto save data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete saved data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Saved data deleted successfully"})
}

// Development version without authentication
func (h *AutoSaveHandler) DeleteSavedDataDev(c *gin.Context) {
	resourceType := c.Param("resource_type")
	resourceIDStr := c.Param("resource_id")

	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
		return
	}

	// Use a default user ID for development
	userID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	if err := h.autoSaveService.DeleteSavedData(userID, resourceID, resourceType); err != nil {
		h.logger.WithError(err).Error("Failed to delete auto save data (dev)")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete saved data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Saved data deleted successfully"})
}

// Helper functions for AutoSaveHandler

// ensureDevUser ensures a development user exists and returns its ID
func (h *AutoSaveHandler) ensureDevUser() uuid.UUID {
	devUserID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	// Try to create the dev user if it doesn't exist
	devUser := &models.User{
		ID:           devUserID,
		Username:     "dev_user",
		Email:        "<EMAIL>",
		PasswordHash: "dev_password_hash",
		FullName:     "Development User",
		// Role字段已移除，通过RBAC系统管理
		IsActive:     true,
	}

	// Use GORM's FirstOrCreate to ensure the user exists
	if err := h.autoSaveService.CreateDevUser(devUser); err != nil {
		h.logger.WithError(err).Warn("Failed to ensure dev user exists, but continuing...")
	}

	return devUserID
}

// mapResourceType maps frontend resource types to database enum values
func (h *AutoSaveHandler) mapResourceType(frontendType string) string {
	// Map frontend resource types to database accepted values
	switch frontendType {
	case "template", "crf_template":
		return "template"
	case "instance", "crf_instance":
		return "instance"
	case "project":
		return "project"
	default:
		// Default to template if unknown
		return "template"
	}
}
