package handlers

import (
	"crf-backend/internal/errors"
	"crf-backend/internal/i18n"
	"crf-backend/internal/interfaces"
	"crf-backend/internal/models"
	"crf-backend/internal/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// UserHandler 用户处理器（优化版）
type OptimizedUserHandler struct {
	userService interfaces.UserServiceInterface
	jwtService  interfaces.JWTServiceInterface
	logger      *logrus.Logger
	i18n        *i18n.I18n
}

// NewOptimizedUserHandler 创建优化的用户处理器
func NewOptimizedUserHandler(
	userService interfaces.UserServiceInterface,
	jwtService interfaces.JWTServiceInterface,
	logger *logrus.Logger,
) *OptimizedUserHandler {
	return &OptimizedUserHandler{
		userService: userService,
		jwtService:  jwtService,
		logger:      logger,
		i18n:        i18n.New(i18n.LangZhCN),
	}
}

// LoginRequest 登录请求
type OptimizedLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest 注册请求
type OptimizedRegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6,max=128"`
	FullName string `json:"full_name" binding:"max=100"`
}

// getLanguage 从请求头获取语言设置
func (h *OptimizedUserHandler) getLanguage(c *gin.Context) i18n.Language {
	acceptLang := c.GetHeader("Accept-Language")
	return i18n.GetLanguageFromAcceptLanguage(acceptLang)
}

// Login 优化的登录处理
func (h *OptimizedUserHandler) Login(c *gin.Context) {
	var req OptimizedLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Login request validation failed")
		apiErr := errors.NewValidationError(h.i18n.T(h.getLanguage(c), i18n.MsgValidationFailed))
		apiErr.Details = err.Error()
		response.HandleAPIError(c, apiErr)
		return
	}

	lang := h.getLanguage(c)

	h.logger.WithFields(logrus.Fields{
		"username": req.Username,
		"language": lang,
	}).Info("Login attempt")

	// 查找用户
	user, err := h.userService.GetUserByUsername(req.Username)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.logger.WithField("username", req.Username).Warn("User not found during login")
			apiErr := errors.NewInvalidCredentialsError()
			apiErr.Message = h.i18n.T(lang, i18n.MsgInvalidCredentials)
			response.HandleAPIError(c, apiErr)
			return
		}

		h.logger.WithError(err).Error("Failed to get user by username")
		apiErr := errors.NewInternalError(h.i18n.T(lang, i18n.MsgInternalError))
		response.HandleAPIError(c, apiErr)
		return
	}

	// 检查用户状态
	if !user.IsActive {
		h.logger.WithField("user_id", user.ID).Warn("Inactive user attempted login")
		apiErr := errors.NewForbiddenError(h.i18n.T(lang, i18n.MsgUserInactive))
		response.HandleAPIError(c, apiErr)
		return
	}

	// 验证密码
	if err := h.userService.ValidatePassword(user, req.Password); err != nil {
		h.logger.WithField("user_id", user.ID).Warn("Password validation failed")
		apiErr := errors.NewInvalidCredentialsError()
		apiErr.Message = h.i18n.T(lang, i18n.MsgInvalidCredentials)
		response.HandleAPIError(c, apiErr)
		return
	}

	// 生成JWT令牌
	accessToken, refreshToken, err := h.jwtService.GenerateTokenPair(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate JWT token")
		apiErr := errors.NewInternalError(h.i18n.T(lang, i18n.MsgInternalError))
		response.HandleAPIError(c, apiErr)
		return
	}

	h.logger.WithField("user_id", user.ID).Info("Login successful")

	// 返回成功响应
	response.SuccessWithMessage(c, h.i18n.T(lang, i18n.MsgLoginSuccess), gin.H{
		"access_token":  accessToken,
		"refresh_token": refreshToken,
		"user": gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"avatar_url": user.AvatarURL,
		},
	})
}

// Register 优化的注册处理
func (h *OptimizedUserHandler) Register(c *gin.Context) {
	var req OptimizedRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Register request validation failed")
		apiErr := errors.NewValidationError(h.i18n.T(h.getLanguage(c), i18n.MsgValidationFailed))
		apiErr.Details = err.Error()
		response.HandleAPIError(c, apiErr)
		return
	}

	lang := h.getLanguage(c)

	h.logger.WithFields(logrus.Fields{
		"username": req.Username,
		"email":    req.Email,
		"language": lang,
	}).Info("Registration attempt")

	// 检查用户名是否已存在
	if _, err := h.userService.GetUserByUsername(req.Username); err == nil {
		h.logger.WithField("username", req.Username).Warn("Username already exists")
		apiErr := errors.NewUserExistsError()
		apiErr.Message = h.i18n.T(lang, i18n.MsgUserExists)
		response.HandleAPIError(c, apiErr)
		return
	}

	// 检查邮箱是否已存在
	if _, err := h.userService.GetUserByEmail(req.Email); err == nil {
		h.logger.WithField("email", req.Email).Warn("Email already exists")
		apiErr := errors.NewUserExistsError()
		apiErr.Message = h.i18n.T(lang, i18n.MsgUserExists)
		response.HandleAPIError(c, apiErr)
		return
	}

	// 创建用户
	user := &models.User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: req.Password, // 将在服务层进行哈希处理
		FullName:     req.FullName,
		IsActive:     true,
		LoginType:    "password",
	}

	if err := h.userService.CreateUser(user); err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		apiErr := errors.NewInternalError(h.i18n.T(lang, i18n.MsgInternalError))
		response.HandleAPIError(c, apiErr)
		return
	}

	h.logger.WithField("user_id", user.ID).Info("User registration successful")

	// 返回成功响应
	response.SuccessWithMessage(c, h.i18n.T(lang, i18n.MsgUserCreated), gin.H{
		"user": gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"avatar_url": user.AvatarURL,
		},
	})
}

// GetUser 获取用户信息
func (h *OptimizedUserHandler) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID format")
		apiErr := errors.NewBadRequestError(h.i18n.T(h.getLanguage(c), i18n.MsgBadRequest))
		response.HandleAPIError(c, apiErr)
		return
	}

	lang := h.getLanguage(c)

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.logger.WithField("user_id", userID).Warn("User not found")
			apiErr := errors.NewUserNotFoundError()
			apiErr.Message = h.i18n.T(lang, i18n.MsgUserNotFound)
			response.HandleAPIError(c, apiErr)
			return
		}

		h.logger.WithError(err).Error("Failed to get user by ID")
		apiErr := errors.NewInternalError(h.i18n.T(lang, i18n.MsgInternalError))
		response.HandleAPIError(c, apiErr)
		return
	}

	// 返回用户信息（不包含敏感字段）
	response.Success(c, gin.H{
		"user": gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"avatar_url": user.AvatarURL,
			"is_active":  user.IsActive,
			"created_at": user.CreatedAt,
			"updated_at": user.UpdatedAt,
		},
	})
}

// UpdateUser 更新用户信息
func (h *OptimizedUserHandler) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID format")
		apiErr := errors.NewBadRequestError(h.i18n.T(h.getLanguage(c), i18n.MsgBadRequest))
		response.HandleAPIError(c, apiErr)
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		h.logger.WithError(err).Error("Update request validation failed")
		apiErr := errors.NewValidationError(h.i18n.T(h.getLanguage(c), i18n.MsgValidationFailed))
		apiErr.Details = err.Error()
		response.HandleAPIError(c, apiErr)
		return
	}

	lang := h.getLanguage(c)

	// 验证用户是否存在
	if _, err := h.userService.GetUserByID(userID); err != nil {
		if err == gorm.ErrRecordNotFound {
			apiErr := errors.NewUserNotFoundError()
			apiErr.Message = h.i18n.T(lang, i18n.MsgUserNotFound)
			response.HandleAPIError(c, apiErr)
			return
		}

		h.logger.WithError(err).Error("Failed to get user by ID")
		apiErr := errors.NewInternalError(h.i18n.T(lang, i18n.MsgInternalError))
		response.HandleAPIError(c, apiErr)
		return
	}

	// 更新用户信息
	if err := h.userService.UpdateUser(userID, updates); err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		apiErr := errors.NewInternalError(h.i18n.T(lang, i18n.MsgInternalError))
		response.HandleAPIError(c, apiErr)
		return
	}

	h.logger.WithField("user_id", userID).Info("User updated successfully")

	response.SuccessWithMessage(c, h.i18n.T(lang, i18n.MsgUserUpdated), nil)
}