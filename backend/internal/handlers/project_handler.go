package handlers

import (
	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/datatypes"
)

type ProjectHandler struct {
	projectService *services.ProjectService
	logger         *logrus.Logger
}

func NewProjectHandler(projectService *services.ProjectService, logger *logrus.Logger) *ProjectHandler {
	return &ProjectHandler{
		projectService: projectService,
		logger:         logger,
	}
}

type CreateProjectRequest struct {
	Name        string          `json:"name" binding:"required"`
	Description string          `json:"description"`
	ProjectType string          `json:"project_type"`
	Category    string          `json:"category"`
	StartDate   *string         `json:"start_date"`
	EndDate     *string         `json:"end_date"`
	Settings    json.RawMessage `json:"settings"`
}

func (h *ProjectHandler) CreateProject(c *gin.Context) {
	var req CreateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from JWT context (set by JWTAuth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	userIDString, ok := userIDStr.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID格式"})
		return
	}

	userID, err := uuid.Parse(userIDString)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID格式"})
		return
	}

	project := &models.Project{
		Name:        req.Name,
		Description: req.Description,
		CreatedBy:   userID,
		Status:      "active",
		ProjectType: req.ProjectType,
		Category:    req.Category,
	}

	// Parse dates if provided
	if req.StartDate != nil && *req.StartDate != "" {
		if startDate, err := parseDate(*req.StartDate); err == nil {
			project.StartDate = startDate
		}
	}
	if req.EndDate != nil && *req.EndDate != "" {
		if endDate, err := parseDate(*req.EndDate); err == nil {
			project.EndDate = endDate
		}
	}

	if req.Settings != nil {
		project.Settings = datatypes.JSON(req.Settings)
	}

	if err := h.projectService.CreateProject(project); err != nil {
		h.logger.WithError(err).Error("Failed to create project")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Project created successfully",
		"project": project,
	})
}

func (h *ProjectHandler) GetProjects(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Convert page-based pagination to limit/offset
	limit := pageSize
	offset := (page - 1) * pageSize

	// Get user ID from JWT context (set by JWTAuth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	userIDString, ok := userIDStr.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID格式"})
		return
	}

	userID, err := uuid.Parse(userIDString)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID格式"})
		return
	}

	// Parse filters
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if projectType := c.Query("project_type"); projectType != "" {
		filters["project_type"] = projectType
	}
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	// Check if statistics are requested
	withStats := c.Query("with_stats") == "true"

	if withStats {
		projects, total, err := h.projectService.GetProjectsWithStatistics(userID, filters, limit, offset)
		if err != nil {
			h.logger.WithError(err).Error("Failed to get projects with statistics")
			response.InternalError(c, "获取项目列表失败")
			return
		}

		response.Success(c, gin.H{
			"projects": projects,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		})
	} else {
		projects, total, err := h.projectService.GetProjects(userID, filters, limit, offset)
		if err != nil {
			h.logger.WithError(err).Error("Failed to get projects")
			response.InternalError(c, "获取项目列表失败")
			return
		}

		response.Success(c, gin.H{
			"projects": projects,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		})
	}
}

func (h *ProjectHandler) GetProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	project, err := h.projectService.GetProject(id)
	if err != nil {
		response.NotFound(c, "项目不存在")
		return
	}

	response.Success(c, gin.H{"project": project})
}

// GetProjectStatistics 获取项目统计信息
func (h *ProjectHandler) GetProjectStatistics(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	stats, err := h.projectService.GetProjectStatistics(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"statistics": stats})
}

// GetProjectTemplates 获取项目下的模板列表
func (h *ProjectHandler) GetProjectTemplates(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Parse filters
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	templates, total, err := h.projectService.GetProjectTemplates(id, filters, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get project templates")
		response.InternalError(c, "获取项目模板失败")
		return
	}

	response.Success(c, gin.H{
		"templates": templates,
		"pagination": gin.H{
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

func (h *ProjectHandler) UpdateProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.projectService.UpdateProject(id, updates); err != nil {
		h.logger.WithError(err).Error("Failed to update project")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project updated successfully"})
}

func (h *ProjectHandler) DeleteProject(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	if err := h.projectService.DeleteProject(id); err != nil {
		h.logger.WithError(err).Error("Failed to delete project")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}

// BatchDeleteProjects 批量删除项目
func (h *ProjectHandler) BatchDeleteProjects(c *gin.Context) {
	var request struct {
		ProjectIDs []string `json:"project_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if len(request.ProjectIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No project IDs provided"})
		return
	}

	// 转换字符串ID为UUID
	var projectIDs []uuid.UUID
	for _, idStr := range request.ProjectIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid project ID: %s", idStr)})
			return
		}
		projectIDs = append(projectIDs, id)
	}

	// 批量删除项目
	if err := h.projectService.BatchDeleteProjects(projectIDs); err != nil {
		h.logger.WithError(err).Error("Failed to batch delete projects")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to batch delete projects"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Successfully deleted %d projects", len(projectIDs)),
		"deleted_count": len(projectIDs),
	})
}

// RefreshProjectStats 刷新项目统计信息
func (h *ProjectHandler) RefreshProjectStats(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	if err := h.projectService.RefreshProjectStats(id); err != nil {
		h.logger.WithError(err).Error("Failed to refresh project stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to refresh project stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project statistics refreshed successfully"})
}

// ValidateProjectName 验证项目名称是否可用
func (h *ProjectHandler) ValidateProjectName(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "项目名称不能为空"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	userIDString, ok := userIDStr.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID格式"})
		return
	}

	userID, err := uuid.Parse(userIDString)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID格式"})
		return
	}

	available, err := h.projectService.IsProjectNameAvailable(name, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate project name")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "验证项目名称失败"})
		return
	}

	response.Success(c, gin.H{
		"available": available,
		"name":      name,
	})
}

// parseDate 解析日期字符串
func parseDate(dateStr string) (*time.Time, error) {
	// 尝试多种日期格式
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return &t, nil
		}
	}

	return nil, fmt.Errorf("invalid date format: %s", dateStr)
}
