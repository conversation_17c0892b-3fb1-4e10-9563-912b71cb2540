package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type User struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Username     string     `json:"username" gorm:"uniqueIndex;not null"`
	Email        string     `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash string     `json:"-" gorm:"not null"`
	FullName     string     `json:"full_name"`
	AvatarURL    string     `json:"avatar_url"`
	IsActive     bool       `json:"is_active" gorm:"default:true"`

	// 多种登录方式支持
	Phone         *string `json:"phone" gorm:"uniqueIndex"`
	WechatOpenID  *string `json:"wechat_open_id" gorm:"uniqueIndex"`
	WechatUnionID *string `json:"wechat_union_id" gorm:"uniqueIndex"`
	LoginType     string  `json:"login_type" gorm:"default:password"`

	// 统一软删除策略
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// RBAC 关联 - 移除物理外键约束
	UserRoles []UserRole `json:"user_roles,omitempty" gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Roles     []Role     `json:"roles_objects,omitempty" gorm:"many2many:user_roles;"`
}

type UserSession struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID       uuid.UUID  `json:"user_id" gorm:"not null"`
	SessionToken string     `json:"session_token" gorm:"uniqueIndex;not null"`
	IPAddress    string     `json:"ip_address"`
	UserAgent    string     `json:"user_agent"`
	CreatedAt    time.Time  `json:"created_at"`
	ExpiresAt    time.Time  `json:"expires_at"`
	LastActivity time.Time  `json:"last_activity"`

	// 统一软删除策略
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`

	// 移除物理外键约束，改为逻辑关联
	User User `json:"user" gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

func (s *UserSession) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}
