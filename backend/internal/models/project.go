package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Project struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name        string         `json:"name" gorm:"not null"`
	Description string         `json:"description"`
	CreatedBy   uuid.UUID      `json:"created_by" gorm:"not null"`
	Status      string         `json:"status" gorm:"default:draft"`

	// 项目类型和分类
	ProjectType string `json:"project_type" gorm:"default:clinical_trial"`
	Category    string `json:"category"`

	// 项目设置
	Settings datatypes.JSON `json:"settings" gorm:"type:jsonb;default:'{}'"`

	// 项目统计字段（冗余存储，提高查询性能）
	TemplateCount int `json:"template_count" gorm:"default:0"`
	InstanceCount int `json:"instance_count" gorm:"default:0"`
	MemberCount   int `json:"member_count" gorm:"default:1"`

	// 项目时间管理
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`

	// 逻辑删除字段
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;index"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联关系
	Creator   User          `json:"creator" gorm:"foreignKey:CreatedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Templates []CRFTemplate `json:"templates,omitempty" gorm:"foreignKey:ProjectID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// ProjectStatistics 项目统计信息
type ProjectStatistics struct {
	ID                       uuid.UUID  `json:"id"`
	Name                     string     `json:"name"`
	Description              string     `json:"description"`
	Status                   string     `json:"status"`
	ProjectType              string     `json:"project_type"`
	Category                 string     `json:"category"`
	StartDate                *time.Time `json:"start_date"`
	EndDate                  *time.Time `json:"end_date"`
	CreatedAt                time.Time  `json:"created_at"`
	UpdatedAt                time.Time  `json:"updated_at"`
	CreatorName              string     `json:"creator_name"`
	CreatorFullName          string     `json:"creator_full_name"`
	TemplateCount            int        `json:"template_count"`
	PublishedTemplateCount   int        `json:"published_template_count"`
	InstanceCount            int        `json:"instance_count"`
	CompletedInstanceCount   int        `json:"completed_instance_count"`
	DraftInstanceCount       int        `json:"draft_instance_count"`
}

func (p *Project) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}
