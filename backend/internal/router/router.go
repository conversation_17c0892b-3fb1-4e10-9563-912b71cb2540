package router

import (
	"time"

	"crf-backend/internal/config"
	"crf-backend/internal/handlers"
	"crf-backend/internal/middleware"
	"crf-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type Router struct {
	engine *gin.Engine
	config *config.Config
}

func New(cfg *config.Config) *Router {
	if cfg.Environment.Name == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()
	engine.Use(gin.Recovery())

	return &Router{
		engine: engine,
		config: cfg,
	}
}

func (r *Router) SetupMiddleware(logger *logrus.Logger) {
	r.engine.Use(middleware.Logger(logger))
	// 保留Session中间件，以防某些地方仍需要使用
	r.engine.Use(middleware.Session(r.config.Security.Session.Secret))
}

func (r *Router) SetupRoutes(
	userHandler *handlers.UserHandler,
	projectHandler *handlers.ProjectHand<PERSON>,
	templateHandler *handlers.Te<PERSON><PERSON><PERSON><PERSON><PERSON>,
	instanceHandler *handlers.InstanceHandler,
	autoSaveHandler interface{},
	historyHandler *handlers.HistoryHandler,
	dataManagementHandler *handlers.DataManagementHandler,
	avatarHandler *handlers.AvatarHandler,
	jwtService *services.JWTService,
	rbacService *services.RBACService,
	logger *logrus.Logger,
) {
	r.setupHealthCheck()
	r.setupAPIRoutes(userHandler, projectHandler, templateHandler, instanceHandler, autoSaveHandler, historyHandler, dataManagementHandler, avatarHandler, jwtService, rbacService, logger)
}

func (r *Router) setupHealthCheck() {
	r.engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"version":   "1.0.0",
		})
	})
}

func (r *Router) setupAPIRoutes(
	userHandler *handlers.UserHandler,
	projectHandler *handlers.ProjectHandler,
	templateHandler *handlers.TemplateHandler,
	instanceHandler *handlers.InstanceHandler,
	autoSaveHandler interface{},
	historyHandler *handlers.HistoryHandler,
	dataManagementHandler *handlers.DataManagementHandler,
	avatarHandler *handlers.AvatarHandler,
	jwtService *services.JWTService,
	rbacService *services.RBACService,
	logger *logrus.Logger,
) {
	api := r.engine.Group("/api")

	r.setupAuthRoutes(api, userHandler)
	r.setupUserRoutes(api, userHandler, avatarHandler, jwtService, rbacService, logger)
	r.setupProjectRoutes(api, projectHandler, jwtService, rbacService, logger)
	r.setupTemplateRoutes(api, templateHandler, jwtService, rbacService, logger)
	r.setupInstanceRoutes(api, instanceHandler, jwtService, rbacService, logger)
	r.setupAutoSaveRoutes(api, autoSaveHandler)
	r.setupHistoryRoutes(api, historyHandler, jwtService, logger)
	r.setupDataManagementRoutes(api, dataManagementHandler, jwtService, rbacService, logger)
	r.setupRoleRoutes(api, rbacService, jwtService, logger)
	r.setupErrorReportingRoutes(api)
}

func (r *Router) setupAuthRoutes(api *gin.RouterGroup, userHandler *handlers.UserHandler) {
	auth := api.Group("/auth")
	{
		auth.POST("/login", userHandler.Login)
		auth.POST("/register", userHandler.Register)
		auth.POST("/logout", userHandler.Logout)
		auth.POST("/refresh", userHandler.RefreshToken) // JWT Token刷新
		// GetCurrentUser需要JWT认证，但不在这里设置中间件，而是在app.go中传递JWT服务
	}
}

func (r *Router) setupUserRoutes(api *gin.RouterGroup, userHandler *handlers.UserHandler, avatarHandler *handlers.AvatarHandler, jwtService *services.JWTService, rbacService *services.RBACService, logger *logrus.Logger) {
	// 获取当前用户信息（需要JWT认证）
	api.GET("/auth/me", middleware.JWTAuth(jwtService, logger), userHandler.GetCurrentUser)

	users := api.Group("/users")
	users.Use(middleware.JWTAuth(jwtService, logger)) // 使用JWT中间件
	{
		// 基础用户查询 - 需要用户读取权限
		users.GET("", middleware.PermissionRequired(rbacService, "user", "read"), userHandler.GetUsers)
		users.GET("/:id", middleware.PermissionRequired(rbacService, "user", "read"), userHandler.GetUser)
		
		// 用户管理 - 需要用户管理权限
		users.PUT("/:id", middleware.UserManagementRequired(rbacService), userHandler.UpdateUser)
		users.DELETE("/:id", middleware.UserManagementRequired(rbacService), userHandler.DeleteUser)
		
		// 头像管理 - 用户可以管理自己的头像，管理员可以管理任何用户的头像
		users.POST("/:id/avatar", avatarHandler.UploadAvatar)
		users.DELETE("/:id/avatar", avatarHandler.DeleteAvatar)
		
		// 用户资料管理
		users.GET("/:id/profile", userHandler.GetUser)
		users.PUT("/:id/profile", userHandler.UpdateUser)
		users.PUT("/:id/password", userHandler.UpdateUser)
		
		// 用户激活/停用
		users.POST("/:id/activate", middleware.UserManagementRequired(rbacService), userHandler.UpdateUser)
		users.POST("/:id/deactivate", middleware.UserManagementRequired(rbacService), userHandler.UpdateUser)
		
		// 角色管理
		users.PUT("/:id/roles", middleware.UserManagementRequired(rbacService), userHandler.UpdateUser)
		
		// 密码重置
		users.POST("/:id/reset-password", middleware.UserManagementRequired(rbacService), userHandler.UpdateUser)
		
		// 批量操作
		users.POST("/batch", middleware.UserManagementRequired(rbacService), userHandler.UpdateUser)
		
		// 统计信息
		users.GET("/stats", middleware.PermissionRequired(rbacService, "user", "read"), userHandler.GetUsers)
		
		// 权限查询
		users.GET("/:id/permissions", middleware.PermissionRequired(rbacService, "user", "read"), userHandler.GetUser)
		
		// 导出功能
		users.GET("/export", middleware.PermissionRequired(rbacService, "user", "read"), userHandler.GetUsers)
		
		// 用户统计
		users.GET("/:id/stats", middleware.PermissionRequired(rbacService, "user", "read"), userHandler.GetUser)
	}
}

func (r *Router) setupProjectRoutes(api *gin.RouterGroup, projectHandler *handlers.ProjectHandler, jwtService *services.JWTService, rbacService *services.RBACService, logger *logrus.Logger) {
	projects := api.Group("/projects")
	projects.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 项目查询 - 需要项目读取权限
		projects.GET("", middleware.PermissionRequired(rbacService, "project", "read"), projectHandler.GetProjects)
		projects.GET("/:id", middleware.PermissionRequired(rbacService, "project", "read"), projectHandler.GetProject)
		projects.GET("/:id/statistics", middleware.PermissionRequired(rbacService, "project", "read"), projectHandler.GetProjectStatistics)
		projects.GET("/:id/templates", middleware.PermissionRequired(rbacService, "project", "read"), projectHandler.GetProjectTemplates)

		// 项目名称验证 - 需要项目创建权限
		projects.GET("/validate-name", middleware.PermissionRequired(rbacService, "project", "create"), projectHandler.ValidateProjectName)

		// 项目管理 - 需要项目创建/更新/删除权限
		projects.POST("", middleware.PermissionRequired(rbacService, "project", "create"), projectHandler.CreateProject)
		projects.PUT("/:id", middleware.PermissionRequired(rbacService, "project", "update"), projectHandler.UpdateProject)
		projects.DELETE("/:id", middleware.PermissionRequired(rbacService, "project", "delete"), projectHandler.DeleteProject)
		
		// 批量删除项目 - 需要项目删除权限
		projects.POST("/batch-delete", middleware.PermissionRequired(rbacService, "project", "delete"), projectHandler.BatchDeleteProjects)

		// 项目统计刷新
		projects.POST("/:id/refresh-stats", middleware.PermissionRequired(rbacService, "project", "update"), projectHandler.RefreshProjectStats)
	}
}

func (r *Router) setupTemplateRoutes(api *gin.RouterGroup, templateHandler *handlers.TemplateHandler, jwtService *services.JWTService, rbacService *services.RBACService, logger *logrus.Logger) {
	templates := api.Group("/templates")
	templates.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 模板查询 - 需要模板读取权限
		templates.GET("", middleware.PermissionRequired(rbacService, "template", "read"), templateHandler.GetTemplates)
		templates.GET("/deleted", middleware.PermissionRequired(rbacService, "template", "read"), templateHandler.GetDeletedTemplates)
		templates.GET("/:id", middleware.PermissionRequired(rbacService, "template", "read"), templateHandler.GetTemplate)
		templates.GET("/:id/versions", middleware.PermissionRequired(rbacService, "template", "read"), templateHandler.GetTemplateVersions)
		templates.GET("/:id/stats", middleware.PermissionRequired(rbacService, "template", "read"), templateHandler.GetTemplateStats)
		templates.GET("/:id/access-link", middleware.PermissionRequired(rbacService, "template", "read"), templateHandler.GetPublicAccessLink)
		templates.GET("/auto-save-config", templateHandler.GetAutoSaveConfig)
		
		// 模板管理 - 需要模板创建/更新/删除权限
		templates.POST("", middleware.PermissionRequired(rbacService, "template", "create"), templateHandler.CreateTemplate)
		templates.PUT("/:id", middleware.PermissionRequired(rbacService, "template", "update"), templateHandler.UpdateTemplate)
		templates.DELETE("/:id", middleware.PermissionRequired(rbacService, "template", "delete"), templateHandler.DeleteTemplate)
		templates.POST("/:id/restore", middleware.PermissionRequired(rbacService, "template", "update"), templateHandler.RestoreTemplate)
		
		// 模板发布 - 需要模板发布权限
		templates.POST("/:id/publish", middleware.PermissionRequired(rbacService, "template", "publish"), templateHandler.PublishTemplate)
		templates.POST("/:id/versions", middleware.PermissionRequired(rbacService, "template", "publish"), templateHandler.CreateTemplateVersion)
		templates.POST("/:id/rollback/:version_id", middleware.PermissionRequired(rbacService, "template", "update"), templateHandler.RollbackToVersion)
		
		// 模板草稿保存 - 需要模板更新权限
		templates.POST("/:id/save-draft", middleware.PermissionRequired(rbacService, "template", "update"), templateHandler.SaveTemplateDraft)
	}
}

func (r *Router) setupInstanceRoutes(api *gin.RouterGroup, instanceHandler *handlers.InstanceHandler, jwtService *services.JWTService, rbacService *services.RBACService, logger *logrus.Logger) {
	instances := api.Group("/instances")
	instances.Use(middleware.JWTAuth(jwtService, logger)) // 所有实例操作都需要JWT认证
	{
		// 实例查询 - 需要实例读取权限
		instances.GET("", middleware.PermissionRequired(rbacService, "instance", "read"), instanceHandler.GetInstances)
		instances.GET("/:id", middleware.PermissionRequired(rbacService, "instance", "read"), instanceHandler.GetInstance)
		
		// 实例管理 - 需要实例创建/更新/删除权限
		instances.POST("", middleware.PermissionRequired(rbacService, "instance", "create"), instanceHandler.CreateInstance)
		instances.PUT("/:id", middleware.PermissionRequired(rbacService, "instance", "update"), instanceHandler.UpdateInstance)
		instances.DELETE("/:id", middleware.PermissionRequired(rbacService, "instance", "delete"), instanceHandler.DeleteInstance)
		
		// 实例提交 - 需要实例提交权限
		instances.POST("/:id/submit", middleware.PermissionRequired(rbacService, "instance", "submit"), instanceHandler.SubmitInstance)
		
		// 实例锁定 - 需要实例锁定权限
		instances.POST("/:id/lock", middleware.PermissionRequired(rbacService, "instance", "lock"), instanceHandler.LockInstance)
		instances.POST("/:id/unlock", middleware.PermissionRequired(rbacService, "instance", "lock"), instanceHandler.UnlockInstance)
	}

	// 表单填写相关路由（需要认证）
	form := api.Group("/form")
	form.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 获取模板信息用于填写 - 需要模板读取权限
		form.GET("/template/:id", middleware.PermissionRequired(rbacService, "template", "read"), instanceHandler.GetTemplateForFill)
	}
}

func (r *Router) setupAutoSaveRoutes(api *gin.RouterGroup, autoSaveHandler interface{}) {
	// TODO: 实现自动保存功能时再启用这些路由
	/*
	autoSave := api.Group("/autosave")
	autoSave.Use(middleware.JWTAuth(jwtService, logger))
	{
		// TODO: 实现自动保存路由
	}
	*/

	if r.config.Environment.Name == "development" {
		_ = api.Group("/dev-autosave")
		// TODO: 实现开发环境自动保存路由
	}
}

func (r *Router) setupHistoryRoutes(api *gin.RouterGroup, historyHandler *handlers.HistoryHandler, jwtService *services.JWTService, logger *logrus.Logger) {
	history := api.Group("/history")
	{
		// 公共路由（用于同步历史记录）
		history.POST("", historyHandler.CreateHistoryEntry)

		// 需要认证的路由
		authenticated := history.Group("")
		authenticated.Use(middleware.JWTAuth(jwtService, logger))
		{
			authenticated.GET("/:resource_type/:resource_id", historyHandler.GetHistory)
			authenticated.GET("/user", historyHandler.GetUserHistory)
		}
	}
}

func (r *Router) setupDataManagementRoutes(api *gin.RouterGroup, dataManagementHandler *handlers.DataManagementHandler, jwtService *services.JWTService, rbacService *services.RBACService, logger *logrus.Logger) {
	data := api.Group("/data")
	data.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 数据查询 - 需要数据读取权限
		data.GET("/templates/:template_id/summary", middleware.PermissionRequired(rbacService, "data", "read"), dataManagementHandler.GetDataSummary)
		data.GET("/templates/:template_id/statistics", middleware.PermissionRequired(rbacService, "data", "read"), dataManagementHandler.GetDataStatistics)


		
		// 数据分析 - 需要数据分析权限
		data.GET("/templates/:template_id/analyze", middleware.PermissionRequired(rbacService, "data", "analyze"), dataManagementHandler.AnalyzeFields)
		
		// 数据导出 - 需要数据导出权限
		data.POST("/templates/:template_id/export", middleware.PermissionRequired(rbacService, "data", "export"), dataManagementHandler.ExportData)
		data.POST("/templates/:template_id/export/preview", middleware.PermissionRequired(rbacService, "data", "export"), dataManagementHandler.GetExportPreview)
	}
}

func (r *Router) setupRoleRoutes(api *gin.RouterGroup, rbacService *services.RBACService, jwtService *services.JWTService, logger *logrus.Logger) {
	roleHandler := handlers.NewRoleHandler(rbacService, logger)
	
	roles := api.Group("/roles")
	roles.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 角色查询 - 需要角色读取权限
		roles.GET("", middleware.PermissionRequired(rbacService, "role", "read"), roleHandler.GetRoles)
		roles.GET("/:id", middleware.PermissionRequired(rbacService, "role", "read"), roleHandler.GetRole)
		
		// 角色管理 - 需要角色管理权限
		roles.POST("", middleware.RoleManagementRequired(rbacService), roleHandler.CreateRole)
		roles.PUT("/:id", middleware.RoleManagementRequired(rbacService), roleHandler.UpdateRole)
		roles.DELETE("/:id", middleware.RoleManagementRequired(rbacService), roleHandler.DeleteRole)
		
		// 权限分配 - 需要权限分配权限
		roles.POST("/:id/permissions", middleware.RoleManagementRequired(rbacService), roleHandler.AssignPermissions)
	}
	
	// 权限管理
	permissions := api.Group("/permissions")
	permissions.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 权限查询 - 需要权限读取权限
		permissions.GET("", middleware.PermissionRequired(rbacService, "role", "read"), roleHandler.GetPermissions)
	}
	
	// 用户角色管理
	userRoles := api.Group("/user-roles")
	userRoles.Use(middleware.JWTAuth(jwtService, logger))
	{
		// 查询用户角色 - 需要用户读取权限
		userRoles.GET("/:user_id", middleware.PermissionRequired(rbacService, "user", "read"), roleHandler.GetUserRoles)
		
		// 分配角色 - 需要用户角色分配权限
		userRoles.POST("", middleware.PermissionRequired(rbacService, "user", "assign_role"), roleHandler.AssignRole)
		
		// 移除角色 - 需要用户角色分配权限
		userRoles.DELETE("/:user_id/roles/:role_id", middleware.PermissionRequired(rbacService, "user", "assign_role"), roleHandler.RemoveRole)
	}
	
	// 权限检查
	api.POST("/check-permission", middleware.JWTAuth(jwtService, logger), roleHandler.CheckPermission)
}

func (r *Router) setupErrorReportingRoutes(api *gin.RouterGroup) {
	api.POST("/errors", func(c *gin.Context) {
		var errorData map[string]interface{}
		if err := c.ShouldBindJSON(&errorData); err != nil {
			c.JSON(400, gin.H{"error": "Invalid error data"})
			return
		}
		c.JSON(200, gin.H{"status": "error logged"})
	})
}

func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}
