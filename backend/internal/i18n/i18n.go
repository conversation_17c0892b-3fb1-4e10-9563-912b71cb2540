package i18n

import (
	"fmt"
	"strings"
)

// MessageKey 消息键类型
type MessageKey string

// 预定义消息键
const (
	// 通用消息
	MsgOperationSuccess MessageKey = "operation.success"
	MsgOperationFailed  MessageKey = "operation.failed"
	MsgInternalError    MessageKey = "error.internal"
	MsgBadRequest       MessageKey = "error.bad_request"
	MsgUnauthorized     MessageKey = "error.unauthorized"
	MsgForbidden        MessageKey = "error.forbidden"
	MsgNotFound         MessageKey = "error.not_found"
	MsgConflict         MessageKey = "error.conflict"
	MsgValidationFailed MessageKey = "error.validation_failed"

	// 用户相关消息
	MsgUserNotFound         MessageKey = "user.not_found"
	MsgUserExists           MessageKey = "user.exists"
	MsgUserCreated          MessageKey = "user.created"
	MsgUserUpdated          MessageKey = "user.updated"
	MsgUserDeleted          MessageKey = "user.deleted"
	MsgInvalidCredentials   MessageKey = "user.invalid_credentials"
	MsgUserInactive         MessageKey = "user.inactive"
	MsgPasswordChanged      MessageKey = "user.password_changed"
	MsgLoginSuccess         MessageKey = "user.login_success"
	MsgLogoutSuccess        MessageKey = "user.logout_success"

	// 项目相关消息
	MsgProjectNotFound MessageKey = "project.not_found"
	MsgProjectExists   MessageKey = "project.exists"
	MsgProjectCreated  MessageKey = "project.created"
	MsgProjectUpdated  MessageKey = "project.updated"
	MsgProjectDeleted  MessageKey = "project.deleted"

	// 模板相关消息
	MsgTemplateNotFound   MessageKey = "template.not_found"
	MsgTemplateExists     MessageKey = "template.exists"
	MsgTemplateCreated    MessageKey = "template.created"
	MsgTemplateUpdated    MessageKey = "template.updated"
	MsgTemplateDeleted    MessageKey = "template.deleted"
	MsgTemplatePublished  MessageKey = "template.published"
	MsgTemplateDuplicated MessageKey = "template.duplicated"

	// 权限相关消息
	MsgPermissionDenied MessageKey = "permission.denied"
	MsgRoleNotFound     MessageKey = "role.not_found"
	MsgRoleAssigned     MessageKey = "role.assigned"
	MsgRoleRemoved      MessageKey = "role.removed"

	// 数据库相关消息
	MsgDBConnectionError MessageKey = "db.connection_error"
	MsgDBQueryError      MessageKey = "db.query_error"

	// 缓存相关消息
	MsgCacheConnectionError MessageKey = "cache.connection_error"
	MsgCacheOperationError  MessageKey = "cache.operation_error"

	// 文件上传相关消息
	MsgFileUploadSuccess MessageKey = "file.upload_success"
	MsgFileUploadFailed  MessageKey = "file.upload_failed"
	MsgFileDeleteSuccess MessageKey = "file.delete_success"
	MsgFileDeleteFailed  MessageKey = "file.delete_failed"
	MsgInvalidFileType   MessageKey = "file.invalid_type"
	MsgFileTooLarge      MessageKey = "file.too_large"
)

// Language 支持的语言
type Language string

const (
	LangZhCN Language = "zh-CN"
	LangEnUS Language = "en-US"
)

// messages 消息映射表
var messages = map[Language]map[MessageKey]string{
	LangZhCN: {
		// 通用消息
		MsgOperationSuccess: "操作成功",
		MsgOperationFailed:  "操作失败",
		MsgInternalError:    "内部服务器错误",
		MsgBadRequest:       "请求参数错误",
		MsgUnauthorized:     "未授权访问",
		MsgForbidden:        "权限不足",
		MsgNotFound:         "资源不存在",
		MsgConflict:         "资源冲突",
		MsgValidationFailed: "参数验证失败",

		// 用户相关消息
		MsgUserNotFound:         "用户不存在",
		MsgUserExists:           "用户已存在",
		MsgUserCreated:          "用户创建成功",
		MsgUserUpdated:          "用户信息更新成功",
		MsgUserDeleted:          "用户删除成功",
		MsgInvalidCredentials:   "用户名或密码错误",
		MsgUserInactive:         "用户账户已被禁用",
		MsgPasswordChanged:      "密码修改成功",
		MsgLoginSuccess:         "登录成功",
		MsgLogoutSuccess:        "退出登录成功",

		// 项目相关消息
		MsgProjectNotFound: "项目不存在",
		MsgProjectExists:   "项目已存在",
		MsgProjectCreated:  "项目创建成功",
		MsgProjectUpdated:  "项目更新成功",
		MsgProjectDeleted:  "项目删除成功",

		// 模板相关消息
		MsgTemplateNotFound:   "模板不存在",
		MsgTemplateExists:     "模板已存在",
		MsgTemplateCreated:    "模板创建成功",
		MsgTemplateUpdated:    "模板更新成功",
		MsgTemplateDeleted:    "模板删除成功",
		MsgTemplatePublished:  "模板发布成功",
		MsgTemplateDuplicated: "模板复制成功",

		// 权限相关消息
		MsgPermissionDenied: "权限不足",
		MsgRoleNotFound:     "角色不存在",
		MsgRoleAssigned:     "角色分配成功",
		MsgRoleRemoved:      "角色移除成功",

		// 数据库相关消息
		MsgDBConnectionError: "数据库连接失败",
		MsgDBQueryError:      "数据库查询失败",

		// 缓存相关消息
		MsgCacheConnectionError: "缓存连接失败",
		MsgCacheOperationError:  "缓存操作失败",

		// 文件上传相关消息
		MsgFileUploadSuccess: "文件上传成功",
		MsgFileUploadFailed:  "文件上传失败",
		MsgFileDeleteSuccess: "文件删除成功",
		MsgFileDeleteFailed:  "文件删除失败",
		MsgInvalidFileType:   "不支持的文件类型",
		MsgFileTooLarge:      "文件过大",
	},
	LangEnUS: {
		// Common messages
		MsgOperationSuccess: "Operation successful",
		MsgOperationFailed:  "Operation failed",
		MsgInternalError:    "Internal server error",
		MsgBadRequest:       "Bad request parameters",
		MsgUnauthorized:     "Unauthorized access",
		MsgForbidden:        "Insufficient permissions",
		MsgNotFound:         "Resource not found",
		MsgConflict:         "Resource conflict",
		MsgValidationFailed: "Parameter validation failed",

		// User related messages
		MsgUserNotFound:         "User not found",
		MsgUserExists:           "User already exists",
		MsgUserCreated:          "User created successfully",
		MsgUserUpdated:          "User information updated successfully",
		MsgUserDeleted:          "User deleted successfully",
		MsgInvalidCredentials:   "Invalid username or password",
		MsgUserInactive:         "User account is disabled",
		MsgPasswordChanged:      "Password changed successfully",
		MsgLoginSuccess:         "Login successful",
		MsgLogoutSuccess:        "Logout successful",

		// Project related messages
		MsgProjectNotFound: "Project not found",
		MsgProjectExists:   "Project already exists",
		MsgProjectCreated:  "Project created successfully",
		MsgProjectUpdated:  "Project updated successfully",
		MsgProjectDeleted:  "Project deleted successfully",

		// Template related messages
		MsgTemplateNotFound:   "Template not found",
		MsgTemplateExists:     "Template already exists",
		MsgTemplateCreated:    "Template created successfully",
		MsgTemplateUpdated:    "Template updated successfully",
		MsgTemplateDeleted:    "Template deleted successfully",
		MsgTemplatePublished:  "Template published successfully",
		MsgTemplateDuplicated: "Template duplicated successfully",

		// Permission related messages
		MsgPermissionDenied: "Permission denied",
		MsgRoleNotFound:     "Role not found",
		MsgRoleAssigned:     "Role assigned successfully",
		MsgRoleRemoved:      "Role removed successfully",

		// Database related messages
		MsgDBConnectionError: "Database connection failed",
		MsgDBQueryError:      "Database query failed",

		// Cache related messages
		MsgCacheConnectionError: "Cache connection failed",
		MsgCacheOperationError:  "Cache operation failed",

		// File upload related messages
		MsgFileUploadSuccess: "File uploaded successfully",
		MsgFileUploadFailed:  "File upload failed",
		MsgFileDeleteSuccess: "File deleted successfully",
		MsgFileDeleteFailed:  "File deletion failed",
		MsgInvalidFileType:   "Unsupported file type",
		MsgFileTooLarge:      "File too large",
	},
}

// I18n 国际化实例
type I18n struct {
	defaultLang Language
}

// New 创建国际化实例
func New(defaultLang Language) *I18n {
	return &I18n{
		defaultLang: defaultLang,
	}
}

// T 翻译消息
func (i *I18n) T(lang Language, key MessageKey, args ...interface{}) string {
	// 获取语言消息映射
	langMessages, exists := messages[lang]
	if !exists {
		// 如果语言不存在，使用默认语言
		langMessages = messages[i.defaultLang]
	}

	// 获取消息模板
	template, exists := langMessages[key]
	if !exists {
		// 如果消息不存在，返回键名
		return string(key)
	}

	// 如果有参数，进行格式化
	if len(args) > 0 {
		return fmt.Sprintf(template, args...)
	}

	return template
}

// GetLanguageFromAcceptLanguage 从Accept-Language头解析语言
func GetLanguageFromAcceptLanguage(acceptLang string) Language {
	if acceptLang == "" {
		return LangZhCN // 默认中文
	}

	// 简单解析Accept-Language头
	langs := strings.Split(acceptLang, ",")
	for _, lang := range langs {
		lang = strings.TrimSpace(lang)
		lang = strings.Split(lang, ";")[0] // 移除权重

		switch {
		case strings.HasPrefix(lang, "zh"):
			return LangZhCN
		case strings.HasPrefix(lang, "en"):
			return LangEnUS
		}
	}

	return LangZhCN // 默认中文
}

// DefaultI18n 默认国际化实例
var DefaultI18n = New(LangZhCN)

// T 全局翻译函数
func T(lang Language, key MessageKey, args ...interface{}) string {
	return DefaultI18n.T(lang, key, args...)
}