package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误码类型
type ErrorCode string

// 预定义错误码
const (
	// 通用错误
	ErrCodeInternal    ErrorCode = "INTERNAL_ERROR"
	ErrCodeBadRequest  ErrorCode = "BAD_REQUEST"
	ErrCodeUnauthorized ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden   ErrorCode = "FORBIDDEN"
	ErrCodeNotFound    ErrorCode = "NOT_FOUND"
	ErrCodeConflict    ErrorCode = "CONFLICT"
	ErrCodeValidation  ErrorCode = "VALIDATION_ERROR"

	// 用户相关错误
	ErrCodeUserNotFound      ErrorCode = "USER_NOT_FOUND"
	ErrCodeUserExists        ErrorCode = "USER_EXISTS"
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeUserInactive      ErrorCode = "USER_INACTIVE"

	// 项目相关错误
	ErrCodeProjectNotFound ErrorCode = "PROJECT_NOT_FOUND"
	ErrCodeProjectExists   ErrorCode = "PROJECT_EXISTS"

	// 模板相关错误
	ErrCodeTemplateNotFound ErrorCode = "TEMPLATE_NOT_FOUND"
	ErrCodeTemplateExists   ErrorCode = "TEMPLATE_EXISTS"

	// 权限相关错误
	ErrCodePermissionDenied ErrorCode = "PERMISSION_DENIED"
	ErrCodeRoleNotFound     ErrorCode = "ROLE_NOT_FOUND"

	// 数据库相关错误
	ErrCodeDBConnection ErrorCode = "DB_CONNECTION_ERROR"
	ErrCodeDBQuery      ErrorCode = "DB_QUERY_ERROR"

	// 缓存相关错误
	ErrCodeCacheConnection ErrorCode = "CACHE_CONNECTION_ERROR"
	ErrCodeCacheOperation  ErrorCode = "CACHE_OPERATION_ERROR"
)

// APIError 统一的API错误结构
type APIError struct {
	Code       ErrorCode   `json:"code"`
	Message    string      `json:"message"`
	Details    interface{} `json:"details,omitempty"`
	StatusCode int         `json:"-"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// WithDetails 添加错误详情
func (e *APIError) WithDetails(details interface{}) *APIError {
	e.Details = details
	return e
}

// 预定义的错误构造函数

// NewInternalError 内部错误
func NewInternalError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeInternal,
		Message:    message,
		StatusCode: http.StatusInternalServerError,
	}
}

// NewBadRequestError 请求参数错误
func NewBadRequestError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeBadRequest,
		Message:    message,
		StatusCode: http.StatusBadRequest,
	}
}

// NewUnauthorizedError 未授权错误
func NewUnauthorizedError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeUnauthorized,
		Message:    message,
		StatusCode: http.StatusUnauthorized,
	}
}

// NewForbiddenError 禁止访问错误
func NewForbiddenError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeForbidden,
		Message:    message,
		StatusCode: http.StatusForbidden,
	}
}

// NewNotFoundError 资源未找到错误
func NewNotFoundError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeNotFound,
		Message:    message,
		StatusCode: http.StatusNotFound,
	}
}

// NewConflictError 冲突错误
func NewConflictError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeConflict,
		Message:    message,
		StatusCode: http.StatusConflict,
	}
}

// NewValidationError 验证错误
func NewValidationError(message string) *APIError {
	return &APIError{
		Code:       ErrCodeValidation,
		Message:    message,
		StatusCode: http.StatusBadRequest,
	}
}

// 业务特定错误构造函数

// NewUserNotFoundError 用户未找到错误
func NewUserNotFoundError() *APIError {
	return &APIError{
		Code:       ErrCodeUserNotFound,
		Message:    "用户不存在",
		StatusCode: http.StatusNotFound,
	}
}

// NewUserExistsError 用户已存在错误
func NewUserExistsError() *APIError {
	return &APIError{
		Code:       ErrCodeUserExists,
		Message:    "用户已存在",
		StatusCode: http.StatusConflict,
	}
}

// NewInvalidCredentialsError 凭据无效错误
func NewInvalidCredentialsError() *APIError {
	return &APIError{
		Code:       ErrCodeInvalidCredentials,
		Message:    "用户名或密码错误",
		StatusCode: http.StatusUnauthorized,
	}
}

// NewProjectNotFoundError 项目未找到错误
func NewProjectNotFoundError() *APIError {
	return &APIError{
		Code:       ErrCodeProjectNotFound,
		Message:    "项目不存在",
		StatusCode: http.StatusNotFound,
	}
}

// NewTemplateNotFoundError 模板未找到错误
func NewTemplateNotFoundError() *APIError {
	return &APIError{
		Code:       ErrCodeTemplateNotFound,
		Message:    "模板不存在",
		StatusCode: http.StatusNotFound,
	}
}

// NewPermissionDeniedError 权限不足错误
func NewPermissionDeniedError() *APIError {
	return &APIError{
		Code:       ErrCodePermissionDenied,
		Message:    "权限不足",
		StatusCode: http.StatusForbidden,
	}
}

// IsAPIError 检查是否为APIError
func IsAPIError(err error) (*APIError, bool) {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr, true
	}
	return nil, false
}

// WrapError 包装标准错误为APIError
func WrapError(err error, code ErrorCode, message string) *APIError {
	statusCode := http.StatusInternalServerError
	switch code {
	case ErrCodeBadRequest, ErrCodeValidation:
		statusCode = http.StatusBadRequest
	case ErrCodeUnauthorized:
		statusCode = http.StatusUnauthorized
	case ErrCodeForbidden:
		statusCode = http.StatusForbidden
	case ErrCodeNotFound:
		statusCode = http.StatusNotFound
	case ErrCodeConflict:
		statusCode = http.StatusConflict
	}

	return &APIError{
		Code:       code,
		Message:    message,
		Details:    err.Error(),
		StatusCode: statusCode,
	}
}