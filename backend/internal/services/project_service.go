package services

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"crf-backend/internal/models"
)

type ProjectService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewProjectService(db *gorm.DB, logger *logrus.Logger) *ProjectService {
	return &ProjectService{
		db:     db,
		logger: logger,
	}
}

// CreateProject 创建项目
func (s *ProjectService) CreateProject(project *models.Project) error {
	if err := s.db.Create(project).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create project")
		return fmt.Errorf("failed to create project: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"project_id":   project.ID,
		"project_name": project.Name,
		"created_by":   project.CreatedBy,
	}).Info("Project created successfully")

	return nil
}

// GetProject 获取项目详情
func (s *ProjectService) GetProject(id uuid.UUID) (*models.Project, error) {
	var project models.Project
	if err := s.db.Preload("Creator").Preload("Templates").First(&project, "id = ? AND is_deleted = false", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("project not found")
		}
		s.logger.WithError(err).Error("Failed to get project")
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	return &project, nil
}

// GetProjectStatistics 获取项目统计信息
func (s *ProjectService) GetProjectStatistics(id uuid.UUID) (*models.ProjectStatistics, error) {
	var stats models.ProjectStatistics
	
	query := `
		SELECT * FROM project_statistics 
		WHERE id = $1
	`
	
	if err := s.db.Raw(query, id).Scan(&stats).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("project not found")
		}
		s.logger.WithError(err).Error("Failed to get project statistics")
		return nil, fmt.Errorf("failed to get project statistics: %w", err)
	}

	return &stats, nil
}

// GetProjects 获取项目列表
func (s *ProjectService) GetProjects(userID uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.Project, int64, error) {
	var projects []models.Project
	var total int64

	query := s.db.Model(&models.Project{}).Where("is_deleted = false")

	// 权限过滤：只能看到自己创建的项目或有权限的项目
	query = query.Where("created_by = ?", userID)

	// 应用过滤器
	if status, ok := filters["status"]; ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if projectType, ok := filters["project_type"]; ok && projectType != "" {
		query = query.Where("project_type = ?", projectType)
	}
	if search, ok := filters["search"]; ok && search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", 
			fmt.Sprintf("%%%s%%", search), fmt.Sprintf("%%%s%%", search))
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count projects")
		return nil, 0, fmt.Errorf("failed to count projects: %w", err)
	}

	// 获取项目列表
	if err := query.Preload("Creator").
		Order("updated_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&projects).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get projects")
		return nil, 0, fmt.Errorf("failed to get projects: %w", err)
	}

	return projects, total, nil
}

// GetProjectsWithStatistics 获取带统计信息的项目列表
func (s *ProjectService) GetProjectsWithStatistics(userID uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.ProjectStatistics, int64, error) {
	var projects []models.ProjectStatistics
	var total int64

	// 构建基础查询
	baseQuery := `
		FROM project_statistics 
		WHERE id IN (
			SELECT id FROM projects 
			WHERE is_deleted = false AND created_by = $1
		)
	`
	args := []interface{}{userID}
	argIndex := 2

	// 应用过滤器
	conditions := []string{}
	if status, ok := filters["status"]; ok && status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}
	if projectType, ok := filters["project_type"]; ok && projectType != "" {
		conditions = append(conditions, fmt.Sprintf("project_type = $%d", argIndex))
		args = append(args, projectType)
		argIndex++
	}
	if search, ok := filters["search"]; ok && search != "" {
		conditions = append(conditions, fmt.Sprintf("(name ILIKE $%d OR description ILIKE $%d)", argIndex, argIndex+1))
		searchPattern := fmt.Sprintf("%%%s%%", search)
		args = append(args, searchPattern, searchPattern)
		argIndex += 2
	}

	if len(conditions) > 0 {
		baseQuery += " AND " + fmt.Sprintf("(%s)", fmt.Sprintf("%s", conditions[0]))
		for i := 1; i < len(conditions); i++ {
			baseQuery += " AND " + conditions[i]
		}
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) " + baseQuery
	if err := s.db.Raw(countQuery, args...).Scan(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count projects with statistics")
		return nil, 0, fmt.Errorf("failed to count projects: %w", err)
	}

	// 获取项目列表
	selectQuery := "SELECT * " + baseQuery + fmt.Sprintf(" ORDER BY updated_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)
	
	if err := s.db.Raw(selectQuery, args...).Scan(&projects).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get projects with statistics")
		return nil, 0, fmt.Errorf("failed to get projects: %w", err)
	}

	return projects, total, nil
}

// UpdateProject 更新项目
func (s *ProjectService) UpdateProject(id uuid.UUID, updates map[string]interface{}) error {
	updates["updated_at"] = time.Now()
	
	if err := s.db.Model(&models.Project{}).Where("id = ? AND is_deleted = false", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).Error("Failed to update project")
		return fmt.Errorf("failed to update project: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"project_id": id,
		"updates":    updates,
	}).Info("Project updated successfully")

	return nil
}

// DeleteProject 软删除项目
func (s *ProjectService) DeleteProject(id uuid.UUID) error {
	now := time.Now()
	updates := map[string]interface{}{
		"is_deleted": true,
		"deleted_at": &now,
		"updated_at": now,
	}

	if err := s.db.Model(&models.Project{}).Where("id = ? AND is_deleted = false", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).Error("Failed to delete project")
		return fmt.Errorf("failed to delete project: %w", err)
	}

	s.logger.WithField("project_id", id).Info("Project deleted successfully")
	return nil
}

// BatchDeleteProjects 批量软删除项目
func (s *ProjectService) BatchDeleteProjects(ids []uuid.UUID) error {
	if len(ids) == 0 {
		return fmt.Errorf("no project IDs provided")
	}

	now := time.Now()
	updates := map[string]interface{}{
		"is_deleted": true,
		"deleted_at": &now,
		"updated_at": now,
	}

	// 批量更新项目状态为已删除
	result := s.db.Model(&models.Project{}).Where("id IN ? AND is_deleted = false", ids).Updates(updates)
	if result.Error != nil {
		s.logger.WithError(result.Error).Error("Failed to batch delete projects")
		return fmt.Errorf("failed to batch delete projects: %w", result.Error)
	}

	s.logger.WithFields(logrus.Fields{
		"project_ids":    ids,
		"affected_rows":  result.RowsAffected,
	}).Info("Projects batch deleted successfully")

	return nil
}

// GetProjectTemplates 获取项目下的模板列表
func (s *ProjectService) GetProjectTemplates(projectID uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.CRFTemplate, int64, error) {
	var templates []models.CRFTemplate
	var total int64

	query := s.db.Model(&models.CRFTemplate{}).Where("project_id = ?", projectID)

	// 应用过滤器
	if status, ok := filters["status"]; ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if search, ok := filters["search"]; ok && search != "" {
		query = query.Where("name ILIKE ? OR title ILIKE ?", 
			fmt.Sprintf("%%%s%%", search), fmt.Sprintf("%%%s%%", search))
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count project templates")
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}

	// 获取模板列表
	if err := query.Preload("Creator").
		Order("updated_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&templates).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get project templates")
		return nil, 0, fmt.Errorf("failed to get templates: %w", err)
	}

	return templates, total, nil
}

// RefreshProjectStats 刷新项目统计信息
func (s *ProjectService) RefreshProjectStats(projectID uuid.UUID) error {
	// 手动触发统计更新
	if err := s.db.Exec("SELECT update_project_stats() WHERE EXISTS (SELECT 1 FROM projects WHERE id = ?)", projectID).Error; err != nil {
		s.logger.WithError(err).Error("Failed to refresh project stats")
		return fmt.Errorf("failed to refresh project stats: %w", err)
	}

	s.logger.WithField("project_id", projectID).Info("Project stats refreshed successfully")
	return nil
}

// IsProjectNameAvailable 检查项目名称是否可用
func (s *ProjectService) IsProjectNameAvailable(name string, userID uuid.UUID) (bool, error) {
	var count int64
	
	// 检查同一用户是否已有相同名称的项目（未删除的）
	if err := s.db.Model(&models.Project{}).
		Where("name = ? AND created_by = ? AND is_deleted = false", name, userID).
		Count(&count).Error; err != nil {
		s.logger.WithError(err).Error("Failed to check project name availability")
		return false, fmt.Errorf("failed to check project name availability: %w", err)
	}

	return count == 0, nil
}

// GetOrCreateDefaultProject 获取或创建默认项目
func (s *ProjectService) GetOrCreateDefaultProject(userID uuid.UUID) (*models.Project, error) {
	// 首先尝试获取用户的第一个项目
	projects, _, err := s.GetProjects(userID, map[string]interface{}{}, 1, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get user projects: %w", err)
	}

	if len(projects) > 0 {
		return &projects[0], nil
	}

	// 如果没有项目，创建默认项目
	defaultProject := &models.Project{
		Name:        "默认项目",
		Description: "系统自动创建的默认项目",
		CreatedBy:   userID,
		Status:      "active",
		ProjectType: "clinical_trial",
	}

	if err := s.CreateProject(defaultProject); err != nil {
		return nil, fmt.Errorf("failed to create default project: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"project_id": defaultProject.ID,
	}).Info("Default project created for user")

	return defaultProject, nil
}
