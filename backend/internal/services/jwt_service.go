package services

import (
	"context"
	"fmt"
	"time"

	"crf-backend/internal/config"
	"crf-backend/internal/models"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type JWTService struct {
	secret              []byte
	accessTokenTTL      time.Duration
	refreshTokenTTL     time.Duration
	refreshThreshold    time.Duration
	longTermTokenTTL    time.Duration
	rememberMeThreshold time.Duration
	logger              *logrus.Logger
	cacheService        *CacheService
}

type Claims struct {
	UserID     string `json:"user_id"`
	Username   string `json:"username"`
	Role       string `json:"role"`
	TokenType  string `json:"token_type"`  // "access", "refresh", "long_term"
	RememberMe bool   `json:"remember_me"` // 是否为记住我token
	jwt.RegisteredClaims
}

func NewJWTService(cfg *config.Config, logger *logrus.Logger, cacheService *CacheService) *JWTService {
	accessTokenTTL, _ := time.ParseDuration(cfg.Security.JWT.AccessTokenTTL)
	refreshTokenTTL, _ := time.ParseDuration(cfg.Security.JWT.RefreshTokenTTL)
	refreshThreshold, _ := time.ParseDuration(cfg.Security.JWT.RefreshThreshold)
	longTermTokenTTL, _ := time.ParseDuration(cfg.Security.JWT.LongTermTokenTTL)
	rememberMeThreshold, _ := time.ParseDuration(cfg.Security.JWT.RememberMeThreshold)

	return &JWTService{
		secret:              []byte(cfg.Security.JWT.Secret),
		accessTokenTTL:      accessTokenTTL,
		refreshTokenTTL:     refreshTokenTTL,
		refreshThreshold:    refreshThreshold,
		longTermTokenTTL:    longTermTokenTTL,
		rememberMeThreshold: rememberMeThreshold,
		logger:              logger,
		cacheService:        cacheService,
	}
}

// GenerateTokenPair 生成访问令牌和刷新令牌对
func (j *JWTService) GenerateTokenPair(user *models.User) (accessToken, refreshToken string, err error) {
	return j.GenerateTokenPairWithRememberMe(user, false)
}

// GenerateTokenPairWithRememberMe 生成token对，支持记住我选项
func (j *JWTService) GenerateTokenPairWithRememberMe(user *models.User, rememberMe bool) (accessToken, refreshToken string, err error) {
	// 生成访问令牌
	accessToken, err = j.generateToken(user, "access", j.accessTokenTTL, rememberMe)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate access token: %w", err)
	}

	// 根据rememberMe选择刷新令牌的有效期
	refreshTTL := j.refreshTokenTTL
	if rememberMe {
		refreshTTL = j.longTermTokenTTL
	}

	// 生成刷新令牌
	refreshToken, err = j.generateToken(user, "refresh", refreshTTL, rememberMe)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate refresh token: %w", err)
	}

	j.logger.WithFields(logrus.Fields{
		"user_id":     user.ID,
		"username":    user.Username,
		"role":        "user", // 默认角色，实际通过RBAC获取
		"remember_me": rememberMe,
	}).Info("JWT token pair generated successfully")

	return accessToken, refreshToken, nil
}



// generateToken 内部方法生成指定类型的token
func (j *JWTService) generateToken(user *models.User, tokenType string, ttl time.Duration, rememberMe bool) (string, error) {
	expirationTime := time.Now().Add(ttl)
	jti := uuid.New().String()

	claims := &Claims{
		UserID:     user.ID.String(),
		Username:   user.Username,
		Role:       "user", // 默认角色，实际应通过RBAC系统获取
		TokenType:  tokenType,
		RememberMe: rememberMe,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "crf-backend",
			Subject:   user.ID.String(),
			ID:        jti,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(j.secret)
	if err != nil {
		j.logger.WithError(err).Error("Failed to sign JWT token")
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// ValidateToken 验证JWT Token
func (j *JWTService) ValidateToken(ctx context.Context, tokenString string) (*Claims, error) {
	// 检查token是否在黑名单中
	if j.isTokenBlacklisted(ctx, tokenString) {
		return nil, fmt.Errorf("token is blacklisted")
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secret, nil
	})

	if err != nil {
		j.logger.WithError(err).Warn("Failed to parse JWT token")
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		j.logger.WithFields(logrus.Fields{
			"user_id":    claims.UserID,
			"username":   claims.Username,
			"role":       claims.Role,
			"token_type": claims.TokenType,
		}).Debug("JWT token validated successfully")
		return claims, nil
	}

	j.logger.Warn("Invalid JWT token claims")
	return nil, fmt.Errorf("invalid token claims")
}

// RefreshToken 刷新Token
func (j *JWTService) RefreshToken(ctx context.Context, refreshTokenString string) (string, string, error) {
	// 验证刷新令牌
	claims, err := j.ValidateToken(ctx, refreshTokenString)
	if err != nil {
		return "", "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// 确保是刷新令牌
	if claims.TokenType != "refresh" {
		return "", "", fmt.Errorf("invalid token type for refresh")
	}

	// 获取用户信息
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return "", "", fmt.Errorf("invalid user ID in token: %w", err)
	}

	// 这里应该从数据库获取用户信息，为了简化示例，我们构造一个user对象
	user := &models.User{
		ID:       userID,
		Username: claims.Username,
		// Role字段已移除，使用默认值
	}

	// 生成新的token对，保持原有的rememberMe状态
	newAccessToken, newRefreshToken, err := j.GenerateTokenPairWithRememberMe(user, claims.RememberMe)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate new tokens: %w", err)
	}

	// 将旧的刷新令牌加入黑名单
	j.BlacklistToken(ctx, refreshTokenString)

	j.logger.WithFields(logrus.Fields{
		"user_id":     claims.UserID,
		"remember_me": claims.RememberMe,
	}).Info("JWT tokens refreshed successfully")

	return newAccessToken, newRefreshToken, nil
}

// BlacklistToken 将token加入黑名单
func (j *JWTService) BlacklistToken(ctx context.Context, tokenString string) error {
	return j.blacklistToken(ctx, tokenString)
}

// blacklistToken 内部方法，将token加入黑名单
func (j *JWTService) blacklistToken(ctx context.Context, tokenString string) error {
	// 解析token获取过期时间
	claims, err := j.ValidateToken(ctx, tokenString)
	if err != nil {
		// 如果token已经无效，不需要加入黑名单
		return nil
	}

	// 计算token剩余有效时间
	ttl := time.Until(claims.ExpiresAt.Time)
	if ttl <= 0 {
		return nil // token已过期
	}

	// 将token加入Redis黑名单
	blacklistKey := fmt.Sprintf("jwt:blacklist:%s", claims.ID)
	err = j.cacheService.Set(ctx, blacklistKey, true, ttl)
	if err != nil {
		j.logger.WithError(err).Error("Failed to blacklist token")
		return fmt.Errorf("failed to blacklist token: %w", err)
	}

	j.logger.WithField("jti", claims.ID).Info("Token blacklisted successfully")
	return nil
}

// isTokenBlacklisted 检查token是否在黑名单中
func (j *JWTService) isTokenBlacklisted(ctx context.Context, tokenString string) bool {
	// 首先解析token获取JTI
	token, _ := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return j.secret, nil
	})

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return false
	}

	blacklistKey := fmt.Sprintf("jwt:blacklist:%s", claims.ID)
	var blacklisted bool
	err := j.cacheService.Get(ctx, blacklistKey, &blacklisted)

	return err == nil && blacklisted
}

// ExtractUserID 从Token中提取用户ID
func (j *JWTService) ExtractUserID(ctx context.Context, tokenString string) (uuid.UUID, error) {
	claims, err := j.ValidateToken(ctx, tokenString)
	if err != nil {
		return uuid.Nil, err
	}

	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid user ID in token: %w", err)
	}

	return userID, nil
}

// ShouldRefreshToken 检查是否应该刷新token
func (j *JWTService) ShouldRefreshToken(ctx context.Context, tokenString string) bool {
	claims, err := j.ValidateToken(ctx, tokenString)
	if err != nil {
		return false
	}

	// 根据是否为记住我token选择不同的刷新阈值
	threshold := j.refreshThreshold
	if claims.RememberMe {
		threshold = j.rememberMeThreshold
	}

	// 如果token剩余时间少于阈值，建议刷新
	return time.Until(claims.ExpiresAt.Time) < threshold
}

// IsRememberMeToken 检查是否为记住我token
func (j *JWTService) IsRememberMeToken(ctx context.Context, tokenString string) bool {
	claims, err := j.ValidateToken(ctx, tokenString)
	if err != nil {
		return false
	}
	return claims.RememberMe
}
