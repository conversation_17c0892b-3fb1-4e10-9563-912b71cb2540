package services

import (
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"crf-backend/internal/models"
)

type HistoryService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewHistoryService(db *gorm.DB, logger *logrus.Logger) *HistoryService {
	return &HistoryService{
		db:     db,
		logger: logger,
	}
}

// GetTemplateHistory 获取模板历史记录 (占位符实现)
func (s *HistoryService) GetTemplateHistory(templateID uuid.UUID, limit, offset int) ([]interface{}, int64, error) {
	// TODO: 实现模板历史记录功能
	return []interface{}{}, 0, nil
}

// CreateTemplateHistory 创建模板历史记录 (占位符实现)
func (s *HistoryService) CreateTemplateHistory(templateID uuid.UUID, action string, data interface{}) error {
	// TODO: 实现模板历史记录功能
	s.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"action":      action,
	}).Info("Template history action logged")
	return nil
}

// GetInstanceHistory 获取实例历史记录 (占位符实现)
func (s *HistoryService) GetInstanceHistory(instanceID uuid.UUID, limit, offset int) ([]interface{}, int64, error) {
	// TODO: 实现实例历史记录功能
	return []interface{}{}, 0, nil
}

// CreateInstanceHistory 创建实例历史记录 (占位符实现)
func (s *HistoryService) CreateInstanceHistory(instanceID uuid.UUID, action string, data interface{}) error {
	// TODO: 实现实例历史记录功能
	s.logger.WithFields(logrus.Fields{
		"instance_id": instanceID,
		"action":      action,
	}).Info("Instance history action logged")
	return nil
}

// CreateHistoryEntry 创建历史记录条目
func (s *HistoryService) CreateHistoryEntry(entry *models.OperationHistory) error {
	if err := s.db.Create(entry).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create history entry")
		return fmt.Errorf("failed to create history entry: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"resource_type": entry.ResourceType,
		"resource_id":   entry.ResourceID,
		"action":        entry.Action,
		"user_id":       entry.UserID,
	}).Info("History entry created")
	return nil
}

// GetHistory 获取历史记录
func (s *HistoryService) GetHistory(resourceType string, resourceID uuid.UUID, limit, offset int) ([]models.OperationHistory, int64, error) {
	var histories []models.OperationHistory
	var total int64

	// 获取总数
	query := s.db.Model(&models.OperationHistory{})
	if resourceType != "" {
		query = query.Where("resource_type = ?", resourceType)
	}
	if resourceID != uuid.Nil {
		query = query.Where("resource_id = ?", resourceID)
	}

	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count history entries")
		return nil, 0, fmt.Errorf("failed to count history entries: %w", err)
	}

	// 获取历史记录
	query = s.db.Preload("User")
	if resourceType != "" {
		query = query.Where("resource_type = ?", resourceType)
	}
	if resourceID != uuid.Nil {
		query = query.Where("resource_id = ?", resourceID)
	}

	if err := query.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&histories).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get history entries")
		return nil, 0, fmt.Errorf("failed to get history entries: %w", err)
	}

	return histories, total, nil
}

// GetUserHistory 获取用户历史记录
func (s *HistoryService) GetUserHistory(userID uuid.UUID, limit, offset int) ([]models.OperationHistory, int64, error) {
	var histories []models.OperationHistory
	var total int64

	// 获取总数
	if err := s.db.Model(&models.OperationHistory{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count user history entries")
		return nil, 0, fmt.Errorf("failed to count user history entries: %w", err)
	}

	// 获取历史记录
	if err := s.db.Preload("User").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&histories).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get user history entries")
		return nil, 0, fmt.Errorf("failed to get user history entries: %w", err)
	}

	return histories, total, nil
}
