package interfaces

import (
	"context"
	"crf-backend/internal/models"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// Claims JWT声明结构
type Claims struct {
	UserID     string `json:"user_id"`
	Username   string `json:"username"`
	Role       string `json:"role"`
	TokenType  string `json:"token_type"`
	RememberMe bool   `json:"remember_me"`
	jwt.RegisteredClaims
}

// UserServiceInterface 用户服务接口
type UserServiceInterface interface {
	// 用户管理
	CreateUser(user *models.User) error
	GetUserByID(id uuid.UUID) (*models.User, error)
	GetUserByUsername(username string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	GetUsers(limit, offset int) ([]models.User, int64, error)
	UpdateUser(id uuid.UUID, updates map[string]interface{}) error
	DeleteUser(id uuid.UUID) error
	
	// 认证相关
	ValidatePassword(user *models.User, password string) error
	UpdatePassword(userID uuid.UUID, newPassword string) error
	
	// 用户状态管理
	ActivateUser(id uuid.UUID) error
	DeactivateUser(id uuid.UUID) error
}

// ProjectServiceInterface 项目服务接口
type ProjectServiceInterface interface {
	CreateProject(project *models.Project) error
	GetProject(id uuid.UUID) (*models.Project, error)
	GetProjects(userID uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.Project, int64, error)
	GetProjectsWithStatistics(userID uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.Project, int64, error)
	UpdateProject(id uuid.UUID, updates map[string]interface{}) error
	DeleteProject(id uuid.UUID) error
	
	// 项目统计
	GetProjectStatistics(id uuid.UUID) (map[string]interface{}, error)
	RefreshProjectStats(id uuid.UUID) error
	
	// 项目模板
	GetProjectTemplates(projectID uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.CRFTemplate, int64, error)
}

// TemplateServiceInterface 模板服务接口
type TemplateServiceInterface interface {
	CreateTemplate(template *models.CRFTemplate) error
	GetTemplate(id uuid.UUID) (*models.CRFTemplate, error)
	GetTemplates(userID *uuid.UUID, filters map[string]interface{}, limit, offset int) ([]models.CRFTemplate, int64, error)
	UpdateTemplate(id uuid.UUID, updates map[string]interface{}) error
	DeleteTemplate(id uuid.UUID) error
	
	// 模板版本管理
	PublishTemplate(id uuid.UUID) (*models.CRFVersion, error)
	GetTemplateVersions(templateID uuid.UUID) ([]models.CRFVersion, error)
	
	// 模板复制
	DuplicateTemplate(id uuid.UUID, newName string, userID uuid.UUID) (*models.CRFTemplate, error)
}

// InstanceServiceInterface 实例服务接口
type InstanceServiceInterface interface {
	CreateInstance(instance *models.CRFInstance) error
	GetInstance(id uuid.UUID) (*models.CRFInstance, error)
	GetInstances(filters map[string]interface{}, limit, offset int) ([]models.CRFInstance, int64, error)
	UpdateInstance(id uuid.UUID, updates map[string]interface{}) error
	DeleteInstance(id uuid.UUID) error
	
	// 实例状态管理
	SubmitInstance(id uuid.UUID) error
	ReviewInstance(id uuid.UUID, reviewerID uuid.UUID, approved bool, comments string) error
}

// AutoSaveServiceInterface 自动保存服务接口
type AutoSaveServiceInterface interface {
	SaveAutoSave(autoSave *models.AutoSave) error
	GetAutoSave(templateID uuid.UUID, userID uuid.UUID) (*models.AutoSave, error)
	DeleteAutoSave(id uuid.UUID) error
	CleanupExpiredAutoSaves() error
}

// CacheServiceInterface 缓存服务接口
type CacheServiceInterface interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value interface{}, expiration int) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	Close() error
}

// JWTServiceInterface JWT服务接口
type JWTServiceInterface interface {
	ValidateToken(ctx context.Context, tokenString string) (*Claims, error)
	RefreshToken(ctx context.Context, tokenString string) (string, string, error)
	InvalidateToken(ctx context.Context, tokenString string) error
	GenerateTokenPair(user *models.User) (accessToken, refreshToken string, err error)
	GenerateTokenPairWithRememberMe(user *models.User, rememberMe bool) (accessToken, refreshToken string, err error)
	BlacklistToken(ctx context.Context, tokenString string) error
}

// RBACServiceInterface RBAC权限服务接口
type RBACServiceInterface interface {
	// 角色管理
	CreateRole(role *models.Role) error
	GetRole(id uuid.UUID) (*models.Role, error)
	GetRoles(limit, offset int) ([]models.Role, int64, error)
	UpdateRole(id uuid.UUID, updates map[string]interface{}) error
	DeleteRole(id uuid.UUID) error
	
	// 权限管理
	CreatePermission(permission *models.Permission) error
	GetPermissions(limit, offset int) ([]models.Permission, int64, error)
	
	// 用户角色关联
	AssignRoleToUser(userID, roleID uuid.UUID, assignedBy uuid.UUID) error
	RemoveRoleFromUser(userID, roleID uuid.UUID) error
	GetUserRoles(userID uuid.UUID) ([]models.Role, error)
	
	// 权限检查
	CheckPermission(ctx context.Context, userID uuid.UUID, resource, action string) (bool, error)
	HasRole(ctx context.Context, userID uuid.UUID, roleCode string) (bool, error)
}

// MinIOServiceInterface MinIO对象存储服务接口
type MinIOServiceInterface interface {
	UploadFile(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error)
	GetFileURL(ctx context.Context, bucketName, objectName string) (string, error)
	DeleteFile(ctx context.Context, bucketName, objectName string) error
	CheckBucketExists(ctx context.Context, bucketName string) (bool, error)
	CreateBucket(ctx context.Context, bucketName string) error
}