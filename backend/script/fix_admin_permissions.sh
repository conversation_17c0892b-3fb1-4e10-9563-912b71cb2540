#!/bin/bash

echo "🔧 为admin用户分配超级管理员权限..."

# 1. 停止当前服务
echo "⏹️  停止当前服务..."
pkill -f "./main" || true

# 2. 运行角色分配脚本（需要你手动执行以下SQL）
echo "📝 需要手动执行SQL脚本来分配权限："
echo "================================================"
cat << 'EOF'
-- 给admin用户分配超级管理员角色的SQL脚本

-- 查找并分配角色
DO $$
DECLARE
    admin_user_id UUID;
    super_admin_role_id UUID;
BEGIN
    -- 获取admin用户ID
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin' LIMIT 1;
    
    IF admin_user_id IS NULL THEN
        RAISE NOTICE 'Admin user not found';
        RETURN;
    END IF;
    
    -- 获取super_admin角色ID
    SELECT id INTO super_admin_role_id FROM roles WHERE name = 'super_admin' LIMIT 1;
    
    IF super_admin_role_id IS NULL THEN
        RAISE NOTICE 'Super admin role not found';
        RETURN;
    END IF;
    
    -- 删除现有的角色分配（如果有）
    DELETE FROM user_roles WHERE user_id = admin_user_id;
    
    -- 分配超级管理员角色
    INSERT INTO user_roles (
        user_id, 
        role_id, 
        project_id, 
        assigned_by, 
        assigned_at,
        created_at,
        updated_at
    ) VALUES (
        admin_user_id,
        super_admin_role_id,
        NULL, -- 全局角色
        admin_user_id, -- 自己分配
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (user_id, role_id, project_id) DO NOTHING;
    
    -- 清除权限缓存
    DELETE FROM user_permission_cache WHERE user_id = admin_user_id;
    
    RAISE NOTICE 'Successfully assigned super_admin role to admin user';
    RAISE NOTICE 'Admin user ID: %', admin_user_id;
    RAISE NOTICE 'Super admin role ID: %', super_admin_role_id;
END $$;

-- 验证分配结果
SELECT 
    u.username,
    u.email,
    r.name as role_name,
    r.display_name as role_display_name,
    ur.assigned_at
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.username = 'admin';

EOF

echo "================================================"
echo ""
echo "⚠️  请手动执行以上SQL脚本，然后按任意键继续..."
read -n 1 -s

# 3. 重新启动服务
echo ""
echo "🚀 重新启动后端服务..."
cd "$(dirname "$0")"
export GOPROXY=https://goproxy.cn,direct
nohup ./main > server.log 2>&1 &
echo "✅ 后端服务已启动，日志文件: server.log"

# 4. 提供前端说明
echo ""
echo "🌐 前端操作步骤："
echo "1. 重新登录 admin 用户"
echo "2. 现在可以访问 '系统管理' 菜单"
echo "3. 访问路径："
echo "   - http://localhost:3000/admin (管理首页)"
echo "   - http://localhost:3000/admin/users (用户管理)"
echo "   - http://localhost:3000/admin/roles (角色管理)"
echo ""
echo "✨ 权限修复完成！"