#!/bin/bash

echo "=== 后端服务状态检查 ==="
ps aux | grep main | grep -v grep

echo ""
echo "=== 后端服务日志 (最后20行) ==="
tail -n 20 server.log

echo ""
echo "=== 测试API响应 ==="
echo "1. 测试登录接口:"
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}' \
  -s | head -c 200

echo ""
echo ""
echo "2. 测试健康检查:"
curl http://localhost:3001/health -s

echo ""
echo ""
echo "=== 前端构建状态 ==="
cd ../apps/crf-editor
echo "前端依赖检查:"
npm list | grep -E "(vue|element-plus|pinia)" | head -5

echo ""
echo "=== 检查完成 ==="
echo "如果API返回正常，请:"
echo "1. 重新登录admin用户"
echo "2. 检查浏览器控制台错误"
echo "3. 确认能看到'系统管理'菜单"