#!/bin/bash

# RBAC数据库迁移脚本
# Description: 应用RBAC系统迁移
# Author: Claude Code
# Date: 2025-07-11

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量
check_env() {
    if [[ -z "$DATABASE_URL" ]]; then
        if [[ -f ".env" ]]; then
            source .env
        fi
    fi
    
    if [[ -z "$DATABASE_URL" ]]; then
        log_error "DATABASE_URL环境变量未设置"
        log_info "请设置DATABASE_URL环境变量或创建.env文件"
        exit 1
    fi
}

# 检查数据库连接
check_db_connection() {
    log_info "检查数据库连接..."
    if ! psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        log_error "无法连接到数据库"
        log_info "请检查DATABASE_URL是否正确，数据库是否运行"
        exit 1
    fi
    log_success "数据库连接正常"
}

# 检查迁移状态
check_migration_status() {
    log_info "检查迁移状态..."
    
    # 检查是否已经应用过此迁移
    migration_exists=$(psql "$DATABASE_URL" -t -c "SELECT EXISTS(SELECT 1 FROM migrations WHERE version = '003');" 2>/dev/null | tr -d ' ')
    
    if [[ "$migration_exists" == "t" ]]; then
        log_warning "RBAC迁移(003)已经应用过"
        read -p "是否要重新应用迁移？这将删除现有RBAC数据 (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消迁移"
            exit 0
        fi
        return 1
    fi
    return 0
}

# 备份数据库
backup_database() {
    log_info "创建数据库备份..."
    backup_file="backup_before_rbac_$(date +%Y%m%d_%H%M%S).sql"
    
    if pg_dump "$DATABASE_URL" > "$backup_file"; then
        log_success "数据库备份已保存到: $backup_file"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 应用迁移
apply_migration() {
    log_info "应用RBAC系统迁移..."
    
    migration_file="backend/database/migrations/003_add_rbac_system.sql"
    
    if [[ ! -f "$migration_file" ]]; then
        log_error "迁移文件不存在: $migration_file"
        exit 1
    fi
    
    # 开始事务并应用迁移
    if psql "$DATABASE_URL" -f "$migration_file"; then
        log_success "RBAC系统迁移应用成功"
    else
        log_error "迁移应用失败"
        log_warning "请检查日志并手动修复"
        exit 1
    fi
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    # 检查表是否创建成功
    tables=("roles" "permissions" "role_permissions" "user_roles" "user_permission_cache")
    
    for table in "${tables[@]}"; do
        if psql "$DATABASE_URL" -t -c "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = '$table');" | grep -q "t"; then
            log_success "表 $table 创建成功"
        else
            log_error "表 $table 创建失败"
            exit 1
        fi
    done
    
    # 检查角色数量
    role_count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM roles;" | tr -d ' ')
    log_info "创建了 $role_count 个系统角色"
    
    # 检查权限数量
    permission_count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM permissions;" | tr -d ' ')
    log_info "创建了 $permission_count 个系统权限"
    
    # 检查用户角色迁移
    user_role_count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM user_roles;" | tr -d ' ')
    log_info "迁移了 $user_role_count 个用户角色"
}

# 显示角色权限摘要
show_rbac_summary() {
    log_info "RBAC系统摘要:"
    echo ""
    echo "=== 系统角色 ==="
    psql "$DATABASE_URL" -c "SELECT name, display_name, description FROM roles WHERE is_system = true ORDER BY name;"
    
    echo ""
    echo "=== 权限统计 ==="
    psql "$DATABASE_URL" -c "SELECT resource, COUNT(*) as permission_count FROM permissions GROUP BY resource ORDER BY resource;"
    
    echo ""
    echo "=== 用户角色分布 ==="
    psql "$DATABASE_URL" -c "
        SELECT r.display_name, COUNT(ur.user_id) as user_count
        FROM roles r
        LEFT JOIN user_roles ur ON r.id = ur.role_id
        WHERE r.is_system = true
        GROUP BY r.id, r.display_name
        ORDER BY r.display_name;
    "
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
}

# 主函数
main() {
    log_info "开始RBAC系统迁移..."
    
    # 检查环境
    check_env
    check_db_connection
    
    # 检查迁移状态
    if ! check_migration_status; then
        log_warning "准备重新应用迁移..."
    fi
    
    # 创建备份
    backup_database
    
    # 应用迁移
    apply_migration
    
    # 验证结果
    verify_migration
    
    # 显示摘要
    show_rbac_summary
    
    log_success "RBAC系统迁移完成！"
    log_info "现在可以重启应用程序以使用新的权限系统"
}

# 错误处理
trap cleanup EXIT

# 运行主函数
main "$@"