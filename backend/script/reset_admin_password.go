package main

import (
	"fmt"
	"log"

	"crf-backend/internal/config"
	"crf-backend/internal/database"

	"golang.org/x/crypto/bcrypt"
)

func main() {
	// Load config
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Connect to database
	db, err := database.Connect(cfg.Database.URL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Generate new password hash for admin123
	newPassword := "admin123"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("Failed to hash password:", err)
	}

	// Update admin user password
	result := db.Exec("UPDATE users SET password_hash = ? WHERE username = ?", string(hashedPassword), "admin")
	if result.Error != nil {
		log.Fatal("Failed to update admin password:", result.Error)
	}

	fmt.Printf("✅ Successfully updated admin password to: %s\n", newPassword)
	fmt.Printf("   New hash: %s\n", string(hashedPassword))
	fmt.Printf("   Affected rows: %d\n", result.RowsAffected)

	// Test the new password
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(newPassword))
	fmt.Printf("   Password verification: %s\n", func() string {
		if err == nil {
			return "✅ SUCCESS"
		}
		return "❌ FAILED"
	}())
}
