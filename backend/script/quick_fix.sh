#!/bin/bash

echo "🔧 快速修复admin用户权限问题..."

# 创建临时修复脚本
cat > temp_fix.go << 'EOF'
package main

import (
    "database/sql"
    "fmt"
    "log"
    _ "github.com/lib/pq"
)

func main() {
    // 数据库连接
    db, err := sql.Open("postgres", "host=************* port=5432 user=postgres dbname=crf_db password=123456 sslmode=disable")
    if err != nil {
        log.Fatal(err)
    }
    defer db.Close()

    // 检查连接
    if err := db.Ping(); err != nil {
        log.Fatal("Cannot connect to database:", err)
    }

    fmt.Println("✅ Connected to database")

    // 查找admin用户和super_admin角色
    var adminUserID, superAdminRoleID string
    
    err = db.QueryRow("SELECT id FROM users WHERE username = 'admin' LIMIT 1").Scan(&adminUserID)
    if err != nil {
        log.Fatal("Admin user not found:", err)
    }
    
    err = db.QueryRow("SELECT id FROM roles WHERE name = 'super_admin' LIMIT 1").Scan(&superAdminRoleID)
    if err != nil {
        log.Fatal("Super admin role not found:", err)
    }

    fmt.Printf("✅ Found admin user: %s\n", adminUserID)
    fmt.Printf("✅ Found super_admin role: %s\n", superAdminRoleID)

    // 删除现有角色分配
    _, err = db.Exec("DELETE FROM user_roles WHERE user_id = $1", adminUserID)
    if err != nil {
        log.Printf("Warning: Failed to delete existing roles: %v", err)
    }

    // 分配超级管理员角色
    _, err = db.Exec(`
        INSERT INTO user_roles (id, user_id, role_id, project_id, assigned_by, assigned_at, created_at, updated_at) 
        VALUES (gen_random_uuid(), $1, $2, NULL, $1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, adminUserID, superAdminRoleID)
    
    if err != nil {
        log.Fatal("Failed to assign role:", err)
    }

    // 清除权限缓存
    _, err = db.Exec("DELETE FROM user_permission_cache WHERE user_id = $1", adminUserID)
    if err != nil {
        log.Printf("Warning: Failed to clear cache: %v", err)
    }

    fmt.Println("🎉 Successfully assigned super_admin role to admin user!")
    
    // 验证
    var username, roleName string
    err = db.QueryRow(`
        SELECT u.username, r.name 
        FROM users u 
        JOIN user_roles ur ON u.id = ur.user_id 
        JOIN roles r ON ur.role_id = r.id 
        WHERE u.username = 'admin' LIMIT 1
    `).Scan(&username, &roleName)
    
    if err == nil {
        fmt.Printf("✅ Verification: %s has role %s\n", username, roleName)
    }
}
EOF

echo "📦 安装必要的依赖..."
go mod tidy

echo "🔨 编译修复工具..."
go build -o temp_fix temp_fix.go

echo "⚡ 执行权限修复..."
./temp_fix

echo "🧹 清理临时文件..."
rm -f temp_fix temp_fix.go

echo "✨ 权限修复完成！现在admin用户应该有超级管理员权限了。"
echo ""
echo "📋 接下来的步骤："
echo "1. 重新登录admin用户"
echo "2. 现在应该可以看到'系统管理'菜单"
echo "3. 可以访问用户管理和角色管理页面"