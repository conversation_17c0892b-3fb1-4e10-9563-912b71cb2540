package main

import (
	"log"

	"crf-backend/internal/config"
	"crf-backend/internal/database"
	"crf-backend/internal/models"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	db, err := database.Connect(cfg.Database.URL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	log.Println("Running database migrations...")

	// 运行自动迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Project{},
		&models.CRFTemplate{},
		&models.CRFInstance{},
		&models.CRFVersion{},
		&models.AutoSave{},
		&models.OperationHistory{},
		&models.Attachment{},
		&models.SystemSetting{},
		&models.Migration{},
		&models.UserSession{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
	)

	if err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	log.Println("Database migrations completed successfully!")
}