# CRF Backend API 接口文档

## 基本信息

- **Base URL**: `http://localhost:3000`
- **API Prefix**: `/api`
- **认证方式**: JWT Token (Bearer Token)
- **数据格式**: JSON
- **缓存**: Redis (用于性能优化)

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述"
}
```

## 认证说明

本API使用JWT Token进行认证：

1. **获取Token**：通过登录接口获取JWT Token
2. **使用Token**：在请求Header中添加 `Authorization: Bearer <token>`
3. **Token刷新**：Token过期前可通过刷新接口获取新Token

需要认证的接口会在文档中标注 🔒 需要认证

**示例请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 权限说明

系统采用项目级权限控制：

1. **项目成员角色**：
   - `owner`: 项目拥有者，拥有所有权限
   - `admin`: 项目管理员，除删除项目外的所有权限
   - `editor`: 编辑者，可编辑模板和实例
   - `viewer`: 查看者，只能查看
   - `member`: 普通成员，基本操作权限

2. **权限检查**：需要项目权限的接口会验证用户在该项目中的权限

---

## 1. 系统健康检查

### 健康检查
- **接口**: `GET /health`
- **说明**: 检查系统状态
- **认证**: 无需认证

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": **********,
  "version": "1.0.0"
}
```

---

## 2. 用户认证模块

### 2.1 用户登录
- **接口**: `POST /api/auth/login`
- **说明**: 用户登录获取JWT Token
- **认证**: 无需认证

**请求参数**:
```json
{
  "username": "用户名",      // required
  "password": "密码"         // required
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "full_name": "用户全名",
      "role": "user"
    }
  }
}
```

**错误响应**:
- `400`: 请求参数验证失败
- `401`: 用户名或密码错误/账号被禁用

### 2.2 用户注册
- **接口**: `POST /api/auth/register`
- **说明**: 新用户注册
- **认证**: 无需认证

**请求参数**:
```json
{
  "username": "用户名",      // required
  "email": "邮箱地址",       // required, email格式
  "password": "密码",        // required, 最少6位
  "full_name": "用户全名"    // optional
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "用户创建成功",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "full_name": "用户全名",
      "role": "user"
    }
  }
}
```

**错误响应**:
- `400`: 请求参数验证失败
- `409`: 用户名或邮箱已存在

### 2.3 用户登出
- **接口**: `POST /api/auth/logout`
- **说明**: 用户登出（JWT无状态，前端删除Token即可）
- **认证**: 无需认证

**响应示例**:
```json
{
  "code": 200,
  "message": "退出登录成功"
}
```

### 2.4 刷新Token
- **接口**: `POST /api/auth/refresh`
- **说明**: 刷新JWT Token（Token快过期时使用）
- **认证**: 🔒 需要认证

**请求头**:
```
Authorization: Bearer <current_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**错误响应**:
- `401`: Token无效或过期
- `400`: Token还未到刷新时间

### 2.5 获取当前用户信息
- **接口**: `GET /api/auth/me`
- **说明**: 获取当前登录用户信息
- **认证**: 🔒 需要认证

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "full_name": "用户全名",
      "role": "user"
    }
  }
}
```

**错误响应**:
- `401`: 未认证或Token无效

---

## 3. 用户管理模块

### 3.1 获取用户列表
- **接口**: `GET /api/users`
- **说明**: 分页获取用户列表，支持搜索和过滤
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:read` 权限

**查询参数**:
- `limit`: 每页数量，默认20，最大100
- `offset`: 偏移量，默认0
- `search`: 搜索关键词（用户名、邮箱、全名）
- `role`: 角色过滤
- `is_active`: 激活状态过滤

**请求示例**:
```
GET /api/users?limit=10&offset=0&search=admin&role=admin&is_active=true
```

**响应示例**:
```json
{
  "success": true,
  "message": "用户列表获取成功",
  "data": {
    "users": [
      {
        "id": "uuid",
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "系统管理员",
        "avatar_url": "https://minio.example.com/avatars/uuid.jpg",
        "is_active": true,
        "roles": ["admin", "user"],
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "limit": 10,
      "offset": 0
    }
  }
}
```

### 3.2 获取用户详情
- **接口**: `GET /api/users/{id}`
- **说明**: 根据ID获取用户详情
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:read` 权限

**路径参数**:
- `id`: 用户ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "用户详情获取成功",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "full_name": "用户全名",
      "avatar_url": "https://minio.example.com/avatars/uuid.jpg",
      "phone": "+86-13800138000",
      "department": "研发部",
      "position": "研究员",
      "is_active": true,
      "roles": ["researcher", "user"],
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

**错误响应**:
- `400`: 无效的用户ID
- `404`: 用户不存在

### 3.3 创建用户
- **接口**: `POST /api/users`
- **说明**: 创建新用户并分配角色
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:create` 权限

**请求参数**:
```json
{
  "username": "newuser",      // required
  "email": "<EMAIL>", // required
  "password": "password123",   // required
  "full_name": "用户全名",     // optional
  "phone": "+86-13800138000",  // optional
  "department": "研发部",      // optional
  "position": "研究员",        // optional
  "roles": ["researcher"],    // optional，默认为user角色
  "is_active": true           // optional，默认为true
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "user": {
      "id": "uuid",
      "username": "newuser",
      "email": "<EMAIL>",
      "full_name": "用户全名",
      "is_active": true,
      "roles": ["researcher", "user"],
      "created_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 3.4 更新用户角色
- **接口**: `PUT /api/users/{id}/roles`
- **说明**: 更新用户的角色分配
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:update` 权限

**路径参数**:
- `id`: 用户ID (UUID)

**请求参数**:
```json
{
  "roles": ["admin", "researcher"] // required，新的角色列表
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "用户角色更新成功"
}
```

### 3.5 激活用户
- **接口**: `POST /api/users/{id}/activate`
- **说明**: 激活用户账户
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:update` 权限

**路径参数**:
- `id`: 用户ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "用户激活成功"
}
```

### 3.6 停用用户
- **接口**: `POST /api/users/{id}/deactivate`
- **说明**: 停用用户账户
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:update` 权限

**路径参数**:
- `id`: 用户ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "用户停用成功"
}
```

### 3.7 重置用户密码
- **接口**: `POST /api/users/{id}/reset-password`
- **说明**: 重置用户密码
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:update` 权限

**路径参数**:
- `id`: 用户ID (UUID)

**请求参数**:
```json
{
  "new_password": "newpassword123" // required
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "密码重置成功"
}
```

### 3.8 获取用户个人资料
- **接口**: `GET /api/users/{id}/profile`
- **说明**: 获取用户个人资料详情
- **认证**: 🔒 需要认证
- **权限**: 用户只能访问自己的资料，管理员可访问所有用户资料

**路径参数**:
- `id`: 用户ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "个人资料获取成功",
  "data": {
    "profile": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "full_name": "用户全名",
      "avatar_url": "https://minio.example.com/avatars/uuid.jpg",
      "phone": "+86-13800138000",
      "department": "研发部",
      "position": "研究员",
      "roles": ["researcher", "user"],
      "is_active": true,
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 3.9 上传用户头像
- **接口**: `POST /api/users/{id}/avatar`
- **说明**: 上传用户头像到MinIO存储
- **认证**: 🔒 需要认证
- **权限**: 用户只能上传自己的头像，管理员可上传所有用户头像
- **文件限制**: 支持JPG、PNG、GIF、WebP格式，最大2MB

**路径参数**:
- `id`: 用户ID (UUID)

**请求参数**:
- `avatar`: 头像文件 (multipart/form-data)

**响应示例**:
```json
{
  "success": true,
  "message": "头像上传成功",
  "data": {
    "avatar_url": "https://minio.example.com/avatars/uuid.jpg"
  }
}
```

**错误响应**:
- `400`: 文件格式不支持或文件过大
- `403`: 没有权限上传头像
- `500`: 文件上传失败

### 3.10 删除用户头像
- **接口**: `DELETE /api/users/{id}/avatar`
- **说明**: 删除用户头像
- **认证**: 🔒 需要认证
- **权限**: 用户只能删除自己的头像，管理员可删除所有用户头像

**路径参数**:
- `id`: 用户ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "头像删除成功"
}
```

### 3.11 获取用户统计信息
- **接口**: `GET /api/users/stats`
- **说明**: 获取用户统计信息
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:read` 权限

**响应示例**:
```json
{
  "success": true,
  "message": "统计信息获取成功",
  "data": {
    "stats": {
      "total_users": 150,
      "active_users": 135,
      "inactive_users": 15,
      "users_by_role": {
        "admin": 5,
        "researcher": 80,
        "data_entry": 40,
        "reviewer": 25
      },
      "new_users_this_month": 12
    }
  }
}
```

### 3.4 删除用户
- **接口**: `DELETE /api/users/{id}`
- **说明**: 删除用户
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 用户ID (UUID)

**响应示例**:
```json
{
  "message": "User deleted successfully"
}
```

---

## 4. 项目管理模块

### 4.1 获取项目列表
- **接口**: `GET /api/projects`
- **说明**: 获取当前用户有权访问的项目列表
- **认证**: 🔒 需要认证

**查询参数**:
```
page=1              // 页码，默认1
page_size=10        // 每页数量，默认10，最大100
```

**响应示例**:
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "项目名称",
      "description": "项目描述",
      "status": "active",
      "template_count": 5,
      "instance_count": 23,
      "member_count": 3,
      "completion_rate": 65.5,
      "is_public": false,
      "allow_anonymous": false,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "creator": {
        "id": "uuid",
        "username": "creator",
        "full_name": "创建者姓名"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 25,
    "pages": 3
  }
}
```

### 4.2 创建项目
- **接口**: `POST /api/projects`
- **说明**: 创建新项目，创建者自动成为项目拥有者
- **认证**: 🔒 需要认证

**请求参数**:
```json
{
  "name": "项目名称",              // required, 2-100字符
  "description": "项目描述",       // optional, 最大500字符
  "is_public": false,            // optional, 是否公开项目
  "allow_anonymous": false       // optional, 是否允许匿名访问
}
```

**响应示例**:
```json
{
  "message": "项目创建成功",
  "data": {
    "id": "uuid",
    "name": "项目名称",
    "description": "项目描述",
    "status": "draft",
    "template_count": 0,
    "instance_count": 0,
    "member_count": 1,
    "completion_rate": 0.0,
    "is_public": false,
    "allow_anonymous": false,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 4.3 获取项目详情
- **接口**: `GET /api/projects/{id}`
- **说明**: 获取指定项目的详细信息
- **认证**: 🔒 需要认证
- **权限**: 需要项目成员身份

**路径参数**:
```
id      // 项目ID (UUID)
```

**响应示例**:
```json
{
  "data": {
    "id": "uuid",
    "name": "项目名称",
    "description": "项目描述",
    "status": "active",
    "template_count": 5,
    "instance_count": 23,
    "member_count": 3,
    "completion_rate": 65.5,
    "is_public": false,
    "allow_anonymous": false,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "creator": {
      "id": "uuid",
      "username": "creator",
      "full_name": "创建者姓名"
    },
    "members": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "role": "owner",
        "status": "active",
        "joined_at": "2024-01-01T00:00:00Z",
        "user": {
          "id": "uuid",
          "username": "username",
          "full_name": "用户姓名",
          "email": "<EMAIL>",
          "avatar_url": "https://example.com/avatar.jpg"
        }
      }
    ],
    "templates": [
      {
        "id": "uuid",
        "name": "模板名称",
        "title": "模板标题",
        "status": "published",
        "usage_count": 10
      }
    ]
  }
}
```

### 4.4 更新项目
- **接口**: `PUT /api/projects/{id}`
- **说明**: 更新项目信息
- **认证**: 🔒 需要认证
- **权限**: 需要项目更新权限

**路径参数**:
```
id      // 项目ID (UUID)
```

**请求参数**:
```json
{
  "name": "新项目名称",           // optional
  "description": "新项目描述",    // optional
  "status": "active",           // optional: draft, active, completed, archived, paused
  "is_public": true,            // optional
  "allow_anonymous": true       // optional
}
```

**响应示例**:
```json
{
  "message": "项目更新成功"
}
```

### 4.5 删除项目
- **接口**: `DELETE /api/projects/{id}`
- **说明**: 删除项目（软删除）
- **认证**: 🔒 需要认证
- **权限**: 只有项目拥有者可以删除项目

**路径参数**:
```
id      // 项目ID (UUID)
```

**响应示例**:
```json
{
  "message": "项目删除成功"
}
```

### 4.6 获取项目仪表板
- **接口**: `GET /api/projects/{id}/dashboard`
- **说明**: 获取项目仪表板数据，包含统计信息和最近活动
- **认证**: 🔒 需要认证
- **权限**: 需要项目成员身份

**路径参数**:
```
id      // 项目ID (UUID)
```

**响应示例**:
```json
{
  "data": {
    "project": {
      "id": "uuid",
      "name": "项目名称",
      "description": "项目描述",
      "status": "active",
      "completion_rate": 65.5
    },
    "templateStats": {
      "total": 10,
      "published": 7,
      "draft": 2,
      "archived": 1
    },
    "instanceStats": {
      "total": 45,
      "completed": 30,
      "in_progress": 12,
      "draft": 3
    },
    "memberCount": 5,
    "recentActivities": [
      {
        "id": "uuid",
        "activity_type": "template_published",
        "activity_description": "发布了模板 患者基线信息",
        "created_at": "2024-01-15T10:30:00Z",
        "user": {
          "id": "uuid",
          "username": "username",
          "full_name": "用户姓名",
          "avatar_url": "https://example.com/avatar.jpg"
        }
      }
    ]
  }
}
```

### 4.7 获取项目成员
- **接口**: `GET /api/projects/{id}/members`
- **说明**: 获取项目成员列表
- **认证**: 🔒 需要认证
- **权限**: 需要项目成员身份

**路径参数**:
```
id      // 项目ID (UUID)
```

**响应示例**:
```json
{
  "data": [
    {
      "id": "uuid",
      "project_id": "uuid",
      "user_id": "uuid",
      "role": "owner",
      "permissions": {
        "templates": {
          "read": true,
          "create": true,
          "update": true,
          "delete": true,
          "publish": true
        },
        "instances": {
          "read": true,
          "create": true,
          "update": true,
          "delete": true,
          "submit": true
        },
        "members": {
          "read": true,
          "invite": true,
          "manage": true
        },
        "analytics": {
          "read": true,
          "export": true
        },
        "project": {
          "read": true,
          "update": true,
          "delete": true
        }
      },
      "status": "active",
      "joined_at": "2024-01-01T00:00:00Z",
      "user": {
        "id": "uuid",
        "username": "username",
        "full_name": "用户姓名",
        "email": "<EMAIL>",
        "avatar_url": "https://example.com/avatar.jpg"
      }
    }
  ]
}
```

### 4.8 添加项目成员
- **接口**: `POST /api/projects/{id}/members`
- **说明**: 邀请用户加入项目
- **认证**: 🔒 需要认证
- **权限**: 需要成员管理权限

**路径参数**:
```
id      // 项目ID (UUID)
```

**请求参数**:
```json
{
  "user_id": "uuid",                    // required, 被邀请用户ID
  "role": "editor"                      // required: owner, admin, editor, viewer, member
}
```

**响应示例**:
```json
{
  "message": "项目成员添加成功"
}
```

### 4.9 移除项目成员
- **接口**: `DELETE /api/projects/{id}/members/{member_id}`
- **说明**: 移除项目成员
- **认证**: 🔒 需要认证
- **权限**: 需要成员管理权限，不能移除项目拥有者

**路径参数**:
```
id          // 项目ID (UUID)
member_id   // 成员ID (UUID)
```

**响应示例**:
```json
{
  "message": "项目成员移除成功"
}
```

### 4.10 更新成员角色
- **接口**: `PUT /api/projects/{id}/members/{member_id}/role`
- **说明**: 更新项目成员角色
- **认证**: 🔒 需要认证
- **权限**: 需要成员管理权限

**路径参数**:
```
id          // 项目ID (UUID)
member_id   // 成员ID (UUID)
```

**请求参数**:
```json
{
  "role": "admin"                       // required: owner, admin, editor, viewer, member
}
```

**响应示例**:
```json
{
  "message": "成员角色更新成功"
}
```

### 4.11 获取项目活动日志
- **接口**: `GET /api/projects/{id}/activities`
- **说明**: 获取项目活动日志
- **认证**: 🔒 需要认证
- **权限**: 需要项目成员身份

**路径参数**:
```
id      // 项目ID (UUID)
```

**查询参数**:
```
page=1              // 页码，默认1
page_size=20        // 每页数量，默认20，最大100
```

**响应示例**:
```json
{
  "data": [
    {
      "id": "uuid",
      "activity_type": "template_created",
      "activity_description": "创建了模板 患者基线信息",
      "resource_type": "template",
      "resource_id": "uuid",
      "resource_name": "患者基线信息",
      "activity_data": {},
      "created_at": "2024-01-15T10:30:00Z",
      "user": {
        "id": "uuid",
        "username": "username",
        "full_name": "用户姓名",
        "avatar_url": "https://example.com/avatar.jpg"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 156,
    "pages": 8
  }
}
```

### 4.12 项目级别的模板操作
项目级别的模板操作提供了基于项目权限的模板管理功能：

- `GET /api/projects/{project_id}/templates` - 获取项目下的模板列表
- `POST /api/projects/{project_id}/templates` - 在项目下创建模板
- `GET /api/projects/{project_id}/templates/{template_id}` - 获取项目下的模板详情
- `PUT /api/projects/{project_id}/templates/{template_id}` - 更新项目下的模板
- `DELETE /api/projects/{project_id}/templates/{template_id}` - 删除项目下的模板
- `POST /api/projects/{project_id}/templates/{template_id}/publish` - 发布项目下的模板

### 4.13 项目级别的实例操作  
项目级别的实例操作提供了基于项目权限的实例管理功能：

- `GET /api/projects/{project_id}/instances` - 获取项目下的实例列表
- `POST /api/projects/{project_id}/instances` - 在项目下创建实例
- `GET /api/projects/{project_id}/instances/{instance_id}` - 获取项目下的实例详情
- `PUT /api/projects/{project_id}/instances/{instance_id}` - 更新项目下的实例
- `DELETE /api/projects/{project_id}/instances/{instance_id}` - 删除项目下的实例
- `POST /api/projects/{project_id}/instances/{instance_id}/submit` - 提交项目下的实例

### 4.14 项目级别的数据管理
项目级别的数据管理提供了基于项目权限的数据分析功能：

- `GET /api/projects/{project_id}/data/summary` - 获取项目数据汇总
- `GET /api/projects/{project_id}/data/statistics` - 获取项目数据统计
- `POST /api/projects/{project_id}/data/export` - 导出项目数据

**错误响应**:
- `400`: 请求参数无效
- `401`: 用户未认证
- `403`: 权限不足，不是项目成员或权限不够
- `404`: 项目不存在或已删除
- `409`: 资源冲突（如重复添加成员）

---

## 5. RBAC角色权限管理模块

### 4.1 获取角色列表
- **接口**: `GET /api/roles`
- **说明**: 获取系统中所有角色列表
- **认证**: 🔒 需要认证
- **权限**: 需要 `role:read` 权限

**响应示例**:
```json
{
  "success": true,
  "message": "角色列表获取成功",
  "data": {
    "roles": [
      {
        "id": "uuid",
        "name": "admin",
        "display_name": "系统管理员",
        "description": "系统管理员角色，拥有所有权限",
        "is_system_role": true,
        "permissions": ["user:*", "role:*", "template:*"],
        "created_at": "2024-01-01T10:00:00Z"
      },
      {
        "id": "uuid",
        "name": "researcher",
        "display_name": "研究者",
        "description": "研究者角色，可以创建和管理CRF表单",
        "is_system_role": true,
        "permissions": ["template:create", "template:read", "template:update"],
        "created_at": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 4.2 创建角色
- **接口**: `POST /api/roles`
- **说明**: 创建新的角色
- **认证**: 🔒 需要认证
- **权限**: 需要 `role:create` 权限

**请求参数**:
```json
{
  "name": "custom_role",           // required，角色名称
  "display_name": "自定义角色",     // required，角色显示名称
  "description": "自定义角色描述",  // optional，角色描述
  "permissions": ["template:read"] // optional，权限列表
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "角色创建成功",
  "data": {
    "role": {
      "id": "uuid",
      "name": "custom_role",
      "display_name": "自定义角色",
      "description": "自定义角色描述",
      "is_system_role": false,
      "permissions": ["template:read"],
      "created_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 4.3 更新角色
- **接口**: `PUT /api/roles/{id}`
- **说明**: 更新角色信息
- **认证**: 🔒 需要认证
- **权限**: 需要 `role:update` 权限

**路径参数**:
- `id`: 角色ID (UUID)

**请求参数**:
```json
{
  "display_name": "新的显示名称",   // optional
  "description": "新的角色描述",   // optional
  "permissions": ["template:*"]   // optional，新的权限列表
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "角色更新成功"
}
```

### 4.4 删除角色
- **接口**: `DELETE /api/roles/{id}`
- **说明**: 删除角色（系统角色不可删除）
- **认证**: 🔒 需要认证
- **权限**: 需要 `role:delete` 权限

**路径参数**:
- `id`: 角色ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "角色删除成功"
}
```

### 4.5 获取权限列表
- **接口**: `GET /api/permissions`
- **说明**: 获取系统中所有可用权限
- **认证**: 🔒 需要认证
- **权限**: 需要 `role:read` 权限

**响应示例**:
```json
{
  "success": true,
  "message": "权限列表获取成功",
  "data": {
    "permissions": [
      {
        "resource": "user",
        "action": "create",
        "permission": "user:create",
        "description": "创建用户"
      },
      {
        "resource": "user",
        "action": "read",
        "permission": "user:read",
        "description": "查看用户"
      },
      {
        "resource": "template",
        "action": "*",
        "permission": "template:*",
        "description": "模板所有权限"
      }
    ]
  }
}
```

### 4.6 为角色分配权限
- **接口**: `POST /api/roles/{id}/permissions`
- **说明**: 为指定角色分配权限
- **认证**: 🔒 需要认证
- **权限**: 需要 `role:update` 权限

**路径参数**:
- `id`: 角色ID (UUID)

**请求参数**:
```json
{
  "permissions": ["user:read", "template:create"] // required，权限列表
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "角色权限分配成功"
}
```

### 4.7 分配用户角色
- **接口**: `POST /api/user-roles`
- **说明**: 为用户分配角色
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:update` 权限

**请求参数**:
```json
{
  "user_id": "uuid",     // required，用户ID
  "role_id": "uuid",     // required，角色ID
  "project_id": "uuid",  // optional，项目ID（项目权限）
  "expires_at": "2024-12-31T23:59:59Z" // optional，过期时间
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "用户角色分配成功"
}
```

### 4.8 移除用户角色
- **接口**: `DELETE /api/user-roles/{user_id}/roles/{role_id}`
- **说明**: 移除用户的指定角色
- **认证**: 🔒 需要认证
- **权限**: 需要 `user:update` 权限

**路径参数**:
- `user_id`: 用户ID (UUID)
- `role_id`: 角色ID (UUID)

**查询参数**:
- `project_id`: 项目ID (可选，用于移除项目角色)

**响应示例**:
```json
{
  "success": true,
  "message": "用户角色移除成功"
}
```

### 4.9 权限检查
- **接口**: `POST /api/check-permission`
- **说明**: 检查用户是否具有指定权限
- **认证**: 🔒 需要认证

**请求参数**:
```json
{
  "user_id": "uuid",        // optional，不提供则检查当前用户
  "permission": "user:read", // required，要检查的权限
  "resource_id": "uuid",    // optional，资源ID
  "project_id": "uuid"      // optional，项目ID
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "权限检查完成",
  "data": {
    "has_permission": true,
    "reason": "用户具有admin角色"
  }
}
```

---

## 5. CRF模板管理模块

### 5.1 获取模板列表
- **接口**: `GET /api/templates`
- **说明**: 分页获取CRF模板列表
- **认证**: 🔒 需要认证

**查询参数**:
- `project_id`: 项目ID，可选过滤参数
- `limit`: 每页数量，默认20，最大100
- `offset`: 偏移量，默认0

**请求示例**:
```
GET /api/templates?project_id=uuid&limit=10&offset=0
```

**响应示例**:
```json
{
  "success": true,
  "message": "Templates retrieved successfully",  
  "data": {
    "templates": [
      {
        "id": "uuid",
        "project_id": "uuid", 
        "name": "模板名称",
        "title": "模板标题",
        "description": "模板描述",
        "keyword": "关键词",
        "version": "1.0.0",
        "status": "draft",
        "page_config": {},
        "form_structure": [],
        "component_configs": {},
        "validation_rules": {},
        "style_config": {},
        "permissions": {},
        "created_by": "uuid",
        "updated_by": "uuid",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "total": 50,
      "limit": 10,
      "offset": 0
    }
  }
}
```

### 4.2 创建CRF模板
- **接口**: `POST /api/templates`
- **说明**: 创建新的CRF模板
- **认证**: 🔒 需要认证

**请求参数**:
```json
{
  "project_id": "项目ID",           // optional，可选参数
  "name": "模板名称",               // required
  "title": "模板标题",             // required
  "description": "模板描述",        // optional
  "keyword": "关键词",             // optional
  "version": "1.0.0",             // optional，默认1.0.0
  "page_config": {},              // optional，页面配置
  "form_structure": [],           // optional，表单结构
  "component_configs": {},        // optional，组件配置
  "validation_rules": {},         // optional，验证规则
  "style_config": {},             // optional，样式配置
  "permissions": {}               // optional，权限配置
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Template created successfully",
  "data": {
    "template": {
      "id": "uuid",
      "project_id": "uuid",
      "name": "模板名称",
      "title": "模板标题",
      "description": "模板描述",
      "version": "1.0.0",
      "status": "draft",
      "created_by": "uuid",
      "created_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 4.3 获取模板详情
- **接口**: `GET /api/templates/{id}`
- **说明**: 根据ID获取CRF模板详情
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "success": true,
  "message": "Template retrieved successfully",
  "data": {
    "template": {
      "id": "uuid",
      "project_id": "uuid",
      "name": "模板名称",
      "title": "模板标题", 
      "description": "模板描述",
      "keyword": "关键词",
      "version": "1.0.0",
      "status": "draft",
      "page_config": {},
      "form_structure": [],
      "component_configs": {},
      "validation_rules": {},
      "style_config": {},
      "permissions": {},
      "created_by": "uuid",
      "updated_by": "uuid",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 4.4 更新CRF模板
- **接口**: `PUT /api/templates/{id}`
- **说明**: 更新CRF模板信息
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**请求参数**:
```json
{
  "name": "新模板名称",             // optional
  "title": "新模板标题",           // optional
  "description": "新模板描述",      // optional
  "keyword": "新关键词",           // optional
  "page_config": {},              // optional
  "form_structure": [],           // optional
  "component_configs": {},        // optional
  "validation_rules": {},         // optional
  "style_config": {},             // optional
  "permissions": {}               // optional
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Template updated successfully"
}
```

### 4.5 删除CRF模板
- **接口**: `DELETE /api/templates/{id}`
- **说明**: 删除CRF模板
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "message": "Template deleted successfully"
}
```

### 4.6 发布CRF模板
- **接口**: `POST /api/templates/{id}/publish`
- **说明**: 发布CRF模板，创建一个发布版本
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "message": "Template published successfully",
  "version": {
    "id": "uuid",
    "template_id": "uuid",
    "version": "1.0.0",
    "title": "模板标题",
    "status": "published",
    "published_at": "2024-01-01T10:00:00Z",
    "published_by": "uuid"
  }
}
```

### 4.7 获取模板版本列表
- **接口**: `GET /api/templates/{id}/versions`
- **说明**: 获取指定模板的所有版本
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "versions": [
    {
      "id": "uuid",
      "template_id": "uuid",
      "version": "1.0.0",
      "title": "模板标题",
      "description": "版本描述",
      "snapshot_data": {},
      "change_log": "更新日志",
      "status": "published",
      "published_at": "2024-01-01T10:00:00Z",
      "published_by": "uuid",
      "created_by": "uuid",
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 4.8 创建模板版本
- **接口**: `POST /api/templates/{id}/versions`
- **说明**: 为指定模板创建新版本
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**请求参数**:
```json
{
  "version": "1.1.0",            // required，版本号
  "description": "版本描述",      // optional
  "change_log": "更新内容"       // optional
}
```

**响应示例**:
```json
{
  "message": "Template version created successfully",
  "version": {
    "id": "uuid",
    "template_id": "uuid",
    "version": "1.1.0",
    "title": "模板标题",
    "description": "版本描述",
    "change_log": "更新内容",
    "status": "draft",
    "created_by": "uuid",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

### 4.9 保存模板草稿
- **接口**: `POST /api/templates/{id}/save-draft`
- **说明**: 保存模板草稿内容
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**请求参数**:
```json
{
  "template_data": {             // required，模板数据
    "sections": [],              // 表单章节
    "pageConfig": {},            // 页面配置
    "formData": {}               // 表单数据
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "草稿保存成功",
  "data": {
    "template_id": "uuid",
    "saved_at": "2024-01-01T10:00:00Z"
  }
}
```

### 4.10 获取自动保存配置
- **接口**: `GET /api/templates/auto-save-config`
- **说明**: 获取自动保存配置信息
- **认证**: 🔒 需要认证

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "auto_save_config": {
      "enabled": true,
      "interval_seconds": 30,      // 自动保存间隔（秒）
      "debounce_ms": 2000,         // 防抖时间（毫秒）
      "max_history": 10,           // 最大历史记录数
      "cleanup_days": 7,           // 自动清理天数
      "storage_type": "backend"    // 存储类型
    }
  }
}
```

### 4.11 获取模板访问链接
- **接口**: `GET /api/templates/{id}/access-link`
- **说明**: 获取已发布模板的公共访问链接
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "access_link": "/form/fill/uuid",
    "full_url": "http://localhost:3000/form/fill/uuid"
  }
}
```

**错误响应**:
- `400`: 模板未发布，无法生成访问链接
- `403`: 只有模板创建者可以获取访问链接
- `404`: 模板不存在

### 4.12 获取模板统计信息
- **接口**: `GET /api/templates/{id}/stats`
- **说明**: 获取模板统计信息
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "stats": {
      "template_id": "uuid",
      "status": "published",
      "usage_count": 25,
      "instance_count": 15,
      "version_stats": {
        "total": 5,
        "published": 2,
        "draft": 3
      },
      "published_at": "2024-01-01T10:00:00Z",
      "published_by": "uuid"
    }
  }
}
```

**错误响应**:
- `403`: 只有模板创建者可以查看统计信息
- `404`: 模板不存在

---

## 5. CRF实例管理模块

CRF实例是基于已发布模板创建的具体表单填写实例。每个实例包含表单数据、完成状态、验证结果等信息。

### 5.1 创建实例
- **接口**: `POST /api/instances`
- **说明**: 基于已发布的模板创建新的表单实例
- **认证**: 🔒 需要认证

**请求参数**:
```json
{
  "template_id": "uuid"              // required - 模板ID
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "实例创建成功",
  "data": {
    "instance": {
      "id": "c5d7ada2-4859-4dbd-b401-d1e2569864c4",
      "template_id": "ed0bc90a-c245-4a95-a2c6-a05f5d4639f0",
      "template_version": "1.0.2",
      "instance_name": "",
      "subject_id": "",
      "visit_id": "",
      "form_data": {},
      "validation_results": {},
      "status": "draft",
      "completion_percentage": 0.0,
      "locked_by": null,
      "locked_at": null,
      "reviewed_by": null,
      "reviewed_at": null,
      "submitted_at": null,
      "review_comment": "",
      "created_by": "7a70c6ee-f079-49c4-9d99-54f45ca25b92",
      "updated_by": null,
      "created_at": "2025-07-12T21:03:34.83993+08:00",
      "updated_at": "2025-07-12T21:03:34.83993+08:00"
    }
  }
}
```

**错误响应**:
- `400`: 请求参数验证失败
- `403`: 权限不足
- `404`: 模板不存在或未发布

### 5.2 获取实例列表
- **接口**: `GET /api/instances`
- **说明**: 获取当前用户的实例列表
- **认证**: 🔒 需要认证

**查询参数**:
- `template_id`: 模板ID (可选)
- `status`: 状态筛选 (可选): draft|in_progress|completed|locked|rejected
- `limit`: 每页数量，默认20，最大100
- `offset`: 偏移量，默认0

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "instances": [
      {
        "id": "uuid",
        "template_id": "uuid",
        "template_version": "1.0.2",
        "instance_name": "实例名称",
        "subject_id": "受试者ID",
        "visit_id": "访问ID",
        "status": "draft",
        "completion_percentage": 75.5,
        "created_at": "2025-07-12T21:03:34Z",
        "updated_at": "2025-07-12T21:03:49Z",
        "template": {
          "id": "uuid",
          "name": "模板名称",
          "title": "模板标题"
        }
      }
    ],
    "pagination": {
      "total": 100,
      "current": 1,
      "size": 20
    }
  }
}
```

### 5.3 获取实例详情
- **接口**: `GET /api/instances/{id}`
- **说明**: 获取指定实例的详细信息
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 实例ID (UUID)

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "instance": {
      "id": "uuid",
      "template_id": "uuid",
      "template_version": "1.0.2",
      "instance_name": "实例名称",
      "subject_id": "受试者ID",
      "visit_id": "访问ID", 
      "form_data": {
        "text-1234567890": "用户填写的文本内容",
        "select-5555555555": "选项1",
        "checkbox-7777777777": ["选项1", "选项3"],
        "date-2222222222": "2025-07-12"
      },
      "validation_results": {
        "valid": true,
        "errors": [],
        "warnings": []
      },
      "status": "completed",
      "completion_percentage": 100.0,
      "locked_by": null,
      "locked_at": null,
      "reviewed_by": null,
      "reviewed_at": null,
      "submitted_at": null,
      "review_comment": "",
      "created_by": "uuid",
      "updated_by": "uuid",
      "created_at": "2025-07-12T21:03:34Z",
      "updated_at": "2025-07-12T21:03:49Z",
      "template": {
        "id": "uuid",
        "name": "模板名称",
        "title": "模板标题",
        "template_data": {}
      },
      "creator": {
        "id": "uuid",
        "username": "创建者用户名",
        "full_name": "创建者姓名"
      }
    }
  }
}
```

**错误响应**:
- `403`: 权限不足（只能访问自己的实例）
- `404`: 实例不存在

### 5.4 更新实例数据
- **接口**: `PUT /api/instances/{id}`
- **说明**: 更新实例的表单数据，系统会自动计算完成百分比并更新状态
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 实例ID (UUID)

**请求参数**:
```json
{
  "form_data": {                     // required - 表单数据对象
    "text-1234567890": "文本内容",
    "textarea-9876543210": "多行文本内容\n可以有多行",
    "select-5555555555": "选项1",
    "radio-3333333333": "选项A",
    "checkbox-7777777777": ["选项1", "选项3"],
    "number-1111111111": 42,
    "date-2222222222": "2025-07-12",
    "email-4444444444": "<EMAIL>"
  },
  "completion_percentage": 75.5,     // optional - 前端计算的完成度（后端会重新计算）
  "status": "draft"                  // optional - 状态（后端会根据完成度自动设置）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "实例更新成功",
  "data": {
    "instance": {
      "id": "uuid",
      "template_id": "uuid",
      "template_version": "1.0.2",
      "form_data": {
        "text-1234567890": "文本内容",
        "textarea-9876543210": "多行文本内容\n可以有多行",
        "select-5555555555": "选项1",
        "radio-3333333333": "选项A",
        "checkbox-7777777777": ["选项1", "选项3"],
        "number-1111111111": 42,
        "date-2222222222": "2025-07-12",
        "email-4444444444": "<EMAIL>"
      },
      "status": "completed",           // 自动根据完成度设置
      "completion_percentage": 100.0, // 后端重新计算的准确值
      "updated_at": "2025-07-12T21:03:49Z",
      // ... 其他字段
    }
  }
}
```

**完成度计算规则**:
- 系统会自动分析表单数据，统计已填写的字段数量
- 支持多种数据类型：字符串、数字、数组、对象、布尔值
- 对于字符串：去除空格后不为空即视为已填写
- 对于数组：至少包含一个有效元素即视为已填写
- 对于对象：至少包含一个有效字段即视为已填写
- 完成度 = (已填写字段数 / 总字段数) × 100%
- 当完成度达到100%时，状态自动设置为 `completed`

**错误响应**:
- `400`: 请求参数验证失败
- `403`: 权限不足或实例已锁定/已提交
- `404`: 实例不存在

### 5.5 提交实例
- **接口**: `POST /api/instances/{id}/submit`
- **说明**: 提交实例供审核，提交后实例将被锁定无法修改
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 实例ID (UUID)

**响应示例**:
```json
{
  "code": 200,
  "message": "实例提交成功",
  "data": {
    "instance": {
      "id": "uuid",
      "status": "submitted",
      "submitted_at": "2025-07-12T21:10:00Z",
      "locked_by": "uuid",
      "locked_at": "2025-07-12T21:10:00Z"
    }
  }
}
```

**错误响应**:
- `400`: 实例未完成或已提交
- `403`: 权限不足
- `404`: 实例不存在

### 5.6 锁定/解锁实例
- **接口**: `POST /api/instances/{id}/lock` / `POST /api/instances/{id}/unlock`
- **说明**: 锁定或解锁实例，防止其他用户同时编辑
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 实例ID (UUID)

**锁定响应示例**:
```json
{
  "code": 200,
  "message": "实例锁定成功",
  "data": {
    "instance": {
      "id": "uuid",
      "locked_by": "uuid",
      "locked_at": "2025-07-12T21:05:00Z"
    }
  }
}
```

**解锁响应示例**:
```json
{
  "code": 200,
  "message": "实例解锁成功",
  "data": {
    "instance": {
      "id": "uuid",
      "locked_by": null,
      "locked_at": null
    }
  }
}
```

**错误响应**:
- `403`: 权限不足或实例被其他用户锁定
- `404`: 实例不存在

### 5.7 删除实例
- **接口**: `DELETE /api/instances/{id}`
- **说明**: 软删除实例（标记为已删除，不会物理删除）
- **认证**: 🔒 需要认证

**路径参数**:
- `id`: 实例ID (UUID)

**响应示例**:
```json
{
  "code": 200,
  "message": "实例删除成功"
}
```

**错误响应**:
- `403`: 权限不足（只能删除自己创建的实例）
- `404`: 实例不存在
- `409`: 实例已提交，无法删除

### 5.8 获取填写模板信息
- **接口**: `GET /api/form/template/{id}`
- **说明**: 获取模板信息用于表单填写（支持匿名访问）
- **认证**: 🔒 需要认证（管理员可配置匿名访问）

**路径参数**:
- `id`: 模板ID (UUID)

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "template": {
      "id": "uuid",
      "name": "模板名称",
      "title": "模板标题",
      "description": "模板描述",
      "template_data": {
        "pageConfig": {
          "title": "表单标题",
          "description": "表单描述"
        },
        "sections": [
          {
            "id": "section-1",
            "name": "章节名称",
            "blocks": [
              {
                "id": "field-1",
                "type": "text",
                "props": {
                  "title": "字段标题",
                  "required": true,
                  "placeholder": "请输入内容"
                }
              }
            ]
          }
        ]
      },
      "version": "1.0.2",
      "status": "published"
    }
  }
}
```

**错误响应**:
- `404`: 模板不存在或未发布
- `403`: 模板不允许公开访问

---

## 6. 数据模型

### 6.1 用户模型 (User)
```json
{
  "id": "uuid",                    // 用户ID
  "username": "string",            // 用户名
  "email": "string",               // 邮箱
  "full_name": "string",           // 全名
  "role": "string",                // 角色: user|admin
  "avatar_url": "string",          // 头像URL
  "is_active": "boolean",          // 是否激活
  "created_at": "datetime",        // 创建时间
  "updated_at": "datetime"         // 更新时间
}
```

### 6.2 CRF模板模型 (CRFTemplate)
```json
{
  "id": "uuid",                    // 模板ID
  "project_id": "uuid",            // 项目ID，可选字段
  "name": "string",                // 模板名称
  "title": "string",               // 模板标题
  "description": "string",         // 模板描述
  "keyword": "string",             // 关键词
  "version": "string",             // 版本号
  "status": "string",              // 状态: draft|published|archived
  "page_config": "object",         // 页面配置
  "form_structure": "array",       // 表单结构
  "component_configs": "object",   // 组件配置
  "validation_rules": "object",    // 验证规则
  "style_config": "object",        // 样式配置
  "permissions": "object",         // 权限配置
  "created_by": "uuid",            // 创建者ID
  "updated_by": "uuid",            // 更新者ID
  "created_at": "datetime",        // 创建时间
  "updated_at": "datetime"         // 更新时间
}
```

### 6.3 CRF实例模型 (CRFInstance)
```json
{
  "id": "uuid",                         // 实例ID
  "template_id": "uuid",                // 模板ID
  "template_version": "string",         // 模板版本
  "instance_name": "string",            // 实例名称
  "subject_id": "string",               // 受试者ID
  "visit_id": "string",                 // 访问ID
  "form_data": "object",                // 表单数据 (JSONB)
  "validation_results": "object",       // 验证结果 (JSONB)
  "status": "string",                   // 状态: draft|in_progress|completed|locked|rejected
  "completion_percentage": "number",    // 完成百分比 (0.0-100.0)
  "locked_by": "uuid",                  // 锁定者ID (可选)
  "locked_at": "datetime",              // 锁定时间 (可选)
  "reviewed_by": "uuid",                // 审核者ID (可选)
  "reviewed_at": "datetime",            // 审核时间 (可选)
  "submitted_at": "datetime",           // 提交时间 (可选)
  "review_comment": "string",           // 审核意见
  "created_by": "uuid",                 // 创建者ID
  "updated_by": "uuid",                 // 更新者ID
  "deleted_at": "datetime",             // 删除时间 (软删除)
  "is_deleted": "boolean",              // 是否已删除
  "created_at": "datetime",             // 创建时间
  "updated_at": "datetime"              // 更新时间
}
```

**实例状态说明**:
- `draft`: 草稿状态，可以编辑
- `in_progress`: 进行中，正在填写
- `completed`: 已完成，填写完毕
- `locked`: 已锁定，暂时无法编辑
- `rejected`: 已驳回，需要重新填写

**表单数据结构示例**:
```json
{
  "form_data": {
    "text-1234567890": "用户填写的文本内容",
    "textarea-9876543210": "多行文本内容\n可以有多行",
    "select-5555555555": "选项1",
    "radio-3333333333": "选项A",
    "checkbox-7777777777": ["选项1", "选项3"],
    "number-1111111111": 42,
    "date-2222222222": "2025-07-12",
    "email-4444444444": "<EMAIL>"
  }
}
```

### 6.4 模板版本模型 (CRFVersion)
```json
{
  "id": "uuid",                    // 版本ID
  "template_id": "uuid",           // 模板ID
  "version": "string",             // 版本号
  "title": "string",               // 标题
  "description": "string",         // 描述
  "snapshot_data": "object",       // 快照数据
  "change_log": "string",          // 更新日志
  "status": "string",              // 状态: draft|published
  "published_at": "datetime",      // 发布时间
  "published_by": "uuid",          // 发布者ID
  "created_by": "uuid",            // 创建者ID
  "created_at": "datetime"         // 创建时间
}
```

---

## 7. HTTP状态码说明

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突（如用户名已存在）
- `422 Unprocessable Entity`: 请求格式正确但语义错误
- `500 Internal Server Error`: 服务器内部错误

---

## 8. 缓存策略

系统使用Redis进行缓存优化：

- **用户会话缓存**: 缓存时间24小时
- **项目列表缓存**: 缓存时间10分钟
- **CRF模板缓存**: 缓存时间30分钟
- **系统设置缓存**: 缓存时间1小时

---

## 9. 开发环境信息

- **服务器地址**: `localhost:3000`
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+
- **认证方式**: JWT Token
- **API版本**: v1

---

## 10. 注意事项

1. 所有时间字段采用 ISO 8601 格式 (YYYY-MM-DDTHH:MM:SSZ)
2. 分页查询默认每页20条记录，最大100条
3. 文件上传大小限制为10MB
4. API请求频率限制：每分钟100次
5. JWT Token有效期为24小时
6. 所有接口都支持CORS跨域请求

---

**更新日志**:
*v1.2.0 - 2024-01-15*
*新增功能: CRF表单发布管理、版本控制优化*