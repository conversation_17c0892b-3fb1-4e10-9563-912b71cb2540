# 开发环境配置
VITE_APP_TITLE=CRF表单编辑器
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=医疗CRF可视化表单编辑器

# API配置 - 使用代理，通过Vite代理到后端API
VITE_API_BASE_URL=/api
VITE_API_PREFIX=/api
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_VUE_DEVTOOLS=true
VITE_ENABLE_CONSOLE_LOG=true
VITE_ENABLE_ERROR_LOG=true
VITE_ENABLE_PERFORMANCE_MONITOR=true
VITE_ENABLE_ERROR_REPORTING=true

# 构建配置
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_ANALYZE=false

# 其他配置
VITE_APP_STORAGE_PREFIX=crf_editor_
VITE_APP_DEFAULT_LANGUAGE=zh-CN
VITE_UPLOAD_MAX_SIZE=10485760
VITE_SUPPORTED_LOCALES=zh-CN,en-US