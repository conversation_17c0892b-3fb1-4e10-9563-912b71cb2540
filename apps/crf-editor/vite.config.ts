import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import * as path from 'node:path'
import UnoCss from 'unocss/vite'
import VueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    resolve: {
      alias: {
        '@/': `${path.resolve(__dirname, 'src')}/`
      }
    },
    css: {
      preprocessorOptions: {
        scss: {}
      }
    },
    plugins: [
      vue({
        // 开发环境启用更多调试功能
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      // 根据环境变量决定是否启用 Vue DevTools
      ...(env.VITE_ENABLE_VUE_DEVTOOLS === 'true' ? [VueDevTools({
        launchEditor: 'code',
        // 移除 componentInspector 配置项，因为它不是有效的 VitePluginVueDevToolsOptions 选项
        vitePlugin: true
      })] : []),
      UnoCss()
    ],
    // 开发服务器配置，包含跨域代理设置
    server: {
      host: '0.0.0.0',
      port: 5173,
      open: true,
      cors: true,
      // 启用热重载
      hmr: {
        overlay: true
      },
      // SPA 历史路由支持
      historyApiFallback: {
        index: '/index.html'
      },
      proxy: {
        // 代理所有以 /api 开头的请求到后端服务器
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path, // 保持原路径不变，因为后端期望/api前缀
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('代理错误:', err)
            })
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('代理请求:', req.method, req.url, '->', proxyReq.path)
            })
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('代理响应:', proxyRes.statusCode, req.url)
            })
          }
        },
        // 代理健康检查接口
        '/health': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('健康检查代理:', req.method, req.url, '->', proxyReq.path)
            })
          }
        },
        // 如果后端有WebSocket需要代理
        '/ws': {
          target: 'ws://localhost:3000',
          changeOrigin: true,
          ws: true
        }
      }
    },
    // 构建配置
    build: {
      // 输出目录
      outDir: 'dist',
      // 资源内联限制
      assetsInlineLimit: 4096,
      // 代码分割配置
      rollupOptions: {
        output: {
          // 分离第三方库
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            naiveUI: ['naive-ui'],
          }
        }
      }
    }
  }
})
