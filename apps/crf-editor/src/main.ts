import { createApp } from 'vue'
import App from './App.vue'

// 修复 Naive UI 导入方式
import * as naive from 'naive-ui'
import CrfUI from '@crf/components'
import router from './router'
import { createPinia } from 'pinia'
import { env } from '@/utils/env'
import { setupErrorHandler } from '@/utils/error-handler'
import 'virtual:uno.css'
import '@/assets/styles/main.scss'
// 导入统一样式系统
import '@crf/components/shared/index.scss'
import draggable from 'vuedraggable'
import { registerConfigComponents } from '@/components/config'

// 初始化网络客户端
import { initNetworkClients } from '@/api'
initNetworkClients()

const app = createApp(App)

// 设置全局错误处理
const errorHandler = setupErrorHandler(app)

// 配置错误处理器（根据环境设置）
if (!env.isDev) {
  errorHandler.configure({
    reportUrl: '/api/errors', // 生产环境上报错误
    maxQueueSize: 100
  })
}

// 开发环境启用 Vue DevTools 和性能监控
if (env.isDev) {
  app.config.performance = env.enablePerformanceMonitor

  // 只在 Vue DevTools 禁用时才过滤错误
  if (!env.enableVueDevtools) {
    // 处理 Vue DevTools 相关错误
    const originalConsoleError = console.error
    console.error = (...args) => {
      const message = args[0]?.toString() || ''
      // 过滤掉 Vue DevTools 的已知错误
      if (message.includes('getAppRootInstance') ||
        message.includes('Cannot read properties of undefined') ||
        message.includes('No root instance found for app') ||
        message.includes("Cannot read properties of undefined (reading 'Vue')") ||
        message.includes('initBackend')) {
        return // 忽略这些错误
      }
      originalConsoleError.apply(console, args)
    }

    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      const message = event.reason?.message || event.reason?.toString() || ''
      if (message.includes("Cannot read properties of undefined (reading 'Vue')") ||
        message.includes('initBackend') ||
        message.includes('Vue DevTools')) {
        event.preventDefault() // 阻止错误显示
        return
      }
    })
  }

  // 打印环境信息
  env.printEnvInfo()
}

const pinia = createPinia()
app.use(pinia)

// 使用 Naive UI（主题在 App.vue 中配置）
app.use(naive)

// 使用新的CRF组件系统
app.use(CrfUI)

app.use(router)

// 注册配置组件
registerConfigComponents(app)

app.component('draggable', draggable)

// 挂载应用
app.mount('#app')

// 开发环境下打印组件注册信息和加载测试工具
if (env.isDev) {
  import('@crf/components').then(({ componentRegistry }) => {
    const stats = componentRegistry.getStats()
    console.log('📊 CRF组件注册统计：', stats)
  })
}
