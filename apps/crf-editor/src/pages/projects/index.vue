<template>
  <div class="projects-page">
    <!-- 页面头部 - 使用UnoCSS样式 -->
    <div class="flex justify-between items-center mb-6 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="flex-1">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">项目管理</h1>
        <p class="text-gray-600">管理您的临床研究项目，组织表单模板和数据</p>
      </div>
      <div class="flex gap-3">
        <n-button type="primary" @click="handleCreateProject">
          <template #icon>
            <n-icon><Plus /></n-icon>
          </template>
          创建项目
        </n-button>
      </div>
    </div>

    <!-- 筛选工具栏 - 使用UnoCSS样式 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <!-- 批量操作 -->
        <div v-if="selectedProjects.length > 0" class="flex items-center gap-3 p-3 bg-blue-50 rounded-md border border-blue-200">
          <n-checkbox
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @update:checked="handleSelectAll"
          >
            全选
          </n-checkbox>
          <n-button size="small" @click="handleBatchArchive" :loading="batchLoading">
            批量归档
          </n-button>
          <n-button size="small" type="error" @click="handleBatchDelete" :loading="batchLoading">
            批量删除
          </n-button>
          <span class="text-sm text-blue-700 font-medium">已选择 {{ selectedProjects.length }} 个项目</span>
        </div>

        <!-- 搜索和筛选 -->
        <div class="flex flex-col sm:flex-row gap-3">
          <n-input
            v-model:value="filters.search"
            placeholder="搜索项目名称"
            clearable
            class="w-full sm:w-80"
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon><Search /></n-icon>
            </template>
          </n-input>
          <n-select
          v-model:value="filters.status"
          placeholder="项目状态"
          clearable
          style="width: 150px"
          @update:value="handleStatusChange"
        >
          <n-option label="草稿" value="draft" />
          <n-option label="进行中" value="active" />
          <n-option label="已完成" value="completed" />
          <n-option label="已归档" value="archived" />
        </n-select>
        <n-select
          v-model:value="filters.project_type"
          placeholder="项目类型"
          clearable
          style="width: 150px"
          @update:value="handleTypeChange"
        >
          <n-option label="临床试验" value="clinical_trial" />
          <n-option label="观察性研究" value="observational_study" />
          <n-option label="注册研究" value="registry" />
          <n-option label="调查问卷" value="survey" />
          <n-option label="其他" value="other" />
        </n-select>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            刷新
          </n-button>
        </div>
      </div>
    </div>

    <!-- 项目卡片列表 -->
    <n-spin :show="loading">
      <div class="projects-grid">
        <div
          v-for="project in projects"
          :key="project.id"
          class="project-card"
          :class="{ selected: selectedProjects.includes(project.id) }"
          @click="handleViewProject(project)"
        >
          <div class="card-header">
            <!-- 项目图标和状态 -->
            <div class="project-info">
              <div class="project-icon">
                <n-icon size="24" color="#3b82f6">
                  <Document />
                </n-icon>
              </div>
              <n-tag :type="getStatusType(project.status)" size="small">
                {{ getStatusText(project.status) }}
              </n-tag>
            </div>
            <!-- 复选框和操作按钮 -->
            <div class="card-actions">
              <n-checkbox
                :checked="selectedProjects.includes(project.id)"
                @update:checked="(checked) => handleProjectSelect(project.id, checked)"
                @click.stop
                class="project-checkbox"
              />
              <n-button
                quaternary
                circle
                type="error"
                class="delete-btn"
                @click.stop="handleDeleteProject(project)"
              >
                <template #icon>
                  <n-icon><Trash /></n-icon>
                </template>
              </n-button>
              <n-dropdown
                @select="(key: string) => handleCardAction(key, project)"
                :options="[
                  {
                    label: '编辑',
                    key: 'edit',
                    icon: () => h(NIcon, null, { default: () => h(Edit) })
                  },
                  {
                    label: '管理模板',
                    key: 'templates',
                    icon: () => h(NIcon, null, { default: () => h(Document) })
                  }
                ]"
              >
                <n-button quaternary circle>
                  <template #icon>
                    <n-icon><MoreFilled /></n-icon>
                  </template>
                </n-button>
              </n-dropdown>
            </div>
          </div>

          <div class="card-content">
            <h3 class="project-title">{{ project.name }}</h3>
            <p class="project-description">{{ project.description || '暂无描述' }}</p>
            
            <div class="project-stats">
              <div class="stat-item">
                <span class="stat-value">{{ project.template_count || 0 }}</span>
                <span class="stat-label">模板</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ project.instance_count || 0 }}</span>
                <span class="stat-label">实例</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ project.member_count || 0 }}</span>
                <span class="stat-label">成员</span>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="project-meta">
              <span class="meta-item">
                <n-icon><Calendar /></n-icon>
                {{ formatDate(project.created_at) }}
              </span>
              <span class="meta-item">
                <n-icon><User /></n-icon>
                {{ project.created_by || '未知' }}
              </span>
            </div>
            <div class="actions">
              <n-button
                type="primary"
                size="small"
                @click.stop="handleManageTemplates(project)"
              >
                管理模板
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </n-spin>

    <!-- 空状态 -->
    <div v-if="!loading && projects.length === 0" class="empty-state">
      <n-empty description="暂无项目数据" :size="120">
        <template #icon>
          <n-icon size="64" color="#C0C4CC"><Document /></n-icon>
        </template>
        <template #extra>
          <n-button type="primary" @click="handleCreateProject">
            创建第一个项目
          </n-button>
        </template>
      </n-empty>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <n-pagination
        v-model:page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :item-count="pagination.total"
        :page-sizes="[12, 24, 48]"
        show-size-picker
        show-quick-jumper
        @update:page="handleCurrentChange"
        @update:page-size="handleSizeChange"
      >
        <template #prefix="{ itemCount }">
          共 {{ itemCount }} 条
        </template>
      </n-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage, useDialog, NDropdown } from 'naive-ui'
import type { DropdownOption, DropdownDivider } from 'naive-ui'
import {
  Add as Plus, Search, Refresh, EllipsisVertical as MoreFilled,
  CreateOutline as Edit, DocumentText as Document, TrashOutline as Trash,
  Calendar, Person as User
} from '@vicons/ionicons5'
import { projectAPI } from '@/api'


defineOptions({
  name: 'ProjectsPage'
})

const router = useRouter()
const route = useRoute()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const projects = ref<Record<string, unknown>[]>([])
const selectedProjects = ref<string[]>([])
const batchLoading = ref(false)

// 筛选条件
const filters = reactive({
  search: '',
  status: '',
  project_type: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0
})

// 批量操作计算属性
const isAllSelected = computed(() => {
  return projects.value.length > 0 && selectedProjects.value.length === projects.value.length
})

const isIndeterminate = computed(() => {
  return selectedProjects.value.length > 0 && selectedProjects.value.length < projects.value.length
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'completed': return 'primary'
    case 'paused': return 'warning'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '进行中'
    case 'completed': return '已完成'
    case 'paused': return '已暂停'
    default: return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      search: filters.search || undefined,
      status: filters.status || undefined,
      project_type: filters.project_type || undefined,
      with_stats: true
    }

    const response = await projectAPI.getProjects(params)
    if (response.success && response.data) {
      projects.value = response.data.projects || []
      pagination.total = Number(response.data.pagination?.total) || 0
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    message.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  fetchProjects()
}

const handleStatusChange = () => {
  pagination.current = 1
  fetchProjects()
}

const handleTypeChange = () => {
  pagination.current = 1
  fetchProjects()
}

const handleRefresh = () => {
  fetchProjects()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  fetchProjects()
}

const handleCurrentChange = (page: number) => {
  pagination.current = page
  fetchProjects()
}

const handleCreateProject = () => {
  router.push('/projects/create')
}

const handleViewProject = (project: Record<string, unknown>) => {
  router.push(`/projects/${project.id}`)
}

const handleManageTemplates = (project: Record<string, unknown>) => {
  router.push(`/projects/${project.id}/templates`)
}

const handleCardAction = async (command: string, project: Record<string, unknown>) => {
  switch (command) {
    case 'edit':
      router.push(`/projects/${project.id}/edit`)
      break
    case 'templates':
      handleManageTemplates(project)
      break
    case 'delete':
      handleDeleteProject(project)
      break
  }
}

const handleDeleteProject = async (project: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认删除',
        content: `确定要删除项目 "${project.name}" 吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    // TODO: 调用删除API
    message.success('项目删除成功')
    fetchProjects()
  } catch (error) {
    if (error !== 'cancel') {
      message.error('删除项目失败')
    }
  }
}

// 批量操作方法
const handleProjectSelect = (projectId: string, checked: boolean) => {
  if (checked) {
    if (!selectedProjects.value.includes(projectId)) {
      selectedProjects.value.push(projectId)
    }
  } else {
    const index = selectedProjects.value.indexOf(projectId)
    if (index > -1) {
      selectedProjects.value.splice(index, 1)
    }
  }
}

const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedProjects.value = projects.value.map(p => p.id as string)
  } else {
    selectedProjects.value = []
  }
}

const handleBatchArchive = async () => {
  if (selectedProjects.value.length === 0) return

  try {
    await new Promise((resolve, reject) => {
      dialog.info({
        title: '确认归档',
        content: `确定要归档选中的 ${selectedProjects.value.length} 个项目吗？`,
        positiveText: '归档',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    batchLoading.value = true
    // TODO: 调用批量归档API
    message.success(`成功归档 ${selectedProjects.value.length} 个项目`)
    selectedProjects.value = []
    fetchProjects()
  } catch (error) {
    if (error !== 'cancel') {
      message.error('批量归档失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const handleBatchDelete = async () => {
  if (selectedProjects.value.length === 0) return

  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedProjects.value.length} 个项目吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    batchLoading.value = true
    // TODO: 调用批量删除API
    message.success(`成功删除 ${selectedProjects.value.length} 个项目`)
    selectedProjects.value = []
    fetchProjects()
  } catch (error) {
    if (error !== 'cancel') {
      message.error('批量删除失败')
    }
  } finally {
    batchLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchProjects()
})

// 监听路由变化，当从创建页面返回时刷新数据
watch(() => route.path, (newPath, oldPath) => {
  if (newPath === '/projects' && oldPath && oldPath.includes('/projects/create')) {
    fetchProjects()
  }
})
</script>

<style lang="scss" scoped>
.projects-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--crf-card-bg);
    border-radius: 8px;

    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;

      .batch-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 16px;
        background: var(--n-color-target);
        border-radius: 6px;
        border: 1px solid var(--n-border-color);

        .selected-count {
          font-size: 12px;
          color: var(--n-text-color-2);
        }
      }
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 32px;

    .project-card {
      background: var(--crf-card-bg);
      border: 1px solid var(--crf-border-color);
      border-radius: 12px;
      padding: 24px;
      cursor: pointer;
      position: relative;

      &:hover {
        box-shadow: none;
        border-color: var(--primary-color);
        
        // 悬停时显示删除按钮
        .card-actions .delete-btn {
          opacity: 1 !important;
          visibility: visible !important;
        }
      }

      &.selected {
        border-color: var(--primary-color);
        background: var(--n-color-target);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .project-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;

          .project-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 8px;
          }
        }

        .card-actions {
          display: flex;
          align-items: center;
          gap: 8px;

          .project-checkbox {
            margin-right: 4px;
          }

          .delete-btn {
            opacity: 0 !important;
            visibility: hidden !important;
            transition: all 0.2s ease !important;
          }
        }
      }

      .card-content {
        .project-title {
          font-size: 18px;
          font-weight: bold;
          color: var(--crf-text-color);
          margin: 0 0 8px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .project-description {
          font-size: 14px;
          font-weight: bold;
          color: var(--crf-text-color-secondary);
          margin: 0 0 20px 0;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .project-stats {
          display: flex;
          justify-content: space-around;
          padding: 16px 0;
          border: 1px solid var(--crf-border-color);
          border-radius: 8px;
          background: var(--gray-100);

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .stat-value {
              font-size: 20px;
              font-weight: bold;
              color: var(--primary-color);
            }

            .stat-label {
              font-size: 12px;
              font-weight: bold;
              color: var(--crf-text-color-secondary);
              margin-top: 2px;
            }
          }
        }
      }

      .card-footer {
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid var(--crf-border-color);

        .project-meta {
          display: flex;
          gap: 16px;
          margin-bottom: 12px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: bold;
            color: var(--crf-text-color-secondary);

            .n-icon {
              font-size: 14px;
            }
          }
        }

        .actions {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 32px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .projects-page {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left {
        flex-direction: column;
        align-items: stretch;

        .n-input, .n-select {
          width: 100% !important;
        }
      }
    }

    .projects-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>