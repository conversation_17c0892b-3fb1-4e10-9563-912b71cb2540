<template>
  <div class="project-edit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <n-button text @click="$router.back()" class="back-button">
            <template #icon>
              <n-icon><ArrowBack /></n-icon>
            </template>
            返回
          </n-button>
          <h1 class="page-title">编辑项目</h1>
        </div>
        <div class="header-actions">
          <n-button @click="handleCancel">取消</n-button>
          <n-button type="primary" @click="handleSave" :loading="saving">
            保存
          </n-button>
        </div>
      </div>
    </div>

    <!-- 编辑表单 -->
    <n-card class="form-card">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="项目名称" path="name">
              <n-input
                v-model:value="formData.name"
                placeholder="请输入项目名称"
                maxlength="100"
                show-count
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="项目类型" path="project_type">
              <n-select
                v-model:value="formData.project_type"
                :options="projectTypeOptions"
                placeholder="请选择项目类型"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-form-item label="项目描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入项目描述"
            :rows="4"
            maxlength="500"
            show-count
          />
        </n-form-item>

        <n-grid :cols="3" :x-gap="24">
          <n-grid-item>
            <n-form-item label="项目状态" path="status">
              <n-select
                v-model:value="formData.status"
                :options="statusOptions"
                placeholder="请选择项目状态"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="开始日期" path="start_date">
              <n-date-picker
                v-model:value="formData.start_date"
                type="date"
                placeholder="请选择开始日期"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="结束日期" path="end_date">
              <n-date-picker
                v-model:value="formData.end_date"
                type="date"
                placeholder="请选择结束日期"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-form-item label="项目分类" path="category">
          <n-input
            v-model:value="formData.category"
            placeholder="请输入项目分类（可选）"
            maxlength="50"
          />
        </n-form-item>
      </n-form>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'
import { ArrowBack } from '@vicons/ionicons5'
import { projectAPI } from '@/api'

defineOptions({
  name: 'ProjectEditPage'
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const saving = ref(false)
const formData = reactive({
  name: '',
  description: '',
  project_type: 'clinical_trial',
  status: 'draft',
  category: '',
  start_date: null as number | null,
  end_date: null as number | null
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  project_type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' }
  ]
}

// 选项数据
const projectTypeOptions = [
  { label: '临床试验', value: 'clinical_trial' },
  { label: '观察性研究', value: 'observational_study' },
  { label: '登记研究', value: 'registry' },
  { label: '调查研究', value: 'survey' },
  { label: '其他', value: 'other' }
]

const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '进行中', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已归档', value: 'archived' }
]

// 获取项目详情
const fetchProjectDetail = async () => {
  try {
    const projectId = route.params.id as string
    const response = await projectAPI.getProject(projectId)
    
    if (response.success && response.data) {
      const project = response.data
      formData.name = project.name || ''
      formData.description = project.description || ''
      formData.project_type = project.project_type || 'clinical_trial'
      formData.status = project.status || 'draft'
      formData.category = project.category || ''
      formData.start_date = project.start_date ? new Date(project.start_date).getTime() : null
      formData.end_date = project.end_date ? new Date(project.end_date).getTime() : null
    }
  } catch (error) {
    console.error('获取项目详情失败:', error)
    message.error('获取项目详情失败')
  }
}

// 事件处理
const handleCancel = () => {
  router.back()
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true
    
    const projectId = route.params.id as string
    const updateData = {
      name: formData.name,
      description: formData.description,
      project_type: formData.project_type,
      status: formData.status,
      category: formData.category,
      start_date: formData.start_date ? new Date(formData.start_date).toISOString().split('T')[0] : null,
      end_date: formData.end_date ? new Date(formData.end_date).toISOString().split('T')[0] : null
    }
    
    const response = await projectAPI.updateProject(projectId, updateData)
    
    if (response.success) {
      message.success('项目更新成功')
      router.push(`/projects/${projectId}`)
    } else {
      message.error(response.message || '项目更新失败')
    }
  } catch (error) {
    console.error('保存项目失败:', error)
    message.error('保存项目失败')
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchProjectDetail()
})
</script>

<style lang="scss" scoped>
.project-edit-page {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--n-text-color);
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.form-card {
  .n-form {
    .n-form-item {
      margin-bottom: 24px;
    }
  }
}
</style>
