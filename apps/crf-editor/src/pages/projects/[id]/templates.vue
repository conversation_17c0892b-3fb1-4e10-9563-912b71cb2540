<template>
  <div class="project-templates-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <n-breadcrumb>
          <n-breadcrumb-item @click="$router.push('/projects')">
            <n-icon><FolderOutline /></n-icon>
            项目管理
          </n-breadcrumb-item>
          <n-breadcrumb-item>
            {{ project?.name || '项目详情' }}
          </n-breadcrumb-item>
        </n-breadcrumb>
        <h1 class="page-title">{{ project?.name || '项目模板' }}</h1>
        <p class="page-description">{{ project?.description || '管理项目下的CRF模板' }}</p>
      </div>
      <div class="header-right">
        <n-button type="primary" @click="handleCreateTemplate">
          <template #icon>
            <n-icon><Add /></n-icon>
          </template>
          创建模板
        </n-button>
      </div>
    </div>

    <!-- 项目统计卡片 -->
    <div class="stats-cards" v-if="statistics">
      <n-card class="stat-card">
        <n-statistic label="模板总数" :value="statistics.template_count" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="已发布模板" :value="statistics.published_template_count" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="实例总数" :value="statistics.instance_count" />
      </n-card>
      <n-card class="stat-card">
        <n-statistic label="已完成实例" :value="statistics.completed_instance_count" />
      </n-card>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <n-space>
        <n-input
          v-model:value="filters.search"
          placeholder="搜索模板..."
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        <n-select
          v-model:value="filters.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          @update:value="handleSearch"
        >
          <n-option value="draft" label="草稿" />
          <n-option value="published" label="已发布" />
          <n-option value="archived" label="已归档" />
        </n-select>
        <n-button @click="handleRefreshStats">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新统计
        </n-button>
      </n-space>
    </div>

    <!-- 模板列表 -->
    <n-spin :show="loading">
      <div class="templates-grid" v-if="templates.length > 0">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-card"
          @click="handleViewTemplate(template)"
        >
          <n-card hoverable>
            <div class="card-header">
              <div class="template-icon">
                <n-icon size="24" :color="template.icon_color || '#3b82f6'">
                  <component :is="getIconComponent(template.icon)" />
                </n-icon>
              </div>
              <div class="template-status">
                <n-tag :type="getStatusType(template.status)" size="small">
                  {{ getStatusText(template.status) }}
                </n-tag>
              </div>
            </div>
            <div class="card-content">
              <h3 class="template-title">{{ template.title || template.name }}</h3>
              <p class="template-description">{{ template.description || '暂无描述' }}</p>
              <div class="template-meta">
                <span class="meta-item">版本: {{ template.version }}</span>
                <span class="meta-item">创建: {{ formatDate(template.created_at) }}</span>
              </div>
            </div>
            <div class="card-actions">
              <n-button size="small" @click.stop="handleEditTemplate(template)">
                编辑
              </n-button>
              <n-button size="small" @click.stop="handleViewData(template)">
                查看数据
              </n-button>
            </div>
          </n-card>
        </div>
      </div>

      <!-- 空状态 -->
      <n-empty
        v-else-if="!loading"
        description="暂无模板"
        :size="120"
      >
        <template #extra>
          <n-button type="primary" @click="handleCreateTemplate">
            创建第一个模板
          </n-button>
        </template>
      </n-empty>
    </n-spin>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <n-pagination
        v-model:page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :item-count="pagination.total"
        :page-sizes="[12, 24, 48]"
        show-size-picker
        show-quick-jumper
        @update:page="handlePageChange"
        @update:page-size="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { 
  FolderOutline, 
  Add, 
  SearchOutline, 
  RefreshOutline,
  DocumentTextOutline 
} from '@vicons/ionicons5'
import { projectAPI } from '@/api'

defineOptions({
  name: 'ProjectTemplatesPage'
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const project = ref<Record<string, unknown> | null>(null)
const statistics = ref<Record<string, unknown> | null>(null)
const templates = ref<Record<string, unknown>[]>([])

// 筛选条件
const filters = reactive({
  search: '',
  status: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0
})

// 计算属性
const projectId = computed(() => route.params.id as string)

// 获取项目信息
const fetchProject = async () => {
  try {
    const response = await projectAPI.getProject(projectId.value)
    if (response.success && response.data) {
      project.value = response.data.project
    }
  } catch (error) {
    console.error('获取项目信息失败:', error)
    message.error('获取项目信息失败')
  }
}

// 获取项目统计
const fetchStatistics = async () => {
  try {
    const response = await projectAPI.getProjectStatistics(projectId.value)
    if (response.success && response.data) {
      statistics.value = response.data.statistics
    }
  } catch (error) {
    console.error('获取项目统计失败:', error)
  }
}

// 获取模板列表
const fetchTemplates = async () => {
  try {
    loading.value = true
    
    const params = {
      limit: pagination.pageSize,
      offset: (pagination.current - 1) * pagination.pageSize,
      search: filters.search || undefined,
      status: filters.status || undefined
    }
    
    const response = await projectAPI.getProjectTemplates(projectId.value, params)
    if (response.success && response.data) {
      templates.value = response.data.templates || []
      pagination.total = response.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  fetchTemplates()
}

const handlePageChange = () => {
  fetchTemplates()
}

const handleSizeChange = () => {
  pagination.current = 1
  fetchTemplates()
}

const handleRefreshStats = async () => {
  try {
    await projectAPI.refreshProjectStats(projectId.value)
    message.success('统计信息已刷新')
    await fetchStatistics()
  } catch (error) {
    console.error('刷新统计失败:', error)
    message.error('刷新统计失败')
  }
}

const handleCreateTemplate = () => {
  router.push(`/editor?project_id=${projectId.value}`)
}

const handleViewTemplate = (template: Record<string, unknown>) => {
  router.push(`/forms/${template.id}/data`)
}

const handleEditTemplate = (template: Record<string, unknown>) => {
  router.push(`/editor?id=${template.id}`)
}

const handleViewData = (template: Record<string, unknown>) => {
  router.push(`/forms/${template.id}/data`)
}

// 辅助函数
const getIconComponent = (iconName: string) => {
  // 这里可以根据iconName返回对应的图标组件
  return DocumentTextOutline
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'warning'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return statusMap[status] || status
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  fetchProject()
  fetchStatistics()
  fetchTemplates()
})
</script>

<style lang="scss" scoped>
.project-templates-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-left {
    .page-title {
      margin: 8px 0 4px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
    }

    .page-description {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-card {
    text-align: center;
  }
}

.filter-toolbar {
  margin-bottom: 24px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .template-card {
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .card-content {
      .template-title {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }

      .template-description {
        margin: 0 0 12px 0;
        color: #6b7280;
        font-size: 14px;
        line-height: 1.5;
      }

      .template-meta {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #9ca3af;

        .meta-item {
          &:not(:last-child)::after {
            content: '•';
            margin-left: 12px;
          }
        }
      }
    }

    .card-actions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style>
