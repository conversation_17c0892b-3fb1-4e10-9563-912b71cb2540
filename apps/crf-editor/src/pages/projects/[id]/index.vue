<template>
  <div class="project-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <n-button text @click="$router.back()" class="back-button">
            <template #icon>
              <n-icon><ArrowBack /></n-icon>
            </template>
            返回
          </n-button>
          <div class="project-info">
            <h1 class="project-title">{{ project?.name || '加载中...' }}</h1>
            <div class="project-meta">
              <n-tag :type="getStatusType(project?.status)" size="small">
                {{ getStatusText(project?.status) }}
              </n-tag>
              <n-tag type="info" size="small">
                {{ getProjectTypeText(project?.project_type) }}
              </n-tag>
              <span class="meta-item">
                <n-icon><Calendar /></n-icon>
                创建于 {{ formatDate(project?.created_at) }}
              </span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <n-button @click="handleEditProject">
            <template #icon>
              <n-icon><Edit /></n-icon>
            </template>
            编辑项目
          </n-button>
          <n-button type="primary" @click="handleCreateTemplate">
            <template #icon>
              <n-icon><Plus /></n-icon>
            </template>
            创建模板
          </n-button>
        </div>
      </div>
    </div>

    <!-- 项目统计卡片 -->
    <div class="stats-section">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon templates">
                <n-icon><Document /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.template_count || 0 }}</div>
                <div class="stat-label">模板数量</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon instances">
                <n-icon><Copy /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.instance_count || 0 }}</div>
                <div class="stat-label">实例数量</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon members">
                <n-icon><People /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.member_count || 0 }}</div>
                <div class="stat-label">成员数量</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon data">
                <n-icon><BarChart /></n-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.data_count || 0 }}</div>
                <div class="stat-label">数据条数</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 项目描述 -->
    <n-card v-if="project?.description" class="description-card">
      <template #header>
        <div class="card-header">
          <n-icon><InformationCircle /></n-icon>
          项目描述
        </div>
      </template>
      <div class="project-description">
        {{ project.description }}
      </div>
    </n-card>

    <!-- 快速操作 -->
    <n-card class="actions-card">
      <template #header>
        <div class="card-header">
          <n-icon><Flash /></n-icon>
          快速操作
        </div>
      </template>
      <div class="quick-actions">
        <n-button-group>
          <n-button @click="handleViewTemplates">
            <template #icon>
              <n-icon><Document /></n-icon>
            </template>
            模板管理
          </n-button>
          <n-button @click="handleViewInstances">
            <template #icon>
              <n-icon><Copy /></n-icon>
            </template>
            实例管理
          </n-button>
          <n-button @click="handleViewData">
            <template #icon>
              <n-icon><BarChart /></n-icon>
            </template>
            数据管理
          </n-button>
        </n-button-group>
      </div>
    </n-card>

    <!-- 最近活动 -->
    <n-card class="activity-card">
      <template #header>
        <div class="card-header">
          <n-icon><Time /></n-icon>
          最近活动
        </div>
      </template>
      <div class="activity-list">
        <n-empty v-if="!activities.length" description="暂无活动记录" />
        <div v-else class="activity-item" v-for="activity in activities" :key="activity.id">
          <div class="activity-icon">
            <n-icon><Document /></n-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ formatDate(activity.created_at) }}</div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline as ArrowBack, CreateOutline as Edit, Add as Plus,
  DocumentTextOutline as Document, CopyOutline as Copy, PeopleOutline as People,
  BarChartOutline as BarChart, InformationCircleOutline as InformationCircle,
  FlashOutline as Flash, TimeOutline as Time, CalendarOutline as Calendar
} from '@vicons/ionicons5'
import { projectAPI } from '@/api'

defineOptions({
  name: 'ProjectDetailPage'
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const project = ref<Record<string, any> | null>(null)
const statistics = ref<Record<string, any> | null>(null)
const activities = ref<Record<string, any>[]>([])

// 项目状态映射
const statusMap = {
  draft: { type: 'default', text: '草稿' },
  active: { type: 'success', text: '进行中' },
  completed: { type: 'info', text: '已完成' },
  archived: { type: 'warning', text: '已归档' }
}

// 项目类型映射
const projectTypeMap = {
  clinical_trial: '临床试验',
  observational_study: '观察性研究',
  registry: '登记研究',
  survey: '调查研究',
  other: '其他'
}

// 工具函数
const getStatusType = (status: string) => {
  return statusMap[status as keyof typeof statusMap]?.type || 'default'
}

const getStatusText = (status: string) => {
  return statusMap[status as keyof typeof statusMap]?.text || status
}

const getProjectTypeText = (type: string) => {
  return projectTypeMap[type as keyof typeof projectTypeMap] || type
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取项目详情
const fetchProjectDetail = async () => {
  try {
    loading.value = true
    const projectId = route.params.id as string
    
    // 获取项目基本信息
    const projectResponse = await projectAPI.getProject(projectId)
    if (projectResponse.success && projectResponse.data) {
      project.value = projectResponse.data
    }
    
    // 获取项目统计信息
    const statsResponse = await projectAPI.getProjectStatistics(projectId)
    if (statsResponse.success && statsResponse.data) {
      statistics.value = statsResponse.data
    }
    
  } catch (error) {
    console.error('获取项目详情失败:', error)
    message.error('获取项目详情失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleEditProject = () => {
  router.push(`/projects/${route.params.id}/edit`)
}

const handleCreateTemplate = () => {
  router.push(`/templates/create?projectId=${route.params.id}`)
}

const handleViewTemplates = () => {
  router.push(`/projects/${route.params.id}/templates`)
}

const handleViewInstances = () => {
  router.push(`/instances?projectId=${route.params.id}`)
}

const handleViewData = () => {
  router.push(`/data?projectId=${route.params.id}`)
}

// 生命周期
onMounted(() => {
  fetchProjectDetail()
})
</script>

<style lang="scss" scoped>
.project-detail-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .header-left {
    display: flex;
    align-items: flex-start;
    gap: 16px;
  }
  
  .back-button {
    margin-top: 8px;
  }
  
  .project-info {
    .project-title {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      color: var(--n-text-color);
    }
    
    .project-meta {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--n-text-color-2);
        font-size: 14px;
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-section {
  margin-bottom: 24px;
  
  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      
      &.templates {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      &.instances {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }
      
      &.members {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }
      
      &.data {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
      }
    }
    
    .stat-info {
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--n-text-color);
        line-height: 1;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--n-text-color-2);
        margin-top: 4px;
      }
    }
  }
}

.description-card,
.actions-card,
.activity-card {
  margin-bottom: 24px;
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
}

.project-description {
  line-height: 1.6;
  color: var(--n-text-color-2);
}

.quick-actions {
  .n-button-group {
    width: 100%;
    
    .n-button {
      flex: 1;
    }
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--n-divider-color);
    
    &:last-child {
      border-bottom: none;
    }
    
    .activity-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: var(--n-color-target);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--n-text-color-2);
    }
    
    .activity-content {
      flex: 1;
      
      .activity-title {
        font-weight: 500;
        color: var(--n-text-color);
        margin-bottom: 4px;
      }
      
      .activity-time {
        font-size: 12px;
        color: var(--n-text-color-3);
      }
    }
  }
}
</style>
