<template>
  <div class="users-page">
    <div class="users-container">
      <h1>用户管理</h1>
      <div class="users-list">
        <div v-for="user in users" :key="user.id" class="user-item">
          <span>{{ user.name }}</span>
          <div class="user-actions">
            <button @click="handleEditUser(user)">编辑</button>
            <button @click="handleDeleteUser(user)">删除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const users = ref<Record<string, unknown>[]>([])
const selectedUser = ref<Record<string, unknown> | null>(null)

const handleUserAction = (action: string, user: Record<string, unknown>) => {
  console.log(action, user)
}

const handleEditUser = (user: Record<string, unknown>) => {
  selectedUser.value = user
}

const handleDeleteUser = (user: Record<string, unknown>) => {
  const index = users.value.findIndex(u => u.id === user.id)
  if (index > -1) {
    users.value.splice(index, 1)
  }
}
</script>

<style scoped>
.users-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.users-container {
  padding: 20px;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 10px 0;
  background: white;
  border-radius: 4px;
}

.user-actions button {
  margin-left: 10px;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>