<template>
  <div class="admin-dashboard">
    <v-container fluid>
      <v-row>
        <v-col cols="12">
          <h1 class="text-h4 mb-6">系统管理</h1>
        </v-col>
      </v-row>
      
      <v-row>
        <v-col cols="12" md="6">
          <v-card class="h-100">
            <v-card-title class="d-flex align-center">
              <v-icon color="primary" class="mr-3">mdi-account-group</v-icon>
              用户管理
            </v-card-title>
            <v-card-text>
              管理系统用户，分配角色和权限
            </v-card-text>
            <v-card-actions>
              <v-btn 
                color="primary" 
                variant="elevated"
                @click="$router.push('/admin/users')"
                :disabled="!canManageUsers"
              >
                <v-icon start>mdi-account-edit</v-icon>
                管理用户
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
        
        <v-col cols="12" md="6">
          <v-card class="h-100">
            <v-card-title class="d-flex align-center">
              <v-icon color="secondary" class="mr-3">mdi-shield-account</v-icon>
              角色管理
            </v-card-title>
            <v-card-text>
              管理系统角色和权限配置
            </v-card-text>
            <v-card-actions>
              <v-btn 
                color="secondary" 
                variant="elevated"
                @click="$router.push('/admin/roles')"
                :disabled="!canManageRoles"
              >
                <v-icon start>mdi-shield-edit</v-icon>
                管理角色
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
      
      <v-row class="mt-6">
        <v-col cols="12">
          <v-alert
            v-if="!canManageUsers && !canManageRoles"
            type="warning"
            variant="tonal"
          >
            <v-alert-title>权限不足</v-alert-title>
            您没有管理系统的权限。请联系管理员获取相应权限。
          </v-alert>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useUserStore } from '@/stores/user-store'

// Stores
const userStore = useUserStore()

// 响应式数据
const adminData = ref<Record<string, unknown>>({})
const systemStats = ref<Record<string, unknown> | null>(null)

// 处理管理员操作
const handleAdminAction = (action: string, data: Record<string, unknown>) => {
  console.log('Admin action:', action, data)
}

// 计算属性
const canManageUsers = computed(() => 
  userStore.hasPermission('user', 'create') ||
  userStore.hasPermission('user', 'update') ||
  userStore.hasPermission('user', 'read', 'global')
)

const canManageRoles = computed(() => 
  userStore.hasPermission('role', 'create') ||
  userStore.hasPermission('role', 'update') ||
  userStore.hasPermission('role', 'read')
)
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.v-card {
  transition: transform 0.2s ease-in-out;
}

.v-card:hover {
  transform: translateY(-2px);
}
</style>