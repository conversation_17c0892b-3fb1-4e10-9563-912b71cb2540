<template>
  <div class="settings-page">
    <div class="page-header">
      <h1 class="page-title">系统设置</h1>
      <p class="page-description">管理系统配置和用户设置</p>
    </div>

    <n-tabs v-model:value="activeTab" class="settings-tabs" type="line">
      <!-- 基本设置 -->
      <n-tab-pane name="basic" tab="基本设置">
        <div class="settings-section">
          <h3>系统信息</h3>
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="系统版本">v1.0.0</n-descriptions-item>
            <n-descriptions-item label="构建时间">2024-01-15</n-descriptions-item>
            <n-descriptions-item label="API版本">v1</n-descriptions-item>
            <n-descriptions-item label="数据库版本">PostgreSQL 13</n-descriptions-item>
          </n-descriptions>
        </div>
      </n-tab-pane>

      <!-- 权限管理 -->
      <n-tab-pane name="permissions" tab="权限管理">
        <div class="settings-section">
          <h3>用户权限</h3>
          <n-alert
            title="权限管理功能开发中"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </n-tab-pane>

      <!-- 数据配置 -->
      <n-tab-pane name="data" tab="数据配置">
        <div class="settings-section">
          <h3>数据设置</h3>
          <n-alert
            title="数据配置功能开发中"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

defineOptions({
  name: 'SettingsPage'
})

const activeTab = ref('basic')
</script>

<style lang="scss" scoped>
.settings-page {
  .page-header {
    margin-bottom: 24px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--crf-text-color);
      margin: 0 0 8px 0;
    }

    .page-description {
      font-size: 14px;
      color: var(--crf-text-color-secondary);
      margin: 0;
    }
  }

  .settings-tabs {
    background: var(--crf-card-bg);
    border-radius: 8px;
    padding: 24px;

    .settings-section {
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 16px 0;
      }
    }
  }
}
</style>