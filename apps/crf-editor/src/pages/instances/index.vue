<template>
  <div class="instances-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">数据管理</h1>
        <p class="page-description">管理数据实例和数据记录</p>
      </div>
      <div class="header-right">
        <n-button type="primary" @click="handleCreateInstance">
          <template #icon>
            <n-icon><PlusIcon /></n-icon>
          </template>
          创建实例
        </n-button>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <n-input
          v-model:value="filters.search"
          placeholder="搜索实例名称或受试者ID"
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon><MagnifyingGlassIcon /></n-icon>
          </template>
        </n-input>
        <n-select
          v-model:value="filters.templateId"
          placeholder="选择模板"
          clearable
          style="width: 200px"
          @update:value="handleTemplateChange"
        >
          <n-option
            v-for="template in templates"
            :key="template.id"
            :label="template.title"
            :value="template.id"
          />
        </n-select>
        <n-select
          v-model:value="filters.status"
          placeholder="实例状态"
          clearable
          style="width: 150px"
          @update:value="handleStatusChange"
        >
          <n-option label="草稿" value="draft" />
          <n-option label="已提交" value="submitted" />
          <n-option label="已锁定" value="locked" />
          <n-option label="已完成" value="completed" />
        </n-select>
      </div>
      <div class="toolbar-right">
        <n-button @click="handleRefresh">
          <template #icon>
            <n-icon><ArrowPathIcon /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button @click="handleBatchExport">
          <template #icon>
            <n-icon><ArrowDownTrayIcon /></n-icon>
          </template>
          批量导出
        </n-button>
      </div>
    </div>

    <!-- 实例表格 -->
    <div class="instances-table">
      <n-data-table
        :loading="loading"
        :data="instances"
        :columns="tableColumns"
        :row-key="(row: Record<string, unknown>) => row.id"
        :checked-row-keys="selectedRowKeys"
        @update:checked-row-keys="handleSelectionChange"
        striped
        :bordered="true"
      />
    </div>

    <!-- 空状态 -->
    <n-empty
      v-if="!loading && instances.length === 0"
      description="暂无实例数据"
      :size="120"
    >
      <template #extra>
        <n-button type="primary" @click="handleCreateInstance">
          创建第一个实例
        </n-button>
      </template>
    </n-empty>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <n-pagination
        v-model:page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :item-count="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        show-size-picker
        show-quick-jumper
        @update:page="handleCurrentChange"
        @update:page-size="handleSizeChange"
      />
    </div>
    
    <!-- 实例详情对话框 -->
    <instance-detail-dialog
      v-model="showDetailDialog"
      :instance="selectedInstance"
      @instance-updated="handleInstanceUpdated"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, useDialog, NButton, NTag, NProgress, NDropdown, NIcon, NInput, NSelect, NDataTable, NEmpty, NPagination } from 'naive-ui'
import { 
  AddOutline as PlusIcon, 
  SearchOutline as MagnifyingGlassIcon, 
  RefreshOutline as ArrowPathIcon,
  DownloadOutline as ArrowDownTrayIcon,
  ChevronDownOutline as ChevronDownIcon,
  EyeOutline as EyeIcon,
  CopyOutline as CopyIcon,
  LockClosedOutline as LockClosedIcon,
  LockOpenOutline as LockOpenIcon,
  TrashOutline as TrashIcon
} from '@vicons/ionicons5'
// 重复导入已删除，使用上面的导入
import { instanceAPI, templateAPI } from '@/api'
import { getErrorMessage } from '@/utils/error-messages'
import InstanceDetailDialog from '@/components/instances/InstanceDetailDialog.vue'

defineOptions({
  name: 'InstancesPage'
})

// 组件注册
const components = {
  InstanceDetailDialog
}

const route = useRoute()
const router = useRouter()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const instances = ref<Record<string, unknown>[]>([])
const templates = ref<Record<string, unknown>[]>([])
const selectedRows = ref<Record<string, unknown>[]>([])
const selectedRowKeys = ref<string[]>([])
const showDetailDialog = ref(false)
const selectedInstance = ref<Record<string, unknown> | null>(null)

// 筛选条件
const filters = reactive({
  search: '',
  templateId: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 表格列定义
const tableColumns = [
  {
    type: 'selection'
  },
  {
    title: '实例名称',
    key: 'instance_name',
    minWidth: 200,
    render: (row: Record<string, unknown>) => {
      return h('div', { class: 'instance-info' }, [
        h('h4', { class: 'instance-name' }, row.instance_name || `实例_${row.id}`),
        h('p', { class: 'instance-id' }, `ID: ${row.id}`)
      ])
    }
  },
  {
    title: '模板',
    key: 'template_name',
    width: 180,
    render: (row: Record<string, unknown>) => {
      return h('div', { class: 'template-info' }, [
        h('span', { class: 'template-name' }, row.template_name),
        h(NTag, { size: 'small', type: 'info' }, { default: () => `v${row.template_version}` })
      ])
    }
  },
  {
    title: '受试者ID',
    key: 'subject_id',
    width: 120
  },
  {
    title: '访问ID',
    key: 'visit_id',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: Record<string, unknown>) => {
      return h(NTag, { 
        type: getStatusType(row.status), 
        size: 'small' 
      }, { default: () => getStatusText(row.status) })
    }
  },
  {
    title: '完成进度',
    key: 'progress',
    width: 120,
    render: (row: Record<string, unknown>) => {
      const progress = row.progress || 0
      return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
        h(NProgress, {
          percentage: progress,
          strokeWidth: 6,
          showIndicator: false,
          color: getProgressColor(progress)
        }),
        h('span', { class: 'progress-text' }, `${progress}%`)
      ])
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render: (row: Record<string, unknown>) => formatDateTime(row.created_at)
  },
  {
    title: '更新时间',
    key: 'updated_at',
    width: 180,
    render: (row: Record<string, unknown>) => formatDateTime(row.updated_at)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: Record<string, unknown>) => {
      const dropdownOptions = [
        {
          label: '查看详情',
          key: 'view',
          icon: () => h(NIcon, null, { default: () => h(EyeIcon) })
        },
        {
          label: '复制实例',
          key: 'duplicate',
          icon: () => h(NIcon, null, { default: () => h(CopyIcon) })
        },
        {
           label: '导出数据',
           key: 'export',
           icon: () => h(NIcon, null, { default: () => h(ArrowDownTrayIcon) })
         }
      ]
      
      if (row.status === 'submitted') {
        dropdownOptions.push({
          label: '锁定实例',
          key: 'lock',
          icon: () => h(NIcon, null, { default: () => h(LockClosedIcon) })
        })
      }
      
      if (row.status === 'locked') {
        dropdownOptions.push({
          label: '解锁实例',
          key: 'unlock',
          icon: () => h(NIcon, null, { default: () => h(LockOpenIcon) })
        })
      }
      
      dropdownOptions.push({
        type: 'divider'
      })
      
      dropdownOptions.push({
        label: '删除实例',
        key: 'delete',
        icon: () => h(NIcon, null, { default: () => h(TrashIcon) }),
        props: {
          style: 'color: #d03050;'
        }
      })
      
      return h('div', { class: 'table-actions' }, [
        h(NButton, {
          type: 'primary',
          size: 'small',
          onClick: () => handleEditInstance(row)
        }, { default: () => '编辑' }),
        row.status === 'draft' ? h(NButton, {
          type: 'success',
          size: 'small',
          onClick: () => handleSubmitInstance(row)
        }, { default: () => '提交' }) : null,
        h(NDropdown, {
          options: dropdownOptions,
          onSelect: (key: string) => handleInstanceAction(key, row)
        }, {
          default: () => h(NButton, { size: 'small' }, {
            default: () => '更多',
            icon: () => h(NIcon, null, { default: () => h(ChevronDownIcon) })
          })
        })
      ].filter(Boolean))
    }
  }
]

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'submitted': return 'primary'
    case 'locked': return 'warning'
    case 'draft': return 'info'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'submitted': return '已提交'
    case 'locked': return '已锁定'
    case 'draft': return '草稿'
    default: return '未知'
  }
}

// 获取进度颜色
const getProgressColor = (progress: number) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取实例列表
const fetchInstances = async () => {
  try {
    loading.value = true
    
    const params: Record<string, unknown> = {
      limit: pagination.pageSize,
      offset: (pagination.current - 1) * pagination.pageSize
    }
    
    if (filters.templateId) {
      params.template_id = filters.templateId
    }
    
    if (filters.status) {
      params.status = filters.status
    }
    
    const response = await instanceAPI.getInstances(params)
    if (response.success && response.data) {
      instances.value = response.data.instances || []
      pagination.total = response.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('获取实例列表失败:', error)
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '获取实例列表失败，请稍后重试'
    })
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 获取模板列表
const fetchTemplates = async () => {
  try {
    const response = await templateAPI.getTemplates({ limit: 100 })
    if (response.success && response.data) {
      templates.value = response.data.templates || []
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  fetchInstances()
}

const handleTemplateChange = () => {
  pagination.current = 1
  fetchInstances()
}

const handleStatusChange = () => {
  pagination.current = 1
  fetchInstances()
}

const handleRefresh = () => {
  fetchInstances()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  fetchInstances()
}

const handleCurrentChange = (page: number) => {
  pagination.current = page
  fetchInstances()
}

const handleSelectionChange = (rowKeys: string[]) => {
  selectedRowKeys.value = rowKeys
  selectedRows.value = instances.value.filter(instance => rowKeys.includes(instance.id))
}

const handleCreateInstance = () => {
  router.push('/instances/create')
}

const handleEditInstance = (instance: Record<string, unknown>) => {
  router.push(`/instances/${instance.id}/edit`)
}

const handleSubmitInstance = async (instance: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认提交',
        content: `确定要提交实例 "${instance.instance_name}" 吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          resolve(true)
        },
        onNegativeClick: () => {
          reject('cancel')
        }
      })
    })

    const response = await instanceAPI.submitInstance(instance.id)
    if (response.success) {
      message.success('实例提交成功')
      fetchInstances()
    }
  } catch (error) {
    if (error !== 'cancel') {
      message.error('提交实例失败')
    }
  }
}

const handleInstanceAction = async (command: string, instance: Record<string, unknown>) => {
  switch (command) {
    case 'view':
      selectedInstance.value = instance
      showDetailDialog.value = true
      break
    case 'duplicate':
      handleDuplicateInstance(instance)
      break
    case 'export':
      handleExportInstance(instance)
      break
    case 'lock':
      handleLockInstance(instance)
      break
    case 'unlock':
      handleUnlockInstance(instance)
      break
    case 'delete':
      handleDeleteInstance(instance)
      break
  }
}

const handleDuplicateInstance = async (instance: Record<string, unknown>) => {
  try {
    const name = await new Promise<string>((resolve, reject) => {
      dialog.create({
        title: '复制实例',
        content: () => {
          const inputRef = ref(`${instance.instance_name}_copy`)
          return h('div', [
            h('p', { style: { marginBottom: '12px' } }, '请输入新实例的名称'),
            h(NInput, {
              value: inputRef.value,
              onUpdateValue: (value: string) => {
                inputRef.value = value
              },
              placeholder: '请输入实例名称'
            })
          ])
        },
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          // TODO: 获取输入值
          resolve(`${instance.instance_name}_copy`)
        },
        onNegativeClick: () => {
          reject('cancel')
        }
      })
    })

    // TODO: 调用复制API
    message.success('实例复制成功')
    fetchInstances()
  } catch (error) {
    if (error !== 'cancel') {
      message.error('复制实例失败')
    }
  }
}

const handleExportInstance = (_instance: Record<string, unknown>) => {
  message.info('导出功能开发中...')
}

const handleLockInstance = async (instance: Record<string, unknown>) => {
  try {
    const response = await instanceAPI.lockInstance(instance.id)
    if (response.success) {
      message.success('实例锁定成功')
      fetchInstances()
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '锁定实例失败，请稍后重试'
    })
    message.error(errorMessage)
  }
}

const handleUnlockInstance = async (instance: Record<string, unknown>) => {
  try {
    const response = await instanceAPI.unlockInstance(instance.id)
    if (response.success) {
      message.success('实例解锁成功')
      fetchInstances()
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '解锁实例失败，请稍后重试'
    })
    message.error(errorMessage)
  }
}

const handleDeleteInstance = async (instance: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.error({
        title: '确认删除',
        content: `确定要删除实例 "${instance.instance_name}" 吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => {
          resolve(true)
        },
        onNegativeClick: () => {
          reject('cancel')
        }
      })
    })

    const response = await instanceAPI.deleteInstance(instance.id)
    if (response.success) {
      message.success('实例删除成功')
      fetchInstances()
    }
  } catch (error) {
    if (error !== 'cancel') {
      const errorMessage = getErrorMessage(error, {
        network: '网络连接失败，请检查网络设置',
        default: '删除实例失败，请稍后重试'
      })
      message.error(errorMessage)
    }
  }
}

const handleBatchExport = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要导出的实例')
    return
  }
  message.info('批量导出功能开发中...')
}

// 处理实例更新
const handleInstanceUpdated = () => {
  fetchInstances()
}

// 生命周期
onMounted(() => {
  // 从URL参数获取模板ID
  const templateId = route.query.template_id as string
  if (templateId) {
    filters.templateId = templateId
  }
  
  fetchTemplates()
  fetchInstances()
})
</script>

<style lang="scss" scoped>
.instances-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--crf-card-bg);
    border-radius: 8px;

    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .instances-table {
    background: var(--crf-card-bg);
    border-radius: 8px;
    overflow: hidden;

    .instance-info {
      .instance-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--crf-text-color);
        margin: 0 0 4px 0;
      }

      .instance-id {
        font-size: 12px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }

    .template-info {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .template-name {
        font-size: 14px;
        color: var(--crf-text-color);
      }
    }

    .progress-text {
      font-size: 12px;
      color: var(--crf-text-color-secondary);
      margin-left: 8px;
    }

    .table-actions {
      display: flex;
      gap: 4px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .instances-page {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left {
        flex-direction: column;
        align-items: stretch;

        .n-input, .n-select {
          width: 100% !important;
        }
      }
    }

    .table-actions {
      flex-direction: column;
      gap: 4px;

      .n-button {
        width: 100%;
      }
    }
  }
}
</style>