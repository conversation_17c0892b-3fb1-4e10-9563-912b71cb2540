<template>
  <div class="profile-page">
    <div class="page-header">
      <h1 class="page-title">个人资料</h1>
      <p class="page-description">管理您的个人信息和账号设置</p>
    </div>

    <n-grid cols="24" :x-gap="24">
      <n-grid-item span="8">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <n-avatar :size="120" :src="userStore.user?.avatar_url">
              {{ userStore.user?.full_name?.[0] || userStore.user?.username?.[0] }}
            </n-avatar>
            <n-button class="upload-btn" size="small" @click="handleUploadAvatar">
              更换头像
            </n-button>
          </div>
        </div>
      </n-grid-item>

      <n-grid-item span="16">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <n-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
          >
            <n-form-item label="用户名" path="username">
              <n-input v-model:value="form.username" disabled />
            </n-form-item>
            <n-form-item label="真实姓名" path="full_name">
              <n-input v-model:value="form.full_name" placeholder="请输入真实姓名" />
            </n-form-item>
            <n-form-item label="邮箱地址" path="email">
              <n-input v-model:value="form.email" placeholder="请输入邮箱地址" />
            </n-form-item>
            <n-form-item>
              <n-button type="primary" :loading="updating" @click="handleUpdateProfile">
                保存修改
              </n-button>
            </n-form-item>
          </n-form>
        </div>

        <!-- 密码修改 -->
        <div class="password-section">
          <h3>修改密码</h3>
          <n-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <n-form-item label="当前密码" path="current_password">
              <n-input
                v-model:value="passwordForm.current_password"
                type="password"
                placeholder="请输入当前密码"
                show-password-on="click"
              />
            </n-form-item>
            <n-form-item label="新密码" path="new_password">
              <n-input
                v-model:value="passwordForm.new_password"
                type="password"
                placeholder="请输入新密码"
                show-password-on="click"
              />
            </n-form-item>
            <n-form-item label="确认密码" path="confirm_password">
              <n-input
                v-model:value="passwordForm.confirm_password"
                type="password"
                placeholder="请确认新密码"
                show-password-on="click"
              />
            </n-form-item>
            <n-form-item>
              <n-button type="primary" :loading="changingPassword" @click="handleChangePassword">
                修改密码
              </n-button>
            </n-form-item>
          </n-form>
        </div>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'
import { useUserStore } from '@/stores/user-store'
import { userAPI } from '@/api'
import type { APIResponse } from '@crf/network'
import type { User, UserProfileResponse } from '@/types/api-migration'

defineOptions({
  name: 'ProfilePage'
})

const userStore = useUserStore()
const message = useMessage()

// 表单引用
const formRef = ref<FormInst>()
const passwordFormRef = ref<FormInst>()

// 状态
const updating = ref(false)
const changingPassword = ref(false)

// 基本信息表单
const form = reactive({
  username: '',
  full_name: '',
  email: ''
})

// 密码表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 验证规则
const rules: FormRules = {
  full_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 密码验证规则
const passwordRules: FormRules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_rule: unknown, value: string) => {
        return value === passwordForm.new_password
      },
      message: '两次输入密码不一致',
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (userStore.user) {
    form.username = userStore.user.username
    form.full_name = userStore.user.full_name || ''
    form.email = userStore.user.email
  }
}

// 更新个人资料
const handleUpdateProfile = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    updating.value = true

    const response = await userAPI.updateProfile({
      full_name: form.full_name,
      email: form.email
    }) as APIResponse<UserProfileResponse>

    if (response.success && response.data) {
      message.success('个人资料更新成功')
      // 更新用户store中的信息
      const userData = response.data
      if (userData.user) {
        await userStore.setUser(userData.user)
      }
    }
  } catch (error: any) {
    console.error('更新个人资料失败:', error)
    if (error?.message) {
      message.error(error.message)
    }
  } finally {
    updating.value = false
  }
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true

    const response = await userAPI.changePassword({
      current_password: passwordForm.current_password,
      new_password: passwordForm.new_password
    }) as APIResponse<any>

    if (response.success) {
      message.success('密码修改成功')
      // 清空表单
      passwordForm.current_password = ''
      passwordForm.new_password = ''
      passwordForm.confirm_password = ''
    }
  } catch (error: any) {
    console.error('修改密码失败:', error)
    if (error?.message) {
      message.error(error.message)
    }
  } finally {
    changingPassword.value = false
  }
}

// 上传头像
const handleUploadAvatar = () => {
  message.info('头像上传功能开发中...')
}

// 生命周期
onMounted(() => {
  initFormData()
})
</script>

<style lang="scss" scoped>
.profile-page {
  .page-header {
    margin-bottom: 32px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--crf-text-color);
      margin: 0 0 8px 0;
    }

    .page-description {
      font-size: 14px;
      color: var(--crf-text-color-secondary);
      margin: 0;
    }
  }

  .avatar-section {
    background: var(--crf-card-bg);
    border: 1px solid var(--crf-border-color);
    border-radius: 12px;
    padding: 24px;
    text-align: center;

    .avatar-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .upload-btn {
        margin-top: 8px;
      }
    }
  }

  .info-section,
  .password-section {
    background: var(--crf-card-bg);
    border: 1px solid var(--crf-border-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--crf-text-color);
      margin: 0 0 20px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .profile-page {
    .n-grid {
      flex-direction: column;

      .n-grid-item {
        width: 100% !important;
        margin-bottom: 20px;
      }
    }
  }
}
</style>