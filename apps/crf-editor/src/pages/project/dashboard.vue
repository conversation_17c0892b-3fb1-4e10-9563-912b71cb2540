<template>
  <div class="project-dashboard">
    <!-- 项目头部信息 -->
    <div class="project-header">
      <div class="project-info">
        <div class="project-title-section">
          <div class="project-icon">
            <n-icon size="32" color="#3b82f6">
              <FolderOpen />
            </n-icon>
          </div>
          <div class="project-details">
            <h1 class="project-title">{{ currentProject?.name }}</h1>
            <p class="project-description">{{ currentProject?.description || '暂无描述' }}</p>
          </div>
        </div>
        <div class="project-status">
          <n-tag
            :type="getStatusType(currentProject?.status)"
            size="large"
          >
            {{ getStatusText(currentProject?.status) }}
          </n-tag>
        </div>
      </div>
      
      <div class="project-actions">
        <n-button @click="showProjectSettings = true">
          <template #icon>
            <n-icon><Settings /></n-icon>
          </template>
          项目设置
        </n-button>
        <n-button type="primary" @click="navigateToTemplates">
          <template #icon>
            <n-icon><Add /></n-icon>
          </template>
          创建模板
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <StatCard
          title="表单模板"
          :value="dashboardData?.templateStats.total || 0"
          :trend="+5"
          color="#3b82f6"
          icon="document"
          @click="navigateToTemplates"
        >
          <template #extra>
            <div class="stat-breakdown">
              <span class="stat-item">已发布: {{ dashboardData?.templateStats.published || 0 }}</span>
              <span class="stat-item">草稿: {{ dashboardData?.templateStats.draft || 0 }}</span>
            </div>
          </template>
        </StatCard>

        <StatCard
          title="数据实例"
          :value="dashboardData?.instanceStats.total || 0"
          :trend="+12"
          color="#10b981"
          icon="database"
          @click="navigateToInstances"
        >
          <template #extra>
            <div class="stat-breakdown">
              <span class="stat-item">已完成: {{ dashboardData?.instanceStats.completed || 0 }}</span>
              <span class="stat-item">进行中: {{ dashboardData?.instanceStats.in_progress || 0 }}</span>
            </div>
          </template>
        </StatCard>

        <StatCard
          title="项目成员"
          :value="dashboardData?.memberCount || 0"
          color="#8b5cf6"
          icon="people"
          @click="showMemberDialog = true"
        />

        <StatCard
          title="完成率"
          :value="`${currentProject?.completion_rate || 0}%`"
          :trend="+3.2"
          color="#f59e0b"
          icon="analytics"
        />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="left-column">
        <!-- 快速操作 -->
        <div class="quick-actions-card">
          <h3 class="card-title">快速操作</h3>
          <div class="quick-actions-grid">
            <div class="quick-action-item" @click="navigateToTemplates">
              <n-icon size="20" color="#3b82f6"><Document /></n-icon>
              <span>管理模板</span>
            </div>
            <div class="quick-action-item" @click="navigateToInstances">
              <n-icon size="20" color="#10b981"><DataBase /></n-icon>
              <span>查看数据</span>
            </div>
            <div class="quick-action-item" @click="showMemberDialog = true">
              <n-icon size="20" color="#8b5cf6"><People /></n-icon>
              <span>管理成员</span>
            </div>
            <div class="quick-action-item" @click="navigateToAnalytics">
              <n-icon size="20" color="#f59e0b"><Analytics /></n-icon>
              <span>数据分析</span>
            </div>
          </div>
        </div>

        <!-- 项目进度 -->
        <div class="progress-card">
          <h3 class="card-title">项目进度</h3>
          <div class="progress-content">
            <div class="progress-item">
              <span class="progress-label">模板完成度</span>
              <div class="progress-bar">
                <n-progress
                  type="line"
                  :percentage="getTemplateProgress()"
                  :height="8"
                  color="#3b82f6"
                />
              </div>
              <span class="progress-value">{{ getTemplateProgress() }}%</span>
            </div>
            <div class="progress-item">
              <span class="progress-label">数据收集进度</span>
              <div class="progress-bar">
                <n-progress
                  type="line"
                  :percentage="getDataProgress()"
                  :height="8"
                  color="#10b981"
                />
              </div>
              <span class="progress-value">{{ getDataProgress() }}%</span>
            </div>
          </div>
        </div>
      </div>

      <div class="right-column">
        <!-- 最近活动 -->
        <div class="activities-card">
          <div class="card-header">
            <h3 class="card-title">最近活动</h3>
            <n-button text @click="navigateToActivities">
              查看全部
            </n-button>
          </div>
          
          <div class="activities-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-avatar">
                <n-avatar
                  :size="32"
                  :src="activity.user.avatar_url"
                  :fallback-src="getDefaultAvatar(activity.user.full_name)"
                >
                  {{ getAvatarText(activity.user.full_name) }}
                </n-avatar>
              </div>
              <div class="activity-content">
                <div class="activity-description">
                  <strong>{{ activity.user.full_name }}</strong>
                  {{ activity.activity_description }}
                </div>
                <div class="activity-time">
                  {{ formatRelativeTime(activity.created_at) }}
                </div>
              </div>
              <div class="activity-type">
                <n-icon size="16" :color="getActivityColor(activity.activity_type)">
                  <component :is="getActivityIcon(activity.activity_type)" />
                </n-icon>
              </div>
            </div>
          </div>

          <div v-if="!recentActivities.length" class="empty-activities">
            <n-empty description="暂无活动记录" size="small" />
          </div>
        </div>
      </div>
    </div>

    <!-- 项目设置对话框 -->
    <ProjectSettingsDialog
      v-model:show="showProjectSettings"
      :project="currentProject"
      @updated="handleProjectUpdated"
    />

    <!-- 成员管理对话框 -->
    <ProjectMembersDialog
      v-model:show="showMemberDialog"
      :project-id="currentProjectId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project-store'
import StatCard from '@/components/dashboard/StatCard.vue'
import ProjectSettingsDialog from '@/components/project/ProjectSettingsDialog.vue'
import ProjectMembersDialog from '@/components/project/ProjectMembersDialog.vue'
import {
  NButton,
  NIcon,
  NTag,
  NProgress,
  NAvatar,
  NEmpty,
} from 'naive-ui'
import {
  FolderOpen,
  Settings,
  Add,
  Document,
  DataBase,
  People,
  Analytics,
} from '@vicons/ionicons5'

const route = useRoute()
const router = useRouter()
const projectStore = useProjectStore()

// 响应式数据
const showProjectSettings = ref(false)
const showMemberDialog = ref(false)

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const currentProjectId = computed(() => projectStore.currentProjectId)
const dashboardData = computed(() => projectStore.projectDashboard)
const recentActivities = computed(() => dashboardData.value?.recentActivities || [])

// 方法
const getStatusType = (status?: string) => {
  const statusMap = {
    'draft': 'default',
    'active': 'success',
    'completed': 'info',
    'archived': 'warning',
    'paused': 'error',
  }
  return statusMap[status as keyof typeof statusMap] || 'default'
}

const getStatusText = (status?: string) => {
  const statusMap = {
    'draft': '草稿',
    'active': '进行中',  
    'completed': '已完成',
    'archived': '已归档',
    'paused': '已暂停',
  }
  return statusMap[status as keyof typeof statusMap] || status || '未知'
}

const getTemplateProgress = () => {
  const stats = dashboardData.value?.templateStats
  if (!stats || stats.total === 0) return 0
  return Math.round((stats.published / stats.total) * 100)
}

const getDataProgress = () => {
  const stats = dashboardData.value?.instanceStats
  if (!stats || stats.total === 0) return 0
  return Math.round((stats.completed / stats.total) * 100)
}

const getDefaultAvatar = (name: string) => {
  // 生成默认头像URL
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=fff`
}

const getAvatarText = (name: string) => {
  return name.charAt(0).toUpperCase()
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString('zh-CN')
}

const getActivityColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'project_created': '#10b981',
    'project_updated': '#3b82f6',
    'member_added': '#8b5cf6',
    'template_created': '#3b82f6',
    'template_published': '#10b981',
    'instance_created': '#f59e0b',
    'instance_submitted': '#10b981',
  }
  return colorMap[type] || '#64748b'
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'project_created': FolderOpen,
    'project_updated': Settings,
    'member_added': People,
    'template_created': Document,
    'template_published': Document,
    'instance_created': DataBase,
    'instance_submitted': DataBase,
  }
  return iconMap[type] || Document
}

// 导航方法
const navigateToTemplates = () => {
  router.push(`/project/${currentProjectId.value}/templates`)
}

const navigateToInstances = () => {
  router.push(`/project/${currentProjectId.value}/instances`)
}

const navigateToAnalytics = () => {
  router.push(`/project/${currentProjectId.value}/analytics`)
}

const navigateToActivities = () => {
  router.push(`/project/${currentProjectId.value}/activities`)
}

const handleProjectUpdated = () => {
  // 重新获取项目信息
  if (currentProjectId.value) {
    projectStore.setCurrentProject(currentProjectId.value)
  }
}

// 监听路由变化
watch(() => route.params.id, async (newId) => {
  if (newId && typeof newId === 'string') {
    await projectStore.setCurrentProject(newId)
    await projectStore.fetchProjectDashboard(newId)
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  const projectId = route.params.id as string
  if (projectId) {
    try {
      if (!currentProject.value || currentProject.value.id !== projectId) {
        await projectStore.setCurrentProject(projectId)
      }
      await projectStore.fetchProjectDashboard(projectId)
    } catch (error) {
      console.error('加载项目仪表板失败:', error)
      // 如果项目不存在或无权限访问，重定向到项目选择页面
      router.push('/projects')
    }
  }
})
</script>

<style scoped>
.project-dashboard {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.project-header {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 24px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-info {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.project-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.project-icon {
  width: 56px;
  height: 56px;
  background: #eff6ff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-details {
  flex: 1;
}

.project-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.project-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.project-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-breakdown {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.stat-item {
  font-size: 12px;
  color: #64748b;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.left-column, .right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-actions-card,
.progress-card,
.activities-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #374151;
}

.quick-action-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-label {
  font-size: 14px;
  color: #374151;
  width: 80px;
  flex-shrink: 0;
}

.progress-bar {
  flex: 1;
}

.progress-value {
  font-size: 14px;
  color: #64748b;
  width: 40px;
  text-align: right;
  flex-shrink: 0;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #f1f5f9;
  background: #fafbfc;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-size: 14px;
  color: #374151;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #94a3b8;
}

.empty-activities {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

@media (max-width: 768px) {
  .project-dashboard {
    padding: 16px;
  }
  
  .project-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .project-info {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .project-title-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>