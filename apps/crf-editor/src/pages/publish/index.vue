<template>
  <div class="publish-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">发布管理</h1>
        <p class="page-description">管理表单模板的版本发布和部署</p>
      </div>
      <div class="header-right">
        <n-button type="primary" @click="handleBatchPublish">
          <template #icon>
            <n-icon><Upload /></n-icon>
          </template>
          批量发布
        </n-button>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <n-input
          v-model:value="filters.search"
          placeholder="搜索模板名称"
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon><Search /></n-icon>
          </template>
        </n-input>
        <n-select
          v-model:value="filters.status"
          placeholder="发布状态"
          clearable
          style="width: 150px"
          @update:value="handleStatusChange"
        >
          <n-option label="已发布" value="published" />
          <n-option label="待发布" value="pending" />
          <n-option label="发布失败" value="failed" />
          <n-option label="已下线" value="offline" />
        </n-select>
        <n-date-picker
          v-model:value="filters.dateRange"
          type="daterange"
          separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
          @update:value="handleDateChange"
        />
      </div>
      <div class="toolbar-right">
        <n-button @click="handleRefresh">
          <template #icon>
            <n-icon><Refresh /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button @click="handleExportLog">
          <template #icon>
            <n-icon><Download /></n-icon>
          </template>
          导出日志
        </n-button>
      </div>
    </div>

    <!-- 发布列表 -->
    <div class="publish-table">
      <n-data-table
        :loading="loading"
        :data="publishList"
        :columns="columns"
        :row-key="(row) => row.id"
        @update:checked-row-keys="handleSelectionChange"
      />
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <n-pagination
        v-model:page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :item-count="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        show-size-picker
        show-quick-jumper
        @update:page="handleCurrentChange"
        @update:page-size="handleSizeChange"
      />
    </div>

    <!-- 发布详情弹窗 -->
    <n-modal
      v-model:show="detailsVisible"
      preset="card"
      title="发布详情"
      style="width: 800px"
      @close="handleCloseDetails"
    >
      <div v-if="selectedPublish" class="publish-details">
        <div class="details-section">
          <h3>基本信息</h3>
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="模板名称">
              {{ selectedPublish.template_name }}
            </n-descriptions-item>
            <n-descriptions-item label="版本号">
              v{{ selectedPublish.version }}
            </n-descriptions-item>
            <n-descriptions-item label="发布状态">
              <n-tag :type="getStatusType(selectedPublish.status)">
                {{ getStatusText(selectedPublish.status) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="发布环境">
              <n-tag :type="getEnvironmentType(selectedPublish.environment)">
                {{ getEnvironmentText(selectedPublish.environment) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="发布时间">
              {{ formatDateTime(selectedPublish.published_at) }}
            </n-descriptions-item>
            <n-descriptions-item label="发布人">
              {{ selectedPublish.published_by }}
            </n-descriptions-item>
          </n-descriptions>
        </div>

        <div class="details-section">
          <h3>版本说明</h3>
          <p class="version-description">
            {{ selectedPublish.description || '暂无版本说明' }}
          </p>
        </div>

        <div class="details-section">
          <h3>变更日志</h3>
          <div class="change-log">
            <pre>{{ selectedPublish.change_log || '暂无变更日志' }}</pre>
          </div>
        </div>

        <div class="details-section">
          <h3>部署信息</h3>
          <n-data-table
            :data="selectedPublish.deployments || []"
            :columns="deploymentColumns"
            size="small"
          />
        </div>
      </div>
    </n-modal>

    <!-- 批量发布弹窗 -->
    <n-modal
      v-model:show="batchPublishVisible"
      preset="card"
      title="批量发布"
      style="width: 600px"
    >
      <div class="batch-publish-form">
        <n-form :model="batchPublishForm" label-width="100px">
          <n-form-item label="发布环境">
            <n-select v-model:value="batchPublishForm.environment" placeholder="选择发布环境">
              <n-option label="开发环境" value="dev" />
              <n-option label="测试环境" value="test" />
              <n-option label="生产环境" value="prod" />
            </n-select>
          </n-form-item>
          <n-form-item label="发布说明">
            <n-input
              v-model:value="batchPublishForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入发布说明"
            />
          </n-form-item>
        </n-form>
        
        <div class="selected-templates">
          <h4>待发布模板 ({{ selectedRows.length }})</h4>
          <div class="template-list">
            <div
              v-for="template in selectedRows"
              :key="template.id"
              class="template-item"
            >
              <span>{{ template.template_name }} v{{ template.version }}</span>
              <n-tag :type="getStatusType(template.status)" size="small">
                {{ getStatusText(template.status) }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #action>
        <div class="dialog-footer">
          <n-button @click="batchPublishVisible = false">取消</n-button>
          <n-button type="primary" :loading="batchPublishing" @click="confirmBatchPublish">
            确认发布
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useMessage, useDialog, NButton, NTag } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import {
  Upload, Search, Refresh, Download
} from '@vicons/ionicons5'

defineOptions({
  name: 'PublishPage'
})

// 初始化 Naive UI 消息和对话框
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const publishList = ref<Record<string, unknown>[]>([])
const selectedRows = ref<Record<string, unknown>[]>([])

// 表格列定义
const columns: DataTableColumns = [
  {
    type: 'selection'
  },
  {
    title: '模板名称',
    key: 'template_name',
    minWidth: 200,
    render(row: Record<string, unknown>) {
      return h('div', { class: 'template-info' }, [
        h('h4', { class: 'template-title' }, row.template_name),
        h('p', { class: 'template-id' }, `ID: ${row.template_id}`)
      ])
    }
  },
  {
    title: '版本',
    key: 'version',
    width: 120,
    render(row: Record<string, unknown>) {
      return h(NTag, { type: 'info', size: 'small' }, { default: () => `v${row.version}` })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    render(row: Record<string, unknown>) {
      return h(NTag, { type: getStatusType(row.status as string), size: 'small' }, { default: () => getStatusText(row.status as string) })
    }
  },
  {
    title: '环境',
    key: 'environment',
    width: 100,
    render(row: Record<string, unknown>) {
      return h(NTag, { type: getEnvironmentType(row.environment as string), size: 'small' }, { default: () => getEnvironmentText(row.environment as string) })
    }
  },
  {
    title: '发布时间',
    key: 'published_at',
    width: 180,
    render(row: Record<string, unknown>) {
      return formatDateTime(row.published_at as string)
    }
  },
  {
    title: '发布人',
    key: 'published_by',
    width: 120
  },
  {
    title: '实例数量',
    key: 'instances_count',
    width: 100,
    align: 'center',
    render(row: Record<string, unknown>) {
      return h('span', { class: 'instances-count' }, row.instances_count || 0)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render(row: Record<string, unknown>) {
      const buttons = []
      
      if (row.status === 'pending') {
        buttons.push(
          h(NButton, {
            type: 'primary',
            size: 'small',
            onClick: () => handlePublish(row)
          }, { default: () => '发布' })
        )
      }
      
      if (row.status === 'published') {
        buttons.push(
          h(NButton, {
            type: 'warning',
            size: 'small',
            onClick: () => handleRollback(row)
          }, { default: () => '回滚' }),
          h(NButton, {
            type: 'error',
            size: 'small',
            onClick: () => handleOffline(row)
          }, { default: () => '下线' })
        )
      }
      
      buttons.push(
        h(NButton, {
          size: 'small',
          onClick: () => handleViewDetails(row)
        }, { default: () => '详情' })
      )
      
      return h('div', { class: 'table-actions', style: 'display: flex; gap: 4px;' }, buttons)
    }
  }
]

// 部署信息表格列定义
const deploymentColumns: DataTableColumns = [
  {
    title: '环境',
    key: 'environment',
    width: 100
  },
  {
    title: '访问地址',
    key: 'url'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row: Record<string, unknown>) {
      return h(NTag, {
        type: row.status === 'active' ? 'success' : 'error',
        size: 'small'
      }, { default: () => row.status === 'active' ? '正常' : '异常' })
    }
  }
]

// 弹窗状态
const detailsVisible = ref(false)
const batchPublishVisible = ref(false)
const batchPublishing = ref(false)
const selectedPublish = ref<Record<string, unknown> | null>(null)

// 筛选条件
const filters = reactive({
  search: '',
  status: '',
  dateRange: null as [number, number] | null
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 批量发布表单
const batchPublishForm = reactive({
  environment: '',
  description: ''
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'published': return 'success'
    case 'pending': return 'warning'
    case 'failed': return 'danger'
    case 'offline': return 'info'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'published': return '已发布'
    case 'pending': return '待发布'
    case 'failed': return '发布失败'
    case 'offline': return '已下线'
    default: return '未知'
  }
}

// 获取环境类型
const getEnvironmentType = (env: string) => {
  switch (env) {
    case 'prod': return 'danger'
    case 'test': return 'warning'
    case 'dev': return 'primary'
    default: return ''
  }
}

// 获取环境文本
const getEnvironmentText = (env: string) => {
  switch (env) {
    case 'prod': return '生产'
    case 'test': return '测试'
    case 'dev': return '开发'
    default: return '未知'
  }
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取发布列表
const fetchPublishList = async () => {
  try {
    loading.value = true
    
    // 模拟API调用，实际项目中应该调用真实的发布管理API
    const mockData = [
      {
        id: 1,
        template_id: 'tpl_001',
        template_name: '患者基本信息表',
        version: '1.2.0',
        status: 'published',
        environment: 'prod',
        published_at: '2024-01-15 14:30:00',
        published_by: '张医生',
        instances_count: 45,
        description: '增加了新的字段验证',
        change_log: '1. 新增过敏史字段\n2. 修复日期格式问题\n3. 优化表单布局',
        deployments: [
          { environment: 'prod', url: 'https://crf.hospital.com/form/tpl_001', status: 'active' }
        ]
      },
      {
        id: 2,
        template_id: 'tpl_002',
        template_name: '手术记录表',
        version: '2.1.0',
        status: 'pending',
        environment: 'test',
        published_at: '2024-01-14 16:45:00',
        published_by: '李医生',
        instances_count: 23,
        description: '优化手术流程记录',
        change_log: '1. 新增术中并发症记录\n2. 修改麻醉方式选项',
        deployments: []
      }
    ]
    
    publishList.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取发布列表失败:', error)
    message.error('获取发布列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  fetchPublishList()
}

const handleStatusChange = () => {
  pagination.current = 1
  fetchPublishList()
}

const handleDateChange = () => {
  pagination.current = 1
  fetchPublishList()
}

const handleRefresh = () => {
  fetchPublishList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  fetchPublishList()
}

const handleCurrentChange = (page: number) => {
  pagination.current = page
  fetchPublishList()
}

const handleSelectionChange = (rowKeys: (string | number)[]) => {
  selectedRows.value = publishList.value.filter(item => rowKeys.includes(item.id))
}

const handlePublish = async (row: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认发布',
        content: `确定要发布模板 "${row.template_name}" v${row.version} 吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消'))
      })
    })

    // TODO: 调用发布API
    message.success('发布成功')
    fetchPublishList()
  } catch (error) {
    if (error.message !== '用户取消') {
      message.error('发布失败')
    }
  }
}

const handleRollback = async (row: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认回滚',
        content: `确定要回滚模板 "${row.template_name}" 吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消'))
      })
    })

    // TODO: 调用回滚API
    message.success('回滚成功')
    fetchPublishList()
  } catch (error) {
    if (error.message !== '用户取消') {
      message.error('回滚失败')
    }
  }
}

const handleOffline = async (row: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.error({
        title: '确认下线',
        content: `确定要下线模板 "${row.template_name}" 吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消'))
      })
    })

    // TODO: 调用下线API
    message.success('下线成功')
    fetchPublishList()
  } catch (error) {
    if (error.message !== '用户取消') {
      message.error('下线失败')
    }
  }
}

const handleViewDetails = (row: Record<string, unknown>) => {
  selectedPublish.value = row
  detailsVisible.value = true
}

const handleCloseDetails = () => {
  detailsVisible.value = false
  selectedPublish.value = null
}

const handleBatchPublish = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要发布的模板')
    return
  }
  batchPublishVisible.value = true
}

const confirmBatchPublish = async () => {
  try {
    batchPublishing.value = true
    
    // TODO: 调用批量发布API
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用
    
    message.success(`成功发布 ${selectedRows.value.length} 个模板`)
    batchPublishVisible.value = false
    selectedRows.value = []
    fetchPublishList()
  } catch (error) {
    message.error('批量发布失败')
  } finally {
    batchPublishing.value = false
  }
}

const handleExportLog = () => {
  message.info('导出功能开发中...')
}

// 生命周期
onMounted(() => {
  fetchPublishList()
})
</script>

<style lang="scss" scoped>
.publish-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--crf-card-bg);
    border-radius: 8px;

    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .publish-table {
    background: var(--crf-card-bg);
    border-radius: 8px;
    overflow: hidden;

    .template-info {
      .template-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--crf-text-color);
        margin: 0 0 4px 0;
      }

      .template-id {
        font-size: 12px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }

    .instances-count {
      font-weight: 500;
      color: var(--primary-color);
    }

    .table-actions {
      display: flex;
      gap: 4px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}

.publish-details {
  .details-section {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--crf-text-color);
      margin: 0 0 12px 0;
    }

    .version-description {
      font-size: 14px;
      color: var(--crf-text-color);
      line-height: 1.6;
      padding: 12px;
      background: var(--gray-100);
      border-radius: 6px;
      margin: 0;
    }

    .change-log {
      background: var(--gray-100);
      border-radius: 6px;
      padding: 12px;

      pre {
        font-size: 14px;
        color: var(--crf-text-color);
        line-height: 1.6;
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}

.batch-publish-form {
  .selected-templates {
    margin-top: 24px;

    h4 {
      font-size: 14px;
      font-weight: 500;
      color: var(--crf-text-color);
      margin: 0 0 12px 0;
    }

    .template-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid var(--crf-border-color);
      border-radius: 6px;

      .template-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid var(--crf-border-color);

        &:last-child {
          border-bottom: none;
        }

        span {
          font-size: 14px;
          color: var(--crf-text-color);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .publish-page {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left {
        flex-direction: column;
        align-items: stretch;

        .n-input, .n-select, .n-date-picker {
          width: 100% !important;
        }
      }
    }

    .table-actions {
      flex-direction: column;
      gap: 4px;

      .n-button {
        width: 100%;
      }
    }
  }
}
</style>