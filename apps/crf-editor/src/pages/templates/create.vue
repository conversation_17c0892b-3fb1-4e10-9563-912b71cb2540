<template>
  <div class="create-template-container">
    <!-- 页面头部 -->
    <div class="create-header">
      <div class="header-content">
        <h1 class="page-title">新建表单</h1>
        <p class="page-subtitle">创建新的CRF表单模板</p>
      </div>
      <div class="header-actions">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="creating" @click="handleCreate">
          创建表单
        </n-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="create-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <div class="section-content">
            <n-grid :cols="2" :x-gap="20">
              <n-grid-item>
                <n-form-item label="表单名称" path="name">
                  <n-input
                    v-model:value="form.name"
                    placeholder="请输入表单名称"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="表单标题" path="title">
                  <n-input
                    v-model:value="form.title"
                    placeholder="请输入表单标题"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>
            
            <n-form-item label="表单描述" path="description">
              <n-input
                v-model:value="form.description"
                type="textarea"
                :rows="3"
                placeholder="请输入表单描述"
              />
            </n-form-item>
            
            <n-grid :cols="2" :x-gap="20">
              <n-grid-item>
                <n-form-item label="关键词" path="keyword">
                  <n-input
                    v-model:value="form.keyword"
                    placeholder="请输入关键词"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="版本号">
                  <n-input
                    v-model:value="form.version"
                    placeholder="例如：1.0.0"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>
          </div>
        </div>

        <!-- 创建方式 -->
        <div class="form-section">
          <h3 class="section-title">创建方式</h3>
          <div class="section-content">
            <n-form-item label="创建方式">
              <n-radio-group v-model:value="form.template_type">
                <n-radio value="blank">空白表单</n-radio>
                <n-radio value="preset">从模板创建</n-radio>
              </n-radio-group>
            </n-form-item>
            
            <n-form-item v-if="form.template_type === 'preset'" label="选择模板">
              <n-select
                v-model:value="form.preset_id"
                placeholder="请选择模板"
                clearable
                filterable
                class="full-width"
              >
                <n-option
                  v-for="preset in presetTemplates"
                  :key="preset.id"
                  :label="preset.name"
                  :value="preset.id"
                >
                  <div class="preset-option">
                    <span>{{ preset.name }}</span>
                    <n-tag size="small" type="info">{{ preset.category }}</n-tag>
                  </div>
                </n-option>
              </n-select>
            </n-form-item>
          </div>
        </div>

        <!-- 项目设置 -->
        <div class="form-section">
          <h3 class="section-title">项目设置</h3>
          <div class="section-content">
            <n-form-item label="所属项目" path="project_id">
              <n-select
                v-model:value="form.project_id"
                placeholder="请选择项目"
                clearable
                filterable
                class="full-width"
              >
                <n-option
                  v-for="project in projects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </n-select>
            </n-form-item>
            
            <n-form-item label="权限设置">
              <n-checkbox-group v-model:value="form.permissions">
                <n-checkbox value="public">公开模板</n-checkbox>
                <n-checkbox value="editable">允许编辑</n-checkbox>
                <n-checkbox value="downloadable">允许下载</n-checkbox>
              </n-checkbox-group>
            </n-form-item>
          </div>
        </div>
      </n-form>
    </div>

    <!-- 模板预览 -->
    <div v-if="form.template_type === 'preset' && selectedPreset" class="preview-section">
      <h3 class="section-title">模板预览</h3>
      <div class="preview-card">
        <div class="preview-info">
          <h4>{{ selectedPreset.name }}</h4>
          <p>{{ selectedPreset.description }}</p>
          <div class="preview-meta">
            <n-tag size="small">{{ selectedPreset.category }}</n-tag>
            <span class="field-count">{{ selectedPreset.field_count }} 个字段</span>
          </div>
        </div>
        <div class="preview-actions">
          <n-button size="small" @click="handlePreviewTemplate">预览详情</n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { templateAPI, projectAPI } from '@/api'

defineOptions({
  name: 'CreateTemplatePage'
})

const router = useRouter()
const message = useMessage()

// 表单引用
const formRef = ref<FormInst>()

// 状态
const creating = ref(false)
const projects = ref<Array<Record<string, unknown>>>([])

// 表单数据
const form = reactive({
  project_id: '',
  name: '',
  title: '',
  description: '',
  keyword: '',
  version: '1.0.0',
  template_type: 'blank',
  preset_id: '',
  import_file: null as File | null,
  permissions: ['editable']
})

// 预设模板数据
const presetTemplates = ref<Array<{
  id: string;
  name: string;
  description: string;
  category: string;
  field_count: number;
}>>([
  {
    id: 'preset_1',
    name: '患者基本信息表',
    description: '包含患者基本信息、病史、体征等常用字段',
    category: '基础信息',
    field_count: 25
  },
  {
    id: 'preset_2',
    name: '手术记录表',
    description: '手术过程、麻醉方式、术中情况等记录',
    category: '手术记录',
    field_count: 30
  },
  {
    id: 'preset_3',
    name: '随访记录表',
    description: '患者随访信息、检查结果、用药情况等',
    category: '随访管理',
    field_count: 20
  }
])

// 验证规则
const rules: FormRules = {
  project_id: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入模板标题', trigger: 'blur' },
    { min: 2, max: 100, message: '模板标题长度在 2 到 100 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const selectedPreset = computed(() => {
  return presetTemplates.value.find(preset => preset.id === form.preset_id)
})

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await projectAPI.getProjects({ limit: 100 })
    if (response.success && response.data) {
      projects.value = response.data.projects || []
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}



// 预览模板
const handlePreviewTemplate = () => {
  message.info('预览功能开发中...')
}

// 创建模板
const handleCreate = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    creating.value = true

    const templateData = {
      project_id: form.project_id,
      name: form.name,
      title: form.title,
      description: form.description,
      template_type: 'template' as const,
      source_type: 'user_created' as const,
      template_data: {
        page_config: {},
        form_structure: [],
        component_configs: {},
        validation_rules: {},
        style_config: {}
      }
    }
    
      const response = await templateAPI.createTemplate(templateData)
      if (response.success) {
      message.success('模板创建成功')
      router.push(`/editor?templateId=${response.data.template.id}`)
    }
  } catch (error) {
    console.error('创建模板失败:', error)
    message.error('创建模板失败')
  } finally {
    creating.value = false
  }
}

// 取消创建
const handleCancel = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  fetchProjects()
})
</script>

<style lang="scss" scoped>
.create-template-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.create-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.header-content {
  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a202c;
    margin: 0 0 4px 0;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.create-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.section-content {
  padding-left: 16px;
}

.full-width {
  width: 100%;
}

.preset-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.preview-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e2e8f0;

  .preview-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;

    .preview-info {
      flex: 1;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #1a202c;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 14px;
        color: #64748b;
        margin: 0 0 12px 0;
      }

      .preview-meta {
        display: flex;
        align-items: center;
        gap: 12px;

        .field-count {
          font-size: 12px;
          color: #64748b;
        }
      }
    }

    .preview-actions {
      flex-shrink: 0;
    }
  }
}

// Element Plus 样式定制
:deep(.n-form-item__label) {
  color: #374151;
  font-weight: 500;
}

:deep(.n-input__inner) {
  border-radius: 6px;
}

:deep(.n-input__textarea-el) {
  border-radius: 6px;
}

:deep(.n-select) {
  width: 100%;
}

:deep(.n-checkbox-group) {
  display: flex;
  gap: 16px;
}

:deep(.n-radio-group) {
  display: flex;
  gap: 24px;
}

@media (max-width: 768px) {
  .create-template-container {
    padding: 16px;
  }
  
  .create-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .form-content {
    padding: 16px;
  }
  
  .create-form {
    max-width: none;
  }
  
  .section-content {
    padding-left: 0;
  }
  
  .preview-card {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>