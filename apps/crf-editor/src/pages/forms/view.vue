<template>
  <div class="form-view-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <n-spin size="large">
        <template #description>
          加载表单中...
        </template>
      </n-spin>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <n-result
        status="error"
        :title="error.title || '加载失败'"
        :description="error.message || '无法加载表单，请稍后重试'"
      >
        <template #footer>
          <n-button type="primary" @click="retryLoad">重新加载</n-button>
          <n-button @click="goBack">返回列表</n-button>
        </template>
      </n-result>
    </div>

    <!-- 表单查看界面 -->
    <div v-else-if="instance && template" class="form-view-content">
      <!-- 表单头部 -->
      <div class="form-header">
        <n-breadcrumb separator="/">
          <n-breadcrumb-item :clickable="false">
            <router-link to="/form-fill">表单填写</router-link>
          </n-breadcrumb-item>
          <n-breadcrumb-item>{{ instance.instance_name || template.title }}</n-breadcrumb-item>
        </n-breadcrumb>
        
        <div class="header-content">
          <div class="header-left">
            <h1 class="form-title">{{ template.title }}</h1>
            <p v-if="template.description" class="form-description">
              {{ template.description }}
            </p>
          </div>
          
          <div class="header-right">
            <div class="actions">
              <n-button @click="goBack">
                <template #icon>
                  <crf-icon icon="material-symbols:arrow-back" size="16px" />
                </template>
                返回列表
              </n-button>
              
              <n-button @click="handlePrint" type="info">
                <template #icon>
                  <crf-icon icon="material-symbols:print" size="16px" />
                </template>
                打印表单
              </n-button>
              
              <n-button @click="handleExport" type="success">
                <template #icon>
                  <crf-icon icon="material-symbols:download" size="16px" />
                </template>
                导出数据
              </n-button>
            </div>
          </div>
        </div>

        <!-- 表单信息卡片 -->
        <div class="info-cards">
          <n-grid cols="4" responsive="screen" :x-gap="16">
            <n-grid-item>
              <div class="info-card">
                <div class="info-icon status">
                  <crf-icon icon="material-symbols:check-circle" size="24px" />
                </div>
                <div class="info-content">
                  <div class="info-label">状态</div>
                  <n-tag :type="getStatusType(instance.status)" size="large">
                    {{ getStatusText(instance.status) }}
                  </n-tag>
                </div>
              </div>
            </n-grid-item>
            
            <n-grid-item>
              <div class="info-card">
                <div class="info-icon completion">
                  <crf-icon icon="material-symbols:analytics" size="24px" />
                </div>
                <div class="info-content">
                  <div class="info-label">完成度</div>
                  <div class="info-value">{{ instance.completion_percentage }}%</div>
                </div>
              </div>
            </n-grid-item>
            
            <n-grid-item>
              <div class="info-card">
                <div class="info-icon creator">
                  <crf-icon icon="material-symbols:person" size="24px" />
                </div>
                <div class="info-content">
                  <div class="info-label">填写者</div>
                  <div class="info-value">{{ instance.creator?.full_name || '匿名用户' }}</div>
                </div>
              </div>
            </n-grid-item>
            
            <n-grid-item>
              <div class="info-card">
                <div class="info-icon time">
                  <crf-icon icon="material-symbols:schedule" size="24px" />
                </div>
                <div class="info-content">
                  <div class="info-label">提交时间</div>
                  <div class="info-value">{{ formatDate(instance.submitted_at || instance.updated_at) }}</div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </div>

        <!-- 患者信息（如果有） -->
        <div v-if="patientInfo" class="patient-info">
          <h3>患者信息</h3>
          <n-grid cols="4" responsive="screen" :x-gap="16">
            <n-grid-item>
              <div class="patient-item">
                <span class="label">患者编号:</span>
                <span class="value">{{ patientInfo.patientId }}</span>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="patient-item">
                <span class="label">患者姓名:</span>
                <span class="value">{{ patientInfo.patientName }}</span>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="patient-item">
                <span class="label">性别:</span>
                <span class="value">{{ patientInfo.gender === 'male' ? '男' : '女' }}</span>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="patient-item">
                <span class="label">年龄:</span>
                <span class="value">{{ patientInfo.age }}岁</span>
              </div>
            </n-grid-item>
          </n-grid>
          <div v-if="patientInfo.visitDate" class="patient-item">
            <span class="label">就诊日期:</span>
            <span class="value">{{ formatDate(patientInfo.visitDate) }}</span>
          </div>
          <div v-if="patientInfo.remarks" class="patient-item">
            <span class="label">备注:</span>
            <span class="value">{{ patientInfo.remarks }}</span>
          </div>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content" id="form-print-area">
        <n-card>
          <!-- 打印头部 -->
          <div class="print-header">
            <h1>{{ template.title }}</h1>
            <div class="print-info">
              <p>填写者: {{ instance.creator?.full_name || '匿名用户' }}</p>
              <p>提交时间: {{ formatDate(instance.submitted_at || instance.updated_at) }}</p>
              <p>完成度: {{ instance.completion_percentage }}%</p>
            </div>
          </div>

          <form-renderer
            v-if="templateData"
            :template-data="templateData"
            :form-data="formData"
            :mode="FormMode.VIEW"
            :readonly="true"
            class="readonly-form"
          />
        </n-card>
      </div>

      <!-- 操作历史 -->
      <div class="history-section">
        <n-card>
          <template #header>
            <div class="card-header">
              <span>操作历史</span>
              <n-button text @click="loadHistory">
                <template #icon>
                  <crf-icon icon="material-symbols:refresh" size="16px" />
                </template>
                刷新
              </n-button>
            </div>
          </template>
          
          <n-timeline v-if="history.length > 0">
            <n-timeline-item
              v-for="item in history"
              :key="item.id"
              :time="formatDate(item.created_at)"
              :type="getHistoryType(item.action)"
            >
              <div class="history-item">
                <h4>{{ getHistoryTitle(item.action) }}</h4>
                <p v-if="item.description">{{ item.description }}</p>
                <div class="history-meta">
                  <span>操作者: {{ item.user?.full_name || '系统' }}</span>
                </div>
              </div>
            </n-timeline-item>
          </n-timeline>
          
          <n-empty v-else description="暂无操作历史" />
        </n-card>
      </div>
    </div>

    <!-- 导出对话框 -->
    <n-modal
      v-model:show="showExportDialog"
      title="导出表单数据"
      :mask-closable="false"
      preset="dialog"
      style="width: 500px"
    >
      <n-form label-width="100px">
        <n-form-item label="导出格式">
          <n-radio-group v-model:value="exportFormat">
            <n-radio value="pdf">PDF文档</n-radio>
            <n-radio value="excel">Excel表格</n-radio>
            <n-radio value="json">JSON数据</n-radio>
          </n-radio-group>
        </n-form-item>
        
        <n-form-item label="包含内容">
          <n-checkbox-group v-model:value="exportContent">
            <n-checkbox value="form_data">表单数据</n-checkbox>
            <n-checkbox value="patient_info">患者信息</n-checkbox>
            <n-checkbox value="metadata">元数据信息</n-checkbox>
            <n-checkbox value="history">操作历史</n-checkbox>
          </n-checkbox-group>
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-button @click="showExportDialog = false">取消</n-button>
        <n-button type="primary" @click="confirmExport" :loading="exporting">
          导出
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { CrfIcon } from '@crf/components'
import FormRenderer from '@/components/form/FormRenderer.vue'
import { instanceAPI, historyAPI } from '@/api'

// 临时类型定义
type ExportFormat = 'json' | 'csv' | 'excel' | 'pdf'

enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview'
}

const route = useRoute()
const router = useRouter()

// 添加message实例
const message = useMessage()

// 路由参数
const instanceId = route.params.id as string

// 状态管理
const loading = ref(true)
const exporting = ref(false)
const error = ref<Error | null>(null)
const instance = ref<Record<string, unknown> | null>(null)
const template = ref<Record<string, unknown> | null>(null)
const templateData = ref<Record<string, unknown> | null>(null)
const formData = reactive<Record<string, unknown>>({})
const history = ref<Record<string, unknown>[]>([])
const showExportDialog = ref(false)

// 导出配置
const exportFormat = ref<ExportFormat>('pdf')
const exportContent = ref(['form_data', 'patient_info', 'metadata'])

// 计算属性
const patientInfo = computed(() => {
  return formData.patientInfo || null
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'draft': return 'warning'
    case 'submitted': return 'success'
    case 'locked': return 'error'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'submitted': return '已提交'
    case 'locked': return '已锁定'
    default: return '未知状态'
  }
}

// 获取历史记录类型
const getHistoryType = (action: string) => {
  switch (action) {
    case 'create': return 'default'
    case 'update': return 'info'
    case 'submit': return 'success'
    case 'lock': return 'warning'
    default: return 'info'
  }
}

// 获取历史记录标题
const getHistoryTitle = (action: string) => {
  switch (action) {
    case 'create': return '创建实例'
    case 'update': return '更新数据'
    case 'submit': return '提交表单'
    case 'lock': return '锁定表单'
    default: return '其他操作'
  }
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 初始化表单
const initForm = async () => {
  try {
    loading.value = true
    error.value = null

    const response = await instanceAPI.getInstance(instanceId)
    if (!response.success) {
      throw new Error(response.message || '获取表单实例失败')
    }

    instance.value = response.data.instance
    template.value = instance.value.template
    templateData.value = template.value.template_data

    // 恢复表单数据
    if (instance.value.form_data) {
      Object.assign(formData, instance.value.form_data)
    }

    // 加载操作历史
    await loadHistory()

  } catch (err) {
    console.error('初始化表单失败:', err)
    error.value = {
      title: '加载失败',
      message: err instanceof Error ? err.message : '未知错误'
    }
  } finally {
    loading.value = false
  }
}

// 加载操作历史
const loadHistory = async () => {
  try {
    const response = await historyAPI.getHistory('instance', instanceId)
    if (response.success) {
      history.value = response.data.history || []
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}

// 打印表单
const handlePrint = () => {
  const printArea = document.getElementById('form-print-area')
  if (!printArea) return

  const printWindow = window.open('', '_blank')
  if (!printWindow) return

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>${template.value.title} - 表单数据</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif; }
        .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #eee; padding-bottom: 20px; }
        .print-header h1 { color: #333; margin: 0 0 15px 0; }
        .print-header .print-info { display: flex; justify-content: space-around; color: #666; }
        .readonly-form { margin-top: 20px; }
        @media print { 
          body { margin: 0; }
          .no-print { display: none !important; }
        }
      </style>
    </head>
    <body>
      ${printArea.innerHTML}
    </body>
    </html>
  `)

  printWindow.document.close()
  printWindow.print()
  printWindow.close()
}

// 导出表单
const handleExport = () => {
  showExportDialog.value = true
}

// 确认导出
const confirmExport = async () => {
  try {
    exporting.value = true
    
    const response = await instanceAPI.exportInstance(instanceId, {
      format: exportFormat.value,
      content: exportContent.value
    })

    if (response.success) {
      // 处理文件下载
      const blob = new Blob([response.data], { 
        type: getContentType(exportFormat.value) 
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${template.value.title}_${instanceId}.${exportFormat.value}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      message.success('导出成功')
      showExportDialog.value = false
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 获取内容类型
const getContentType = (format: string) => {
  switch (format) {
    case 'pdf': return 'application/pdf'
    case 'excel': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    case 'json': return 'application/json'
    default: return 'application/octet-stream'
  }
}

// 返回列表
const goBack = () => {
  router.push('/form-fill')
}

// 重新加载
const retryLoad = () => {
  initForm()
}

// 初始化
onMounted(() => {
  initForm()
})
</script>

<style lang="scss" scoped>
.form-view-page {
  min-height: 100vh;
  background: #f5f7fa;
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
  }
  
  .form-view-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    
    .form-header {
      background: white;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin: 16px 0 24px 0;
        
        .header-left {
          flex: 1;
          
          .form-title {
            font-size: 28px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
          }
          
          .form-description {
            color: #6b7280;
            margin: 0;
            line-height: 1.6;
            font-size: 16px;
          }
        }
        
        .header-right {
          .actions {
            display: flex;
            gap: 12px;
            
            .n-button {
              display: flex;
              align-items: center;
              gap: 6px;
            }
          }
        }
      }
      
      .info-cards {
        margin-bottom: 24px;
        
        .info-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: #f9fafb;
          border-radius: 8px;
          height: 100%;
          
          .info-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            
            &.status {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            }
            
            &.completion {
              background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            }
            
            &.creator {
              background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            }
            
            &.time {
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            }
          }
          
          .info-content {
            .info-label {
              font-size: 14px;
              color: #6b7280;
              margin-bottom: 4px;
            }
            
            .info-value {
              font-size: 16px;
              font-weight: 500;
              color: #1f2937;
            }
          }
        }
      }
      
      .patient-info {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 20px;
        
        h3 {
          margin: 0 0 16px 0;
          color: #0c4a6e;
          font-size: 18px;
        }
        
        .patient-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .label {
            color: #475569;
            font-weight: 500;
            margin-right: 8px;
            min-width: 80px;
          }
          
          .value {
            color: #1e293b;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .form-content {
      margin-bottom: 20px;
      
      .n-card {
        border-radius: 12px;
      }
      
      .print-header {
        display: none;
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #eee;
        padding-bottom: 20px;
        
        h1 {
          color: #333;
          margin: 0 0 15px 0;
        }
        
        .print-info {
          display: flex;
          justify-content: space-around;
          color: #666;
          
          p {
            margin: 0;
          }
        }
      }
      
      .readonly-form {
        :deep(.crf-form) {
          .form-section {
            margin-bottom: 24px;
          }
          
          .n-input,
          .n-input,
          .n-select {
            .n-input__wrapper,
            .n-input__inner {
              background-color: #f9fafb;
              border-color: #e5e7eb;
            }
          }
        }
      }
    }
    
    .history-section {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .history-item {
        h4 {
          margin: 0 0 8px 0;
          color: #1f2937;
          font-size: 16px;
        }
        
        p {
          margin: 0 0 8px 0;
          color: #6b7280;
          line-height: 1.5;
        }
        
        .history-meta {
          font-size: 12px;
          color: #9ca3af;
        }
      }
    }
  }
}

// 打印样式
@media print {
  .form-view-page {
    .form-view-content {
      max-width: none;
      margin: 0;
      padding: 0;
      
      .form-header,
      .history-section {
        display: none !important;
      }
      
      .form-content {
        margin: 0;
        
        .n-card {
          border: none;
          box-shadow: none;
        }
        
        .print-header {
          display: block !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-view-page {
    .form-view-content {
      padding: 16px;
      
      .form-header {
        padding: 16px;
        
        .header-content {
          flex-direction: column;
          gap: 16px;
          
          .header-right {
            width: 100%;
            
            .actions {
              justify-content: center;
              flex-wrap: wrap;
            }
          }
        }
        
        .info-cards {
          .n-row {
            --n-row-gutter: 8px;
          }
          
          .n-col {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>