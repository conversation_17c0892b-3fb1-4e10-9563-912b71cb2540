<template>
  <div class="form-edit-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <n-spin size="large" show>
        <template #description>
          加载表单中...
        </template>
      </n-spin>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <n-result
        status="error"
        :title="error.title || '加载失败'"
        :description="error.message || '无法加载表单，请稍后重试'"
      >
        <template #footer>
          <n-space>
            <n-button type="primary" @click="retryLoad">重新加载</n-button>
            <n-button @click="goBack">返回列表</n-button>
          </n-space>
        </template>
      </n-result>
    </div>

    <!-- 表单编辑界面 -->
    <div v-else-if="instance && template" class="form-edit-content">
      <!-- 表单头部 -->
      <div class="form-header">
        <n-breadcrumb separator="/">
          <n-breadcrumb-item :to="{ path: '/form-fill' }">表单填写</n-breadcrumb-item>
          <n-breadcrumb-item>{{ instance.instance_name || template.title }}</n-breadcrumb-item>
        </n-breadcrumb>
        
        <div class="header-content">
          <div class="header-left">
            <h1 class="form-title">{{ template.title }}</h1>
            <p v-if="template.description" class="form-description">
              {{ template.description }}
            </p>
            
            <!-- 表单信息 -->
            <div class="form-info">
              <div class="info-item">
                <span class="label">创建者:</span>
                <span class="value">{{ userStore.user?.full_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">模板版本:</span>
                <span class="value">{{ instance.template_version }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后保存:</span>
                <span class="value">{{ formatDate(instance.updated_at) }}</span>
              </div>
            </div>
          </div>
          
          <div class="header-right">
            <!-- 状态显示 -->
            <div class="status-card">
              <n-tag
                :type="getStatusType(instance.status)"
                size="large"
                :bordered="false"
              >
                {{ getStatusText(instance.status) }}
              </n-tag>
              
              <div class="completion-info">
                <span class="completion-label">完成度</span>
                <n-progress 
                  :percentage="instance.completion_percentage" 
                  :stroke-width="8"
                  class="completion-progress"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 医疗专用信息栏 -->
      <div class="medical-info-bar">
        <div class="medical-info-content">
          <div class="medical-meta">
            <div class="meta-item">
              <span class="label">操作人员:</span>
              <span class="value">{{ userStore.user?.full_name || userStore.user?.username }}</span>
            </div>
            <div class="meta-item">
              <span class="label">科室:</span>
              <span class="value">{{ userStore.user?.department || '未设置' }}</span>
            </div>
            <div class="meta-item">
              <span class="label">当前时间:</span>
              <span class="value">{{ currentTime }}</span>
            </div>
          </div>
          <div class="medical-status">
            <n-tag :type="getStatusType(instance?.status || 'draft')" size="small">
              {{ getStatusText(instance?.status || 'draft') }}
            </n-tag>
            <span v-if="autoSaving" class="auto-save-indicator">
              <n-icon class="is-loading"><Loading /></n-icon>
              自动保存中...
            </span>
            <span v-else-if="lastSaveTime" class="last-save-time">
              最后保存: {{ formatTime(lastSaveTime) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content">
        <n-card>
          <unified-form-renderer
            v-if="formRenderData"
            :data="formRenderData"
            :config="formRendererConfig"
            @field-change="handleFieldChange"
            @validation-change="handleValidationChange"
            @form-submit="handleFormSubmit"
            @auto-save="handleAutoSave"
          />
        </n-card>
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <n-card>
          <div class="actions-content">
            <div class="actions-left">
              <n-button text @click="goBack" class="back-button">
                <template #icon>
                  <n-icon><ArrowBackOutline /></n-icon>
                </template>
                返回列表
              </n-button>
              
              <n-button
                @click="handleSaveDraft"
                :loading="saving"
                :disabled="instance.status === 'submitted'"
              >
                <crf-icon icon="material-symbols:save" size="16px" />
                保存草稿
              </n-button>
              
              <n-button @click="handlePreview" type="info">
                <crf-icon icon="material-symbols:visibility" size="16px" />
                预览表单
              </n-button>
            </div>
            
            <div class="actions-right">
              <n-button 
                @click="handleSubmit" 
                type="primary" 
                :loading="submitting"
                :disabled="!canSubmit || instance.status === 'submitted'"
                size="large"
              >
                <crf-icon icon="material-symbols:check" size="16px" />
                {{ instance.status === 'submitted' ? '已提交' : '提交表单' }}
              </n-button>
            </div>
          </div>
        </n-card>
      </div>
    </div>

    <!-- 预览对话框 -->
    <n-modal 
      v-model:show="showPreview" 
      title="表单预览" 
      style="width: 80%"
      :mask-closable="false"
      preset="dialog"
      class="fullscreen-modal"
    >
      <div class="preview-content">
        <div class="preview-header">
          <h2>{{ template?.title }}</h2>
          <p>{{ template?.description }}</p>
        </div>
        
        <unified-form-renderer
          v-if="formRenderData"
          :data="formRenderData"
          :config="{ 
            ...formRendererConfig,
            mode: FormRenderMode.PREVIEW,
            readonly: true,
            enableAutoSave: false,
            showMedicalInfo: false
          }"
        />
      </div>
      
      <template #action>
        <n-button @click="showPreview = false">关闭预览</n-button>
        <n-button
          type="primary"
          @click="showPreview = false; handleSubmit()"
          :disabled="!canSubmit || instance?.status === 'submitted'"
        >
          确认提交
        </n-button>
      </template>
    </n-modal>

    <!-- 患者信息对话框（医疗场景专用） -->
    <n-modal
      v-model:show="showPatientDialog"
      title="完善患者信息"
      style="width: 600px"
      :mask-closable="false"
      :closable="false"
      preset="dialog"
    >
      <n-form
        ref="patientFormRef"
        :model="patientInfo"
        :rules="patientRules"
        label-width="120px"
      >
        <n-form-item label="患者编号" path="patientId">
          <n-input
            v-model:value="patientInfo.patientId"
            placeholder="请输入患者编号"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="患者姓名" path="patientName">
          <n-input
            v-model:value="patientInfo.patientName"
            placeholder="请输入患者姓名"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="性别" path="gender">
          <n-radio-group v-model:value="patientInfo.gender">
            <n-radio value="male">男</n-radio>
            <n-radio value="female">女</n-radio>
          </n-radio-group>
        </n-form-item>
        
        <n-form-item label="年龄" path="age">
          <n-input-number
            v-model:value="patientInfo.age"
            :min="0"
            :max="150"
            placeholder="请输入年龄"
          />
        </n-form-item>
        
        <n-form-item label="就诊日期" path="visitDate">
          <n-date-picker
            v-model:value="patientInfo.visitDate"
            type="date"
            placeholder="选择就诊日期"
            style="width: 100%"
          />
        </n-form-item>
        
        <n-form-item label="备注">
          <n-input
            v-model:value="patientInfo.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-button type="primary" @click="handlePatientInfoSubmit">
          确认并开始填写
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDialog, useMessage } from 'naive-ui'
import { ArrowBackOutline } from '@vicons/ionicons5'
import { CrfIcon } from '@crf/components'
import UnifiedFormRenderer from '@/components/UnifiedFormRenderer.vue'
import { instanceAPI } from '@/api'
import { useUserStore } from '@/stores/user-store'
import { 
  FormRenderMode, 
  FormDataSource, 
  type FormRendererConfig 
} from '@/types/form-renderer'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const message = useMessage()
const dialog = useDialog()

// 医疗相关状态
const currentTime = ref('')
const autoSaving = ref(false)
const lastSaveTime = ref<Date | null>(null)

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 格式化时间
const formatTime = (time: Date) => {
  return time.toLocaleTimeString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'draft': return 'info'
    case 'submitted': return 'success'
    case 'locked': return 'warning'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'submitted': return '已提交'
    case 'locked': return '已锁定'
    default: return '未知状态'
  }
}

// 路由参数
const instanceId = route.params.id as string

// 状态管理
const loading = ref(true)
const saving = ref(false)
const submitting = ref(false)
const error = ref<Error | null>(null)
const instance = ref<Record<string, unknown> | null>(null)
const template = ref<Record<string, unknown> | null>(null)
const templateData = ref<Record<string, unknown> | null>(null)
const formData = reactive<Record<string, unknown>>({})
const validationErrors = ref<Record<string, string>>({})
const showPreview = ref(false)
const showPatientDialog = ref(false)

// 患者信息表单
const patientFormRef = ref()
const patientInfo = reactive({
  patientId: '',
  patientName: '',
  gender: '',
  age: null,
  visitDate: '',
  remarks: ''
})

const patientRules = {
  patientId: [
    { required: true, message: '请输入患者编号', trigger: 'blur' }
  ],
  patientName: [
    { required: true, message: '请输入患者姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' }
  ],
  visitDate: [
    { required: true, message: '请选择就诊日期', trigger: 'change' }
  ]
}

// 表单渲染器配置
const formRendererConfig = computed<FormRendererConfig>(() => ({
  mode: FormRenderMode.FILL,
  dataSource: FormDataSource.INSTANCE,
  enableValidation: true,
  enableAutoSave: true,
  autoSaveInterval: 30000, // 30秒自动保存
  readonly: instance.value?.status === 'submitted',
  showProgress: true,
  showMedicalInfo: true,
  onAutoSave: handleAutoSave
}))

// 准备传递给UnifiedFormRenderer的数据
const formRenderData = computed(() => {
  if (!instance.value) return null
  
  return {
    instance: instance.value,
    template: template.value,
    formData: formData
  }
})

// 计算属性
const canSubmit = computed(() => {
  return Object.keys(validationErrors.value).length === 0 && 
         instance.value?.completion_percentage >= 80 // 要求至少80%完成度
})

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 初始化表单
const initForm = async () => {
  try {
    loading.value = true
    error.value = null

    const response = await instanceAPI.getInstance(instanceId)
    if (!response.success) {
      throw new Error(response.message || '获取表单实例失败')
    }

    instance.value = response.data.instance
    template.value = instance.value.template
    templateData.value = template.value.template_data

    // 恢复表单数据
    if (instance.value.form_data) {
      Object.assign(formData, instance.value.form_data)
    }

    // 如果是新建的表单实例且没有患者信息，显示患者信息对话框
    if (instance.value.status === 'draft' && 
        instance.value.completion_percentage === 0 &&
        !formData.patientInfo) {
      showPatientDialog.value = true
    }

  } catch (err) {
    console.error('初始化表单失败:', err)
    error.value = {
      title: '加载失败',
      message: err instanceof Error ? err.message : '未知错误'
    }
  } finally {
    loading.value = false
  }
}

// 处理患者信息提交
const handlePatientInfoSubmit = async () => {
  try {
    await patientFormRef.value.validate()
    
    // 将患者信息加入表单数据
    formData.patientInfo = { ...patientInfo }
    
    // 自动保存
    await saveDraft()
    
    showPatientDialog.value = false
    message.success('患者信息已保存')
  } catch (error) {
    console.error('患者信息验证失败:', error)
  }
}

// 处理字段变更
const handleFieldChange = (fieldId: string, value: unknown) => {
  // 更新表单数据
  formData[fieldId] = value
  
  // 触发自动保存
  debouncedSave()
}

// 处理表单提交
const handleFormSubmit = (data: Record<string, unknown>) => {
  console.log('表单提交:', data)
  handleSubmit()
}

// 处理验证变更
const handleValidationChange = (errors: Record<string, string>) => {
  validationErrors.value = errors
}

// 处理自动保存
const handleAutoSave = async (data: Record<string, unknown>) => {
  console.log('自动保存触发:', data)
  await saveDraft(true) // 标记为自动保存
}

// 防抖保存
let saveTimeout: NodeJS.Timeout | null = null
const debouncedSave = () => {
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
  saveTimeout = setTimeout(async () => {
    await saveDraft(true) // 标记为自动保存
  }, 2000)
}

// 保存草稿
const saveDraft = async (isAutoSave = false) => {
  if (!instance.value || saving.value || instance.value.status === 'submitted') return

  try {
    if (isAutoSave) {
      autoSaving.value = true
    } else {
      saving.value = true
    }
    
    const response = await instanceAPI.updateInstance(instance.value.id, {
      form_data: formData
    })
    
    if (response.success) {
      lastSaveTime.value = new Date()
      
      // 更新实例信息
      const instanceResponse = await instanceAPI.getInstance(instance.value.id)
      if (instanceResponse.success) {
        const newInstance = instanceResponse.data.instance
        instance.value.completion_percentage = newInstance.completion_percentage
        instance.value.updated_at = newInstance.updated_at
      }
      
      if (!isAutoSave) {
        message.success('草稿保存成功')
      }
    }
  } catch (err) {
    console.error('保存草稿失败:', err)
    if (!isAutoSave) {
      message.error('保存失败')
    }
  } finally {
    if (isAutoSave) {
      autoSaving.value = false
    } else {
      saving.value = false
    }
  }
}

// 手动保存草稿
const handleSaveDraft = async () => {
  if (!instance.value) return

  try {
    saving.value = true
    await saveDraft()
    message.success('草稿保存成功')
  } catch (err) {
    message.error('草稿保存失败')
  } finally {
    saving.value = false
  }
}

// 预览表单
const handlePreview = () => {
  showPreview.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!instance.value || !canSubmit.value) {
    if (instance.value?.completion_percentage < 80) {
      message.warning('表单完成度不足80%，请继续完善表单内容')
      return
    }
    return
  }

  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认提交',
        content: `确定要提交表单吗？提交后将无法修改。
      
当前完成度：${instance.value.completion_percentage}%
填写者：${userStore.user?.full_name}
填写时间：${formatDate(new Date().toISOString())}`,
        positiveText: '确定提交',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    submitting.value = true
    
    // 先保存最新数据
    await saveDraft()
    
    // 提交表单
    const response = await instanceAPI.submitInstance(instance.value.id)
    
    if (response.success) {
      message.success('表单提交成功')
      
      // 更新实例状态
      instance.value.status = 'submitted'
      instance.value.completion_percentage = 100
      
      // 3秒后跳转到列表页面
      setTimeout(() => {
        router.push('/form-fill')
      }, 3000)
    }
  } catch (err) {
    if (err !== 'cancel') {
      console.error('提交失败:', err)
      message.error('提交失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 返回列表
const goBack = () => {
  // 获取项目ID - 从模板信息中获取
  const projectId = template.value?.project_id

  if (projectId) {
    // 如果有项目ID，返回到该项目的表单管理页面
    router.push(`/forms?project_id=${projectId}`)
  } else {
    // 如果没有项目ID，返回到全局表单列表
    router.push('/forms')
  }
}

// 重新加载
const retryLoad = () => {
  initForm()
}

// 页面卸载前提醒保存
const beforeUnload = (e: BeforeUnloadEvent) => {
  if (instance.value?.status === 'draft' && !saving.value) {
    e.preventDefault()
    e.returnValue = '您有未保存的更改，确定要离开吗？'
    return '您有未保存的更改，确定要离开吗？'
  }
}

// 初始化
onMounted(() => {
  initForm()
  
  // 启动时间更新定时器
  updateCurrentTime()
  const timeTimer = setInterval(updateCurrentTime, 1000)
  
  window.addEventListener('beforeunload', beforeUnload)
  
  // 清理定时器
  onUnmounted(() => {
    clearInterval(timeTimer)
    window.removeEventListener('beforeunload', beforeUnload)
  })
})
</script>

<style lang="scss" scoped>
.form-edit-page {
  min-height: 100vh;
  background: #f5f7fa;
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
  }
  
  .form-edit-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    
    .medical-info-bar {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #bae6fd;
      border-radius: 8px;
      margin-bottom: 20px;
      padding: 16px 20px;
      
      .medical-info-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        @media (max-width: 768px) {
          flex-direction: column;
          gap: 12px;
        }
      }
      
      .medical-meta {
        display: flex;
        gap: 24px;
        
        @media (max-width: 768px) {
          flex-direction: column;
          gap: 8px;
        }
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          
          .label {
            color: #0369a1;
            font-weight: 500;
          }
          
          .value {
            color: #1e40af;
            font-weight: 600;
          }
        }
      }
      
      .medical-status {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .auto-save-indicator {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #059669;
          
          .n-icon {
            font-size: 14px;
          }
        }
        
        .last-save-time {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
    
    .form-header {
      background: white;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 16px;
        
        .header-left {
          flex: 1;
          
          .form-title {
            font-size: 28px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
          }
          
          .form-description {
            color: #6b7280;
            margin: 0 0 16px 0;
            line-height: 1.6;
            font-size: 16px;
          }
          
          .form-info {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
            
            .info-item {
              display: flex;
              align-items: center;
              font-size: 14px;
              
              .label {
                color: #6b7280;
                margin-right: 8px;
              }
              
              .value {
                color: #374151;
                font-weight: 500;
              }
            }
          }
        }
        
        .header-right {
          .status-card {
            text-align: center;
            min-width: 200px;
            
            .completion-info {
              margin-top: 16px;
              
              .completion-label {
                display: block;
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 8px;
              }
              
              .completion-progress {
                width: 100%;
              }
            }
          }
        }
      }
    }
    
    .form-content {
      margin-bottom: 20px;
      
      .n-card {
        border-radius: 12px;
        min-height: 500px;
      }
    }
    
    .form-actions {
      position: sticky;
      bottom: 20px;
      z-index: 100;
      
      .n-card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }
      
      .actions-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .actions-left,
        .actions-right {
          display: flex;
          gap: 12px;
        }
        
        .n-button {
          display: flex;
          align-items: center;
          gap: 6px;
        }
      }
    }
  }
  
  .preview-content {
    .preview-header {
      text-align: center;
      margin-bottom: 24px;
      padding-bottom: 24px;
      border-bottom: 1px solid #e5e7eb;
      
      h2 {
        margin: 0 0 8px 0;
        color: #1f2937;
      }
      
      p {
        margin: 0;
        color: #6b7280;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-edit-page {
    .form-edit-content {
      padding: 16px;
      
      .form-header {
        padding: 16px;
        
        .header-content {
          flex-direction: column;
          gap: 16px;
          
          .header-right {
            width: 100%;
            
            .status-card {
              min-width: auto;
            }
          }
        }
        
        .form-info {
          flex-direction: column;
          gap: 8px !important;
        }
      }
      
      .form-actions {
        .actions-content {
          flex-direction: column;
          gap: 12px;
          
          .actions-left,
          .actions-right {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 统一返回按钮样式
.back-button {
  color: var(--color-text-secondary);
  font-weight: 500;

  &:hover {
    color: var(--color-primary);
  }

  :deep(.n-button__content) {
    gap: 6px;
  }
}
</style>