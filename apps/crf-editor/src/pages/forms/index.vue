<template>
  <div class="forms-container">
    <!-- 项目信息显示 -->
    <div v-if="currentProject" class="project-info-banner">
      <div class="project-info-content">
        <div class="project-icon">
          <n-icon size="20" color="#3b82f6">
            <FolderOutline />
          </n-icon>
        </div>
        <div class="project-details">
          <h3 class="project-name">{{ currentProject.name }}</h3>
          <p class="project-description">{{ currentProject.description || '暂无描述' }}</p>
        </div>
        <div class="project-actions">
          <n-button text size="small" @click="viewProjectDetails">
            查看项目详情
          </n-button>
        </div>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <StatisticsCards
      :active-tab="activeTab"
      :published-count="publishedCount"
      :unpublished-count="unpublishedCount"
      :deleted-count="deletedCount"
      :total-count="totalCount"
      @tab-change="handleTabChange"
    />

    <!-- 模板管理容器 -->
    <div class="forms-management-container">
      <!-- 工具栏 -->
      <FormListToolbar
        :view-mode="viewMode"
        :search-keyword="searchKeyword"
        :selected-count="selectedCount"
        :total-count="tabFilteredForms.length"
        :is-all-selected="isAllSelected(tabFilteredForms)"
        :is-indeterminate="isIndeterminate(tabFilteredForms)"
        :batch-deleting="batchDeleting"
        @create-form="createDialogVisible = true"
        @select-all="handleSelectAll"
        @batch-delete="handleBatchDelete"
        @clear-selection="clearSelection"
        @search="handleSearch"
        @view-mode-change="handleViewModeChange"
      />

      <!-- 表单列表区域 -->
      <div class="forms-list">
        <!-- 加载状态 -->
        <LoadingSkeletonGrid 
          v-if="shouldShowSkeleton" 
          :skeleton-count="6" 
        />

        <!-- 内容区域 -->
        <div v-else-if="tabFilteredForms.length > 0" class="forms-content">
          <!-- 顶部加载指示器 -->
          <LoadingIndicator 
            :visible="loading" 
            message="正在更新..." 
          />
          
          <!-- 表格视图 -->
          <FormTableView
            v-if="viewMode === 'list'"
            :forms="tabFilteredForms"
            :columns="tableColumns"
            :selected-form-ids="selectedFormIds"
            :pagination="pagination"
            :loading="loading"
            @selection-change="handleTableSelection"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
          
          <!-- 网格视图 -->
          <FormGridView
            v-else
            :forms="tabFilteredForms"
            :selected-form-ids="selectedFormIds"
            :duplicating-form-ids="duplicatingFormIds"
            :deleting-form-ids="deletingFormIds"
            :pagination="pagination"
            :has-more="hasMoreData"
            :loading="loading"
            :creating="creating"
            @select="handleFormSelect"
            @edit="(form) => handleEdit(form, activeTab, currentProject?.id)"
            @fill="(form) => handleFillForm(form, activeTab)"
            @view-data="handleViewData"
            @command="handleCommand"
            @load-more="handleLoadMore"
          />
        </div>

        <!-- 空状态 -->
        <EmptyState
          v-else-if="!loading"
          @create-form="createDialogVisible = true"
        />
      </div>
    </div>

    <!-- 新建表单对话框 -->
    <CreateFormDialog
      v-model:visible="createDialogVisible"
      :loading="creating"
      :existing-forms="forms"
      @submit="handleCreateForm"
      @cancel="createDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { FolderOutline } from '@vicons/ionicons5'

// 组件导入
import StatisticsCards from '@/components/forms/StatisticsCards.vue'
import FormListToolbar from '@/components/forms/FormListToolbar.vue'
import FormGridView from '@/components/forms/FormGridView.vue'
import FormTableView from '@/components/forms/FormTableView.vue'
import LoadingSkeletonGrid from '@/components/forms/LoadingSkeletonGrid.vue'
import LoadingIndicator from '@/components/forms/LoadingIndicator.vue'
import EmptyState from '@/components/forms/EmptyState.vue'
import CreateFormDialog from '@/components/forms/CreateFormDialog.vue'

// 组合式API导入
import { useFormsPage } from '@/composables/forms/useFormsPage'

defineOptions({
  name: 'FormsPage'
})

// 使用主要的业务逻辑组合式函数
const {
  // 状态
  forms,
  loading,
  activeTab,
  searchKeyword,
  viewMode,
  tabSwitching,
  operationLoading,
  createDialogVisible,
  creating,
  currentProject,

  // 分页
  pagination,
  hasMoreData,

  // 计算属性
  publishedCount,
  unpublishedCount,
  deletedCount,
  totalCount,
  tabFilteredForms,
  tableColumns,

  // 选择相关
  selectedFormIds,
  batchDeleting,
  selectedCount,
  isAllSelected,
  isIndeterminate,

  // 操作状态
  duplicatingFormIds,
  deletingFormIds,

  // 方法
  handleTabChange,
  handleSelectAll,
  handleSearch,
  handleBatchDelete,
  handleCommand,
  handleCreateForm,
  handleFormSelect,
  handleTableSelection,
  clearSelection,
  handleViewModeChange,
  handleEdit,
  handleFillForm,
  handleViewData,
  handlePageChange,
  handlePageSizeChange,
  handleLoadMore
} = useFormsPage()

const router = useRouter()

// 查看项目详情
const viewProjectDetails = () => {
  if (currentProject.value?.id) {
    router.push(`/projects/${currentProject.value.id}`)
  }
}

// 计算是否显示骨架屏
const shouldShowSkeleton = computed(() => {
  return (loading.value && forms.value.length === 0) || tabSwitching.value || operationLoading.value
})
</script>

<style lang="scss" scoped>
@use './styles/index.scss';
@use './styles/form-list.scss';
@use './styles/form-card.scss';
@use './styles/form-table.scss';
@use './styles/dialogs.scss';
@use './styles/form-drawer.scss';
@use './styles/statistics.scss';

// 骨架屏样式
.form-card-skeleton {
  .skeleton-checkbox {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 2;
  }
  
  .skeleton-line {
    border-radius: 4px;
  }
  
  .skeleton-circle-line {
    border-radius: 50%;
  }
}

// 顶部加载指示器
.top-loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  color: #0369a1;
  font-size: 14px;
  
  .is-loading {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 加载指示器过渡动画
.loading-indicator-enter-active,
.loading-indicator-leave-active {
  transition: all 0.3s ease;
}

.loading-indicator-enter-from,
.loading-indicator-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 表单卡片过渡动画
.form-card-fade-enter-active,
.form-card-fade-leave-active {
  transition: all 0.3s ease;
}

.form-card-fade-enter-from,
.form-card-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.form-card-fade-move {
  transition: transform 0.3s ease;
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
}

.empty-description {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6b7280;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

// 高亮新创建表单的动画
:deep(.highlight-new-form) {
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
  }
}
</style>