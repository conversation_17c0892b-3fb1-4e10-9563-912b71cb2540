<template>
  <div class="form-fill-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <n-breadcrumb separator="/">
        <n-breadcrumb-item :to="{ path: '/forms' }">表单管理</n-breadcrumb-item>
        <n-breadcrumb-item>表单填写</n-breadcrumb-item>
      </n-breadcrumb>
      
      <div class="header-title">
        <h1>表单填写中心</h1>
        <p>选择并填写已发布的CRF表单</p>
      </div>
      
      <div class="header-actions">
        <n-button @click="refreshList" :loading="loading">
          <template #icon>
            <crf-icon icon="material-symbols:refresh" size="16px" />
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <crf-icon icon="material-symbols:description" size="24px" />
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total }}</div>
                <div class="stats-label">可用模板</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon draft">
                <crf-icon icon="material-symbols:edit-document" size="24px" />
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.draft }}</div>
                <div class="stats-label">草稿实例</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon submitted">
                <crf-icon icon="material-symbols:check-circle" size="24px" />
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.submitted }}</div>
                <div class="stats-label">已提交表单</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon completed">
                <crf-icon icon="material-symbols:task-alt" size="24px" />
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.completed }}</div>
                <div class="stats-label">已完成表单</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <n-card>
        <div class="filter-content">
          <n-grid :cols="4" :x-gap="16" align="center">
            <n-grid-item :span="2">
              <n-input
                v-model:value="filters.search"
                placeholder="搜索表单名称或描述"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <crf-icon icon="material-symbols:search" size="16px" />
                </template>
              </n-input>
            </n-grid-item>
            
            <n-grid-item>
              <n-select
                v-model:value="filters.status"
                placeholder="表单状态"
                clearable
                @update:value="handleFilter"
              >
                <n-option label="全部状态" value="" />
                <n-option label="草稿" value="draft" />
                <n-option label="已提交" value="submitted" />
                <n-option label="已锁定" value="locked" />
                <n-option label="已完成" value="completed" />
              </n-select>
            </n-grid-item>
            
            <n-grid-item>
              <n-select
                v-model:value="filters.template"
                placeholder="选择模板"
                clearable
                @update:value="handleFilter"
              >
                <n-option label="全部模板" value="" />
                <n-option
                  v-for="template in availableTemplates"
                  :key="template.id"
                  :label="template.title"
                  :value="template.id"
                />
              </n-select>
            </n-grid-item>
          </n-grid>
          
          <div style="margin-top: 16px; text-align: right;">
            <n-button type="primary" @click="handleCreateNew">
              <template #icon>
                <crf-icon icon="material-symbols:add" size="16px" />
              </template>
              新建实例
            </n-button>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 表单列表 -->
    <div class="form-list-section">
      <n-card>
        <template #header>
          <div class="card-header">
            <span>我的表单实例</span>
            <n-button text @click="showHistory = true; loadHistory()">
              <template #icon>
                <crf-icon icon="material-symbols:history" size="16px" />
              </template>
              查看历史
            </n-button>
          </div>
        </template>
        
        <n-spin :show="loading">
          <div class="form-list">
            <div v-if="paginatedInstances.length === 0" class="empty-state">
              <crf-icon icon="material-symbols:description-outline" size="64px" />
              <h3>暂无数据实例</h3>
              <p>点击"新建实例"开始录入数据</p>
              <n-button type="primary" @click="handleCreateNew">
                新建实例
              </n-button>
            </div>
            
            <div v-else class="instances-grid">
              <div
                v-for="instance in paginatedInstances"
                :key="instance.id"
                class="instance-card"
                @click="handleOpenInstance(instance)"
              >
                <div class="instance-header">
                  <div class="instance-title">
                    {{ instance.template?.title || instance.instance_name || '未命名表单' }}
                  </div>
                  <n-tag
                    :type="getStatusType(instance.status)"
                    size="small"
                  >
                    {{ getStatusText(instance.status) }}
                  </n-tag>
                </div>
                
                <div class="instance-info">
                  <div class="info-item">
                    <span class="label">模板版本:</span>
                    <span class="value">{{ instance.template_version }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">完成度:</span>
                    <n-progress
                      :percentage="instance.completion_percentage"
                      :stroke-width="6"
                      :show-indicator="false"
                      class="progress"
                    />
                    <span class="value">{{ instance.completion_percentage }}%</span>
                  </div>
                  <div class="info-item">
                    <span class="label">更新时间:</span>
                    <span class="value">{{ formatDate(instance.updated_at) }}</span>
                  </div>
                </div>
                
                <div class="instance-actions" @click.stop>
                  <n-button
                    type="primary"
                    size="small"
                    @click="handleFillForm(instance)"
                  >
                    {{ instance.status === 'submitted' ? '查看' : '继续填写' }}
                  </n-button>
                  
                  <n-dropdown 
                    @select="(key) => handleInstanceAction({ action: key, instance })" 
                    trigger="click"
                    :options="[
                      { label: '查看详情', key: 'view', disabled: instance.status === 'draft' },
                      { label: '复制实例', key: 'copy' },
                      { label: '导出数据', key: 'export', disabled: instance.status === 'draft' },
                      { label: '删除实例', key: 'delete', disabled: instance.status === 'submitted' }
                    ]"
                  >
                    <n-button size="small" text>
                      <crf-icon icon="material-symbols:more-vert" size="16px" />
                    </n-button>
                  </n-dropdown>
                </div>
              </div>
            </div>
            
            <!-- 分页 -->
            <div v-if="filteredInstances.length > pageSize" class="pagination">
              <n-pagination
                v-model:page="currentPage"
                :page-size="pageSize"
                :item-count="filteredInstances.length"
                show-quick-jumper
              />
            </div>
          </div>
        </n-spin>
      </n-card>
    </div>

    <!-- 新建表单对话框 -->
    <n-modal
      v-model:show="showCreateDialog"
      preset="dialog"
      title="选择模板"
      style="width: 800px"
      :mask-closable="false"
    >
      <div class="template-selection">
        <n-spin :show="loadingTemplates">
          <div class="templates-grid">
            <div
              v-for="template in availableTemplates"
              :key="template.id"
              class="template-card"
              :class="{ selected: selectedTemplate?.id === template.id }"
              @click="selectedTemplate = template"
            >
              <div class="template-header">
                <h4>{{ template.title }}</h4>
                <n-tag size="small">{{ template.version }}</n-tag>
              </div>
              <p class="template-description">
                {{ template.description || '暂无描述' }}
              </p>
              <div class="template-meta">
                <span>创建者: {{ template.creator?.full_name }}</span>
                <span>发布时间: {{ formatDate(template.published_at) }}</span>
              </div>
            </div>
          </div>
        </n-spin>
      </div>
      
      <template #action>
        <n-button @click="showCreateDialog = false">取消</n-button>
        <n-button
          type="primary"
          :disabled="!selectedTemplate"
          :loading="creating"
          @click="handleCreateInstance"
        >
          开始填写
        </n-button>
      </template>
    </n-modal>

    <!-- 历史记录对话框 -->
    <n-modal
      v-model:show="showHistory"
      preset="dialog"
      title="表单填写历史"
      style="width: 1000px"
    >
      <div class="history-content">
        <n-spin :show="loadingHistory">
          <div class="history-list">
            <!-- 筛选条件 -->
            <div class="history-filters">
              <n-grid :cols="3" :x-gap="16">
                <n-grid-item>
                  <n-select
                    v-model:value="historyFilters.status"
                    placeholder="状态筛选"
                    clearable
                    @update:value="loadHistory"
                  >
                    <n-option label="全部状态" value="" />
                    <n-option label="草稿" value="draft" />
                    <n-option label="已提交" value="submitted" />
                    <n-option label="已完成" value="completed" />
                  </n-select>
                </n-grid-item>
                <n-grid-item>
                  <n-date-picker
                    v-model:value="historyFilters.dateRange"
                    type="daterange"
                    separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @update:value="loadHistory"
                  />
                </n-grid-item>
                <n-grid-item>
                  <n-button @click="refreshHistory">
                    <template #icon>
                      <crf-icon icon="material-symbols:refresh" size="16px" />
                    </template>
                    刷新
                  </n-button>
                </n-grid-item>
              </n-grid>
            </div>

            <!-- 历史记录列表 -->
            <div v-if="historyList.length > 0" class="history-items">
              <div
                v-for="item in historyList"
                :key="item.id"
                class="history-item"
                @click="handleViewHistory(item)"
              >
                <div class="history-header">
                  <div class="history-info">
                    <h4 class="history-title">{{ item.template?.title || '未知表单' }}</h4>
                    <p class="history-meta">
                      实例ID: {{ item.id }} | 模板版本: v{{ item.template_version }}
                    </p>
                  </div>
                  <n-tag
                    :type="getStatusType(item.status)"
                    size="small"
                  >
                    {{ getStatusText(item.status) }}
                  </n-tag>
                </div>
                
                <div class="history-details">
                  <div class="detail-row">
                    <span class="label">完成进度:</span>
                    <n-progress
                      :percentage="item.completion_percentage"
                      :stroke-width="6"
                      :show-indicator="false"
                      class="progress"
                    />
                    <span class="value">{{ item.completion_percentage }}%</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">创建时间:</span>
                    <span class="value">{{ formatDate(item.created_at) }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">最后更新:</span>
                    <span class="value">{{ formatDate(item.updated_at) }}</span>
                  </div>
                  <div v-if="item.submitted_at" class="detail-row">
                    <span class="label">提交时间:</span>
                    <span class="value">{{ formatDate(item.submitted_at) }}</span>
                  </div>
                </div>
                
                <div class="history-actions">
                  <n-button
                    size="small"
                    @click.stop="handleContinueFill(item)"
                    :disabled="item.status === 'submitted'"
                  >
                    {{ item.status === 'submitted' ? '查看详情' : '继续填写' }}
                  </n-button>
                  <n-dropdown 
                    @select="(cmd) => handleHistoryAction(cmd, item)" 
                    trigger="click"
                    :options="[
                      { label: '查看详情', key: 'view' },
                      { label: '复制实例', key: 'copy' },
                      { label: '导出数据', key: 'export', disabled: item.status === 'draft' },
                      { label: '删除实例', key: 'delete', disabled: item.status === 'submitted' }
                    ]"
                  >
                    <n-button size="small" text>
                      <crf-icon icon="material-symbols:more-vert" size="16px" />
                    </n-button>
                  </n-dropdown>
                </div>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-else-if="!loadingHistory" class="history-empty">
              <crf-icon icon="material-symbols:history" size="64px" />
              <h3>暂无填写历史</h3>
              <p>您还没有填写过任何表单</p>
              <n-button type="primary" @click="showHistory = false">
                开始填写表单
              </n-button>
            </div>
            
            <!-- 分页 -->
            <div v-if="historyList.length > 0" class="history-pagination">
              <n-pagination
                v-model:page="historyPagination.current"
                :page-size="historyPagination.size"
                :item-count="historyPagination.total"
                show-quick-jumper
                @update:page="loadHistory"
              />
            </div>
          </div>
        </n-spin>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  useDialog, 
  useMessage,
  NBreadcrumb,
  NBreadcrumbItem,
  NButton,
  NGrid,
  NGridItem,
  NCard,
  NInput,
  NSelect,
  NTag,
  NProgress,
  NSpin,
  NPagination,
  NModal,
  NDropdown,
  NDatePicker
} from 'naive-ui'
import { CrfIcon } from '@crf/components'
import { templateAPI, instanceAPI } from '@/api'
import { useUserStore } from '@/stores/user-store'

const router = useRouter()
const userStore = useUserStore()
const message = useMessage()
const dialog = useDialog()

// 状态管理
const loading = ref(false)
const loadingTemplates = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)
const showHistory = ref(false)
const currentPage = ref(1)
const pageSize = 20

// 数据
const instances = ref<Record<string, unknown>[]>([])
const availableTemplates = ref<Record<string, unknown>[]>([])
const selectedTemplate = ref<Record<string, unknown> | null>(null)

// 筛选条件
const filters = reactive({
  search: '',
  status: '',
  template: ''
})

// 统计数据
const stats = computed(() => {
  const total = instances.value.length
  const draft = instances.value.filter(i => i.status === 'draft').length
  const submitted = instances.value.filter(i => i.status === 'submitted').length
  const completed = instances.value.filter(i => i.status === 'completed').length
  
  return { total, draft, submitted, completed }
})

// 过滤后的实例
const filteredInstances = computed(() => {
  let result = instances.value

  if (filters.search) {
    result = result.filter(instance => 
      (instance.template?.title || '').toLowerCase().includes(filters.search.toLowerCase()) ||
      (instance.instance_name || '').toLowerCase().includes(filters.search.toLowerCase())
    )
  }

  if (filters.status) {
    result = result.filter(instance => instance.status === filters.status)
  }

  if (filters.template) {
    result = result.filter(instance => instance.template_id === filters.template)
  }

  return result
})

// 分页后的实例
const paginatedInstances = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredInstances.value.slice(start, end)
})

// 历史记录相关
const loadingHistory = ref(false)
const historyList = ref<Record<string, unknown>[]>([])
const historyFilters = reactive({
  status: '',
  dateRange: null as unknown
})
const historyPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'draft': return 'info'
    case 'submitted': return 'success'
    case 'locked': return 'warning'
    case 'completed': return 'success'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'submitted': return '已提交'
    case 'locked': return '已锁定'
    case 'completed': return '已完成'
    default: return '未知状态'
  }
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 加载历史记录
const loadHistory = async () => {
  try {
    loadingHistory.value = true
    
    const params: Record<string, unknown> = {
      limit: historyPagination.size,
      offset: (historyPagination.current - 1) * historyPagination.size
    }
    
    if (historyFilters.status) {
      params.status = historyFilters.status
    }
    
    if (historyFilters.dateRange && historyFilters.dateRange.length === 2) {
      params.start_date = historyFilters.dateRange[0].toISOString()
      params.end_date = historyFilters.dateRange[1].toISOString()
    }
    
    const response = await instanceAPI.getMyInstances(params)
    
    if (response.success) {
      historyList.value = response.data.instances || []
      historyPagination.total = response.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
    message.error('加载历史记录失败')
  } finally {
    loadingHistory.value = false
  }
}

// 刷新历史记录
const refreshHistory = () => {
  historyPagination.current = 1
  loadHistory()
}

// 查看历史记录详情
const handleViewHistory = (item: Record<string, unknown>) => {
  if (item.status === 'submitted') {
    router.push(`/form-fill/view/${item.id}`)
  } else {
    router.push(`/form-fill/edit/${item.id}`)
  }
  showHistory.value = false
}

// 继续填写
const handleContinueFill = (item: Record<string, unknown>) => {
  if (item.status === 'submitted') {
    router.push(`/form-fill/view/${item.id}`)
  } else {
    router.push(`/form-fill/edit/${item.id}`)
  }
  showHistory.value = false
}

// 历史记录操作
const handleHistoryAction = async (action: string, item: Record<string, unknown>) => {
  switch (action) {
    case 'view':
      router.push(`/form-fill/view/${item.id}`)
      showHistory.value = false
      break
      
    case 'copy':
      try {
        const response = await instanceAPI.copyInstance(item.id)
        if (response.success) {
          message.success('实例复制成功')
          loadHistory()
        }
      } catch (error) {
        message.error('复制失败')
      }
      break
      
    case 'export':
      try {
        const response = await instanceAPI.exportInstance(item.id)
        if (response.success) {
          message.success('导出成功')
        }
      } catch (error) {
        message.error('导出失败')
      }
      break
      
    case 'delete':
      try {
        await new Promise<void>((resolve, reject) => {
          dialog.warning({
            title: '确认删除',
            content: '确定要删除这个表单实例吗？此操作不可恢复。',
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => resolve(),
            onNegativeClick: () => reject(new Error('User cancelled'))
          })
        })
        
        const response = await instanceAPI.deleteInstance(item.id)
        if (response.success) {
          message.success('删除成功')
          loadHistory()
        }
      } catch (error) {
        if (error !== 'cancel') {
          message.error('删除失败')
        }
      }
      break
  }
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    // 并行加载实例和模板
    const [instancesResponse, templatesResponse] = await Promise.all([
      instanceAPI.getMyInstances(),
      templateAPI.getPublishedTemplates()
    ])
    
    if (instancesResponse.success) {
      instances.value = instancesResponse.data.instances || []
    }
    
    if (templatesResponse.success) {
      availableTemplates.value = templatesResponse.data.templates || []
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新列表
const refreshList = () => {
  loadData()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 筛选处理
const handleFilter = () => {
  currentPage.value = 1
}

// 新建实例
const handleCreateNew = async () => {
  try {
    loadingTemplates.value = true
    const response = await templateAPI.getPublishedTemplates()
    
    if (response.success) {
      availableTemplates.value = response.data.templates || []
      showCreateDialog.value = true
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  } finally {
    loadingTemplates.value = false
  }
}

// 创建实例
const handleCreateInstance = async () => {
  if (!selectedTemplate.value) return
  
  try {
    creating.value = true
    const response = await instanceAPI.createInstance({
      template_id: selectedTemplate.value.id
    })
    
    if (response.success) {
      message.success('表单实例创建成功')
      showCreateDialog.value = false
      selectedTemplate.value = null
      
      // 跳转到填写页面
      const instanceId = response.data.instance.id
      router.push(`/form-fill/edit/${instanceId}`)
    }
  } catch (error) {
    console.error('创建实例失败:', error)
    message.error('创建实例失败')
  } finally {
    creating.value = false
  }
}

// 打开实例
const handleOpenInstance = (instance: Record<string, unknown>) => {
  if (instance.status === 'draft') {
    handleFillForm(instance)
  } else {
    // 查看已提交的表单
    router.push(`/form-fill/view/${instance.id}`)
  }
}

// 填写表单
const handleFillForm = (instance: Record<string, unknown>) => {
  if (instance.status === 'submitted') {
    router.push(`/form-fill/view/${instance.id}`)
  } else {
    router.push(`/form-fill/edit/${instance.id}`)
  }
}

// 实例操作
const handleInstanceAction = async ({ action, instance }: { action: string; instance: Record<string, unknown> }) => {
  switch (action) {
    case 'view':
      router.push(`/form-fill/view/${instance.id}`)
      break
      
    case 'copy':
      try {
        const response = await instanceAPI.copyInstance(instance.id)
        if (response.success) {
          message.success('实例复制成功')
          loadData()
        }
      } catch (error) {
        message.error('复制失败')
      }
      break
      
    case 'export':
      try {
        const response = await instanceAPI.exportInstance(instance.id)
        if (response.success) {
          // 处理导出逻辑
          message.success('导出成功')
        }
      } catch (error) {
        message.error('导出失败')
      }
      break
      
    case 'delete':
      try {
        await new Promise<void>((resolve, reject) => {
          dialog.warning({
            title: '确认删除',
            content: '确定要删除这个表单实例吗？此操作不可恢复。',
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => resolve(),
            onNegativeClick: () => reject(new Error('User cancelled'))
          })
        })
        
        const response = await instanceAPI.deleteInstance(instance.id)
        if (response.success) {
          message.success('删除成功')
          loadData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          message.error('删除失败')
        }
      }
      break
  }
}

// 初始化
onMounted(() => {
  loadData()
  
  // 检查是否有模板参数，如果有则自动创建实例
  const route = useRoute()
  const templateId = route.query.template as string
  if (templateId) {
    handleCreateInstanceFromTemplate(templateId)
  }
})

// 从模板自动创建实例
const handleCreateInstanceFromTemplate = async (templateId: string) => {
  try {
    // 先检查模板是否存在于可用模板列表中
    const template = availableTemplates.value.find(t => t.id === templateId)
    if (!template) {
      message.warning('模板不存在或未发布')
      return
    }
    
    selectedTemplate.value = template
    creating.value = true
    
    const response = await instanceAPI.createInstance({
      template_id: templateId
    })
    
    if (response.success) {
      message.success('表单实例创建成功，即将跳转填写页面')
      
      // 跳转到填写页面
      const instanceId = response.data.instance.id
      router.push(`/form-fill/edit/${instanceId}`)
    }
  } catch (error) {
    console.error('自动创建实例失败:', error)
    message.error('创建表单实例失败，请手动创建')
  } finally {
    creating.value = false
    selectedTemplate.value = null
  }
}
</script>

<style lang="scss" scoped>
.form-fill-page {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    .header-title {
      margin: 16px 0;
      
      h1 {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 8px 0;
      }
      
      p {
        color: #6b7280;
        margin: 0;
      }
    }
    
    .header-actions {
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stats-card {
      .stats-content {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stats-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          
          &.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.draft {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.submitted {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.completed {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .stats-info {
          .stats-number {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1;
          }
          
          .stats-label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .filter-section {
    margin-bottom: 20px;
    
    .filter-content {
      padding: 16px 0;
    }
  }
  
  .form-list-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #6b7280;
      
      h3 {
        margin: 16px 0 8px 0;
        color: #374151;
      }
      
      p {
        margin: 0 0 20px 0;
      }
    }
    
    .instances-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
      
      .instance-card {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
        
        .instance-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          
          .instance-title {
            font-weight: 500;
            color: #1f2937;
            flex: 1;
            margin-right: 12px;
            line-height: 1.4;
          }
        }
        
        .instance-info {
          margin-bottom: 16px;
          
          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            
            .label {
              color: #6b7280;
              width: 80px;
              flex-shrink: 0;
            }
            
            .value {
              color: #374151;
              margin-left: 8px;
            }
            
            .progress {
              flex: 1;
              margin: 0 8px;
            }
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
        
        .instance-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
    
    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
  
  .template-selection {
    .templates-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
      max-height: 400px;
      overflow-y: auto;
      
      .template-card {
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          border-color: #3b82f6;
        }
        
        &.selected {
          border-color: #3b82f6;
          background-color: #eff6ff;
        }
        
        .template-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
          
          h4 {
            margin: 0;
            color: #1f2937;
            font-size: 16px;
            flex: 1;
            margin-right: 12px;
          }
        }
        
        .template-description {
          color: #6b7280;
          font-size: 14px;
          margin: 0 0 12px 0;
          line-height: 1.4;
        }
        
        .template-meta {
          display: flex;
          flex-direction: column;
          gap: 4px;
          font-size: 12px;
          color: #9ca3af;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-fill-page {
    padding: 16px;
    
    .stats-cards {
      .n-row {
        --n-row-gutter: 8px;
      }
      
      .n-col {
        margin-bottom: 8px;
      }
    }
    
    .instances-grid {
      grid-template-columns: 1fr !important;
    }
    
    .templates-grid {
      grid-template-columns: 1fr !important;
    }
  }
}

.history-content {
  .history-filters {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
  }
  
  .history-items {
    .history-item {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
      }
      
      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .history-info {
          flex: 1;
          
          .history-title {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
          }
          
          .history-meta {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
      
      .history-details {
        margin-bottom: 12px;
        
        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          font-size: 14px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label {
            width: 80px;
            color: #6b7280;
            flex-shrink: 0;
          }
          
          .value {
            color: #374151;
            margin-left: 8px;
          }
          
          .progress {
            flex: 1;
            margin: 0 8px;
          }
        }
      }
      
      .history-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .n-button {
          font-size: 12px;
        }
      }
    }
  }
  
  .history-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
    
    h3 {
      margin: 16px 0 8px 0;
      color: #374151;
    }
    
    p {
      margin: 0 0 20px 0;
    }
  }
  
  .history-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>