<template>
  <div class="form-data-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <n-button @click="goBack" text class="back-button">
          <template #icon>
            <n-icon><ArrowLeft /></n-icon>
          </template>
          返回
        </n-button>
        <div class="header-info">
          <h1 class="page-title">{{ template?.title || template?.name || '表单数据' }}</h1>
        </div>
      </div>
      
      <div class="header-actions">
        <n-tag v-if="template" type="info" size="large">
          版本 {{ template.version || '1.0' }}
        </n-tag>
        <n-badge :value="instances.length" :max="999" class="instances-count">
          <n-button quaternary>
            <template #icon>
              <n-icon><Document /></n-icon>
            </template>
            数据条目
          </n-button>
        </n-badge>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-cards" v-if="instances.length > 0">
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <n-icon><DataBoard /></n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ instances.length }}</div>
                <div class="stat-label">总记录数</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon completed">
                <n-icon><CircleCheck /></n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ completedCount }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon progress">
                <n-icon><Clock /></n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ inProgressCount }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon average">
                <n-icon><TrendCharts /></n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ averageProgress }}%</div>
                <div class="stat-label">平均完成度</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-container">
      <n-card>
        <template #header>
          <div class="table-header">
            <span>表单数据详情</span>
            <div class="table-actions">
              <n-button @click="refreshData" :loading="loading">
                <template #icon>
                  <n-icon><Refresh /></n-icon>
                </template>
                刷新数据
              </n-button>
            </div>
          </div>
        </template>

        <!-- 空状态 -->
        <div v-if="!loading && instances.length === 0" class="empty-state">
          <n-empty description="暂无数据">
            <template #icon>
              <n-icon size="64" color="#C0C4CC"><DataBoard /></n-icon>
            </template>
            <template #extra>
              <p>该模板还没有录入数据</p>
              <p>用户录入数据后，数据将在这里显示</p>
              <n-button type="primary" @click="goBack">
                返回
              </n-button>
            </template>
          </n-empty>
        </div>

        <!-- 数据表格 -->
        <DataTable
          v-else-if="!loading"
          :instances="instances"
          :template-id="templateId"
          :template-schema="template?.schema"
          :height="600"
          :enable-version-diff="true"
          @instance-selected="handleInstanceSelected"
          @export-requested="handleExportRequested"
          @version-compare="handleVersionCompare"
          @refresh="refreshData"
        />
        
        <!-- 加载状态 -->
        <n-spin v-if="loading" style="width: 100%; height: 200px;" />
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import {
  ArrowBackOutline as ArrowLeft, 
  DocumentOutline as Document, 
  GridOutline as DataBoard, 
  CheckmarkCircleOutline as CircleCheck, 
  TimeOutline as Clock, 
  TrendingUpOutline as TrendCharts, 
  RefreshOutline as Refresh
} from '@vicons/ionicons5'
import DataTable from '@/components/data/DataTable.vue'
import { templateAPI, instanceAPI, initNetworkClients } from '@/api'

// 路由信息
const route = useRoute()
const router = useRouter()
const templateId = ref(route.params.id as string)

// 初始化 Naive UI 消息和对话框
const message = useMessage()
const dialog = useDialog()

// 数据状态
const loading = ref(true)
const template = ref<Record<string, unknown> | null>(null)
const instances = ref<Record<string, unknown>[]>([])

// 计算属性 - 数据统计
const completedCount = computed(() => 
  instances.value.filter(instance => 
    instance.status === 'completed' || instance.status === 'submitted'
  ).length
)

const inProgressCount = computed(() => 
  instances.value.filter(instance => 
    instance.status === 'in_progress' || instance.status === 'draft'
  ).length
)

const averageProgress = computed(() => {
  if (instances.value.length === 0) return 0
  const totalProgress = instances.value.reduce((sum, instance) => 
    sum + (instance.completion_percentage || 0), 0
  )
  return Math.round(totalProgress / instances.value.length)
})

// 方法
const goBack = async () => {
  // 尝试从模板信息中获取项目ID
  if (templateId.value && templateId.value !== 'demo' && templateId.value !== 'demo-template-123') {
    try {
      const response = await templateAPI.getTemplate(templateId.value)
      if (response.success && response.data?.template?.project_id) {
        const projectId = response.data.template.project_id
        router.push(`/forms?project_id=${projectId}`)
        return
      }
    } catch (error) {
      console.warn('获取模板项目信息失败:', error)
    }
  }

  // 如果无法获取项目ID，返回全局表单列表
  router.push('/forms')
}

const loadTemplate = async () => {
  try {
    console.log('开始加载模板信息，模板ID:', templateId.value)
    
    // 模拟模板数据，包含schema
    const mockTemplate = {
      id: templateId.value,
      title: 'CRF临床试验表单',
      name: 'CRF临床试验表单',
      description: '用于临床数据收集的标准表单',
      version: '1.0',
      status: 'published',
      created_at: '2024-07-01T00:00:00Z',
      schema: {
        list: [
          {
            type: 'input',
            model: 'input-patient-name',
            config: {
              label: '患者姓名',
              placeholder: '请输入患者姓名',
              required: true
            }
          },
          {
            type: 'number',
            model: 'input-patient-age',
            config: {
              label: '患者年龄',
              placeholder: '请输入年龄',
              required: true
            }
          },
          {
            type: 'select',
            model: 'select-gender',
            config: {
              label: '性别',
              required: true,
              options: [
                { label: '男', value: '男' },
                { label: '女', value: '女' }
              ]
            }
          },
          {
            type: 'textarea',
            model: 'textarea-medical-history',
            config: {
              label: '病史',
              placeholder: '请输入病史信息',
              required: false
            }
          },
          {
            type: 'checkbox',
            model: 'checkbox-symptoms',
            config: {
              label: '症状',
              required: false,
              options: [
                { label: '头痛', value: '头痛' },
                { label: '胸闷', value: '胸闷' },
                { label: '失眠', value: '失眠' },
                { label: '其他', value: '其他' }
              ]
            }
          },
          {
            type: 'radio',
            model: 'radio-severity',
            config: {
              label: '严重程度',
              required: true,
              options: [
                { label: '轻度', value: '轻度' },
                { label: '中度', value: '中度' },
                { label: '重度', value: '重度' }
              ]
            }
          },
          {
            type: 'date',
            model: 'date-visit-date',
            config: {
              label: '随访日期',
              required: true
            }
          }
        ],
        config: {
          title: 'CRF临床试验表单',
          description: '用于临床数据收集的标准表单'
        }
      }
    }
    
    try {
      const response = await templateAPI.getTemplate(templateId.value)
      console.log('模板API响应:', response)
      
      if (response.success && response.data?.template) {
        template.value = response.data.template
        console.log('模板信息加载成功:', template.value)
      } else {
        console.warn('模板API响应无数据，使用模拟数据')
        template.value = mockTemplate
      }
    } catch (apiError) {
      console.warn('模板API调用失败，使用模拟数据:', apiError.message)
      template.value = mockTemplate
    }
    
  } catch (error) {
    console.error('加载模板信息失败:', error)
    message.error(`加载模板信息失败: ${error.message}`)
  }
}

const loadInstances = async () => {
  try {
    loading.value = true
    console.log('开始加载实例数据，模板ID:', templateId.value)
    
    // 暂时使用模拟数据来测试VxeTable集成
    const mockInstances = [
      {
        id: 'instance-1',
        template_id: templateId.value,
        template_version: '1.0',
        instance_name: '患者001-随访1',
        subject_id: 'SUBJ001',
        visit_id: 'VISIT001',
        status: 'completed',
        completion_percentage: 100,
        form_data: {
          'input-patient-name': '张三',
          'input-patient-age': 35,
          'select-gender': '男',
          'textarea-medical-history': '高血压病史3年',
          'checkbox-symptoms': ['头痛', '胸闷'],
          'radio-severity': '中度',
          'date-visit-date': '2024-07-10'
        },
        created_at: '2024-07-10T09:00:00Z',
        updated_at: '2024-07-10T15:30:00Z'
      },
      {
        id: 'instance-2',
        template_id: templateId.value,
        template_version: '1.0',
        instance_name: '患者002-随访1',
        subject_id: 'SUBJ002',
        visit_id: 'VISIT001',
        status: 'in_progress',
        completion_percentage: 75,
        form_data: {
          'input-patient-name': '李四',
          'input-patient-age': 28,
          'select-gender': '女',
          'textarea-medical-history': '无特殊病史',
          'checkbox-symptoms': ['失眠'],
          'radio-severity': '轻度'
        },
        created_at: '2024-07-11T10:15:00Z',
        updated_at: '2024-07-11T14:45:00Z'
      },
      {
        id: 'instance-3',
        template_id: templateId.value,
        template_version: '1.1',
        instance_name: '患者003-随访2',
        subject_id: 'SUBJ003',
        visit_id: 'VISIT002',
        status: 'draft',
        completion_percentage: 30,
        form_data: {
          'input-patient-name': '王五',
          'input-patient-age': 42,
          'select-gender': '男',
          'textarea-medical-history': '糖尿病病史5年，冠心病病史2年'
        },
        created_at: '2024-07-12T08:30:00Z',
        updated_at: '2024-07-12T11:20:00Z'
      }
    ]
    
    // 尝试真实API调用，如果失败则使用模拟数据
    try {
      const response = await instanceAPI.getInstances({
        template_id: templateId.value,
        limit: 1000,
        offset: 0
      })
      
      console.log('实例API响应:', response)
      
      if (response.success && response.data?.instances && response.data.instances.length > 0) {
        instances.value = response.data.instances
        console.log('实例数据加载成功:', instances.value.length, '条记录')
        console.log('实例数据示例:', instances.value.slice(0, 2))
      } else {
        console.warn('API响应无数据，使用模拟数据')
        instances.value = mockInstances
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError.message)
      instances.value = mockInstances
    }
    
    console.log('最终实例数据:', instances.value.length, '条记录')
    
  } catch (error) {
    console.error('加载实例数据失败:', error)
    message.error(`加载数据失败: ${error.message}`)
    instances.value = []
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await Promise.all([
    loadTemplate(),
    loadInstances()
  ])
  message.success('数据刷新成功')
}

// 事件处理
const handleInstanceSelected = (instance: Record<string, unknown>) => {
  console.log('选中实例:', instance)
  // 可以在这里添加更多的实例选中逻辑
}

const handleExportRequested = async (format: string, data: Record<string, unknown>[]) => {
  console.log('导出请求:', format, data.length, '条记录')
  // VxeDataTable 组件已经处理了导出逻辑
}

const handleVersionCompare = (instances: Record<string, unknown>[]) => {
  console.log('版本对比:', instances.length, '个实例')
  // 可以在这里添加版本对比的额外逻辑
}

// 生命周期
onMounted(async () => {
  console.log('表单数据页面初始化，模板ID:', templateId.value)
  
  // 初始化网络管理器
  initNetworkClients()
  
  // 如果没有模板ID或是demo，使用demo数据
  if (!templateId.value || templateId.value === 'demo') {
    templateId.value = 'demo-template-123'
    console.log('使用演示数据')
  }
  
  await refreshData()
})

// 监听路由参数变化
watch(() => route.params.id, async (newId) => {
  if (newId && newId !== templateId.value) {
    templateId.value = newId as string
    await refreshData()
  }
})
</script>

<style lang="scss" scoped>
.form-data-page {
  width: 100%;
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-info {
      .page-title {
        margin: 0 0 4px 0;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.2;
      }

      .page-subtitle {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .instances-count {
      :deep(.n-badge__content) {
        right: -10px;
        top: 5px;
      }
    }
  }
}

.stats-cards {
  margin-bottom: 24px;

  .stat-card {
    border-radius: 8px;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    :deep(.n-card__body) {
      padding: 20px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.progress {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        &.average {
          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 28px;
          font-weight: 700;
          color: #1f2937;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
      }
    }
  }
}

.data-table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #1f2937;

    .table-actions {
      display: flex;
      gap: 12px;
    }
  }

  :deep(.n-card) {
    border-radius: 8px;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.empty-state {
  padding: 60px 20px;
  text-align: center;

  :deep(.n-empty__description) {
    p {
      margin: 8px 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-data-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }

  .stats-cards {
    :deep(.n-col) {
      margin-bottom: 16px;
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .form-data-page {
    background: #111827;
  }

  .page-header {
    .header-info {
      .page-title {
        color: #f9fafb;
      }

      .page-subtitle {
        color: #9ca3af;
      }
    }
  }

  .stat-card {
    :deep(.n-card) {
      background: #1f2937;
      border-color: #374151;
    }

    .stat-content {
      .stat-value {
        color: #f9fafb;
      }

      .stat-label {
        color: #9ca3af;
      }
    }
  }
}

// 统一返回按钮样式
.back-button {
  color: var(--color-text-secondary);
  font-weight: 500;

  &:hover {
    color: var(--color-primary);
  }

  :deep(.n-button__content) {
    gap: 6px;
  }
}
</style>