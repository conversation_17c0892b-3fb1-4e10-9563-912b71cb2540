<template>
  <div class="form-fill-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <n-spin size="large" show>
        <template #description>
          加载表单中...
        </template>
      </n-spin>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <n-result
        status="error"
        :title="error.title || '加载失败'"
        :description="error.message || '无法加载表单，请稍后重试'"
      >
        <template #footer>
          <n-button type="primary" @click="retryLoad">重新加载</n-button>
        </template>
      </n-result>
    </div>

    <!-- 表单填写界面 -->
    <div v-else-if="template" class="form-fill-content">
      <!-- 表单标题 -->
      <div class="form-header">
        <h1 class="form-title">{{ template.title }}</h1>
        <p v-if="template.description" class="form-description">
          {{ template.description }}
        </p>
        
        <!-- 填写状态 -->
        <div class="form-status">
          <div class="status-item">
            <n-tag v-if="instance" :type="getStatusType(instance.status)">
              {{ getStatusText(instance.status) }}
            </n-tag>
            <n-tag v-else type="info">新建</n-tag>
          </div>
          <div v-if="instance" class="status-item">
            <span class="completion-label">完成度:</span>
            <n-progress 
              :percentage="instance.completion_percentage" 
              :stroke-width="8"
              :show-text="true"
              class="completion-progress"
            />
          </div>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content">
        <form-renderer
          v-if="templateData"
          :template-data="templateData"
          :form-data="formData"
          :mode="FormMode.FILL"
          :readonly="instance?.status === 'submitted'"
          @data-change="handleDataChange"
        />
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <div class="action-left">
          <n-button @click="handleSaveDraft" :loading="saving">
            <crf-icon icon="material-symbols:save" size="16px" />
            保存草稿
          </n-button>
          <n-button @click="handlePreview" type="info">
            <crf-icon icon="material-symbols:visibility" size="16px" />
            预览
          </n-button>
        </div>
        
        <div class="action-right">
          <n-button 
            @click="handleSubmit" 
            type="primary" 
            :loading="submitting"
            :disabled="!canSubmit || instance?.status === 'submitted'"
          >
            <crf-icon icon="material-symbols:check" size="16px" />
            {{ instance?.status === 'submitted' ? '已提交' : '提交表单' }}
          </n-button>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <n-modal 
      v-model:show="showPreview" 
      title="表单预览" 
      style="width: 80%"
      :mask-closable="false"
      preset="dialog"
    >
      <form-renderer
        v-if="templateData"
        :template-data="templateData"
        :form-data="formData"
        :mode="FormMode.PREVIEW"
        :readonly="true"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import { CrfIcon } from '@crf/components'
import FormRenderer from '@/components/form/FormRenderer.vue'
import { templateAPI, instanceAPI } from '@/api'

// 临时类型定义
enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview'
}

interface Template {
  id: string
  name: string
  title: string
  description: string
  version: string
  template_data: Record<string, unknown>
}

interface Instance {
  id: string
  template_id: string
  template_version: string
  status: string
  completion_percentage: number
  form_data: Record<string, unknown>
  created_at: string
  updated_at: string
}

interface FormError {
  title: string
  message: string
}

// 路由参数
const route = useRoute()
const templateId = route.params.id as string
const message = useMessage()
const dialog = useDialog()

// 状态管理
const loading = ref(true)
const saving = ref(false)
const submitting = ref(false)
const error = ref<FormError | null>(null)
const template = ref<Template | null>(null)
const instance = ref<Instance | null>(null)
const templateData = ref<Record<string, unknown> | null>(null)
const formData = reactive<Record<string, unknown>>({})
const validationErrors = ref<Record<string, string>>({})
const showPreview = ref(false)

// 计算属性
const canSubmit = computed(() => {
  return Object.keys(validationErrors.value).length === 0
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'draft': return 'info'
    case 'submitted': return 'success'
    case 'locked': return 'warning'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'submitted': return '已提交'
    case 'locked': return '已锁定'
    default: return '未知状态'
  }
}

// 初始化表单
const initForm = async () => {
  try {
    loading.value = true
    error.value = null

    // 获取模板信息
    const templateResponse = await fetch(`/form/fill/${templateId}`)
    if (!templateResponse.ok) {
      throw new Error('模板不存在或未发布')
    }
    
    const templateResult = await templateResponse.json()
    if (!templateResult.success) {
      throw new Error(templateResult.message || '获取模板失败')
    }

    template.value = templateResult.data.template
    templateData.value = template.value.template_data

    // 检查是否有现有实例（从localStorage或URL参数）
    const instanceId = route.query.instance as string
    if (instanceId) {
      await loadInstance(instanceId)
    } else {
      // 创建新实例
      await createInstance()
    }
  } catch (err) {
    console.error('初始化表单失败:', err)
    error.value = {
      title: '加载失败',
      message: err instanceof Error ? err.message : '未知错误'
    }
  } finally {
    loading.value = false
  }
}

// 加载实例
const loadInstance = async (instanceId: string) => {
  try {
    const response = await instanceAPI.getInstance(instanceId)
    if (response.success && response.data) {
      instance.value = response.data.instance
      // 恢复表单数据
      if (instance.value.form_data) {
        Object.assign(formData, instance.value.form_data)
      }
    }
  } catch (err) {
    console.error('加载实例失败:', err)
    // 如果实例不存在，创建新实例
    await createInstance()
  }
}

// 创建实例
const createInstance = async () => {
  try {
    const response = await instanceAPI.createInstance({
      template_id: templateId
    })
    
    if (response.success && response.data) {
      instance.value = response.data.instance
      // 更新URL参数
      const url = new URL(window.location.href)
      url.searchParams.set('instance', instance.value.id)
      window.history.replaceState({}, '', url.toString())
    }
  } catch (err) {
    console.error('创建实例失败:', err)
    throw new Error('创建表单实例失败')
  }
}

// 处理数据变更
const handleDataChange = (newData: Record<string, unknown>) => {
  Object.assign(formData, newData)
  // 自动保存草稿
  debouncedSave()
}

// 处理验证变更
const handleValidationChange = (errors: Record<string, string>) => {
  validationErrors.value = errors
}

// 防抖保存
let saveTimeout: NodeJS.Timeout | null = null
const debouncedSave = () => {
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
  saveTimeout = setTimeout(async () => {
    await saveDraft()
  }, 2000)
}

// 保存草稿
const saveDraft = async () => {
  if (!instance.value || saving.value) return

  try {
    saving.value = true
    const response = await instanceAPI.updateInstance(instance.value.id, {
      form_data: formData
    })
    
    if (response.success) {
      // 更新实例信息
      const instanceResponse = await instanceAPI.getInstance(instance.value.id)
      if (instanceResponse.success) {
        instance.value = instanceResponse.data.instance
      }
    }
  } catch (err) {
    console.error('保存草稿失败:', err)
  } finally {
    saving.value = false
  }
}

// 手动保存草稿
const handleSaveDraft = async () => {
  if (!instance.value) return

  try {
    saving.value = true
    await saveDraft()
    message.success('草稿保存成功')
  } catch (err) {
    message.error('草稿保存失败')
  } finally {
    saving.value = false
  }
}

// 预览表单
const handlePreview = () => {
  showPreview.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!instance.value || !canSubmit.value) return

  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认提交',
        content: '确定要提交表单吗？提交后将无法修改。',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    submitting.value = true
    
    // 先保存最新数据
    await saveDraft()
    
    // 提交表单
    const response = await instanceAPI.submitInstance(instance.value.id)
    
    if (response.success) {
      message.success('表单提交成功')
      // 更新实例状态
      instance.value.status = 'submitted'
      instance.value.completion_percentage = 100
    }
  } catch (err) {
    if (err !== 'cancel') {
      console.error('提交失败:', err)
      message.error('提交失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 重新加载
const retryLoad = () => {
  initForm()
}

// 初始化
onMounted(() => {
  initForm()
})
</script>

<style lang="scss" scoped>
.form-fill-container {
  min-height: 100vh;
  background: #f5f7fa;
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
  }
  
  .form-fill-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    
    .form-header {
      background: white;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .form-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 8px 0;
      }
      
      .form-description {
        color: #6b7280;
        margin: 0 0 16px 0;
        line-height: 1.6;
      }
      
      .form-status {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
        
        .status-item {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .completion-label {
            font-size: 14px;
            color: #374151;
          }
          
          .completion-progress {
            width: 200px;
          }
        }
      }
    }
    
    .form-content {
      background: white;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      min-height: 400px;
    }
    
    .form-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: white;
      border-radius: 8px;
      padding: 16px 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .action-left,
      .action-right {
        display: flex;
        gap: 12px;
      }
      
      .n-button {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-fill-container {
    .form-fill-content {
      padding: 10px;
      
      .form-header,
      .form-content,
      .form-actions {
        padding: 16px;
      }
      
      .form-status {
        flex-direction: column;
        align-items: flex-start;
        
        .status-item {
          width: 100%;
          
          .completion-progress {
            width: 100%;
          }
        }
      }
      
      .form-actions {
        flex-direction: column;
        gap: 12px;
        
        .action-left,
        .action-right {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}
</style>