<template>
  <div class="not-found-page">
    <n-result
      status="404"
      title="404"
      description="抱歉，您访问的页面不存在"
    >
      <template #footer>
        <n-space>
          <n-button type="primary" @click="handleGoHome">返回首页</n-button>
          <n-button @click="handleGoBack">返回上页</n-button>
        </n-space>
      </template>
    </n-result>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

defineOptions({
  name: 'NotFoundPage'
})

const router = useRouter()

const handleGoHome = () => {
  router.push('/dashboard')
}

const handleGoBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.not-found-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}
</style>