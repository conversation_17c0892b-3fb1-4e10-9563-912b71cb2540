<template>
  <div class="w-full h-full bg-gray-50 p-6 overflow-y-auto">
    <!-- 欢迎区域 - 响应式优化 -->
    <div class="welcome-section flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6 mb-8 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="welcome-content flex-1">
        <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
          欢迎回来，{{ userStore.user?.full_name || userStore.user?.username }}
        </h1>
        <p class="text-gray-600 m-t-10px text-14px">您的个人工作概览和快速访问入口</p>
      </div>
      <div class="welcome-stats flex gap-6 lg:gap-8">
        <div class="stat-item text-center">
          <div class="text-2xl lg:text-3xl font-bold text-blue-600">{{ stats.projectCount }}</div>
          <div class="text-sm text-gray-500 mt-1">项目</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-2xl lg:text-3xl font-bold text-green-600">{{ stats.templateCount }}</div>
          <div class="text-sm text-gray-500 mt-1">模板</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-2xl lg:text-3xl font-bold text-purple-600">{{ stats.instanceCount }}</div>
          <div class="text-sm text-gray-500 mt-1">数据</div>
        </div>
      </div>
    </div>

    <!-- 项目概览 - 全屏展示 -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900">最近项目</h2>
          <!-- 批量操作工具栏 -->
          <div v-if="selectedProjects.length > 0" class="flex items-center gap-3">
            <span class="text-sm text-gray-600">已选择 {{ selectedProjects.length }} 个项目</span>
            <n-button size="small" @click="handleSelectAll">
              {{ selectedProjects.length === filteredProjects.length ? '取消全选' : '全选' }}
            </n-button>
            <n-button size="small" type="error" @click="handleBatchDelete">
              <n-icon class="mr-1"><TrashOutline /></n-icon>
              批量删除
            </n-button>
            <n-button size="small" @click="handleClearSelection">
              取消选择
            </n-button>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <!-- 搜索框 -->
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索项目名称"
            clearable
            size="medium"
            style="width: 240px;"
            @update:value="handleSearch"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
          
          <!-- 刷新按钮 -->
          <n-button
            @click="loadDashboardData"
            :loading="loading"
            size="medium"
            title="刷新项目列表"
          >
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            刷新
          </n-button>
          
          <!-- 创建项目按钮 -->
          <n-button type="primary" @click="handleCreateProject">
            <template #icon>
              <n-icon><FolderOutline /></n-icon>
            </template>
            创建项目
          </n-button>
        </div>
      </div>

      <!-- 项目列表 -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <n-spin size="medium" />
        <span class="ml-3 text-gray-500">加载项目中...</span>
      </div>

      <div v-else-if="filteredProjects.length === 0 && !searchKeyword.trim()" class="text-center py-12 bg-white rounded-lg border border-gray-200">
        <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <n-icon size="32" class="text-gray-400"><FolderOutline /></n-icon>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">还没有项目</h3>
        <p class="text-gray-500 mb-6">创建您的第一个临床研究项目开始工作</p>
        <n-button type="primary" @click="handleCreateProject">
          创建第一个项目
        </n-button>
      </div>

      <div v-else-if="filteredProjects.length === 0 && searchKeyword.trim()" class="text-center py-12 bg-white rounded-lg border border-gray-200">
        <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <n-icon size="32" class="text-gray-400"><SearchOutline /></n-icon>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">未找到相关项目</h3>
        <p class="text-gray-500">尝试使用其他关键词搜索</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 group relative cursor-pointer"
          :class="{
            'border-blue-500 bg-blue-50 shadow-md': selectedProjects.includes(project.id),
            'hover:border-blue-300 hover:-translate-y-1': !selectedProjects.includes(project.id)
          }"
        >
          <!-- 项目卡片头部 -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center gap-3">
              <!-- 项目图标 -->
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <n-icon size="20" class="text-blue-600"><FolderOutline /></n-icon>
              </div>
              <!-- 多选复选框 -->
              <div class="z-10">
                <n-checkbox
                  :checked="selectedProjects.includes(project.id)"
                  @update:checked="(checked: boolean) => handleProjectSelect(project.id, checked)"
                  class="opacity-0 group-hover:opacity-100 transition-opacity"
                  :class="{ 'opacity-100': selectedProjects.includes(project.id) }"
                />
              </div>
            </div>
            <div class="flex items-center gap-2">
              <n-tag :type="getStatusType(project.status)" size="small">
                {{ getStatusText(project.status) }}
              </n-tag>
            </div>
          </div>

          <!-- 项目信息 -->
          <div class="cursor-pointer" @click="handleViewProject(project)">
            <h3 class="text-lg font-medium text-gray-900 mb-2 truncate">{{ project.name }}</h3>
            <p class="text-sm text-gray-600 mb-4 line-clamp-2">{{ project.description || '暂无描述' }}</p>

            <!-- 项目统计 -->
            <div class="grid grid-cols-3 gap-2 mb-4 text-xs">
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="font-medium text-gray-900">{{ project.template_count || 0 }}</div>
                <div class="text-gray-500">模板</div>
              </div>
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="font-medium text-gray-900">{{ project.instance_count || 0 }}</div>
                <div class="text-gray-500">实例</div>
              </div>
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="font-medium text-gray-900">{{ project.member_count || 0 }}</div>
                <div class="text-gray-500">成员</div>
              </div>
            </div>

            <div class="flex items-center justify-between text-xs text-gray-400">
              <span>{{ formatTime(project.updated_at) }}</span>
              <div class="flex items-center gap-2">
                <!-- 项目操作下拉菜单 -->
                <n-dropdown
                  trigger="click"
                  :options="getProjectDropdownOptions()"
                  @select="(key: string) => handleProjectAction(key, project)"
                  @click.stop
                  placement="bottom-end"
                >
                  <n-button text size="small" class="dropdown-trigger">
                    <n-icon size="14"><EllipsisHorizontalOutline /></n-icon>
                  </n-button>
                </n-dropdown>
                <span class="text-blue-600 group-hover:text-blue-700">管理表单 →</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <n-modal
      v-model:show="showCreateDialog"
      preset="dialog"
      title="创建新项目"
      style="width: 500px"
      :mask-closable="false"
    >
      <n-form
        ref="createFormRef"
        :model="createForm"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="项目名称" path="name" required>
          <n-input
            v-model:value="createForm.name"
            placeholder="请输入项目名称"
            maxlength="50"
            show-count
          />
        </n-form-item>

        <n-form-item label="项目描述" path="description">
          <n-input
            v-model:value="createForm.description"
            type="textarea"
            placeholder="请输入项目描述（可选）"
            maxlength="200"
            show-count
            :rows="3"
          />
        </n-form-item>

        <n-form-item label="项目设置">
          <n-space vertical>
            <n-checkbox v-model:checked="createForm.is_public">
              公开项目（其他用户可以查看）
            </n-checkbox>
            <n-checkbox v-model:checked="createForm.allow_anonymous">
              允许匿名访问
            </n-checkbox>
          </n-space>
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="handleCreateProjectCancel">取消</n-button>
          <n-button
            type="primary"
            :loading="createLoading"
            @click="handleCreateProjectSubmit"
          >
            创建项目
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, useDialog, type FormInst } from 'naive-ui'
import {
  FolderOutline,
  TrashOutline,
  SearchOutline,
  RefreshOutline,
  EllipsisHorizontalOutline,
  DocumentTextOutline,
  InformationCircleOutline
} from '@vicons/ionicons5'
import { useUserStore } from '@/stores/user-store'
import { projectAPI } from '@/api/projects'
import { initNetworkClients } from '@/api/index'

interface Project {
  id: string
  name: string
  description?: string
  status: string
  template_count?: number
  instance_count?: number
  member_count?: number
  updated_at: string
}

defineOptions({
  name: 'DashboardPage'
})

const router = useRouter()
const message = useMessage()
const dialog = useDialog()
const userStore = useUserStore()

// 响应式数据 - 使用显式导出确保模板可访问
const loading = ref(false)
const stats = ref({
  projectCount: 0,
  templateCount: 0,
  instanceCount: 0
})
const recentProjects = ref<Project[]>([])
const searchKeyword = ref('')
const selectedProjects = ref<string[]>([])

// 创建项目对话框
const showCreateDialog = ref(false)
const createForm = ref({
  name: '',
  description: '',
  is_public: false,
  allow_anonymous: false
})
const createLoading = ref(false)
const createFormRef = ref<FormInst | null>(null)

// 快速操作处理函数
const handleCreateProject = () => {
  showCreateDialog.value = true
}

// 搜索处理函数
const handleSearch = (keyword: string) => {
  searchKeyword.value = keyword
  // 客户端搜索，不需要重新加载数据
}

// 过滤后的项目列表
const filteredProjects = computed(() => {
  if (!searchKeyword.value.trim()) {
    return recentProjects.value
  }
  
  return recentProjects.value.filter((project: any) => 
    project.name?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    project.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const handleViewProject = (project: any) => {
  // 基于CRF系统的业务特点，医疗研究人员进入项目后主要工作是管理CRF表单
  // 跳转到表单管理页面，传递项目ID参数，显示该项目的表单
  router.push(`/forms?project_id=${project.id}`)
}

// 项目选择处理函数
const handleProjectSelect = (projectId: string, checked: boolean) => {
  if (checked) {
    if (!selectedProjects.value.includes(projectId)) {
      selectedProjects.value.push(projectId)
    }
  } else {
    const index = selectedProjects.value.indexOf(projectId)
    if (index > -1) {
      selectedProjects.value.splice(index, 1)
    }
  }
}

// 全选/取消全选
const handleSelectAll = () => {
  if (selectedProjects.value.length === filteredProjects.value.length) {
    selectedProjects.value = []
  } else {
    selectedProjects.value = filteredProjects.value.map(p => p.id)
  }
}

// 清除选择
const handleClearSelection = () => {
  selectedProjects.value = []
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedProjects.value.length === 0) {
    message.warning('请先选择要删除的项目')
    return
  }

  try {
    await new Promise<boolean>((resolve, reject) => {
      dialog.warning({
        title: '批量删除确认',
        content: `确定要删除选中的 ${selectedProjects.value.length} 个项目吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    loading.value = true
    await projectAPI.batchDeleteProjects(selectedProjects.value)
    
    message.success(`成功删除 ${selectedProjects.value.length} 个项目`)
    selectedProjects.value = []
    await loadDashboardData()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除项目失败:', error)
      message.error('批量删除项目失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 单个项目删除
const handleDeleteProject = async (project: any) => {
  try {
    await new Promise<boolean>((resolve, reject) => {
      dialog.warning({
        title: '删除确认',
        content: `确定要删除项目 "${project.name}" 吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel')
      })
    })

    loading.value = true
    await projectAPI.deleteProject(project.id)
    
    message.success('项目删除成功')
    
    // 如果删除的项目在选中列表中，移除它
    const index = selectedProjects.value.indexOf(project.id)
    if (index > -1) {
      selectedProjects.value.splice(index, 1)
    }
    
    await loadDashboardData()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除项目失败:', error)
      message.error('删除项目失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 获取项目下拉菜单选项
const getProjectDropdownOptions = () => {
  return [
    {
      label: '管理表单',
      key: 'view',
      icon: () => h('n-icon', null, { default: () => h(DocumentTextOutline) })
    },
    {
      label: '查看详情',
      key: 'details',
      icon: () => h('n-icon', null, { default: () => h(InformationCircleOutline) })
    },
    {
      type: 'divider',
      key: 'd1'
    },
    {
      label: '删除项目',
      key: 'delete',
      icon: () => h('n-icon', null, { default: () => h(TrashOutline) })
    }
  ]
}

// 项目操作处理函数
const handleProjectAction = async (action: string, project: any) => {
  switch (action) {
    case 'view':
      handleViewProject(project)
      break
    case 'details':
      // 查看项目详情页面
      router.push(`/projects/${project.id}`)
      break
    case 'edit':
      // TODO: 实现编辑项目对话框
      message.info('编辑项目功能开发中...')
      break
    case 'templates':
      router.push(`/projects/${project.id}/templates`)
      break
    case 'delete':
      handleDeleteProject(project)
      break
    default:
      console.warn('未知的项目操作:', action)
  }
}

// 创建项目处理函数
const handleCreateProjectSubmit = async () => {
  // 验证表单
  if (!createForm.value.name.trim()) {
    message.error('请输入项目名称')
    return
  }

  createLoading.value = true
  try {
    console.log('开始创建项目:', createForm.value)
    
    // 调用创建项目 API
    const response = await projectAPI.createProject(createForm.value)
    
    console.log('创建项目响应:', response)
    
    // 检查响应是否成功
    if (response && (response.success || response.data)) {
      const projectName = createForm.value.name
      
      // 显示成功消息
      message.success(`项目 "${projectName}" 创建成功！`)
      
      // 立即关闭对话框
      showCreateDialog.value = false
      
      // 重置表单数据
      createForm.value = {
        name: '',
        description: '',
        is_public: false,
        allow_anonymous: false
      }
      
      console.log('开始刷新项目数据...')
      
      // 重新加载数据，获取最新项目列表
      await loadDashboardData()
      
      console.log(`项目 "${projectName}" 创建成功，数据已刷新`)
      
      // 可选：短暂延迟后高亮新项目（等待DOM更新）
      setTimeout(() => {
        console.log('新项目已加载到列表中')
      }, 500)
      
    } else {
      // 创建失败
      console.error('创建项目响应异常:', response)
      message.error('创建项目失败，请检查输入信息')
    }
    
  } catch (error) {
    console.error('创建项目异常:', error)
    
    // 更具体的错误处理
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as any
      if (apiError.response?.status === 400) {
        message.error('项目信息格式不正确，请检查后重试')
      } else if (apiError.response?.status === 409) {
        message.error('项目名称已存在，请使用其他名称')
      } else {
        message.error('创建项目失败，请稍后重试')
      }
    } else {
      message.error('网络错误，请检查网络连接')
    }
  } finally {
    createLoading.value = false
  }
}

// 取消创建项目
const handleCreateProjectCancel = () => {
  showCreateDialog.value = false
  createForm.value = {
    name: '',
    description: '',
    is_public: false,
    allow_anonymous: false
  }
}

// 状态处理函数
const getStatusType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'draft': return 'warning'
    case 'completed': return 'info'
    case 'archived': return 'default'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '进行中'
    case 'draft': return '草稿'
    case 'completed': return '已完成'
    case 'archived': return '已归档'
    default: return '未知'
  }
}

// 时间格式化
const formatTime = (time: string) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString()
}

// 加载数据
const loadDashboardData = async () => {
  loading.value = true
  try {
    // 加载项目数据 - 使用正确的参数格式
    console.log('开始调用项目API...')
    console.log('localStorage token:', localStorage.getItem('auth_token') ? '存在' : '不存在')

    const projectsRes = await projectAPI.getProjects({ 
      page: 1, 
      page_size: 20 // 获取更多项目以确保新项目可见
    })

    console.log('API 响应数据:', projectsRes)
    console.log('响应类型:', typeof projectsRes)
    console.log('响应success:', projectsRes.success)
    console.log('响应data:', projectsRes.data)
    console.log('响应data类型:', typeof projectsRes.data)

    // 检查响应是否成功
    if (projectsRes && projectsRes.data) {
      // 现在 projectsRes.data 直接包含 { projects: [...], pagination: {...} }
      const projects = projectsRes.data.projects || []
      const pagination = projectsRes.data.pagination || { total: 0 }

      console.log(`成功加载 ${projects.length} 个项目`)
      console.log('最新项目列表:', projects.slice(0, 3).map((p: any) => ({ id: p.id, name: p.name, updated_at: p.updated_at })))

      // 更新统计数据
      stats.value = {
        projectCount: Number(pagination.total) || 0,
        templateCount: 0, // 模板数量将在项目详情中显示
        instanceCount: 0 // TODO: 从实例API获取
      }

      // 更新最近项目
      recentProjects.value = projects
      
      console.log('项目数据刷新完成 - 当前显示', projects.length, '个项目')

    } else {
      console.error('API 响应失败:', projectsRes)
      message.error('获取项目数据失败')
    }

  } catch (error) {
    console.error('加载工作台数据失败:', error)
    message.error('加载数据失败')
 } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  // 初始化网络管理器
  initNetworkClients()
  loadDashboardData()
})

// 显式导出给模板使用的变量
defineExpose({
  userStore,
  loading,
  stats,
  recentProjects,
  searchKeyword,
  selectedProjects,
  showCreateDialog,
  createForm,
  createLoading,
  createFormRef,
  filteredProjects,
  handleCreateProject,
  handleSearch,
  handleViewProject,
  handleProjectSelect,
  handleSelectAll,
  handleClearSelection,
  handleBatchDelete,
  handleDeleteProject,
  handleProjectAction,
  getProjectDropdownOptions,
  handleCreateProjectSubmit,
  handleCreateProjectCancel,
  getStatusType,
  getStatusText,
  formatTime
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: var(--spacing-6);
  background: var(--color-bg-page);
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
}

/* 欢迎区域 */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.welcome-content {
  .welcome-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
    margin: 0 0 var(--spacing-2) 0;
  }

  .welcome-subtitle {
    font-size: var(--font-size-base);
    color: var(--color-text-secondary);
    margin: 0;
  }
}

.welcome-stats {
  display: flex;
  gap: var(--spacing-6);
}

.stat-item {
  text-align: center;

  .stat-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    line-height: 1;
  }

  .stat-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-tertiary);
    margin-top: var(--spacing-1);
  }
}

/* 快速操作区域 */
.quick-actions {
  margin-bottom: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  
  /* 3个卡片的最佳布局 */
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 1023px) and (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
  }
}

.action-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }

  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-3) auto;
    color: white;

    &.primary {
      background: var(--color-primary);
    }

    &.success {
      background: var(--color-success);
    }

    &.info {
      background: var(--color-info);
    }

    &.warning {
      background: var(--color-warning);
    }
  }

  h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0 0 var(--spacing-2) 0;
  }

  p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: 0;
    line-height: 1.5;
  }
}

/* 最近活动区域 */
.recent-activity {
  .subsection-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin: 0 0 var(--spacing-3) 0;
  }
}

.activity-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
}

.recent-projects,
.recent-templates {
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  border: 1px solid var(--color-border-primary);
}

.project-list,
.template-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.project-item,
.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--color-bg-secondary);
  }
}

.project-info,
.template-info {
  flex: 1;

  h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin: 0 0 var(--spacing-1) 0;
  }

  p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: 0 0 var(--spacing-1) 0;
    line-height: 1.4;
  }

  span {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
  }
}

.project-status,
.template-status {
  margin-left: var(--spacing-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-4);
  }

  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-4);
  }

  .welcome-stats {
    align-self: stretch;
    justify-content: space-around;
  }

  .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .activity-content {
    grid-template-columns: 1fr;
  }

  .action-card {
    padding: var(--spacing-4);

    .card-icon {
      width: 40px;
      height: 40px;
    }

    h3 {
      font-size: var(--font-size-base);
    }

    p {
      font-size: var(--font-size-xs);
    }
  }
}

// 支持line-clamp的样式
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 下拉菜单触发器样式
.dropdown-trigger {
  opacity: 0.6;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
    background-color: rgba(59, 130, 246, 0.1);
  }
}

// 项目卡片悬停时显示下拉菜单
.group:hover .dropdown-trigger {
  opacity: 1;
}
</style>