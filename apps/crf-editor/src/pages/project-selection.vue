<template>
  <div class="project-selection-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">选择项目</h1>
        <p class="page-description">选择一个项目来开始工作，或创建新项目</p>
      </div>
      <div class="header-actions">
        <n-button type="primary" @click="showCreateDialog = true">
          <template #icon>
            <n-icon><Plus /></n-icon>
          </template>
          创建项目
        </n-button>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="filter-section">
      <n-input
        v-model:value="searchKeyword"
        placeholder="搜索项目名称..."
        clearable
        style="width: 300px"
        @input="handleSearch"
      >
        <template #prefix>
          <n-icon><Search /></n-icon>
        </template>
      </n-input>
    </div>

    <!-- 项目列表 -->
    <div class="projects-container">
      <div v-if="loading" class="loading-container">
        <n-spin size="large">
          <template #description>
            正在加载项目...
          </template>
        </n-spin>
      </div>

      <div v-else-if="filteredProjects.length === 0" class="empty-state">
        <n-empty description="暂无项目">
          <template #extra>
            <n-button type="primary" @click="showCreateDialog = true">
              创建第一个项目
            </n-button>
          </template>
        </n-empty>
      </div>

      <div v-else class="projects-grid">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="project-card"
          @click="selectProject(project)"
        >
          <div class="project-card-header">
            <div class="project-icon">
              <n-icon size="24" color="#3b82f6">
                <FolderOpen />
              </n-icon>
            </div>
            <div class="project-status">
              <n-tag
                :type="getStatusType(project.status)"
                size="small"
              >
                {{ getStatusText(project.status) }}
              </n-tag>
            </div>
          </div>

          <div class="project-card-body">
            <h3 class="project-name">{{ project.name }}</h3>
            <p class="project-description">
              {{ project.description || '暂无描述' }}
            </p>

            <div class="project-stats">
              <div class="stat-item">
                <n-icon><Document /></n-icon>
                <span>{{ project.template_count }} 个模板</span>
              </div>
              <div class="stat-item">
                <n-icon><DataBase /></n-icon>
                <span>{{ project.instance_count }} 个实例</span>
              </div>
              <div class="stat-item">
                <n-icon><People /></n-icon>
                <span>{{ project.member_count }} 个成员</span>
              </div>
            </div>
          </div>

          <div class="project-card-footer">
            <div class="project-progress">
              <span class="progress-label">完成度</span>
              <n-progress
                type="line"
                :percentage="project.completion_rate"
                :height="6"
                :show-indicator="false"
              />
              <span class="progress-value">{{ project.completion_rate }}%</span>
            </div>
            <div class="project-meta">
              <span class="meta-item">
                创建者: {{ project.creator.full_name }}
              </span>
              <span class="meta-item">
                {{ formatDate(project.updated_at) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > pagination.pageSize" class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-picker="true"
          :page-sizes="[10, 20, 50]"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <n-modal v-model:show="showCreateDialog" preset="dialog" title="创建新项目">
      <n-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        label-placement="top"
      >
        <n-form-item label="项目名称" path="name">
          <n-input
            v-model:value="createForm.name"
            placeholder="请输入项目名称"
            maxlength="100"
            show-count
          />
        </n-form-item>
        
        <n-form-item label="项目描述" path="description">
          <n-input
            v-model:value="createForm.description"
            type="textarea"
            placeholder="请输入项目描述（可选）"
            :rows="3"
            maxlength="500"
            show-count
          />
        </n-form-item>

        <n-form-item label="项目设置">
          <n-space vertical>
            <n-checkbox v-model:checked="createForm.is_public">
              公开项目（其他用户可以查看）
            </n-checkbox>
            <n-checkbox v-model:checked="createForm.allow_anonymous">
              允许匿名访问（无需登录即可填写表单）
            </n-checkbox>
          </n-space>
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showCreateDialog = false">取消</n-button>
          <n-button
            type="primary"
            :loading="loading"
            @click="handleCreateProject"
          >
            创建项目
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project-store'
import type { Project } from '@/api/project'
import {
  NButton,
  NIcon,
  NInput,
  NSpin,
  NEmpty,
  NTag,
  NProgress,
  NPagination,
  NModal,
  NForm,
  NFormItem,
  NCheckbox,
  NSpace,
  FormInst,
  FormRules,
} from 'naive-ui'
import {
  Plus,
  Search,
  FolderOpen,
  Document,
  DataBase,
  People,
} from '@vicons/ionicons5'

const router = useRouter()
const projectStore = useProjectStore()

// 响应式数据
const searchKeyword = ref('')
const showCreateDialog = ref(false)
const createFormRef = ref<FormInst | null>(null)

// 创建表单数据
const createForm = ref({
  name: '',
  description: '',
  is_public: false,
  allow_anonymous: false,
})

// 表单验证规则
const createFormRules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度应在2-100字符之间', trigger: 'blur' },
  ],
}

// 计算属性
const { projects, loading, pagination } = projectStore
const filteredProjects = computed(() => {
  if (!searchKeyword.value) return projects
  return projects.filter(project =>
    project.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (project.description && project.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handlePageChange = (page: number) => {
  projectStore.fetchProjects({ page, page_size: pagination.pageSize })
}

const handlePageSizeChange = (pageSize: number) => {
  projectStore.fetchProjects({ page: 1, page_size: pageSize })
}

const selectProject = async (project: Project) => {
  try {
    await projectStore.setCurrentProject(project.id)
    // 跳转到项目仪表板
    router.push(`/project/${project.id}/dashboard`)
  } catch (error) {
    console.error('选择项目失败:', error)
  }
}

const handleCreateProject = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    const newProject = await projectStore.createProject(createForm.value)
    
    // 重置表单
    createForm.value = {
      name: '',
      description: '',
      is_public: false,
      allow_anonymous: false,
    }
    showCreateDialog.value = false
    
    // 自动选择新创建的项目
    await selectProject(newProject)
  } catch (error) {
    console.error('创建项目失败:', error)
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    'draft': 'default',
    'active': 'success',
    'completed': 'info',
    'archived': 'warning',
    'paused': 'error',
  }
  return statusMap[status as keyof typeof statusMap] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'draft': '草稿',
    'active': '进行中',
    'completed': '已完成',
    'archived': '已归档',
    'paused': '已暂停',
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  projectStore.fetchProjects()
})
</script>

<style scoped>
.project-selection-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 0 4px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 0 4px;
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.project-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 280px;
}

.project-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.project-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.project-icon {
  width: 40px;
  height: 40px;
  background: #eff6ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-description {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 16px 0;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.project-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #64748b;
}

.stat-item .n-icon {
  font-size: 14px;
}

.project-card-footer {
  margin-top: auto;
}

.project-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.progress-label {
  font-size: 12px;
  color: #64748b;
  width: 40px;
  flex-shrink: 0;
}

.progress-value {
  font-size: 12px;
  color: #64748b;
  width: 30px;
  text-align: right;
  flex-shrink: 0;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item {
  font-size: 12px;
  color: #94a3b8;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}
</style>