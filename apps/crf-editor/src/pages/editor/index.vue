<template>
  <div class="crf-editor-container">
    <!-- 骨架屏加载状态 -->
    <div v-if="isLoading" class="editor-skeleton">
      <!-- 头部骨架 -->
      <div class="skeleton-header">
        <div class="skeleton-header-left">
          <div class="skeleton-back-btn"></div>
          <div class="skeleton-title-section">
            <div class="skeleton-title"></div>
            <div class="skeleton-subtitle"></div>
          </div>
        </div>
        <div class="skeleton-header-center">
          <div class="skeleton-status"></div>
        </div>
        <div class="skeleton-header-right">
          <div class="skeleton-button"></div>
          <div class="skeleton-button"></div>
          <div class="skeleton-button primary"></div>
        </div>
      </div>
      
      <!-- 主要内容区域骨架 -->
      <div class="skeleton-main">
        <!-- 左侧物料面板骨架 -->
        <div class="skeleton-material-panel">
          <div class="skeleton-panel-header">
            <div class="skeleton-panel-title"></div>
          </div>
          <div class="skeleton-material-items">
            <div v-for="i in 6" :key="i" class="skeleton-material-item">
              <div class="skeleton-material-icon"></div>
              <div class="skeleton-material-text"></div>
            </div>
          </div>
        </div>
        
        <!-- 中间渲染区域骨架 -->
        <div class="skeleton-render-panel">
          <div class="skeleton-render-header">
            <div class="skeleton-render-title"></div>
            <div class="skeleton-render-actions">
              <div class="skeleton-action-btn"></div>
              <div class="skeleton-action-btn"></div>
            </div>
          </div>
          <div class="skeleton-render-content">
            <!-- 使用Naive UI骨架屏效果 -->
            <n-skeleton :repeat="8" text />
          </div>
        </div>
        
        <!-- 右侧配置面板骨架 -->
        <div class="skeleton-config-panel">
          <div class="skeleton-panel-header">
            <div class="skeleton-panel-title"></div>
          </div>
          <div class="skeleton-config-content">
            <!-- 使用Naive UI骨架屏效果 -->
            <n-skeleton :repeat="6" text />
          </div>
        </div>
      </div>
    </div>

    <!-- 实际编辑器内容 -->
    <template v-else>
      <edit-header 
        :template-id="currentTemplateId" 
        :project-id="currentProjectId" 
      />
      <div class="editor-main">
        <!-- 物料区 - 预览模式和填写模式下使用动画隐藏 -->
        <edit-block 
          :class="{ 'material-hidden': editorStore.mode === 'preview' || editorStore.mode === 'fill' }"
          class="material-panel"
        />
        
        <!-- 渲染区 - 统一使用，预览模式和填写模式下组件进入相应状态 -->
        <edit-render 
          :class="{ 'render-fullwidth': editorStore.mode === 'preview' || editorStore.mode === 'fill' }"
          class="render-panel"
        />
        
        <!-- 配置区 - 预览模式和填写模式下使用动画隐藏 -->
        <edit-config 
          :class="{ 'config-hidden': editorStore.mode === 'preview' || editorStore.mode === 'fill' }"
          class="config-panel"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import EditHeader from '@/components/edit/header/EditHeader.vue'
import EditBlock from '@/components/edit/block/EditBlock.vue'
import EditRender from '@/components/edit/render/EditRender.vue'
import EditConfig from '@/components/edit/config/EditConfig.vue'
import { useEditorStore } from '@/stores/editor-store'
import { useAutoSave } from '@/composables/useAutoSave'
import { useUndoRedo } from '@/composables/useUndoRedo'
import { EditorMode } from '@crf/types/core'
import { useNaive } from '@/composables/useNaive'
import { templateAPI, instanceAPI } from '@/api'

// 使用统一编辑器Store
const editorStore = useEditorStore()
const route = useRoute()

// 获取 Naive UI 实例
const { message } = useNaive()

// 从路由参数获取模板ID和项目ID
const currentTemplateId = ref(route.query.id as string || route.query.templateId as string || '')
const currentProjectId = ref(route.query.project_id as string || '')

// 获取传递的表单名称
const initialFormName = ref(route.query.name as string || '')

// 实例相关状态（填写模式专用）
const currentInstanceId = ref<string | null>(null)
const instanceData = ref<Record<string, unknown> | null>(null)
const savingInstance = ref(false)

// 加载状态管理
const isLoading = ref(true)

// 自动保存功能
const autoSave = useAutoSave({
  resourceType: 'template',
  resourceId: currentTemplateId.value,
  interval: 30000, // 30秒自动保存
  debounceTime: 1000, // 1秒防抖
  enabled: true
})

// 撤销/重做功能
const undoRedo = useUndoRedo({
  templateId: route.query.templateId as string,
  enableServerSync: true,
  autoTrack: true
})

// 获取编辑器状态
const showConfigPanel = computed(() => editorStore.editorConfig.showConfigPanel)
const toggleConfigPanel = () => {
  if (editorStore.editorConfig.showConfigPanel) {
    editorStore.hideConfigPanel()
  } else {
    editorStore.showConfigPanel()
  }
}

// 加载模板数据
const loadTemplateData = async () => {
  try {
    isLoading.value = true
    
    // 模拟最小加载时间，让用户看到骨架屏效果
    const minLoadingTime = new Promise(resolve => setTimeout(resolve, 800))
    
    if (!currentTemplateId.value) {
      console.log('🆕 新建表单，清空所有内容')
      
      // 清空所有编辑器内容
      editorStore.sections.splice(0, editorStore.sections.length)
      editorStore.clearFormData()
      
      // 重置页面配置为默认值
      editorStore.updatePageConfig({
        title: initialFormName.value || '新建CRF表单',
        description: ''
      })
      
      console.log('✅ 新建表单初始化完成')
      await minLoadingTime
      return
    }

    const loadPromise = (async () => {
      const response = await templateAPI.getTemplate(currentTemplateId.value)
      if (response.success && response.data?.template) {
        const template = response.data.template
        
        console.log('加载的模板数据:', template)
        
        // 更新页面配置 - 优先使用传递的名称，然后是模板中的名称
        editorStore.updatePageConfig({
          title: initialFormName.value || template.title || template.name,
          description: template.description || ''
        })
        
        // 加载模板数据到编辑器
        if (template.template_data) {
          const templateData: Record<string, unknown> = typeof template.template_data === 'string' 
            ? JSON.parse(template.template_data) 
            : template.template_data
          
          console.log('解析的模板数据:', templateData)
          
          // 恢复sections数据
          if (templateData.sections && Array.isArray(templateData.sections)) {
            editorStore.sections.splice(0, editorStore.sections.length, ...templateData.sections)
            console.log('✅ 恢复sections数据:', templateData.sections.length, '个章节')
          }
          
          // 恢复pageConfig数据
          if (templateData.pageConfig && typeof templateData.pageConfig === 'object') {
            // 合并配置，保持已设置的title
            const currentTitle = editorStore.pageConfig.title
            Object.assign(editorStore.pageConfig, templateData.pageConfig as Record<string, unknown>)
            if (currentTitle) {
              editorStore.pageConfig.title = currentTitle
            }
            console.log('✅ 恢复pageConfig数据')
          }
          
          // 恢复formData数据
          if (templateData.formData && typeof templateData.formData === 'object') {
            Object.assign(editorStore.formData, templateData.formData as Record<string, unknown>)
            console.log('✅ 恢复formData数据:', Object.keys(templateData.formData as Record<string, unknown>).length, '个字段')
          }
        }
        
        // 兼容旧的数据结构
        else if (template.form_structure) {
          console.log('使用旧版数据结构:', template.form_structure)
          // 这里可以添加旧数据结构的转换逻辑
        }
        
        console.log('模板数据加载完成')
      }
    })()

    // 等待数据加载和最小加载时间都完成
    await Promise.all([loadPromise, minLoadingTime])
    
  } catch (error) {
    console.error('加载模板数据失败:', error)
    message.error('加载模板数据失败')
    // 即使出错也要等待最小加载时间
    await new Promise(resolve => setTimeout(resolve, 800))
  } finally {
    isLoading.value = false
  }
}

// 监听页面配置变化，同步到后端
let titleWatchEnabled = false
watch(() => editorStore.pageConfig.title, async (newTitle) => {
  // 只有在初始化完成后才同步标题
  if (!titleWatchEnabled || !currentTemplateId.value) {
    return
  }
  
  try {
    await templateAPI.updateTemplate(currentTemplateId.value, {
      title: newTitle,
      name: newTitle // 同时更新name字段
    })
    console.log('模板标题已同步到后端:', newTitle)
  } catch (error) {
    console.error('同步模板标题失败:', error)
  }
}, { immediate: false })

// 键盘事件处理
let keyboardEventHandler: ((event: KeyboardEvent) => void) | null = null

// 生命周期
onMounted(async () => {
  console.log('🚀 CRF编辑器启动')
  
  // 检查URL参数中的mode
  const urlMode = route.query.mode as string
  console.log('🔍 检测到URL模式:', urlMode)
  
  if (urlMode === 'fill') {
    // 填写模式：设置为填写模式
    console.log('📝 进入填写模式')
    editorStore.setMode('fill')
    
    // 加载模板数据用于填写
    await loadTemplateData()
    
    // 创建新的表单实例
    await createNewInstance()
    
    // 初始化实例自动保存功能（区别于模板自动保存）
    // autoSave.init() // 暂时不启用模板自动保存
    
  } else {
    // 编辑模式：设置为编辑模式（默认）
    console.log('✏️ 进入编辑模式')
    editorStore.setMode('edit')
    
    // 首先加载模板数据
    await loadTemplateData()
    
    // 初始化自动保存功能
    autoSave.init()
    
    // 只有在新建表单时才尝试恢复自动保存的数据
    if (currentTemplateId.value === 'demo-template-id') {
      try {
        const restored = await autoSave.restore()
        if (restored) {
          message.success('已恢复自动保存的数据')
        } else {
          console.log('📝 没有找到自动保存的数据，使用默认配置')
        }
      } catch (error) {
        console.warn('恢复自动保存数据失败:', error)
        // 不显示错误消息给用户，因为这是正常情况（第一次使用时没有保存数据）
      }
    } else {
      console.log('📝 使用数据库中的模板数据，跳过自动保存恢复')
    }
  }
  
  // 初始化撤销/重做功能
  undoRedo.init()
  
  // 启用标题监听（在数据加载完成后）
  titleWatchEnabled = true
  
  // 键盘事件处理
  keyboardEventHandler = (event: KeyboardEvent) => {
    // ESC键关闭配置面板
    if (event.key === 'Escape') {
      if (showConfigPanel.value) {
        toggleConfigPanel()
      }
    }
    
    // Ctrl+S 手动保存
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault()
      handleManualSave()
    }
  }
  
  document.addEventListener('keydown', keyboardEventHandler)
})

onUnmounted(() => {
  console.log('🛑 CRF编辑器卸载')
  
  if (keyboardEventHandler) {
    document.removeEventListener('keydown', keyboardEventHandler)
    keyboardEventHandler = null
  }
})

// 手动保存处理
const handleManualSave = async () => {
  try {
    if (editorStore.mode === 'fill') {
      // 填写模式：保存到实例
      await saveInstance()
    } else {
      // 编辑模式：保存到模板
      await autoSave.save()
      message.success('保存成功')
    }
  } catch (error) {
    message.error('保存失败')
    console.error('手动保存失败:', error)
  }
}

// 创建新的表单实例
const createNewInstance = async () => {
  if (!currentTemplateId.value) {
    console.error('❌ 创建实例失败：无效的模板ID')
    message.error('无效的模板ID，无法创建实例')
    return
  }

  try {
    console.log('🔄 正在创建新实例...')
    
    // 创建实例数据
    const newInstanceData = {
      template_id: currentTemplateId.value,
      template_version: '1.0.0', // 可以从模板数据中获取
      instance_name: `${initialFormName.value}_实例_${Date.now()}`,
      subject_id: `subject_${Date.now()}`, // 可以让用户输入或自动生成
      visit_id: `visit_${Date.now()}`, // 可以让用户输入或自动生成
      form_data: {},
      status: 'draft',
      completion_percentage: 0.0
    }

    // 调用实际的API
    const response = await instanceAPI.createInstance(newInstanceData)
    if (response.success) {
      currentInstanceId.value = response.data.instance.id
      instanceData.value = response.data.instance
      
      // 同时设置到editorStore中
      editorStore.setCurrentInstance(response.data.instance)
      
      console.log('✅ 实例创建成功:', response.data.instance)
      message.success('实例创建成功，可以开始填写表单', {
        duration: 3000,
        closable: true
      })
    } else {
      throw new Error(response.message || '创建实例失败')
    }
    
  } catch (error) {
    console.error('❌ 创建实例失败:', error)
    
    // 如果API调用失败，使用临时模拟数据
    console.log('🔄 API调用失败，使用临时模拟数据')
    currentInstanceId.value = `instance-${Date.now()}`
    instanceData.value = {
      id: currentInstanceId.value,
      template_id: currentTemplateId.value,
      template_version: '1.0.0',
      instance_name: `${initialFormName.value}_实例_${Date.now()}`,
      subject_id: `subject_${Date.now()}`,
      visit_id: `visit_${Date.now()}`,
      form_data: {},
      status: 'draft',
      completion_percentage: 0.0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    // 设置到editorStore中
    editorStore.setCurrentInstance(instanceData.value)
    
    console.log('✅ 实例创建成功 (模拟):', instanceData.value)
    message.success('实例创建成功，可以开始填写表单', {
      duration: 3000,
      closable: true
    })
  }
}

// 保存实例数据
const saveInstance = async () => {
  if (!currentInstanceId.value) {
    console.error('❌ 保存实例失败：无实例ID')
    message.error('无实例ID，无法保存')
    return
  }

  try {
    savingInstance.value = true
    console.log('🔄 正在保存实例数据...')
    
    // 收集当前表单数据
    const currentFormData = { ...editorStore.formData }
    
    // 计算完成进度（使用新的字段统计逻辑）
    const fieldStats = editorStore.getFieldStats()
    const progress = fieldStats.progress

    const saveData = {
      form_data: currentFormData,
      completion_percentage: progress,
      status: progress === 100 ? 'completed' : 'draft'
    }

    // 调用实际的API
    try {
      const response = await instanceAPI.updateInstance(currentInstanceId.value, saveData)
      if (response.success) {
        // 使用API返回的最新实例数据，而不是本地合并的数据
        const updatedInstance = response.data.instance
        instanceData.value = updatedInstance
        
        // 更新editorStore中的实例数据
        editorStore.setCurrentInstance(updatedInstance)
        
        console.log('✅ 实例保存成功:', updatedInstance)
        console.log('📊 最新完成度:', updatedInstance.completion_percentage)
        console.log('📋 最新表单数据:', updatedInstance.form_data)
        
        message.success(`表单保存成功 (完成度: ${updatedInstance.completion_percentage || progress}%)`, {
          duration: 2000,
          closable: true
        })
      } else {
        throw new Error(response.message || '保存实例失败')
      }
    } catch (apiError) {
      console.error('❌ API保存失败，使用临时模拟:', apiError)
      
      // API失败时使用临时模拟保存
      instanceData.value = { ...instanceData.value, ...saveData, updated_at: new Date().toISOString() }
      editorStore.setCurrentInstance(instanceData.value)
      
      console.log('✅ 实例保存成功 (模拟):', instanceData.value)
      message.success(`表单保存成功 (完成度: ${progress}%)`, {
        duration: 2000,
        closable: true
      })
    }
    
  } catch (error) {
    console.error('❌ 保存实例失败:', error)
    message.error('保存实例失败')
  } finally {
    savingInstance.value = false
  }
}

// 提供给子组件的方法
const exposedMethods: Record<string, unknown> = {
  autoSave,
  undoRedo,
  save: handleManualSave,
  canUndo: undoRedo.canUndo,
  canRedo: undoRedo.canRedo,
  undo: undoRedo.undo,
  redo: undoRedo.redo,
  // 实例相关方法
  createNewInstance,
  saveInstance,
  currentInstanceId,
  instanceData,
  savingInstance
}

// 暴露给父组件
defineExpose(exposedMethods)
</script>

<style lang="scss" scoped>
.crf-editor-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: var(--edit-background-color);
  display: flex;
  flex-direction: column;
}

.editor-main {
  flex: 1;
  display: flex;
  min-height: 0; // 重要：确保flex子元素能正确缩放
  min-width: 100%;
  width: 100%;
  position: relative; // 为蒙版定位
}

// 预览模式动画隐藏样式
.material-panel {
  width: var(--edit-block-width);
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  flex-shrink: 0;
  
  &.material-hidden {
    transform: translateX(-100%);
    opacity: 0;
    width: 0;
    overflow: hidden;
  }
}

.config-panel {
  width: var(--edit-config-width);
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  flex-shrink: 0;
  
  &.config-hidden {
    transform: translateX(100%);
    opacity: 0;
    width: 0;
    overflow: hidden;
  }
}

.render-panel {
  flex: 1;
  min-width: 0;
  transition: all 0.3s ease-out;
  
  &.render-fullwidth {
    // 预览模式下渲染区占满整个剩余空间
    width: 100%;
  }
}

// 确保编辑器组件正确布局
:deep(.edit-block) {
  flex-shrink: 0;
}

:deep(.edit-render) {
  flex: 1;
  min-width: 0;
}

:deep(.edit-config) {
  flex-shrink: 0;
}

// 医疗CRF主题色彩
:root {
  --crf-primary-color: #1890ff;
  --crf-success-color: #52c41a;
  --crf-warning-color: #faad14;
  --crf-error-color: #ff4d4f;
  --crf-info-color: #13c2c2;
  
  --crf-bg-color: #f5f5f5;
  --crf-card-bg: #ffffff;
  --crf-border-color: #d9d9d9;
  --crf-text-color: #262626;
  --crf-text-color-secondary: #8c8c8c;
}



// 骨架屏样式
.editor-skeleton {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--crf-bg-color);
  
  // 骨架屏通用动画
  .skeleton-item {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 400px 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 4px;
  }
  
  // 骨架屏动画
  @keyframes skeleton-loading {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }
}

// 骨架屏头部 - 简化样式
.skeleton-header {
  height: 64px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .skeleton-header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .skeleton-back-btn {
      @extend .skeleton-item;
      width: 32px;
      height: 32px;
      border-radius: 6px;
    }
    
    .skeleton-title-section {
      display: flex;
      flex-direction: column;
      gap: 6px;
      
      .skeleton-title {
        @extend .skeleton-item;
        width: 160px;
        height: 14px; // 更细的线条
      }
      
      .skeleton-subtitle {
        @extend .skeleton-item;
        width: 100px;
        height: 10px; // 更细的线条
      }
    }
  }
  
  .skeleton-header-center {
    .skeleton-status {
      @extend .skeleton-item;
      width: 60px;
      height: 20px; // 更细的线条
      border-radius: 10px;
    }
  }
  
  .skeleton-header-right {
    display: flex;
    gap: 12px;
    
    .skeleton-button {
      @extend .skeleton-item;
      width: 70px;
      height: 28px; // 更细的线条
      border-radius: 4px;
      
      &.primary {
        background: linear-gradient(90deg, #e8f4fd 25%, #d1e9fb 50%, #e8f4fd 75%);
        background-size: 400px 100%;
        animation: skeleton-loading 1.4s ease infinite;
      }
    }
  }
}

// 骨架屏主要内容
.skeleton-main {
  flex: 1;
  display: flex;
  min-height: 0;
}

// 左侧物料面板骨架 - 简化
.skeleton-material-panel {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  
  .skeleton-panel-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .skeleton-panel-title {
      @extend .skeleton-item;
      width: 80px;
      height: 12px; // 更细的线条
    }
  }
  
  .skeleton-material-items {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    
    .skeleton-material-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      padding: 12px;
      
      .skeleton-material-icon {
        @extend .skeleton-item;
        width: 28px;
        height: 28px;
        border-radius: 4px;
      }
      
      .skeleton-material-text {
        @extend .skeleton-item;
        width: 40px;
        height: 8px; // 更细的线条
      }
    }
  }
}

// 中间渲染区域骨架 - 简化
.skeleton-render-panel {
  flex: 1;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  
  .skeleton-render-header {
    height: 60px;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    
    .skeleton-render-title {
      @extend .skeleton-item;
      width: 120px;
      height: 12px; // 更细的线条
    }
    
    .skeleton-render-actions {
      display: flex;
      gap: 8px;
      
      .skeleton-action-btn {
        @extend .skeleton-item;
        width: 24px;
        height: 24px;
        border-radius: 4px;
      }
    }
  }
  
  .skeleton-render-content {
    flex: 1;
    padding: 20px;
    background: #ffffff;
    margin: 20px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }
}

// 右侧配置面板骨架 - 简化
.skeleton-config-panel {
  width: 320px;
  background: #ffffff;
  border-left: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  
  .skeleton-panel-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .skeleton-panel-title {
      @extend .skeleton-item;
      width: 60px;
      height: 12px; // 更细的线条
    }
  }
  
  .skeleton-config-content {
    padding: 20px;
  }
}
</style>