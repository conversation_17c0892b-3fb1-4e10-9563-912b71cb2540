<template>
  <div class="login-wrapper">
    <!-- 左侧品牌展示区域 -->
    <LoginBrandSection />

    <!-- 右侧登录表单区域 -->
    <div class="login-right">
      <LoginFormCard 
        :loading="loading"
        @login="handleLogin"
        @register="handleRegister"
        @update:loading="loading = $event"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from '@/composables/useNaive'
import { authAPI, initNetworkClients } from '@/api'
import { useUserStore } from '@/stores/user-store'
import { getErrorMessage } from '@/utils/error-messages'
import LoginBrandSection from '@/components/auth/LoginBrandSection.vue'
import LoginFormCard from '@/components/auth/LoginFormCard.vue'

defineOptions({
  name: 'LoginPage'
})

const router = useRouter()
const userStore = useUserStore()
const message = useMessage()

const loading = ref(false)

// 处理登录
const handleLogin = async (loginData: Record<string, unknown>) => {
  try {
    console.log('前端发送的登录数据:', loginData)
    const response = await authAPI.login(loginData)
    
    if (response.success) {
      console.log('登录响应数据:', response.data)
      
      // 按顺序设置用户数据和token
      if (response.data?.access_token) {
        userStore.setTokens(response.data.access_token, response.data.refresh_token)
        console.log('Tokens已设置:', {
          access_token: response.data.access_token,
          refresh_token: response.data.refresh_token,
          remember_me: response.data.remember_me
        })
      }
      
      if (response.data?.user) {
        await userStore.setUser(response.data.user)
        console.log('用户信息已设置:', response.data?.user)
      }
      
      // 根据记住我状态显示不同的成功消息
      if (response.data?.remember_me) {
        message.success('登录成功，已记住登录状态！')
      } else {
        message.success('登录成功，欢迎回来！')
      }
      
      // 等待一小段时间确保状态更新
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const redirect = router.currentRoute.value.query.redirect as string
      await router.push(redirect || '/dashboard')
      
    } else {
      message.error(response.message || '登录失败，请检查登录信息')
    }
  } catch (error: unknown) {
    console.error('登录错误:', error)
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      auth: '登录失败，请检查登录信息',
      default: '登录失败，请稍后重试'
    })
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 处理注册
const handleRegister = async (registerData: Record<string, unknown>) => {
  try {
    console.log('前端发送的注册数据:', registerData)
    // 这里可以调用注册API
    // const response = await authAPI.register(registerData)
    
    // 暂时模拟注册成功
    message.success('注册成功！请使用新账号登录')
    
    // 注册成功后可以自动切换到登录模式
    // 或者直接跳转到登录页面
    
  } catch (error: unknown) {
    console.error('注册错误:', error)
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      auth: '注册失败，请检查注册信息',
      default: '注册失败，请稍后重试'
    })
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化网络管理器
  initNetworkClients()
  
  const token = localStorage.getItem('auth_token')
  if (token && userStore.isLoggedIn) {
    router.push('/dashboard')
  }
})
</script>

<style lang="scss" scoped>
// CSS 变量定义 - 基于统一主题系统
:root {
  // 渐变色 - 与 Naive UI 主题色保持一致
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --surface-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  
  // 文本颜色 - 与 Naive UI 主题保持一致
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --text-disabled: #a0aec0;
  
  // 边框颜色 - 与 Naive UI 主题保持一致
  --border-color: #e2e8f0;
  
  // 背景颜色 - 与 Naive UI 主题保持一致
  --input-bg: #f9fafb;
  --page-bg: #f8fafc;
  --card-bg: #ffffff;
  
  // 阴影 - 与 Naive UI 主题保持一致
  --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.15);
  
  // 圆角 - 与 Naive UI 主题保持一致
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;
}

// 主容器
.login-wrapper {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4f8 0%, #e5eaf2 100%);
  position: relative;
  overflow: hidden;
}

// 右侧表单区域
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  min-width: 600px;
  background: linear-gradient(135deg, var(--page-bg) 0%, #f1f5f9 100%);
  position: relative;
}

// Naive UI 样式优化 - 基于统一主题配置
:deep(.n-input) {
  --n-color-focus: #ffffff;
  --n-border-focus: 2px solid #667eea;
  --n-border-hover: 1px solid #667eea;
  --n-box-shadow-focus: 0 0 0 2px rgba(102, 126, 234, 0.2);
  
  .n-input-wrapper {
    padding-left: 12px;
  }
  
  .n-input__prefix {
    margin-right: 8px;
    margin-left: 4px;
  }
  
  .n-input__suffix {
    margin-left: 8px;
  }
}

:deep(.n-checkbox) {
  --n-size: 18px;
  --n-color-checked: #667eea;
  --n-border-checked: #667eea;
  --n-color-table-header: #667eea;
  --n-text-color-checked: #ffffff;
}

:deep(.n-button) {
  --n-height-large: 48px;
  --n-font-size-large: 1rem;
  
  &.n-button--primary-type {
    --n-color: var(--primary-gradient);
    --n-color-hover: var(--primary-gradient);
    --n-color-pressed: var(--primary-gradient);
    --n-border: none;
    --n-border-hover: none;
    --n-border-pressed: none;
  }
}

:deep(.n-form-item-feedback-wrapper) {
  font-size: 0.8rem;
  margin-top: 4px;
}

// 响应式设计
@media (max-width: 1200px) {
  .login-right {
    min-width: 500px;
  }
}

@media (max-width: 1024px) {
  .login-wrapper {
    flex-direction: column;
  }

  .login-right {
    min-width: 0;
    padding: 20px;
  }
}

@media (max-width: 640px) {
  .login-right {
    padding: 15px;
  }
}
</style>

