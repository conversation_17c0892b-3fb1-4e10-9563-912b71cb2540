<template>
  <div class="register-wrapper">
    <!-- 左侧品牌展示区域 -->
    <LoginBrandSection />
    
    <!-- 右侧注册表单区域 (保留原有内容) -->
    <div class="register-right">
      <!-- 其他内容保持不变 -->
      <div class="register-container">
        <div class="register-card">
          <div class="card-header">
            <h2 class="card-title">创建账号</h2>
            <p class="card-subtitle">加入CRF表单制作平台，开始您的临床研究之旅</p>
          </div>
          <!-- 注册表单内容保持原样 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginBrandSection from '@/components/auth/LoginBrandSection.vue'
// 其他导入保持不变
</script>

<style lang="scss" scoped>
// 样式保持不变，只需要将 .register-left 相关样式移除
.register-wrapper {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4f8 0%, #e5eaf2 100%);
  position: relative;
  overflow: hidden;
}

.register-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  min-width: 600px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

// 其他样式保持不变...
</style>