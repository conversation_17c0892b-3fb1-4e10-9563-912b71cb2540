import { useMessage as useNaiveMessage, useDialog, useNotification, useLoadingBar, lightTheme, type GlobalTheme, type GlobalThemeOverrides } from 'naive-ui'
import { useMessage } from './useMessage'

// 定义基于登录页的主题色彩系统
const themeColors = {
  // 主色调 - 基于登录页的渐变色 (#667eea -> #764ba2)
  primary: '#667eea',
  primaryHover: '#5a67d8',
  primaryPressed: '#4c51bf',
  primarySuppl: '#764ba2',
  primaryActive: '#667eea',
  
  // 辅助色调 - 与主色调和谐搭配
  info: '#4facfe',
  infoHover: '#3b9afe',
  success: '#10b981',
  successHover: '#059669',
  warning: '#f59e0b',
  warningHover: '#d97706',
  error: '#ef4444',
  errorHover: '#dc2626',
  
  // 中性色调 - 与登录页一致的文本色彩
  textPrimary: '#2d3748',     // 与登录页的 --text-primary 一致
  textSecondary: '#4a5568',   // 与登录页的 --text-secondary 一致
  textTertiary: '#718096',    // 与登录页的 --text-muted 一致
  textDisabled: '#a0aec0',
  
  // 背景色调 - 与登录页保持一致
  bodyColor: '#ffffff',
  cardColor: '#ffffff',
  modalColor: '#ffffff',
  popoverColor: '#ffffff',
  tableHeaderColor: '#f7fafc',
  pageBackground: '#f8fafc',   // 与登录页右侧背景一致
  
  // 边框色调 - 与登录页的边框色保持一致
  borderColor: '#e2e8f0',     // 与登录页的 --border-color 一致
  dividerColor: '#e2e8f0',
  
  // 阴影色调 - 与登录页的阴影效果一致
  boxShadow1: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  boxShadow2: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  boxShadow3: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  
  // 圆角 - 与登录页的圆角保持一致
  borderRadius: '4px',        // 与登录页的 --radius-sm 一致
  borderRadiusSmall: '6px',
  borderRadiusLarge: '12px',  // 与登录页的 --radius-md 一致
  borderRadiusXLarge: '20px'  // 与登录页的 --radius-lg 一致
}

// 创建自定义主题覆盖配置 - 基于登录页设计的完整主题系统
export const createCustomThemeOverrides = (): GlobalThemeOverrides => ({
  common: {
    // 主色调配置
    primaryColor: themeColors.primary,
    primaryColorHover: themeColors.primaryHover,
    primaryColorPressed: themeColors.primaryPressed,
    primaryColorSuppl: themeColors.primarySuppl,
    
    // 功能色配置
    infoColor: themeColors.info,
    infoColorHover: themeColors.infoHover,
    successColor: themeColors.success,
    successColorHover: themeColors.successHover,
    warningColor: themeColors.warning,
    warningColorHover: themeColors.warningHover,
    errorColor: themeColors.error,
    errorColorHover: themeColors.errorHover,
    
    // 文本色配置
    textColorBase: themeColors.textPrimary,
    textColor1: themeColors.textPrimary,
    textColor2: themeColors.textSecondary,
    textColor3: themeColors.textTertiary,
    textColorDisabled: themeColors.textDisabled,
    
    // 背景色配置
    bodyColor: themeColors.bodyColor,
    cardColor: themeColors.cardColor,
    modalColor: themeColors.modalColor,
    popoverColor: themeColors.popoverColor,
    tableHeaderColor: themeColors.tableHeaderColor,
    
    // 边框配置 - 使用统一的圆角
    borderColor: themeColors.borderColor,
    dividerColor: themeColors.dividerColor,
    borderRadius: themeColors.borderRadius,
    borderRadiusSmall: themeColors.borderRadiusSmall,
    
    // 阴影配置
    boxShadow1: themeColors.boxShadow1,
    boxShadow2: themeColors.boxShadow2,
    boxShadow3: themeColors.boxShadow3,
    
    // 字体配置
    fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'`,
    fontWeight: '400',
    fontWeightStrong: '600'
  },
  Button: {
    // 尺寸配置
    heightTiny: '24px',
    heightSmall: '32px',
    heightMedium: '40px',
    heightLarge: '48px',
    
    // 字体配置
    fontSizeTiny: '12px',
    fontSizeSmall: '14px',
    fontSizeMedium: '14px',
    fontSizeLarge: '16px',
    fontWeight: '500',
  
    // 主要按钮样式
    colorPrimary: themeColors.primary,
    colorHoverPrimary: themeColors.primaryHover,
    colorPressedPrimary: themeColors.primaryPressed,
    colorFocusPrimary: themeColors.primary,
    
    // 边框配置
    borderPrimary: `1px solid ${themeColors.primary}`,
    borderHoverPrimary: `1px solid ${themeColors.primaryHover}`,
    borderPressedPrimary: `1px solid ${themeColors.primaryPressed}`,
    borderFocusPrimary: `1px solid ${themeColors.primary}`,
    
    // 阴影配置 - 移除不存在的属性
    
    // 文本颜色
    textColorPrimary: '#ffffff',
    textColorHoverPrimary: '#ffffff',
    textColorPressedPrimary: '#ffffff',
    textColorFocusPrimary: '#ffffff',
    
    // 次要按钮样式 - 移除不存在的属性
    textColorGhost: themeColors.primary
  },
  Input: {
    // 尺寸配置
    heightTiny: '24px',
    heightSmall: '32px',
    heightMedium: '40px',
    heightLarge: '44px',
    
    // 字体配置
    fontSizeTiny: '12px',
    fontSizeSmall: '14px',
    fontSizeMedium: '14px',
    fontSizeLarge: '15px',
    
    // 圆角配置 - 使用统一的圆角标准
    borderRadius: themeColors.borderRadius,
    
    // 边框配置 - 明确设置所有状态的边框颜色
    border: `1px solid ${themeColors.borderColor}`,
    borderHover: `1px solid ${themeColors.primary}`,
    borderFocus: `2px solid ${themeColors.primary}`,
    borderError: `1px solid ${themeColors.error}`,
    borderWarning: `1px solid ${themeColors.warning}`,
    
    // 背景配置
    color: themeColors.bodyColor,
    colorFocus: themeColors.bodyColor,
    colorDisabled: '#f7fafc',
    
    // 文本配置
    textColor: themeColors.textPrimary,
    textColorDisabled: themeColors.textDisabled,
    placeholderColor: themeColors.textTertiary,
    placeholderColorDisabled: themeColors.textDisabled,
    
    // 阴影配置 - 重点：确保焦点时使用我们的主色调
    boxShadowFocus: `0 0 0 2px rgba(102, 126, 234, 0.2)`,
    
    // 内边距配置
    paddingTiny: '0 8px',
    paddingSmall: '0 12px',
    paddingMedium: '0 14px',
    paddingLarge: '0 16px'
  },
  // 新增日期选择器主题配置
  DatePicker: {
    // 颜色配置
    itemColorHover: `rgba(102, 126, 234, 0.1)`,
    itemColorActive: themeColors.primary,
    itemTextColorActive: '#ffffff',
    itemTextColor: themeColors.textPrimary,
    itemTextColorDisabled: themeColors.textDisabled,
    calendarTitleTextColor: themeColors.textPrimary,
    
    // 边框和分割线
    panelHeaderDividerColor: themeColors.dividerColor,
    
    // 圆角配置 - 使用统一的圆角标准
    itemBorderRadius: themeColors.borderRadiusSmall,
    
    // 尺寸配置
    itemSize: '32px',
    panelActionPadding: '12px 16px',
    calendarLeftPaddingDate: '12px',
    calendarRightPaddingDate: '12px',
    
    // 箭头配置 - 移除不存在的属性
    
    // 阴影配置
    panelBoxShadow: themeColors.boxShadow3
  },
  Checkbox: {
    // 尺寸配置 - 确保使用medium尺寸
    fontSizeSmall: '14px',
    fontSizeMedium: '16px',
    fontSizeLarge: '18px',
    sizeMedium: '18px', // 添加medium尺寸的大小设置
    
    // 颜色配置
    colorChecked: themeColors.primary,
    colorDisabledChecked: themeColors.textDisabled,
    color: '#ffffff', // 确保复选框在正常状态下可见
    
    // 边框配置
    borderChecked: `1px solid ${themeColors.primary}`,
    border: `1px solid ${themeColors.borderColor}`,
    borderDisabled: `1px solid ${themeColors.borderColor}`,
    borderDisabledChecked: `1px solid ${themeColors.textDisabled}`,
    borderFocus: `1px solid ${themeColors.primary}`, // 使用borderFocus替代borderHover
    
    // 圆角配置 - 使用统一的圆角标准
    borderRadius: '4px',
    
    // 文本配置
    textColor: themeColors.textPrimary,
    textColorDisabled: themeColors.textDisabled,
    
    // 阴影配置
    boxShadowFocus: `0 0 0 2px rgba(102, 126, 234, 0.2)`
  },
  Form: {
    // 标签配置
    labelFontWeight: '500',
    labelTextColor: themeColors.textPrimary,
    
    // 反馈信息配置
    feedbackTextColor: themeColors.textSecondary,
    feedbackTextColorError: themeColors.error,
    feedbackTextColorWarning: themeColors.warning,
    
    // 移除不存在的间距配置属性
  },
  
  // 新增组件主题配置
  Card: {
    borderRadius: themeColors.borderRadiusLarge,
    color: themeColors.cardColor,
    colorModal: themeColors.modalColor,
    borderColor: themeColors.borderColor,
    titleTextColor: themeColors.textPrimary,
    titleFontWeight: '600',
    paddingMedium: '20px 24px',
    paddingLarge: '24px 32px'
  },
  
  Table: {
    borderColor: themeColors.borderColor,
    thColor: themeColors.tableHeaderColor,
    tdColor: themeColors.bodyColor,
    borderRadius: themeColors.borderRadius,
    thTextColor: themeColors.textPrimary,
    tdTextColor: themeColors.textPrimary,
    thFontWeight: '600'
  },
  

  
  // 新增对话框主题配置
  Dialog: {
    borderRadius: themeColors.borderRadiusLarge,
    color: themeColors.modalColor
  },
  
  // 新增通知主题配置
  Notification: {
    borderRadius: themeColors.borderRadius,
    color: themeColors.cardColor
  },
  
  // 新增选择器主题配置
  Select: {
    // 移除不存在的属性，Select组件的样式通过InternalSelection控制
  },
  
  // 新增分页主题配置  
  Pagination: {
    itemBorderRadius: themeColors.borderRadiusSmall
  }
})

export const useNaive = () => {
  // 使用统一的 message 系统
  const { success, error, warning, info, loading, destroyAll, $naive } = useMessage()
  const dialog = useDialog()
  const notification = useNotification()
  const loadingBar = useLoadingBar()

  return {
    // 统一的 message 接口
    message: {
      success,
      error,
      warning,
      info,
      loading,
      destroyAll,
      $naive // 原始 Naive UI 实例
    },
    dialog,
    notification,
    loadingBar
  }
}

// 导出单独的 hooks 以便直接使用
// 注意：这里的 useMessage 现在指向我们的统一实现
export { useMessage } from './useMessage'
export { useDialog, useNotification, useLoadingBar }