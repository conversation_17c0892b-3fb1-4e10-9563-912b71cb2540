import { ref, computed, reactive } from 'vue'
import { templateAPI } from '@/api'
import { useNaive } from '@/composables/useNaive'

interface FormItem {
  id: string
  name?: string
  title?: string
  description?: string
  icon?: string
  iconColor?: string
  version?: string
  status?: string
  created_at?: string
  updated_at?: string
  [key: string]: any
}

interface FormListState {
  forms: FormItem[]
  loading: boolean
  pagination: {
    current: number
    size: number
    total: number
  }
}

interface DeletedFormListState {
  forms: FormItem[]
  loading: boolean
  pagination: {
    current: number
    size: number
    total: number
  }
}

export function useFormList() {
  const { message } = useNaive()

  // 项目过滤状态
  const projectId = ref<string | null>(null)

  // 表单列表状态 - 默认12个卡片（2行）
  const formListState = ref<FormListState>({
    forms: [],
    loading: false,
    pagination: { current: 1, size: 12, total: 0 }
  })

  // 已删除表单状态 - 默认12个卡片（2行）
  const deletedFormListState = ref<DeletedFormListState>({
    forms: [],
    loading: false,
    pagination: { current: 1, size: 12, total: 0 }
  })

  // 计算属性
  const forms = computed(() => formListState.value.forms)
  const deletedForms = computed(() => deletedFormListState.value.forms)
  const loading = computed(() => formListState.value.loading)
  const pagination = computed(() => formListState.value.pagination)

  // 统计数量
  const publishedCount = computed(() => {
    return forms.value.filter(form => form.status === 'published').length
  })

  const unpublishedCount = computed(() => {
    return forms.value.filter(form => form.status === 'draft').length
  })

  const deletedCount = computed(() => {
    return deletedForms.value.length
  })

  const totalCount = computed(() => {
    return forms.value.length + deletedForms.value.length
  })

  // 表单列表操作函数
  const addFormToList = (form: FormItem) => {
    formListState.value.forms.unshift(form)
  }

  const removeFormFromList = (formId: string) => {
    const index = formListState.value.forms.findIndex(f => f.id === formId)
    if (index > -1) {
      formListState.value.forms.splice(index, 1)
    }
  }

  const updateFormInList = (formId: string, updates: Partial<FormItem>) => {
    const index = formListState.value.forms.findIndex(f => f.id === formId)
    if (index > -1) {
      Object.assign(formListState.value.forms[index], updates)
    }
  }

  const setLoading = (loading: boolean) => {
    formListState.value.loading = loading
  }

  const setForms = (forms: FormItem[]) => {
    formListState.value.forms = forms
  }

  const setPagination = (pagination: Partial<FormListState['pagination']>) => {
    Object.assign(formListState.value.pagination, pagination)
  }

  // 获取表单列表
  const fetchForms = async () => {
    try {
      setLoading(true)
      const params: any = {
        limit: pagination.value.size,
        offset: (pagination.value.current - 1) * pagination.value.size
      }

      // 如果有项目ID，添加到参数中
      if (projectId.value) {
        params.project_id = projectId.value
      }

      const response = await templateAPI.getTemplates(params)

      if (response.success) {
        const templates = response.data?.templates || []
        // 确保每个表单都有必需的id字段
        const formsWithId = templates.map((template: any) => ({
          ...template,
          id: template.id || template._id || Math.random().toString(36).substr(2, 9)
        }))
        setForms(formsWithId)
        setPagination({
          total: response.data?.pagination?.total || 0
        })
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      message.error('获取表单列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取已删除表单列表
  const fetchDeletedForms = async () => {
    try {
      deletedFormListState.value.loading = true
      const params: any = {
        limit: deletedFormListState.value.pagination.size,
        offset: (deletedFormListState.value.pagination.current - 1) * deletedFormListState.value.pagination.size
      }

      // 如果有项目ID，添加到参数中
      if (projectId.value) {
        params.project_id = projectId.value
      }

      const response = await templateAPI.getDeletedTemplates(params)

      if (response.success) {
        const templates = response.data?.templates || []
        // 确保每个表单都有必需的id字段
        const formsWithId = templates.map((template: any) => ({
          ...template,
          id: template.id || template._id || Math.random().toString(36).substr(2, 9),
          status: 'deleted' // 标记为已删除状态
        }))
        deletedFormListState.value.forms = formsWithId
        deletedFormListState.value.pagination = {
          ...deletedFormListState.value.pagination,
          total: response.data?.pagination?.total || 0
        }
      }
    } catch (error) {
      console.error('获取已删除表单列表失败:', error)
      message.error('获取已删除表单列表失败')
    } finally {
      deletedFormListState.value.loading = false
    }
  }

  // 刷新所有列表
  const refreshAllLists = async () => {
    await Promise.all([fetchForms(), fetchDeletedForms()])
  }

  // 设置项目过滤
  const setProjectFilter = (id: string | null) => {
    projectId.value = id
  }

  return {
    // 状态
    formListState,
    deletedFormListState,
    projectId,

    // 计算属性
    forms,
    deletedForms,
    loading,
    pagination,
    publishedCount,
    unpublishedCount,
    deletedCount,
    totalCount,

    // 方法
    addFormToList,
    removeFormFromList,
    updateFormInList,
    setLoading,
    setForms,
    setPagination,
    fetchForms,
    fetchDeletedForms,
    refreshAllLists,
    setProjectFilter
  }
}