import { ref, computed, watch } from 'vue'
import { useEditorStore } from '@/stores/editor-store'


interface PreviewOptions {
  mode?: 'readonly' | 'interactive' // 只读模式或交互模式
  showValidation?: boolean // 是否显示验证结果
  enableSubmit?: boolean // 是否启用提交功能
  theme?: 'default' | 'compact' | 'clinical' // 预览主题
}

interface PreviewData {
  sections: Record<string, unknown>[]
  pageConfig: Record<string, unknown>
  formData: Record<string, unknown>
  validationResults?: Record<string, unknown>
  componentConfigs?: Record<string, unknown>
}

export function usePreview(options: PreviewOptions = {}) {
  const editorStore = useEditorStore()
  
  const {
    mode = 'readonly',
    showValidation = true,
    enableSubmit = false,
    theme = 'default'
  } = options

  // 预览状态
  const isPreviewMode = ref(false)
  const previewData = ref<PreviewData | null>(null)
  const currentFormData = ref<Record<string, unknown>>({})
  const validationErrors = ref<Record<string, string[]>>({})
  const isSubmitting = ref(false)

  // 生成预览数据
  const generatePreviewData = (): PreviewData => {
    return {
      sections: JSON.parse(JSON.stringify(editorStore.sections)),
      pageConfig: JSON.parse(JSON.stringify(editorStore.pageConfig)),
      formData: JSON.parse(JSON.stringify(editorStore.formData)),
      validationResults: showValidation ? JSON.parse(JSON.stringify(editorStore.validationResults)) : undefined,
      componentConfigs: {}
    }
  }

  // 进入预览模式
  const enterPreview = () => {
    previewData.value = generatePreviewData()
    currentFormData.value = { ...previewData.value.formData }
    isPreviewMode.value = true
    
    console.log('👁️ 进入预览模式')
    return previewData.value
  }

  // 退出预览模式
  const exitPreview = () => {
    isPreviewMode.value = false
    previewData.value = null
    currentFormData.value = {}
    validationErrors.value = {}
    
    console.log('🚪 退出预览模式')
  }

  // 更新表单字段值
  const updateField = (fieldId: string, value: unknown) => {
    if (mode === 'readonly') {
      console.warn('⚠️ 只读模式下无法修改字段值')
      return
    }

    currentFormData.value[fieldId] = value
    
    // 触发验证
    if (showValidation) {
      validateField(fieldId)
    }
  }

  // 验证单个字段
  const validateField = (fieldId: string) => {
    const errors: string[] = []
    
    // 获取字段配置
    const fieldConfig = getFieldConfig(fieldId)
    if (!fieldConfig) return
    
    // 获取字段值
    const value = currentFormData.value[fieldId]
    
    // 必填验证
    if (fieldConfig.required && (value === null || value === undefined || value === '')) {
      errors.push('此字段为必填项')
    }
    
    // 类型验证
    if (value !== null && value !== undefined && value !== '') {
      switch (fieldConfig.type) {
        case 'email':
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            errors.push('请输入有效的邮箱地址')
          }
          break
        case 'number':
          if (isNaN(Number(value))) {
            errors.push('请输入有效的数字')
          } else {
            const num = Number(value)
            if (fieldConfig.min !== undefined && num < fieldConfig.min) {
              errors.push(`最小值为 ${fieldConfig.min}`)
            }
            if (fieldConfig.max !== undefined && num > fieldConfig.max) {
              errors.push(`最大值为 ${fieldConfig.max}`)
            }
          }
          break
        case 'text':
          if (fieldConfig.minLength && value.length < fieldConfig.minLength) {
            errors.push(`最少输入 ${fieldConfig.minLength} 个字符`)
          }
          if (fieldConfig.maxLength && value.length > fieldConfig.maxLength) {
            errors.push(`最多输入 ${fieldConfig.maxLength} 个字符`)
          }
          break
      }
    }
    
    // 自定义验证规则
    if (fieldConfig.pattern && value) {
      const regex = new RegExp(fieldConfig.pattern)
      if (!regex.test(value)) {
        errors.push(fieldConfig.patternMessage || '格式不正确')
      }
    }
    
    // 更新验证结果
    if (errors.length > 0) {
      validationErrors.value[fieldId] = errors
    } else {
      delete validationErrors.value[fieldId]
    }
  }

  // 获取字段配置
  const getFieldConfig = (fieldId: string) => {
    if (!previewData.value) return null
    
    // 在所有章节中查找字段
    const findField = (sections: Record<string, unknown>[]): Record<string, unknown> | null => {
      for (const section of sections) {
        const field = section.blocks?.find((block: Record<string, unknown>) => block.id === fieldId)
        if (field) return field
        
        if (section.children) {
          const found = findField(section.children)
          if (found) return found
        }
      }
      return null
    }
    
    return findField(previewData.value.sections)
  }

  // 验证整个表单
  const validateForm = () => {
    if (!previewData.value) return false
    
    // 收集所有字段
    const collectFields = (sections: Record<string, unknown>[]): Record<string, unknown>[] => {
      const fields: Record<string, unknown>[] = []
      for (const section of sections) {
        if (section.blocks) {
          fields.push(...section.blocks)
        }
        if (section.children) {
          fields.push(...collectFields(section.children))
        }
      }
      return fields
    }
    
    const allFields = collectFields(previewData.value.sections)
    
    // 验证每个字段
    for (const field of allFields) {
      validateField(field.id)
    }
    
    return Object.keys(validationErrors.value).length === 0
  }

  // 计算完成度
  const completionPercentage = computed(() => {
    if (!previewData.value) return 0
    
    const collectFields = (sections: Record<string, unknown>[]): Record<string, unknown>[] => {
      const fields: Record<string, unknown>[] = []
      for (const section of sections) {
        if (section.blocks) {
          fields.push(...section.blocks)
        }
        if (section.children) {
          fields.push(...collectFields(section.children))
        }
      }
      return fields
    }
    
    const allFields = collectFields(previewData.value.sections)
    const filledFields = allFields.filter(field => {
      const value = currentFormData.value[field.id]
      return value !== null && value !== undefined && value !== ''
    })
    
    return allFields.length > 0 ? Math.round((filledFields.length / allFields.length) * 100) : 0
  })

  // 提交表单
  const submitForm = async () => {
    if (!enableSubmit) {
      console.warn('⚠️ 未启用提交功能')
      return false
    }
    
    if (mode === 'readonly') {
      console.warn('⚠️ 只读模式下无法提交表单')
      return false
    }
    
    // 验证表单
    if (!validateForm()) {
      console.warn('⚠️ 表单验证失败')
      return false
    }
    
    try {
      isSubmitting.value = true
      
      // 这里可以调用 API 提交表单数据
      // await submitFormData(currentFormData.value)
      
      console.log('✅ 表单提交成功')
      return true
    } catch (error) {
      console.error('❌ 表单提交失败:', error)
      return false
    } finally {
      isSubmitting.value = false
    }
  }

  // 导出表单数据
  const exportFormData = (format: 'json' | 'csv' | 'pdf' = 'json') => {
    if (!previewData.value) return null
    
    const exportData = {
      metadata: {
        title: previewData.value.pageConfig.title || '未命名表单',
        exportTime: new Date().toISOString(),
        completionPercentage: completionPercentage.value,
        format
      },
      formData: currentFormData.value,
      validationResults: validationErrors.value,
      schema: previewData.value.sections
    }
    
    switch (format) {
      case 'json':
        return JSON.stringify(exportData, null, 2)
      case 'csv':
        return convertToCSV(exportData)
      case 'pdf':
        // 这里需要实现 PDF 导出逻辑
        console.warn('PDF 导出功能待实现')
        return null
      default:
        return exportData
    }
  }

  // 转换为 CSV 格式
  const convertToCSV = (data: Record<string, unknown>) => {
    const headers = ['字段ID', '字段名称', '字段值', '验证状态']
    const rows = [headers]
    
    for (const [fieldId, value] of Object.entries(data.formData)) {
      const fieldConfig = getFieldConfig(fieldId)
      const fieldName = fieldConfig?.title || fieldConfig?.name || fieldId
      const hasErrors = data.validationResults[fieldId]?.length > 0
      const status = hasErrors ? '验证失败' : '验证通过'
      
      rows.push([fieldId, fieldName, String(value || ''), status])
    }
    
    return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')
  }

  // 重置表单
  const resetForm = () => {
    currentFormData.value = previewData.value ? { ...previewData.value.formData } : {}
    validationErrors.value = {}
    console.log('🔄 表单已重置')
  }

  // 获取主题类名
  const getThemeClass = () => {
    return `preview-theme-${theme}`
  }

  // 监听预览数据变化
  watch(
    () => previewData.value,
    (newData) => {
      if (newData && mode === 'interactive') {
        // 在交互模式下，监听表单数据变化
        watch(
          () => currentFormData.value,
          () => {
            // 可以在这里实现实时保存等功能
          },
          { deep: true }
        )
      }
    }
  )

  return {
    // 状态
    isPreviewMode,
    previewData,
    currentFormData,
    validationErrors,
    isSubmitting,
    completionPercentage,
    
    // 方法
    enterPreview,
    exitPreview,
    updateField,
    validateField,
    validateForm,
    submitForm,
    exportFormData,
    resetForm,
    getThemeClass,
    
    // 配置
    mode,
    showValidation,
    enableSubmit,
    theme
  }
}