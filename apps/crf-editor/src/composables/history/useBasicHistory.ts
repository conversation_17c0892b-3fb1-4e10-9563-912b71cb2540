/**
 * 基础历史记录功能
 * 提供撤销/重做的核心功能
 */

import { ref, computed } from 'vue'
import { deepClone } from '@/utils/deep-clone'
import { use<PERSON>rror<PERSON><PERSON><PERSON>, ErrorCategory, ErrorLevel } from '@/utils/error-handler'

// 历史记录条目
export interface HistoryEntry<T = unknown> {
  id: string
  timestamp: number
  action: string
  description: string
  data: T
  metadata?: {
    user?: string
    component?: string
    size?: number
    duration?: number
  }
}

// 基础历史记录配置
export interface BasicHistoryConfig {
  maxSize: number
  trackMetadata: boolean
}

/**
 * 基础历史记录管理器
 */
export function useBasicHistory<T = unknown>(config: Partial<BasicHistoryConfig> = {}) {
  const { handleError } = useErrorHandler()
  
  // 默认配置
  const defaultConfig: BasicHistoryConfig = {
    maxSize: 50,
    trackMetadata: true
  }
  
  const finalConfig = { ...defaultConfig, ...config }
  
  // 状态
  const history = ref<HistoryEntry<T>[]>([])
  const currentIndex = ref(-1)
  const isRecording = ref(true)
  
  // 计算属性
  const canUndo = computed(() => currentIndex.value > 0)
  const canRedo = computed(() => currentIndex.value < history.value.length - 1)
  const currentEntry = computed(() => {
    return currentIndex.value >= 0 ? history.value[currentIndex.value] : null
  })
  
  const size = computed(() => history.value.length)
  const memoryUsage = computed(() => {
    return history.value.reduce((total, entry) => {
      const entrySize = JSON.stringify(entry).length
      return total + entrySize
    }, 0)
  })
  
  // 方法
  const generateId = (): string => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  const calculateSize = (data: unknown): number => {
    try {
      return JSON.stringify(data).length
    } catch {
      return 0
    }
  }
  
  const addEntry = (action: string, data: T, description?: string): string => {
    if (!isRecording.value) return ''
    
    try {
      const id = generateId()
      const entry: HistoryEntry<T> = {
        id,
        timestamp: Date.now(),
        action,
        description: description || action,
        data: deepClone(data)
      }
      
      if (finalConfig.trackMetadata) {
        entry.metadata = {
          size: calculateSize(data)
        }
      }
      
      // 如果当前位置不在末尾，删除后续记录
      if (currentIndex.value < history.value.length - 1) {
        history.value = history.value.slice(0, currentIndex.value + 1)
      }
      
      // 添加新记录
      history.value.push(entry as HistoryEntry<T>)
      currentIndex.value = history.value.length - 1
      
      // 限制历史记录大小
      if (history.value.length > finalConfig.maxSize) {
        const removeCount = history.value.length - finalConfig.maxSize
        history.value = history.value.slice(removeCount)
        currentIndex.value = Math.max(0, currentIndex.value - removeCount)
      }
      
      return id
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, ErrorCategory.SYSTEM)
      return ''
    }
  }
  
  const undo = (): T | null => {
    if (!canUndo.value) return null
    
    try {
      currentIndex.value--
      return currentEntry.value ? deepClone(currentEntry.value.data) as T : null
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, ErrorCategory.BUSINESS)
      return null
    }
  }
  
  const redo = (): T | null => {
    if (!canRedo.value) return null
    
    try {
      currentIndex.value++
      return currentEntry.value ? deepClone(currentEntry.value.data) as T : null
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, ErrorCategory.BUSINESS)
      return null
    }
  }
  
  const goToEntry = (id: string): T | null => {
    const index = history.value.findIndex(entry => entry.id === id)
    if (index === -1) return null
    
    try {
      currentIndex.value = index
      return currentEntry.value ? deepClone(currentEntry.value.data) as T : null
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, ErrorCategory.BUSINESS)
      return null
    }
  }
  
  const clear = (): void => {
    history.value = []
    currentIndex.value = -1
  }
  
  const setRecording = (recording: boolean): void => {
    isRecording.value = recording
  }
  
  const getHistory = () => {
    return history.value.map(entry => ({
      ...entry,
      data: deepClone(entry.data)
    }))
  }
  
  const getEntryById = (id: string): HistoryEntry<T> | null => {
    const entry = history.value.find(e => e.id === id)
    return entry ? { ...entry, data: deepClone(entry.data) as T } : null
  }
  
  return {
    // 状态
    history: computed(() => history.value),
    currentIndex: computed(() => currentIndex.value),
    currentEntry,
    canUndo,
    canRedo,
    size,
    memoryUsage,
    isRecording: computed(() => isRecording.value),
    
    // 方法
    addEntry,
    undo,
    redo,
    goToEntry,
    clear,
    setRecording,
    getHistory,
    getEntryById
  }
}