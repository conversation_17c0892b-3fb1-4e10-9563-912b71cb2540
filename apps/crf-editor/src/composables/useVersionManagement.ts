import { ref, computed } from 'vue'
import { templateAPI } from '@/api'
import type { CRFTemplate, CRFVersion } from '@/types/api'

interface VersionManagementOptions {
  templateId: string
  projectId?: string
}

interface PublishOptions {
  version?: string
  description?: string
  changeLog?: string
  autoIncrement?: boolean // 是否自动递增版本号
}

interface VersionInfo {
  major: number
  minor: number
  patch: number
}

export function useVersionManagement(options: VersionManagementOptions) {
  const { templateId } = options

  // 状态
  const isPublishing = ref(false)
  const currentTemplate = ref<CRFTemplate | null>(null)
  const versions = ref<CRFVersion[]>([])
  const publishedVersions = ref<CRFVersion[]>([])
  const draftVersions = ref<CRFVersion[]>([])

  // 计算属性
  const latestVersion = computed(() => {
    if (versions.value.length === 0) return null
    return versions.value.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0]
  })

  const latestPublishedVersion = computed(() => {
    if (publishedVersions.value.length === 0) return null
    return publishedVersions.value.sort((a, b) =>
      new Date(b.published_at || b.created_at).getTime() - new Date(a.published_at || a.created_at).getTime()
    )[0]
  })

  const currentVersion = computed(() => {
    return currentTemplate.value?.version || '1.0.0'
  })

  const hasUnpublishedChanges = computed(() => {
    // 检查是否有未发布的更改
    if (!currentTemplate.value) return false
    return currentTemplate.value.status === 'draft' ||
      (latestPublishedVersion.value &&
        currentTemplate.value.updated_at > (latestPublishedVersion.value.published_at || latestPublishedVersion.value.created_at))
  })

  // 解析版本号
  const parseVersion = (version: string): VersionInfo => {
    const parts = version.split('.').map(Number)
    return {
      major: parts[0] || 1,
      minor: parts[1] || 0,
      patch: parts[2] || 0
    }
  }

  // 生成版本号
  const generateVersion = (baseVersion: string, type: 'major' | 'minor' | 'patch' = 'patch'): string => {
    const version = parseVersion(baseVersion)

    switch (type) {
      case 'major':
        version.major++
        version.minor = 0
        version.patch = 0
        break
      case 'minor':
        version.minor++
        version.patch = 0
        break
      case 'patch':
        version.patch++
        break
    }

    return `${version.major}.${version.minor}.${version.patch}`
  }

  // 获取下一个版本号
  const getNextVersion = (type: 'major' | 'minor' | 'patch' = 'patch'): string => {
    const baseVersion = latestPublishedVersion.value?.version || currentVersion.value
    return generateVersion(baseVersion, type)
  }

  // 加载模板信息
  const loadTemplate = async () => {
    try {
      const response = await templateAPI.getTemplate(templateId)
      currentTemplate.value = response.data?.template
      return currentTemplate.value
    } catch (error) {
      console.error('加载模板失败:', error)
      throw error
    }
  }

  // 加载版本历史
  const loadVersions = async () => {
    try {
      const response = await templateAPI.getTemplateVersions(templateId)
      versions.value = response.data?.versions || []

      // 分类版本
      publishedVersions.value = versions.value.filter(v => v.status === 'published')
      draftVersions.value = versions.value.filter(v => v.status === 'draft')

      return versions.value
    } catch (error) {
      console.error('加载版本历史失败:', error)
      throw error
    }
  }

  // 创建新版本
  const createVersion = async (templateId: string, versionData: Partial<CRFVersion>) => {
    try {
      const response = await templateAPI.createVersion({
        template_id: templateId,
        version: versionData.version || '',
        description: versionData.description,
        change_log: versionData.change_log
      })
      return response.data as CRFVersion
    } catch (error) {
      console.error('创建版本失败:', error)
      throw error
    }
  }

  // 发布当前版本
  const publishTemplate = async (options: PublishOptions = {}) => {
    if (isPublishing.value) return null

    try {
      isPublishing.value = true

      // 确定版本号
      let version = options.version
      if (!version && options.autoIncrement) {
        version = getNextVersion('patch')
      } else if (!version) {
        version = currentVersion.value
      }

      // 更新模板版本
      if (version !== currentVersion.value) {
        await templateAPI.updateTemplate(templateId, {
          version: version,
          description: options.description,
          status: 'published'
        })
      }

      // 发布模板
      const response = await templateAPI.publishTemplate(templateId)
      const publishedVersion = response.data?.version

      // 更新本地状态
      await loadTemplate()
      await loadVersions()

      console.log(`🚀 模板已发布，版本: ${publishedVersion.version}`)
      return publishedVersion
    } catch (error) {
      console.error('发布失败:', error)
      throw error
    } finally {
      isPublishing.value = false
    }
  }

  // 回滚到指定版本
  const rollbackToVersion = async (versionId: string) => {
    try {
      const targetVersion = versions.value.find(v => v.id === versionId)
      if (!targetVersion) {
        throw new Error('找不到指定版本')
      }

      // 使用版本快照数据更新当前模板
      const updateData = {
        form_structure: targetVersion.snapshot_data,
        version: generateVersion(targetVersion.version, 'patch'), // 生成新的补丁版本
        description: `回滚到版本 ${targetVersion.version}`,
        status: 'draft'
      }

      await templateAPI.updateTemplate(templateId, updateData)

      // 更新编辑器状态
      if (targetVersion.snapshot_data) {
        // 这里需要将快照数据恢复到编辑器
        // 具体实现取决于数据结构
        console.log('恢复版本数据到编辑器')
      }

      await loadTemplate()
      console.log(`↶ 已回滚到版本 ${targetVersion.version}`)
      return true
    } catch (error) {
      console.error('回滚失败:', error)
      throw error
    }
  }

  // 比较两个版本
  const compareVersions = (version1Id: string, version2Id: string) => {
    const v1 = versions.value.find(v => v.id === version1Id)
    const v2 = versions.value.find(v => v.id === version2Id)

    if (!v1 || !v2) {
      throw new Error('找不到指定版本')
    }

    return {
      version1: v1,
      version2: v2,
      differences: generateDifferences(v1.snapshot_data, v2.snapshot_data)
    }
  }

  // 生成差异比较
  const generateDifferences = (data1: Record<string, unknown>, data2: Record<string, unknown>) => {
    // 这里实现具体的差异比较逻辑
    // 可以使用 deep-diff 或其他差异比较库
    const differences = []

    // 简单的示例实现
    if (JSON.stringify(data1) !== JSON.stringify(data2)) {
      differences.push({
        type: 'change',
        path: 'form_structure',
        oldValue: data1,
        newValue: data2
      })
    }

    return differences
  }

  // 获取版本统计信息
  const getVersionStats = () => {
    return {
      total: versions.value.length,
      published: publishedVersions.value.length,
      draft: draftVersions.value.length,
      latestVersion: latestVersion.value?.version,
      latestPublished: latestPublishedVersion.value?.version
    }
  }

  // 检查版本兼容性
  const checkCompatibility = (version: string) => {
    const current = parseVersion(currentVersion.value)
    const target = parseVersion(version)

    // 简单的兼容性检查规则
    if (target.major > current.major) {
      return { compatible: false, reason: '主版本号不兼容' }
    }

    if (target.major < current.major) {
      return { compatible: false, reason: '版本过低' }
    }

    return { compatible: true, reason: '版本兼容' }
  }

  // 生成变更日志
  const generateChangeLog = () => {
    if (!currentTemplate.value || !latestPublishedVersion.value) {
      return '首次发布'
    }

    // 这里可以根据实际变更生成更详细的日志
    const changes = [
      '更新表单结构',
      '修复已知问题',
      '优化用户体验'
    ]

    return changes.join('\n- ')
  }

  // 初始化
  const init = async () => {
    try {
      await Promise.all([
        loadTemplate(),
        loadVersions()
      ])
      console.log('✅ 版本管理初始化完成')
    } catch (error) {
      console.error('❌ 版本管理初始化失败:', error)
      throw error
    }
  }

  return {
    // 状态
    isPublishing,
    currentTemplate,
    versions,
    publishedVersions,
    draftVersions,
    latestVersion,
    latestPublishedVersion,
    currentVersion,
    hasUnpublishedChanges,

    // 方法
    loadTemplate,
    loadVersions,
    createVersion,
    publishTemplate,
    rollbackToVersion,
    compareVersions,
    getVersionStats,
    checkCompatibility,
    generateChangeLog,
    parseVersion,
    generateVersion,
    getNextVersion,
    init
  }
}