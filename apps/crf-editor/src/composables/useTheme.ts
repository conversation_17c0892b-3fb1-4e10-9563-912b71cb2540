/**
 * 主题管理器
 * 支持多主题切换、动态主题、自定义主题、主题继承等
 */

import { ref, shallowRef, computed, watch, nextTick } from 'vue'
import { useCacheManager } from '@/composables/useCacheManager'
import { useErrorHandler, ErrorCategory } from '@/utils/error-handler'

// 主题模式
export type ThemeMode = 'light' | 'dark' | 'auto' | 'custom'

// 颜色格式
export interface ThemeColor {
  hex: string
  rgb: { r: number; g: number; b: number }
  hsl: { h: number; s: number; l: number }
  alpha?: number
}

// 主题变量
export interface ThemeVariables {
  // 基础颜色
  primary: string
  success: string
  warning: string
  danger: string
  info: string

  // 背景颜色
  bgColor: string
  bgColorPage: string
  bgColorOverlay: string

  // 文字颜色
  textColorPrimary: string
  textColorRegular: string
  textColorSecondary: string
  textColorPlaceholder: string
  textColorDisabled: string

  // 边框颜色
  borderColor: string
  borderColorLight: string
  borderColorLighter: string
  borderColorExtraLight: string
  borderColorDark: string
  borderColorDarker: string

  // 填充颜色
  fillColor: string
  fillColorLight: string
  fillColorLighter: string
  fillColorExtraLight: string
  fillColorDark: string
  fillColorDarker: string
  fillColorBlank: string

  // 阴影
  boxShadow: string
  boxShadowLight: string
  boxShadowBase: string
  boxShadowDark: string

  // 字体
  fontFamily: string
  fontSizeLarge: string
  fontSizeBase: string
  fontSizeSmall: string
  fontSizeExtraSmall: string

  // 圆角
  borderRadiusBase: string
  borderRadiusSmall: string
  borderRadiusRound: string
  borderRadiusCircle: string

  // 间距
  spacingXs: string
  spacingSm: string
  spacingBase: string
  spacingMd: string
  spacingLg: string
  spacingXl: string

  // 动画
  transitionDuration: string
  transitionBase: string
  transitionFade: string
  transitionMdFade: string
  transitionSlowFade: string
  transitionBorder: string
  transitionBox: string
  transitionColor: string

  // 层级
  zIndexNormal: string
  zIndexTop: string
  zIndexPopper: string
}

// 主题配置
export interface ThemeConfig {
  name: string
  displayName: string
  mode: ThemeMode
  variables: Partial<ThemeVariables>
  extends?: string // 继承的主题名称
  author?: string
  version?: string
  description?: string
  preview?: string // 预览图片URL
  tags?: string[]
  created?: number
  updated?: number
}

// 主题事件
export type ThemeEvent = 'change' | 'load' | 'create' | 'update' | 'delete'

type ThemeEventListener = (event: ThemeEvent, themeName: string, config?: ThemeConfig) => void

/**
 * 主题管理器组合式API
 */
export function useTheme() {
  const cache = useCacheManager<ThemeConfig>({
    strategy: 'LRU',
    maxSize: 20,
    defaultTTL: 86400000, // 24小时
    storageKey: 'theme-cache'
  })
  const { handleError } = useErrorHandler()

  // 当前主题状态
  const currentTheme = ref<string>('light')
  const currentMode = ref<ThemeMode>('light')
  const isChanging = ref(false)
  const systemPrefersDark = ref(false)

  // 主题注册表
  const themes = shallowRef<Map<string, ThemeConfig>>(new Map())
  const customThemes = shallowRef<Map<string, ThemeConfig>>(new Map())
  const eventListeners = shallowRef<ThemeEventListener[]>([])

  // 默认主题变量
  const defaultLightVariables: ThemeVariables = {
    primary: '#409eff',
    success: '#67c23a',
    warning: '#e6a23c',
    danger: '#f56c6c',
    info: '#909399',

    bgColor: '#ffffff',
    bgColorPage: '#f2f3f5',
    bgColorOverlay: '#ffffff',

    textColorPrimary: '#303133',
    textColorRegular: '#606266',
    textColorSecondary: '#909399',
    textColorPlaceholder: '#a8abb2',
    textColorDisabled: '#c0c4cc',

    borderColor: '#dcdfe6',
    borderColorLight: '#e4e7ed',
    borderColorLighter: '#ebeef5',
    borderColorExtraLight: '#f2f6fc',
    borderColorDark: '#d4d7de',
    borderColorDarker: '#cdd0d6',

    fillColor: '#f0f2f5',
    fillColorLight: '#f5f7fa',
    fillColorLighter: '#fafafa',
    fillColorExtraLight: '#fafcff',
    fillColorDark: '#ebedf0',
    fillColorDarker: '#e6e8eb',
    fillColorBlank: '#ffffff',

    boxShadow: '0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08)',
    boxShadowLight: '0 0 12px rgba(0, 0, 0, 0.12)',
    boxShadowBase: '0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04)',
    boxShadowDark: '0 4px 8px rgba(0, 0, 0, 0.12), 0 0 8px rgba(0, 0, 0, 0.08)',

    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',
    fontSizeLarge: '18px',
    fontSizeBase: '14px',
    fontSizeSmall: '13px',
    fontSizeExtraSmall: '12px',

    borderRadiusBase: '4px',
    borderRadiusSmall: '2px',
    borderRadiusRound: '20px',
    borderRadiusCircle: '100%',

    spacingXs: '4px',
    spacingSm: '8px',
    spacingBase: '12px',
    spacingMd: '16px',
    spacingLg: '20px',
    spacingXl: '24px',

    transitionDuration: '0s',
    transitionBase: 'none',
    transitionFade: 'none',
    transitionMdFade: 'none',
    transitionSlowFade: 'none',
    transitionBorder: 'none',
    transitionBox: 'none',
    transitionColor: 'none',

    zIndexNormal: '1',
    zIndexTop: '1000',
    zIndexPopper: '2000'
  }

  const defaultDarkVariables: ThemeVariables = {
    ...defaultLightVariables,

    primary: '#409eff',
    success: '#67c23a',
    warning: '#e6a23c',
    danger: '#f56c6c',
    info: '#909399',

    bgColor: '#141414',
    bgColorPage: '#0a0a0a',
    bgColorOverlay: '#1d1e1f',

    textColorPrimary: '#e5eaf3',
    textColorRegular: '#cfd3dc',
    textColorSecondary: '#a3a6ad',
    textColorPlaceholder: '#8d9095',
    textColorDisabled: '#6c6e72',

    borderColor: '#4c4d4f',
    borderColorLight: '#414243',
    borderColorLighter: '#363637',
    borderColorExtraLight: '#2b2b2c',
    borderColorDark: '#58585b',
    borderColorDarker: '#636466',

    fillColor: '#303030',
    fillColorLight: '#262727',
    fillColorLighter: '#1d1d1d',
    fillColorExtraLight: '#191919',
    fillColorDark: '#39393a',
    fillColorDarker: '#424243',
    fillColorBlank: '#141414',

    boxShadow: '0 12px 32px 4px rgba(0, 0, 0, 0.36), 0 8px 20px rgba(0, 0, 0, 0.72)',
    boxShadowLight: '0 0 12px rgba(0, 0, 0, 0.72)',
    boxShadowBase: '0 2px 4px rgba(0, 0, 0, 0.72), 0 0 6px rgba(0, 0, 0, 0.54)',
    boxShadowDark: '0 4px 8px rgba(0, 0, 0, 0.72), 0 0 8px rgba(0, 0, 0, 0.54)'
  }

  // 计算属性
  const availableThemes = computed(() => Array.from(themes.value.keys()))
  const currentThemeConfig = computed(() => themes.value.get(currentTheme.value))
  const isDarkMode = computed(() => currentMode.value === 'dark' ||
    (currentMode.value === 'auto' && systemPrefersDark.value))

  /**
   * 触发主题事件
   */
  function emitEvent(event: ThemeEvent, themeName: string, config?: ThemeConfig): void {
    eventListeners.value.forEach(listener => {
      try {
        listener(event, themeName, config)
      } catch (error) {
        console.warn('主题事件监听器错误:', error)
      }
    })
  }

  /**
   * 注册内置主题
   */
  function registerBuiltinThemes(): void {
    // 浅色主题
    registerTheme({
      name: 'light',
      displayName: '浅色主题',
      mode: 'light',
      variables: defaultLightVariables,
      author: 'System',
      version: '1.0.0',
      description: '默认的浅色主题',
      tags: ['default', 'light']
    })

    // 深色主题
    registerTheme({
      name: 'dark',
      displayName: '深色主题',
      mode: 'dark',
      variables: defaultDarkVariables,
      author: 'System',
      version: '1.0.0',
      description: '默认的深色主题',
      tags: ['default', 'dark']
    })

    // 蓝色主题
    registerTheme({
      name: 'blue',
      displayName: '蓝色主题',
      mode: 'light',
      extends: 'light',
      variables: {
        primary: '#1890ff',
        bgColorPage: '#f0f5ff'
      },
      author: 'System',
      version: '1.0.0',
      description: '以蓝色为主色调的主题',
      tags: ['color', 'blue']
    })

    // 绿色主题
    registerTheme({
      name: 'green',
      displayName: '绿色主题',
      mode: 'light',
      extends: 'light',
      variables: {
        primary: '#52c41a',
        bgColorPage: '#f6ffed'
      },
      author: 'System',
      version: '1.0.0',
      description: '以绿色为主色调的主题',
      tags: ['color', 'green']
    })

    // 紫色主题
    registerTheme({
      name: 'purple',
      displayName: '紫色主题',
      mode: 'light',
      extends: 'light',
      variables: {
        primary: '#722ed1',
        bgColorPage: '#f9f0ff'
      },
      author: 'System',
      version: '1.0.0',
      description: '以紫色为主色调的主题',
      tags: ['color', 'purple']
    })
  }

  /**
   * 注册主题
   */
  function registerTheme(config: ThemeConfig): void {
    const now = Date.now()
    const themeConfig: ThemeConfig = {
      ...config,
      created: config.created || now,
      updated: now
    }

    themes.value.set(config.name, themeConfig)

    // 缓存主题配置
    cache.set(config.name, themeConfig)

    emitEvent('create', config.name, themeConfig)

    console.log(`主题已注册: ${config.name}`)
  }

  /**
   * 注销主题
   */
  function unregisterTheme(name: string): boolean {
    if (!themes.value.has(name)) return false

    const config = themes.value.get(name)
    themes.value.delete(name)
    customThemes.value.delete(name)

    // 清除缓存
    cache.remove(name)

    emitEvent('delete', name, config)

    // 如果删除的是当前主题，切换到默认主题
    if (currentTheme.value === name) {
      setTheme('light')
    }

    console.log(`主题已注销: ${name}`)
    return true
  }

  /**
   * 解析主题变量（支持继承）
   */
  function resolveThemeVariables(config: ThemeConfig): ThemeVariables {
    let variables: Partial<ThemeVariables> = {}

    // 如果有继承，先解析父主题
    if (config.extends) {
      const parentConfig = themes.value.get(config.extends)
      if (parentConfig) {
        variables = { ...resolveThemeVariables(parentConfig) }
      }
    } else {
      // 使用默认变量作为基础
      variables = config.mode === 'dark' ?
        { ...defaultDarkVariables } :
        { ...defaultLightVariables }
    }

    // 合并当前主题的变量
    return { ...variables, ...config.variables } as ThemeVariables
  }

  /**
   * 应用主题变量到CSS
   */
  function applyCSSVariables(variables: ThemeVariables): void {
    const root = document.documentElement

    Object.entries(variables).forEach(([key, value]) => {
      const cssVar = `--crf-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
      root.style.setProperty(cssVar, value)
    })
  }

  /**
   * 设置主题
   */
  async function setTheme(themeName: string): Promise<void> {
    if (themeName === currentTheme.value || isChanging.value) {
      return
    }

    const config = themes.value.get(themeName)
    if (!config) {
      throw new Error(`主题不存在: ${themeName}`)
    }

    try {
      isChanging.value = true

      // 解析主题变量
      const variables = resolveThemeVariables(config)

      // 应用CSS变量
      applyCSSVariables(variables)

      // 更新状态
      const oldTheme = currentTheme.value
      currentTheme.value = themeName
      currentMode.value = config.mode

      // 更新body类名
      document.body.classList.remove(`theme-${oldTheme}`)
      document.body.classList.add(`theme-${themeName}`)

      // 更新meta主题色
      updateMetaThemeColor(variables.primary)

      // 持久化主题设置
      localStorage.setItem('preferred-theme', themeName)

      // 等待DOM更新
      await nextTick()

      emitEvent('change', themeName, config)

      console.log(`主题已切换: ${oldTheme} -> ${themeName}`)

    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM)
      throw new Error(`主题切换失败: ${themeName}`)
    } finally {
      isChanging.value = false
    }
  }

  /**
   * 设置主题模式
   */
  async function setThemeMode(mode: ThemeMode): Promise<void> {
    currentMode.value = mode

    let targetTheme: string

    switch (mode) {
      case 'light':
        targetTheme = 'light'
        break
      case 'dark':
        targetTheme = 'dark'
        break
      case 'auto':
        targetTheme = systemPrefersDark.value ? 'dark' : 'light'
        break
      default:
        targetTheme = mode // 自定义主题名称
    }

    if (targetTheme !== currentTheme.value) {
      await setTheme(targetTheme)
    }

    localStorage.setItem('preferred-theme-mode', mode)
  }

  /**
   * 创建自定义主题
   */
  function createCustomTheme(
    name: string,
    displayName: string,
    baseTheme: string,
    customVariables: Partial<ThemeVariables>
  ): ThemeConfig {
    const baseConfig = themes.value.get(baseTheme)
    if (!baseConfig) {
      throw new Error(`基础主题不存在: ${baseTheme}`)
    }

    const customConfig: ThemeConfig = {
      name,
      displayName,
      mode: baseConfig.mode,
      extends: baseTheme,
      variables: customVariables,
      author: 'User',
      version: '1.0.0',
      description: `基于 ${baseConfig.displayName} 的自定义主题`,
      tags: ['custom']
    }

    registerTheme(customConfig)
    customThemes.value.set(name, customConfig)

    // 持久化自定义主题
    saveCustomThemes()

    return customConfig
  }

  /**
   * 更新自定义主题
   */
  function updateCustomTheme(
    name: string,
    updates: Partial<ThemeConfig>
  ): boolean {
    const config = customThemes.value.get(name)
    if (!config) return false

    const updatedConfig = {
      ...config,
      ...updates,
      updated: Date.now()
    }

    themes.value.set(name, updatedConfig)
    customThemes.value.set(name, updatedConfig)

    // 如果是当前主题，重新应用
    if (currentTheme.value === name) {
      const variables = resolveThemeVariables(updatedConfig)
      applyCSSVariables(variables)
    }

    saveCustomThemes()
    emitEvent('update', name, updatedConfig)

    return true
  }

  /**
   * 删除自定义主题
   */
  function deleteCustomTheme(name: string): boolean {
    if (!customThemes.value.has(name)) return false

    customThemes.value.delete(name)
    unregisterTheme(name)
    saveCustomThemes()

    return true
  }

  /**
   * 保存自定义主题到本地存储
   */
  function saveCustomThemes(): void {
    try {
      const customThemeData = Array.from(customThemes.value.entries())
      localStorage.setItem('custom-themes', JSON.stringify(customThemeData))
    } catch (error) {
      console.warn('保存自定义主题失败:', error)
    }
  }

  /**
   * 从本地存储恢复自定义主题
   */
  function loadCustomThemes(): void {
    try {
      const stored = localStorage.getItem('custom-themes')
      if (stored) {
        const customThemeData = JSON.parse(stored)
        customThemeData.forEach(([name, config]: [string, ThemeConfig]) => {
          customThemes.value.set(name, config)
          themes.value.set(name, config)
        })
        console.log(`恢复了 ${customThemeData.length} 个自定义主题`)
      }
    } catch (error) {
      console.warn('加载自定义主题失败:', error)
    }
  }

  /**
   * 导出主题配置
   */
  function exportTheme(themeName: string): string {
    const config = themes.value.get(themeName)
    if (!config) {
      throw new Error(`主题不存在: ${themeName}`)
    }

    return JSON.stringify(config, null, 2)
  }

  /**
   * 导入主题配置
   */
  function importTheme(themeData: string): ThemeConfig {
    try {
      const config = JSON.parse(themeData) as ThemeConfig

      // 验证主题配置
      if (!config.name || !config.displayName || !config.mode) {
        throw new Error('主题配置格式不正确')
      }

      registerTheme(config)

      if (config.author === 'User' || config.tags?.includes('custom')) {
        customThemes.value.set(config.name, config)
        saveCustomThemes()
      }

      console.log(`主题导入成功: ${config.name}`)
      return config

    } catch (error) {
      throw new Error(`主题导入失败: ${(error as Error).message}`)
    }
  }

  /**
   * 检测系统主题偏好
   */
  function detectSystemTheme(): (() => void) | void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

      systemPrefersDark.value = mediaQuery.matches

      // 监听系统主题变化
      const handleChange = (e: MediaQueryListEvent) => {
        systemPrefersDark.value = e.matches

        // 如果当前是auto模式，自动切换主题
        if (currentMode.value === 'auto') {
          const targetTheme = e.matches ? 'dark' : 'light'
          if (targetTheme !== currentTheme.value) {
            setTheme(targetTheme)
          }
        }
      }

      mediaQuery.addEventListener('change', handleChange)

      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    }
  }

  /**
   * 更新meta主题色
   */
  function updateMetaThemeColor(color: string): void {
    let metaTag = document.querySelector('meta[name="theme-color"]') as HTMLMetaElement

    if (!metaTag) {
      metaTag = document.createElement('meta')
      metaTag.name = 'theme-color'
      document.head.appendChild(metaTag)
    }

    metaTag.content = color
  }

  /**
   * 获取主题列表
   */
  function getThemeList(): ThemeConfig[] {
    return Array.from(themes.value.values())
  }

  /**
   * 获取自定义主题列表
   */
  function getCustomThemes(): ThemeConfig[] {
    return Array.from(customThemes.value.values())
  }

  /**
   * 切换主题（在浅色和深色之间）
   */
  async function toggleTheme(): Promise<void> {
    const targetTheme = isDarkMode.value ? 'light' : 'dark'
    await setTheme(targetTheme)
  }

  /**
   * 添加事件监听器
   */
  function addEventListener(listener: ThemeEventListener): () => void {
    eventListeners.value.push(listener)

    return () => {
      const index = eventListeners.value.indexOf(listener)
      if (index > -1) {
        eventListeners.value.splice(index, 1)
      }
    }
  }

  /**
   * 生成主题预览
   */
  function generateThemePreview(themeName: string): Promise<string> {
    return new Promise((resolve) => {
      const config = themes.value.get(themeName)
      if (!config) {
        resolve('')
        return
      }

      // 创建预览画布
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!

      canvas.width = 200
      canvas.height = 120

      const variables = resolveThemeVariables(config)

      // 绘制背景
      ctx.fillStyle = variables.bgColor
      ctx.fillRect(0, 0, 200, 120)

      // 绘制主色
      ctx.fillStyle = variables.primary
      ctx.fillRect(10, 10, 180, 30)

      // 绘制文字色
      ctx.fillStyle = variables.textColorPrimary
      ctx.fillRect(10, 50, 60, 20)

      // 绘制边框色
      ctx.strokeStyle = variables.borderColor
      ctx.lineWidth = 2
      ctx.strokeRect(80, 50, 60, 20)

      // 绘制成功色
      ctx.fillStyle = variables.success
      ctx.fillRect(150, 50, 40, 20)

      // 绘制页面背景色
      ctx.fillStyle = variables.bgColorPage
      ctx.fillRect(10, 80, 180, 30)

      resolve(canvas.toDataURL())
    })
  }

  /**
   * 初始化
   */
  async function init(): Promise<void> {
    // 注册内置主题
    registerBuiltinThemes()

    // 恢复自定义主题
    loadCustomThemes()

    // 检测系统主题
    detectSystemTheme()

    // 恢复主题设置
    const savedTheme = localStorage.getItem('preferred-theme')
    const savedMode = localStorage.getItem('preferred-theme-mode') as ThemeMode

    if (savedMode) {
      await setThemeMode(savedMode)
    } else if (savedTheme && themes.value.has(savedTheme)) {
      await setTheme(savedTheme)
    } else {
      // 使用系统偏好
      await setThemeMode('auto')
    }
  }

  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    document.documentElement.setAttribute('data-theme', newTheme)
  })

  return {
    // 状态
    currentTheme,
    currentMode,
    currentThemeConfig,
    isDarkMode,
    isChanging,
    systemPrefersDark,
    availableThemes,

    // 主题管理
    setTheme,
    setThemeMode,
    toggleTheme,
    registerTheme,
    unregisterTheme,

    // 自定义主题
    createCustomTheme,
    updateCustomTheme,
    deleteCustomTheme,
    getCustomThemes,

    // 主题列表
    getThemeList,

    // 导入导出
    exportTheme,
    importTheme,

    // 预览
    generateThemePreview,

    // 事件
    addEventListener,

    // 工具方法
    resolveThemeVariables,
    detectSystemTheme,

    // 初始化
    init,

    // 清理
    cleanup: () => {
      cache.cleanup()
    }
  }
}