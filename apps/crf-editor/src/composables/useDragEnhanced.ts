import { ref, reactive } from 'vue'
import { useTimerManager } from './useTimerManager'
import { useErrorHandler } from '@/utils/error-handler'

/**
 * 拖拽配置接口
 */
export interface DragConfig {
  animation: number
  ghostClass: string
  chosenClass: string
  dragClass: string
  forceFallback: boolean
  fallbackClass: string
  fallbackTolerance: number
  scroll: boolean
  bubbleScroll: boolean
  scrollSensitivity: number
  scrollSpeed: number
  disabled: boolean
}

/**
 * 拖拽状态
 */
export interface DragState {
  isDragging: boolean
  draggedItem: Record<string, unknown> | null
  draggedIndex: number
  targetContainer: string | null
  insertIndex: number
  isOverValidTarget: boolean
}

/**
 * 增强的拖拽组合式API
 */
export function useDragEnhanced() {
  const { safeSetTimeout, clearAllTimers } = useTimerManager()
  const { handleError, ErrorCategory } = useErrorHandler()
  
  // 拖拽状态
  const dragState = reactive<DragState>({
    isDragging: false,
    draggedItem: null,
    draggedIndex: -1,
    targetContainer: null,
    insertIndex: -1,
    isOverValidTarget: false
  })
  
  // 拖拽预览元素
  const dragPreview = ref<HTMLElement | null>(null)
  const insertIndicator = ref<HTMLElement | null>(null)
  
  /**
   * 默认拖拽配置
   */
  const defaultDragConfig: DragConfig = {
    animation: 150,
    ghostClass: 'drag-ghost',
    chosenClass: 'drag-chosen',
    dragClass: 'drag-moving',
    forceFallback: true,
    fallbackClass: 'drag-fallback',
    fallbackTolerance: 3,
    scroll: true,
    bubbleScroll: true,
    scrollSensitivity: 30,
    scrollSpeed: 10,
    disabled: false
  }
  
  /**
   * 创建拖拽预览元素
   */
  const createDragPreview = (item: Record<string, unknown>, event: DragEvent) => {
    try {
      const preview = document.createElement('div')
      preview.className = 'component-drag-preview'
      preview.innerHTML = `
        <div style="display: flex; align-items: center;">
          <div class="component-icon">📝</div>
          <div class="component-name">${item.title || item.name || '组件'}</div>
        </div>
      `
      
      // 设置预览位置
      preview.style.left = `${event.clientX + 10}px`
      preview.style.top = `${event.clientY - 10}px`
      
      document.body.appendChild(preview)
      dragPreview.value = preview
      
      return preview
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, false)
      return null
    }
  }
  
  /**
   * 更新拖拽预览位置
   */
  const updateDragPreview = (event: DragEvent) => {
    if (dragPreview.value) {
      dragPreview.value.style.left = `${event.clientX + 10}px`
      dragPreview.value.style.top = `${event.clientY - 10}px`
    }
  }
  
  /**
   * 清理拖拽预览
   */
  const cleanupDragPreview = () => {
    if (dragPreview.value) {
      document.body.removeChild(dragPreview.value)
      dragPreview.value = null
    }
  }
  
  /**
   * 创建插入位置指示器
   */
  const createInsertIndicator = () => {
    const indicator = document.createElement('div')
    indicator.className = 'insert-indicator'
    indicator.style.display = 'none'
    document.body.appendChild(indicator)
    insertIndicator.value = indicator
    return indicator
  }
  
  /**
   * 显示插入位置指示器
   */
  const showInsertIndicator = (element: HTMLElement, position: 'before' | 'after') => {
    if (!insertIndicator.value) {
      createInsertIndicator()
    }
    
    if (insertIndicator.value) {
      const rect = element.getBoundingClientRect()
      const scrollY = window.pageYOffset
      const scrollX = window.pageXOffset
      
      insertIndicator.value.style.display = 'block'
      insertIndicator.value.style.left = `${rect.left + scrollX}px`
      insertIndicator.value.style.width = `${rect.width}px`
      
      if (position === 'before') {
        insertIndicator.value.style.top = `${rect.top + scrollY - 2}px`
      } else {
        insertIndicator.value.style.top = `${rect.bottom + scrollY - 2}px`
      }
    }
  }
  
  /**
   * 隐藏插入位置指示器
   */
  const hideInsertIndicator = () => {
    if (insertIndicator.value) {
      insertIndicator.value.style.display = 'none'
    }
  }
  
  /**
   * 清理插入位置指示器
   */
  const cleanupInsertIndicator = () => {
    if (insertIndicator.value) {
      document.body.removeChild(insertIndicator.value)
      insertIndicator.value = null
    }
  }
  
  /**
   * 拖拽开始处理
   */
  const onDragStart = (event: Record<string, unknown>) => {
    try {
      const { item, oldIndex, from } = event
      
      dragState.isDragging = true
      dragState.draggedItem = item
      dragState.draggedIndex = oldIndex
      dragState.targetContainer = from.id || from.dataset.containerId
      
      // 创建拖拽预览
      if (event.originalEvent) {
        createDragPreview(item, event.originalEvent)
      }
      
      // 添加全局拖拽样式
      document.body.classList.add('dragging')
      
      console.log('拖拽开始:', {
        item: item.title || item.name,
        index: oldIndex,
        container: dragState.targetContainer
      })
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, false)
    }
  }
  
  /**
   * 拖拽移动处理
   */
  const onDragMove = (event: Record<string, unknown>) => {
    try {
      const { relatedElement, to, willInsertAfter } = event
      
      // 更新拖拽预览位置
      if (event.originalEvent) {
        updateDragPreview(event.originalEvent)
      }
      
      // 显示插入位置指示器
      if (relatedElement) {
        showInsertIndicator(relatedElement, willInsertAfter ? 'after' : 'before')
      }
      
      // 更新目标容器
      dragState.targetContainer = to.id || to.dataset.containerId
      dragState.isOverValidTarget = to.classList.contains('drop-zone')
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, false)
    }
  }
  
  /**
   * 拖拽结束处理
   */
  const onDragEnd = (event: Record<string, unknown>) => {
    try {
      const { item, newIndex, oldIndex, from, to } = event
      
      console.log('拖拽结束:', {
        item: item.title || item.name,
        oldIndex,
        newIndex,
        from: from.id || from.dataset.containerId,
        to: to.id || to.dataset.containerId,
        success: from !== to || oldIndex !== newIndex
      })
      
      // 清理状态
      dragState.isDragging = false
      dragState.draggedItem = null
      dragState.draggedIndex = -1
      dragState.targetContainer = null
      dragState.insertIndex = -1
      dragState.isOverValidTarget = false
      
      // 清理UI元素
      cleanupDragPreview()
      hideInsertIndicator()
      document.body.classList.remove('dragging')
      
      // 延迟清理，确保动画完成
      safeSetTimeout(() => {
        const dragElements = document.querySelectorAll('.drag-chosen, .drag-ghost, .drag-moving')
        dragElements.forEach(el => {
          el.classList.remove('drag-chosen', 'drag-ghost', 'drag-moving')
        })
      }, 200)
      
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, false)
    }
  }
  
  /**
   * 拖拽添加处理（从外部拖入）
   */
  const onDragAdd = (event: Record<string, unknown>) => {
    try {
      const { item, newIndex, to } = event
      
      console.log('组件添加:', {
        item: item.title || item.name,
        index: newIndex,
        container: to.id || to.dataset.containerId
      })
      
      // 触发添加成功动画
      safeSetTimeout(() => {
        const addedElement = to.children[newIndex]
        if (addedElement) {
          addedElement.classList.add('drag-enter')
          safeSetTimeout(() => {
            addedElement.classList.remove('drag-enter')
          }, 300)
        }
      }, 50)
      
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, false)
    }
  }
  
  /**
   * 拖拽移除处理（拖出到外部）
   */
  const onDragRemove = (event: Record<string, unknown>) => {
    try {
      const { item, oldIndex, from } = event
      
      console.log('组件移除:', {
        item: item.title || item.name,
        index: oldIndex,
        container: from.id || from.dataset.containerId
      })
      
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, false)
    }
  }
  
  /**
   * 拖拽进入容器
   */
  const onDragEnter = (event: DragEvent, _containerId: string) => {
    const container = event.currentTarget as HTMLElement
    if (container) {
      container.classList.add('drag-over')
    }
  }
  
  /**
   * 拖拽离开容器
   */
  const onDragLeave = (event: DragEvent, _containerId: string) => {
    const container = event.currentTarget as HTMLElement
    if (container) {
      container.classList.remove('drag-over')
    }
  }
  
  /**
   * 获取增强的拖拽配置
   */
  const getEnhancedDragConfig = (customConfig: Partial<DragConfig> = {}): DragConfig => {
    return {
      ...defaultDragConfig,
      ...customConfig,
      onStart: onDragStart,
      onMove: onDragMove,
      onEnd: onDragEnd,
      onAdd: onDragAdd,
      onRemove: onDragRemove
    } as DragConfig & Record<string, unknown>
  }
  
  /**
   * 清理所有拖拽相关资源
   */
  const cleanup = () => {
    cleanupDragPreview()
    cleanupInsertIndicator()
    clearAllTimers()
    
    // 移除全局样式
    document.body.classList.remove('dragging')
    
    // 清理所有拖拽状态类
    const elements = document.querySelectorAll('.drag-chosen, .drag-ghost, .drag-moving, .drag-over')
    elements.forEach(el => {
      el.classList.remove('drag-chosen', 'drag-ghost', 'drag-moving', 'drag-over')
    })
  }
  
  return {
    // 状态
    dragState,
    
    // 配置
    defaultDragConfig,
    getEnhancedDragConfig,
    
    // 事件处理器
    onDragStart,
    onDragMove,
    onDragEnd,
    onDragAdd,
    onDragRemove,
    onDragEnter,
    onDragLeave,
    
    // 工具方法
    createDragPreview,
    showInsertIndicator,
    hideInsertIndicator,
    cleanup
  }
}