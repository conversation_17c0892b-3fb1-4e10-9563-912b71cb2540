/**
 * 缓存管理器
 * 支持多级缓存、LRU淘汰、缓存预热、持久化等
 */

import { shallowRef, computed } from 'vue'
import { useErrorHandler, ErrorCategory } from '@/utils/error-handler'
import { useTimerManager } from '@/composables/useTimerManager'

// 缓存策略
export type CacheStrategy = 'LRU' | 'LFU' | 'FIFO' | 'TTL'

// 缓存配置
export interface CacheConfig {
  strategy: CacheStrategy
  maxSize: number
  defaultTTL: number
  persistence: boolean
  storageKey: string
  compressionThreshold: number
}

// 缓存项元数据
export interface CacheMetadata {
  key: string
  size: number
  accessCount: number
  lastAccessed: number
  createdAt: number
  expiresAt: number
  compressed: boolean
  tags: string[]
}

// 缓存项
interface CacheItem<T = unknown> {
  data: T
  metadata: CacheMetadata
}

// 缓存统计
export interface CacheStats {
  totalItems: number
  totalSize: number
  hitCount: number
  missCount: number
  hitRate: number
  evictionCount: number
  compressionRatio: number
}

// 缓存事件
type CacheEvent = 'hit' | 'miss' | 'set' | 'delete' | 'evict' | 'clear'

type CacheEventListener = (event: CacheEvent, key: string, data?: unknown) => void

/**
 * 缓存管理器组合式API
 */
export function useCacheManager<T = unknown>(config: Partial<CacheConfig> = {}) {
  const { handleError } = useErrorHandler()
  const { safeSetTimeout, clearAllTimers } = useTimerManager()
  
  // 默认配置
  const defaultConfig: CacheConfig = {
    strategy: 'LRU',
    maxSize: 100,
    defaultTTL: 300000, // 5分钟
    persistence: false,
    storageKey: 'app-cache',
    compressionThreshold: 1024 // 1KB
  }
  
  const finalConfig = { ...defaultConfig, ...config }
  
  // 缓存存储
  const cache = shallowRef<Map<string, CacheItem<T>>>(new Map())
  const accessOrder = shallowRef<string[]>([]) // LRU顺序
  const eventListeners = shallowRef<CacheEventListener[]>([])
  
  // 统计信息
  const stats = shallowRef<CacheStats>({
    totalItems: 0,
    totalSize: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    evictionCount: 0,
    compressionRatio: 0
  })
  
  // 计算属性
  const size = computed(() => cache.value.size)
  const isEmpty = computed(() => cache.value.size === 0)
  const isFull = computed(() => cache.value.size >= finalConfig.maxSize)
  const memoryUsage = computed(() => stats.value.totalSize)
  
  /**
   * 触发缓存事件
   */
  function emitEvent(event: CacheEvent, key: string, data?: unknown): void {
    eventListeners.value.forEach(listener => {
      try {
        listener(event, key, data)
      } catch (error) {
        console.warn('缓存事件监听器错误:', error)
      }
    })
  }
  
  /**
   * 计算数据大小
   */
  function calculateSize(data: T): number {
    try {
      return JSON.stringify(data).length
    } catch {
      return 0
    }
  }
  
  /**
   * 压缩数据
   */
  function compressData(data: T): { compressed: boolean; data: string | T } {
    const size = calculateSize(data)
    
    if (size < finalConfig.compressionThreshold) {
      return { compressed: false, data }
    }
    
    try {
      // 简单的JSON压缩（实际项目中可以使用更高效的压缩算法）
      const jsonString = JSON.stringify(data)
      const compressed = btoa(encodeURIComponent(jsonString))
      
      if (compressed.length < jsonString.length) {
        return { compressed: true, data: compressed }
      }
    } catch (error) {
      console.warn('数据压缩失败:', error)
    }
    
    return { compressed: false, data }
  }
  
  /**
   * 解压数据
   */
  function decompressData(compressedData: string): T {
    try {
      const decodedString = decodeURIComponent(atob(compressedData))
      return JSON.parse(decodedString)
    } catch (error) {
      throw new Error('数据解压失败')
    }
  }
  
  /**
   * 创建缓存项
   */
  function createCacheItem(key: string, data: T, ttl?: number, tags: string[] = []): CacheItem<T> {
    const { compressed, data: processedData } = compressData(data)
    const size = calculateSize(processedData as T)
    
    try {
      const now = Date.now()
      return {
        data: processedData as T,
        metadata: {
          key,
          size,
          accessCount: 0,
          lastAccessed: now,
          createdAt: now,
          expiresAt: now + (ttl || finalConfig.defaultTTL),
          compressed,
          tags
        }
      }
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM)
      throw error
    }
  }
  
  /**
   * 更新访问统计
   */
  function updateAccessStats(item: CacheItem<T>): void {
    item.metadata.accessCount++
    item.metadata.lastAccessed = Date.now()
    
    // 更新LRU顺序
    const index = accessOrder.value.indexOf(item.metadata.key)
    if (index > -1) {
      accessOrder.value.splice(index, 1)
    }
    accessOrder.value.push(item.metadata.key)
  }
  
  /**
   * 检查缓存项是否过期
   */
  function isExpired(item: CacheItem<T>): boolean {
    return Date.now() > item.metadata.expiresAt
  }
  
  /**
   * 淘汰策略
   */
  function selectVictim(): string | null {
    if (cache.value.size === 0) return null
    
    switch (finalConfig.strategy) {
      case 'LRU':
        return accessOrder.value[0] || null
        
      case 'LFU':
        let minAccess = Infinity
        let victim = null
        for (const [key, item] of cache.value.entries()) {
          if (item.metadata.accessCount < minAccess) {
            minAccess = item.metadata.accessCount
            victim = key
          }
        }
        return victim
        
      case 'FIFO':
        let oldest = Infinity
        let oldestKey = null
        for (const [key, item] of cache.value.entries()) {
          if (item.metadata.createdAt < oldest) {
            oldest = item.metadata.createdAt
            oldestKey = key
          }
        }
        return oldestKey
        
      case 'TTL':
        // 找到最快过期的项
        let earliestExpire = Infinity
        let expireKey = null
        for (const [key, item] of cache.value.entries()) {
          if (item.metadata.expiresAt < earliestExpire) {
            earliestExpire = item.metadata.expiresAt
            expireKey = key
          }
        }
        return expireKey
        
      default:
        return accessOrder.value[0] || null
    }
  }
  
  /**
   * 淘汰缓存项
   */
  function evictItems(count: number = 1): void {
    for (let i = 0; i < count && cache.value.size > 0; i++) {
      const victimKey = selectVictim()
      if (!victimKey) break
      
      const item = cache.value.get(victimKey)
      if (item) {
        cache.value.delete(victimKey)
        
        // 更新访问顺序
        const index = accessOrder.value.indexOf(victimKey)
        if (index > -1) {
          accessOrder.value.splice(index, 1)
        }
        
        // 更新统计
        stats.value.evictionCount++
        updateStats()
        
        emitEvent('evict', victimKey, item)
      }
    }
  }
  
  /**
   * 清理过期项
   */
  function cleanupExpired(): void {
    const expiredKeys: string[] = []
    
    for (const [key, item] of cache.value.entries()) {
      if (isExpired(item)) {
        expiredKeys.push(key)
      }
    }
    
    expiredKeys.forEach(key => {
      const item = cache.value.get(key)
      cache.value.delete(key)
      
      const index = accessOrder.value.indexOf(key)
      if (index > -1) {
        accessOrder.value.splice(index, 1)
      }
      
      emitEvent('delete', key, item)
    })
    
    if (expiredKeys.length > 0) {
      updateStats()
    }
  }
  
  /**
   * 更新统计信息
   */
  function updateStats(): void {
    let totalSize = 0
    let compressedItems = 0
    
    for (const item of cache.value.values()) {
      totalSize += item.metadata.size
      if (item.metadata.compressed) {
        compressedItems++
      }
    }
    
    stats.value.totalItems = cache.value.size
    stats.value.totalSize = totalSize
    stats.value.hitRate = stats.value.hitCount + stats.value.missCount > 0
      ? stats.value.hitCount / (stats.value.hitCount + stats.value.missCount)
      : 0
    stats.value.compressionRatio = cache.value.size > 0
      ? compressedItems / cache.value.size
      : 0
  }
  
  /**
   * 设置缓存
   */
  function set(key: string, data: T, ttl?: number, tags: string[] = []): boolean {
    try {
      // 清理过期项
      cleanupExpired()
      
      // 如果缓存已满且不包含该key，进行淘汰
      if (isFull.value && !cache.value.has(key)) {
        evictItems(1)
      }
      
      const item = createCacheItem(key, data, ttl, tags)
      cache.value.set(key, item)
      
      // 更新访问顺序
      const index = accessOrder.value.indexOf(key)
      if (index > -1) {
        accessOrder.value.splice(index, 1)
      }
      accessOrder.value.push(key)
      
      updateStats()
      emitEvent('set', key, data)
      
      // 持久化
      if (finalConfig.persistence) {
        persistToStorage()
      }
      
      return true
      
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM)
      return false
    }
  }
  
  /**
   * 获取缓存
   */
  function get(key: string): T | null {
    // 清理过期项
    cleanupExpired()
    
    const item = cache.value.get(key)
    
    if (!item) {
      stats.value.missCount++
      updateStats()
      emitEvent('miss', key)
      return null
    }
    
    if (isExpired(item)) {
      cache.value.delete(key)
      const index = accessOrder.value.indexOf(key)
      if (index > -1) {
        accessOrder.value.splice(index, 1)
      }
      
      stats.value.missCount++
      updateStats()
      emitEvent('miss', key)
      return null
    }
    
    // 更新访问统计
    updateAccessStats(item)
    
    stats.value.hitCount++
    updateStats()
    emitEvent('hit', key, item.data)
    
    // 解压数据
    if (item.metadata.compressed) {
      return decompressData(item.data as string)
    }
    
    return item.data
  }
  
  /**
   * 检查缓存是否存在
   */
  function has(key: string): boolean {
    const item = cache.value.get(key)
    return item ? !isExpired(item) : false
  }
  
  /**
   * 删除缓存
   */
  function remove(key: string): boolean {
    const item = cache.value.get(key)
    if (!item) return false
    
    cache.value.delete(key)
    
    const index = accessOrder.value.indexOf(key)
    if (index > -1) {
      accessOrder.value.splice(index, 1)
    }
    
    updateStats()
    emitEvent('delete', key, item)
    
    if (finalConfig.persistence) {
      persistToStorage()
    }
    
    return true
  }
  
  /**
   * 清空缓存
   */
  function clear(): void {
    cache.value.clear()
    accessOrder.value = []
    
    stats.value = {
      totalItems: 0,
      totalSize: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      evictionCount: 0,
      compressionRatio: 0
    }
    
    emitEvent('clear', '')
    
    if (finalConfig.persistence) {
      localStorage.removeItem(finalConfig.storageKey)
    }
  }
  
  /**
   * 按标签删除缓存
   */
  function removeByTag(tag: string): number {
    let removedCount = 0
    const keysToRemove: string[] = []
    
    for (const [key, item] of cache.value.entries()) {
      if (item.metadata.tags.includes(tag)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      if (remove(key)) {
        removedCount++
      }
    })
    
    return removedCount
  }
  
  /**
   * 批量设置
   */
  function setMany(items: Record<string, T>, ttl?: number, tags: string[] = []): void {
    Object.entries(items).forEach(([key, value]) => {
      set(key, value, ttl, tags)
    })
  }
  
  /**
   * 批量获取
   */
  function getMany(keys: string[]): Record<string, T | null> {
    const result: Record<string, T | null> = {}
    keys.forEach(key => {
      result[key] = get(key)
    })
    return result
  }
  
  /**
   * 预热缓存
   */
  async function warmup(loader: (key: string) => Promise<T>, keys: string[]): Promise<void> {
    const promises = keys.map(async key => {
      try {
        if (!has(key)) {
          const data = await loader(key)
          set(key, data)
        }
      } catch (error) {
        console.warn(`缓存预热失败: ${key}`, error)
      }
    })
    
    await Promise.allSettled(promises)
  }
  
  /**
   * 持久化到本地存储
   */
  function persistToStorage(): void {
    if (!finalConfig.persistence) return
    
    try {
      const data = {
        cache: Array.from(cache.value.entries()),
        accessOrder: accessOrder.value,
        stats: stats.value,
        timestamp: Date.now()
      }
      
      localStorage.setItem(finalConfig.storageKey, JSON.stringify(data))
    } catch (error) {
      console.warn('缓存持久化失败:', error)
    }
  }
  
  /**
   * 从本地存储恢复
   */
  function restoreFromStorage(): void {
    if (!finalConfig.persistence) return
    
    try {
      const stored = localStorage.getItem(finalConfig.storageKey)
      if (!stored) return
      
      const data = JSON.parse(stored)
      
      // 恢复缓存
      cache.value = new Map(data.cache)
      accessOrder.value = data.accessOrder || []
      
      // 清理过期项
      cleanupExpired()
      
      updateStats()
      
      console.log(`从存储恢复缓存，共 ${cache.value.size} 项`)
      
    } catch (error) {
      console.warn('缓存恢复失败:', error)
      clear()
    }
  }
  
  /**
   * 添加事件监听器
   */
  function addEventListener(listener: CacheEventListener): () => void {
    eventListeners.value.push(listener)
    
    return () => {
      const index = eventListeners.value.indexOf(listener)
      if (index > -1) {
        eventListeners.value.splice(index, 1)
      }
    }
  }
  
  /**
   * 获取缓存键列表
   */
  function keys(): string[] {
    return Array.from(cache.value.keys())
  }
  
  /**
   * 获取缓存值列表
   */
  function values(): T[] {
    return Array.from(cache.value.values()).map(item => {
      if (item.metadata.compressed) {
        return decompressData(item.data as string)
      }
      return item.data
    })
  }
  
  /**
   * 获取缓存项元数据
   */
  function getMetadata(key: string): CacheMetadata | null {
    const item = cache.value.get(key)
    return item ? { ...item.metadata } : null
  }
  
  /**
   * 设置TTL
   */
  function setTTL(key: string, ttl: number): boolean {
    const item = cache.value.get(key)
    if (!item) return false
    
    item.metadata.expiresAt = Date.now() + ttl
    return true
  }
  
  /**
   * 获取剩余TTL
   */
  function getTTL(key: string): number {
    const item = cache.value.get(key)
    if (!item) return -1
    
    const remaining = item.metadata.expiresAt - Date.now()
    return Math.max(0, remaining)
  }
  
  // 定期清理过期项
  safeSetTimeout(() => {
    cleanupExpired()
  }, 60000) // 每分钟清理一次
  
  // 初始化时恢复持久化数据
  if (finalConfig.persistence) {
    restoreFromStorage()
  }
  
  return {
    // 状态
    cache,
    stats,
    size,
    isEmpty,
    isFull,
    memoryUsage,
    
    // 基础操作
    set,
    get,
    has,
    remove,
    clear,
    
    // 批量操作
    setMany,
    getMany,
    removeByTag,
    
    // 元数据
    getMetadata,
    setTTL,
    getTTL,
    
    // 工具方法
    keys,
    values,
    warmup,
    cleanupExpired,
    
    // 事件
    addEventListener,
    
    // 持久化
    persistToStorage,
    restoreFromStorage,
    
    // 配置
    config: finalConfig,
    
    // 清理
    cleanup: () => {
      clearAllTimers()
      if (finalConfig.persistence) {
        persistToStorage()
      }
    }
  }
}

/**
 * 多级缓存管理器
 */
export function useMultiLevelCache() {
  const memoryCache = useCacheManager({ strategy: 'LRU', maxSize: 50 })
  const diskCache = useCacheManager({ 
    strategy: 'TTL', 
    maxSize: 200, 
    persistence: true,
    storageKey: 'disk-cache'
  })
  
  async function get<T>(key: string): Promise<T | null> {
    // 先查内存缓存
    let data = memoryCache.get(key)
    if (data) {
      return data as T
    }
    
    // 再查磁盘缓存
    data = diskCache.get(key)
    if (data) {
      // 提升到内存缓存
      memoryCache.set(key, data)
      return data as T
    }
    
    return null
  }
  
  function set<T>(key: string, data: T, ttl?: number): void {
    memoryCache.set(key, data, ttl)
    diskCache.set(key, data, ttl)
  }
  
  function remove(key: string): void {
    memoryCache.remove(key)
    diskCache.remove(key)
  }
  
  function clear(): void {
    memoryCache.clear()
    diskCache.clear()
  }
  
  return {
    get,
    set,
    remove,
    clear,
    memoryCache,
    diskCache,
    cleanup: () => {
      memoryCache.cleanup()
      diskCache.cleanup()
    }
  }
}