/**
 * 统一的 Message 使用 Composable
 * 采用思维链模式，提供一致的消息提示体验
 * 
 * 使用方式：
 * import { useMessage } from '@/composables/useMessage'
 * const message = useMessage()
 * 
 * 或者使用解构：
 * import { useMessage } from '@/composables/useMessage'
 * const { success, error, warning, info, loading } = useMessage()
 */

import { useMessage as useNaiveMessage, type MessageApi, type MessageOptions, type MessageReactive } from 'naive-ui'
import { ref, type Ref } from 'vue'
import type { VNodeChild } from 'vue'

// 消息类型枚举
export enum MessageType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  LOADING = 'loading'
}

/**
 * 扩展的消息选项接口
 * 在 Naive UI 原有选项基础上添加自定义功能
 */
export interface CustomMessageOptions extends Omit<MessageOptions, 'icon'> {
  /** 消息分组，用于批量管理相关消息 */
  group?: string
  /** 消息图标（字符串形式或渲染函数） */
  icon?: string | (() => VNodeChild)
  /** 消息优先级，影响显示顺序 */
  priority?: 'low' | 'normal' | 'high'
  /** 是否在页面切换时保持显示 */
  persistent?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 消息标签，用于标识和查找 */
  tag?: string
}

/**
 * 消息实例接口
 * 扩展 Naive UI 的 MessageReactive 接口
 */
export interface CustomMessageInstance extends MessageReactive {
  /** 消息唯一标识 */
  id: string
  /** 消息类型 */
  type: 'success' | 'error' | 'warning' | 'info' | 'loading'
  /** 消息内容 */
  content: string
  /** 创建时间戳 */
  timestamp: number
  /** 消息分组 */
  group?: string
  /** 消息标签 */
  tag?: string
  /** 销毁消息的方法 */
  destroy: () => void
}

/**
 * 消息管理器类
 * 实现思维链模式的消息管理，提供统一的消息接口
 */
class MessageManager {
  private naiveMessage: MessageApi
  private messageQueue: Ref<CustomMessageInstance[]> = ref([])
  private maxQueueSize = 5

  constructor(naiveMessage: MessageApi) {
    this.naiveMessage = naiveMessage
  }

  /**
   * 显示成功消息
   * 用于操作成功的反馈
   */
  success(content: string, options?: CustomMessageOptions): CustomMessageInstance {
    return this.createMessage(MessageType.SUCCESS, content, {
      duration: 3000,
      ...options
    })
  }

  /**
   * 显示错误消息
   * 用于错误信息的反馈
   */
  error(content: string, options?: CustomMessageOptions): CustomMessageInstance {
    return this.createMessage(MessageType.ERROR, content, {
      duration: 5000,
      closable: true,
      ...options
    })
  }

  /**
   * 显示警告消息
   * 用于警告信息的反馈
   */
  warning(content: string, options?: CustomMessageOptions): CustomMessageInstance {
    return this.createMessage(MessageType.WARNING, content, {
      duration: 4000,
      ...options
    })
  }

  /**
   * 显示信息消息
   * 用于一般信息的反馈
   */
  info(content: string, options?: CustomMessageOptions): CustomMessageInstance {
    return this.createMessage(MessageType.INFO, content, {
      duration: 3000,
      ...options
    })
  }

  /**
   * 显示加载消息
   * 用于长时间操作的反馈
   */
  loading(content: string, options?: CustomMessageOptions): CustomMessageInstance {
    return this.createMessage(MessageType.LOADING, content, {
      duration: 0, // 加载消息不自动消失
      ...options
    })
  }

  /**
   * 创建消息实例
   */
  private createMessage(type: MessageType, content: string, options: CustomMessageOptions = {}): CustomMessageInstance {
    // 清理过期消息
    this.cleanupQueue()

    // 处理选项，移除自定义字段
    const { group, priority, persistent, className, tag, ...naiveOptions } = options
    
    // 创建 Naive UI 消息
    const naiveInstance = this.naiveMessage[type](content, naiveOptions)

    // 创建我们的消息实例
    const messageInstance: CustomMessageInstance = {
      ...naiveInstance,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: type as any,
      content,
      timestamp: Date.now(),
      group,
      tag,
      destroy: () => {
        naiveInstance.destroy()
        this.removeFromQueue(messageInstance)
      }
    }

    // 添加到队列
    this.addToQueue(messageInstance)

    return messageInstance
  }

  /**
   * 添加消息到队列
   */
  private addToQueue(message: CustomMessageInstance) {
    this.messageQueue.value.push(message)
    
    // 如果队列超过最大长度，移除最旧的消息
    if (this.messageQueue.value.length > this.maxQueueSize) {
      const oldestMessage = this.messageQueue.value.shift()
      if (oldestMessage) {
        oldestMessage.destroy()
      }
    }
  }

  /**
   * 从队列中移除消息
   */
  private removeFromQueue(message: CustomMessageInstance) {
    const index = this.messageQueue.value.indexOf(message)
    if (index > -1) {
      this.messageQueue.value.splice(index, 1)
    }
  }

  /**
   * 清理过期消息
   */
  private cleanupQueue() {
    const now = Date.now()
    this.messageQueue.value = this.messageQueue.value.filter(message => {
      // 保留最近5分钟内的消息
      return now - message.timestamp < 5 * 60 * 1000
    })
  }

  /**
   * 销毁指定分组的所有消息
   */
  destroyGroup(group: string) {
    this.messageQueue.value
      .filter(message => message.group === group)
      .forEach(message => message.destroy())
  }

  /**
   * 销毁所有消息
   */
  destroyAll() {
    this.messageQueue.value.forEach(message => message.destroy())
    this.messageQueue.value = []
  }

  /**
   * 获取当前消息队列
   */
  getQueue(): CustomMessageInstance[] {
    return [...this.messageQueue.value]
  }

  /**
   * 根据标签查找消息
   */
  findByTag(tag: string): CustomMessageInstance[] {
    return this.messageQueue.value.filter(message => message.tag === tag)
  }

  /**
   * 根据分组查找消息
   */
  findByGroup(group: string): CustomMessageInstance[] {
    return this.messageQueue.value.filter(message => message.group === group)
  }

  /**
   * 获取原始 Naive UI Message 实例
   * 用于需要直接访问原始 API 的场景
   */
  get $naive(): MessageApi {
    return this.naiveMessage
  }
}

// 全局消息管理器实例
let globalMessageManager: MessageManager | null = null

/**
 * 统一的 useMessage Hook
 * 提供思维链模式的消息管理
 */
export function useMessage() {
  const naiveMessage = useNaiveMessage()

  // 如果没有全局实例，创建一个
  if (!globalMessageManager) {
    globalMessageManager = new MessageManager(naiveMessage)
  }

  // 返回消息管理器的方法
  return {
    // 基础消息方法
    success: globalMessageManager.success.bind(globalMessageManager),
    error: globalMessageManager.error.bind(globalMessageManager),
    warning: globalMessageManager.warning.bind(globalMessageManager),
    info: globalMessageManager.info.bind(globalMessageManager),
    loading: globalMessageManager.loading.bind(globalMessageManager),

    // 管理方法
    destroyAll: globalMessageManager.destroyAll.bind(globalMessageManager),
    destroyGroup: globalMessageManager.destroyGroup.bind(globalMessageManager),
    getQueue: globalMessageManager.getQueue.bind(globalMessageManager),

    // 原始 Naive UI 实例（用于特殊情况）
    $naive: naiveMessage
  }
}

// 导出类型
export type { MessageApi, MessageOptions, CustomMessageInstance, CustomMessageOptions }
export { MessageManager }

// 默认导出
export default useMessage