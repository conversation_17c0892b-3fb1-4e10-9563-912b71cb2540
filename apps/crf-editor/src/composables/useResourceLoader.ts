/**
 * 资源加载器
 * 支持图片预加载、脚本加载、样式加载、字体加载等
 */

import { ref, shallowRef, computed, type Ref, type ComputedRef } from 'vue'
import { useNetworkManager } from '@crf/network'
import { useCacheManager } from '@/composables/useCacheManager'
import { useErrorHandler, ErrorCategory } from '@/utils/error-handler'
import { useTimerManager } from '@/composables/useTimerManager'

// 资源类型
export type ResourceType = 'image' | 'script' | 'style' | 'font' | 'json' | 'xml' | 'text'

// 加载优先级
export type LoadPriority = 'high' | 'medium' | 'low'

// 资源配置
export interface ResourceConfig {
  url: string
  type: ResourceType
  priority: LoadPriority
  crossOrigin?: string
  integrity?: string
  timeout?: number
  retries?: number
  cache?: boolean
  preload?: boolean
}

// 加载状态
export interface LoadState {
  loading: boolean
  loaded: boolean
  error: Error | null
  progress: number
  size: number
  startTime: number
  endTime?: number
}

// 加载结果
export interface LoadResult<T = unknown> {
  data: T
  url: string
  type: ResourceType
  size: number
  duration: number
  fromCache: boolean
}

// 资源组
export interface ResourceGroup {
  name: string
  resources: ResourceConfig[]
  parallel: boolean
  required: boolean
}

// 资源加载器返回类型
export interface ResourceLoaderReturn {
  // 状态
  isLoading: ComputedRef<boolean>
  activeLoadCount: ComputedRef<number>
  queueLength: ComputedRef<number>
  stats: Ref<{
    totalLoaded: number
    totalSize: number
    averageLoadTime: number
    cacheHits: number
    loadFailures: number
  }>

  // 加载方法
  loadResource: (config: ResourceConfig) => Promise<LoadResult>
  loadImage: (url: string, config?: Partial<ResourceConfig>) => Promise<LoadResult<HTMLImageElement>>
  loadScript: (url: string, config?: Partial<ResourceConfig>) => Promise<LoadResult<HTMLScriptElement>>
  loadStyle: (url: string, config?: Partial<ResourceConfig>) => Promise<LoadResult<HTMLLinkElement>>
  loadFont: (url: string, config?: Partial<ResourceConfig>) => Promise<LoadResult<FontFace>>
  loadJSON: <T = unknown>(url: string, config?: Partial<ResourceConfig>) => Promise<LoadResult<T>>

  // 批量加载
  loadResources: (configs: ResourceConfig[]) => Promise<LoadResult[]>
  loadResourceGroup: (group: ResourceGroup) => Promise<LoadResult[]>
  preloadResources: (configs: ResourceConfig[]) => Promise<void>

  // 队列管理
  queueResource: (config: ResourceConfig) => void
  cancelLoad: (url: string) => void
  cancelAllLoads: () => void

  // 状态查询
  getLoadState: (url: string) => LoadState | null

  // 缓存管理
  cache: ReturnType<typeof useCacheManager>
  clearCache: () => void

  // 配置
  maxConcurrent: Ref<number>

  // 清理
  cleanup: () => void
}

/**
 * 资源加载器组合式API
 */
export function useResourceLoader(): ResourceLoaderReturn {
  const { get } = useNetworkManager()
  const cache = useCacheManager<unknown>({
    strategy: 'LRU',
    maxSize: 100,
    defaultTTL: 3600000, // 1小时
    persistence: true,
    storageKey: 'resource-cache'
  })
  const { handleError } = useErrorHandler()
  const { safeSetTimeout } = useTimerManager()

  // 加载队列
  const loadQueue = shallowRef<ResourceConfig[]>([])
  const activeLoads = shallowRef<Map<string, LoadState>>(new Map())
  const maxConcurrent = ref(6)

  // 统计信息
  const stats = shallowRef({
    totalLoaded: 0,
    totalSize: 0,
    averageLoadTime: 0,
    cacheHits: 0,
    loadFailures: 0
  })

  // 计算属性
  const isLoading = computed(() => activeLoads.value.size > 0 || loadQueue.value.length > 0)
  const activeLoadCount = computed(() => activeLoads.value.size)
  const queueLength = computed(() => loadQueue.value.length)

  /**
   * 创建加载状态
   */
  function createLoadState(): LoadState {
    return {
      loading: false,
      loaded: false,
      error: null,
      progress: 0,
      size: 0,
      startTime: Date.now()
    }
  }

  /**
   * 生成缓存键
   */
  function getCacheKey(config: ResourceConfig): string {
    return `${config.type}:${config.url}`
  }

  /**
   * 加载图片
   */
  async function loadImage(url: string, config: Partial<ResourceConfig> = {}): Promise<LoadResult<HTMLImageElement>> {
    const cacheKey = getCacheKey({ url, type: 'image', ...config } as ResourceConfig)

    // 检查缓存
    if (config.cache !== false) {
      const cached = cache.get(cacheKey)
      if (cached) {
        stats.value.cacheHits++
        return {
          data: cached,
          url,
          type: 'image',
          size: 0,
          duration: 0,
          fromCache: true
        }
      }
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      const startTime = Date.now()

      // 设置跨域
      if (config.crossOrigin) {
        img.crossOrigin = config.crossOrigin
      }

      // 超时处理
      const timeoutId = config.timeout ? safeSetTimeout(() => {
        reject(new Error(`图片加载超时: ${url}`))
      }, config.timeout) : null

      img.onload = () => {
        if (timeoutId) clearTimeout(timeoutId)

        const duration = Date.now() - startTime
        const size = img.naturalWidth * img.naturalHeight * 4 // 估算大小

        // 缓存图片
        if (config.cache !== false) {
          cache.set(cacheKey, img)
        }

        // 更新统计
        updateStats(size, duration)

        resolve({
          data: img,
          url,
          type: 'image',
          size,
          duration,
          fromCache: false
        })
      }

      img.onerror = () => {
        if (timeoutId) clearTimeout(timeoutId)
        stats.value.loadFailures++
        reject(new Error(`图片加载失败: ${url}`))
      }

      img.src = url
    })
  }

  /**
   * 加载脚本
   */
  async function loadScript(url: string, config: Partial<ResourceConfig> = {}): Promise<LoadResult<HTMLScriptElement>> {
    // 检查是否已经加载
    const existingScript = document.querySelector(`script[src=\"${url}\"]`)
    if (existingScript) {
      return {
        data: existingScript as HTMLScriptElement,
        url,
        type: 'script',
        size: 0,
        duration: 0,
        fromCache: true
      }
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      const startTime = Date.now()

      script.src = url
      script.async = true

      if (config.crossOrigin) {
        script.crossOrigin = config.crossOrigin
      }

      if (config.integrity) {
        script.integrity = config.integrity
      }

      // 超时处理
      const timeoutId = config.timeout ? safeSetTimeout(() => {
        document.head.removeChild(script)
        reject(new Error(`脚本加载超时: ${url}`))
      }, config.timeout) : null

      script.onload = () => {
        if (timeoutId) clearTimeout(timeoutId)

        const duration = Date.now() - startTime
        updateStats(0, duration) // 脚本大小难以准确测量

        resolve({
          data: script,
          url,
          type: 'script',
          size: 0,
          duration,
          fromCache: false
        })
      }

      script.onerror = () => {
        if (timeoutId) clearTimeout(timeoutId)
        document.head.removeChild(script)
        stats.value.loadFailures++
        reject(new Error(`脚本加载失败: ${url}`))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 加载样式
   */
  async function loadStyle(url: string, config: Partial<ResourceConfig> = {}): Promise<LoadResult<HTMLLinkElement>> {
    // 检查是否已经加载
    const existingLink = document.querySelector(`link[href=\"${url}\"]`)
    if (existingLink) {
      return {
        data: existingLink as HTMLLinkElement,
        url,
        type: 'style',
        size: 0,
        duration: 0,
        fromCache: true
      }
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      const startTime = Date.now()

      link.rel = 'stylesheet'
      link.href = url

      if (config.crossOrigin) {
        link.crossOrigin = config.crossOrigin
      }

      if (config.integrity) {
        link.integrity = config.integrity
      }

      // 超时处理
      const timeoutId = config.timeout ? safeSetTimeout(() => {
        document.head.removeChild(link)
        reject(new Error(`样式加载超时: ${url}`))
      }, config.timeout) : null

      link.onload = () => {
        if (timeoutId) clearTimeout(timeoutId)

        const duration = Date.now() - startTime
        updateStats(0, duration)

        resolve({
          data: link,
          url,
          type: 'style',
          size: 0,
          duration,
          fromCache: false
        })
      }

      link.onerror = () => {
        if (timeoutId) clearTimeout(timeoutId)
        document.head.removeChild(link)
        stats.value.loadFailures++
        reject(new Error(`样式加载失败: ${url}`))
      }

      document.head.appendChild(link)
    })
  }

  /**
   * 加载字体
   */
  async function loadFont(url: string, config: Partial<ResourceConfig> = {}): Promise<LoadResult<FontFace>> {
    const cacheKey = getCacheKey({ url, type: 'font', ...config } as ResourceConfig)

    // 检查缓存
    if (config.cache !== false) {
      const cached = cache.get(cacheKey)
      if (cached) {
        stats.value.cacheHits++
        return {
          data: cached,
          url,
          type: 'font',
          size: 0,
          duration: 0,
          fromCache: true
        }
      }
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now()

      const fontFace = new FontFace('custom-font', `url(${url})`)

      fontFace.load().then(() => {
        const duration = Date.now() - startTime

        // 将字体添加到文档
        document.fonts.add(fontFace)

        // 缓存字体
        if (config.cache !== false) {
          cache.set(cacheKey, fontFace)
        }

        updateStats(0, duration)

        resolve({
          data: fontFace,
          url,
          type: 'font',
          size: 0,
          duration,
          fromCache: false
        })
      }).catch(error => {
        stats.value.loadFailures++
        reject(new Error(`字体加载失败: ${url} - ${error.message}`))
      })
    })
  }

  /**
   * 加载JSON数据
   */
  async function loadJSON<T = unknown>(url: string, config: Partial<ResourceConfig> = {}): Promise<LoadResult<T>> {
    const cacheKey = getCacheKey({ url, type: 'json', ...config } as ResourceConfig)

    // 检查缓存
    if (config.cache !== false) {
      const cached = cache.get(cacheKey)
      if (cached) {
        stats.value.cacheHits++
        return {
          data: cached,
          url,
          type: 'json',
          size: JSON.stringify(cached).length,
          duration: 0,
          fromCache: true
        }
      }
    }

    try {
      const startTime = Date.now()
      const result = await get<T>(url, {
        ...(config.timeout !== undefined && { timeout: config.timeout }),
        ...(config.retries !== undefined && { retries: config.retries }),
        ...(config.cache !== undefined && { enableCache: config.cache })
      })

      const duration = Date.now() - startTime
      const size = JSON.stringify(result.data).length

      // 缓存数据
      if (config.cache !== false && !result.fromCache) {
        cache.set(cacheKey, result.data)
      }

      updateStats(size, duration)

      return {
        data: result.data,
        url,
        type: 'json',
        size,
        duration,
        fromCache: result.fromCache
      }
    } catch (error) {
      stats.value.loadFailures++
      throw new Error(`JSON加载失败: ${url} - ${(error as Error).message}`)
    }
  }

  /**
   * 通用资源加载
   */
  async function loadResource<T = unknown>(config: ResourceConfig): Promise<LoadResult<T>> {
    const state = createLoadState()
    activeLoads.value.set(config.url, state)

    try {
      state.loading = true
      state.startTime = Date.now()

      let result: LoadResult<T>

      switch (config.type) {
        case 'image':
          result = await loadImage(config.url, config) as LoadResult<T>
          break
        case 'script':
          result = await loadScript(config.url, config) as LoadResult<T>
          break
        case 'style':
          result = await loadStyle(config.url, config) as LoadResult<T>
          break
        case 'font':
          result = await loadFont(config.url, config) as LoadResult<T>
          break
        case 'json':
          result = await loadJSON<T>(config.url, config)
          break
        default:
          throw new Error(`不支持的资源类型: ${config.type}`)
      }

      state.loaded = true
      state.endTime = Date.now()

      return result

    } catch (error) {
      state.error = error as Error
      handleError(error as Error, undefined, ErrorCategory.NETWORK)
      throw error
    } finally {
      state.loading = false
      activeLoads.value.delete(config.url)
      processQueue()
    }
  }

  /**
   * 批量加载资源
   */
  async function loadResources(configs: ResourceConfig[], parallel = true): Promise<LoadResult[]> {
    if (parallel) {
      const promises = configs.map(config => loadResource(config))
      return Promise.allSettled(promises).then(results =>
        results.map((result, index) => {
          if (result.status === 'fulfilled') {
            return result.value
          } else {
            console.error(`资源加载失败: ${configs[index]?.url}`, result.reason)
            throw result.reason
          }
        })
      )
    } else {
      const results: LoadResult[] = []
      for (const config of configs) {
        try {
          const result = await loadResource(config)
          results.push(result)
        } catch (error) {
          console.error(`资源加载失败: ${config.url}`, error)
          if ((config as Record<string, unknown>).required !== false) {
            throw error
          }
        }
      }
      return results
    }
  }

  /**
   * 加载资源组
   */
  async function loadResourceGroup(group: ResourceGroup): Promise<LoadResult[]> {
    console.log(`开始加载资源组: ${group.name}`)

    try {
      const results = await loadResources(group.resources, group.parallel)
      console.log(`资源组加载完成: ${group.name}`)
      return results
    } catch (error) {
      if (group.required) {
        console.error(`必需资源组加载失败: ${group.name}`, error)
        throw error
      } else {
        console.warn(`可选资源组加载失败: ${group.name}`, error)
        return []
      }
    }
  }

  /**
   * 预加载资源
   */
  async function preloadResources(configs: ResourceConfig[]): Promise<void> {
    try {
      await loadResources(configs, true)
      console.log(`预加载完成: ${configs.length} 个资源`)
    } catch (error) {
      console.warn('预加载部分失败:', error)
    }
  }

  /**
   * 处理加载队列
   */
  function processQueue(): void {
    while (loadQueue.value.length > 0 && activeLoadCount.value < maxConcurrent.value) {
      const config = loadQueue.value.shift()!
      loadResource(config).catch(error => {
        console.error('队列中的资源加载失败:', config.url, error)
      })
    }
  }

  /**
   * 添加到加载队列
   */
  function queueResource(config: ResourceConfig): void {
    if (activeLoadCount.value < maxConcurrent.value) {
      loadResource(config).catch(error => {
        console.error('资源加载失败:', config.url, error)
      })
    } else {
      // 按优先级排序插入队列
      const priorityOrder = { high: 0, medium: 1, low: 2 }
      const insertIndex = loadQueue.value.findIndex(
        item => priorityOrder[item.priority] > priorityOrder[config.priority]
      )

      if (insertIndex === -1) {
        loadQueue.value.push(config)
      } else {
        loadQueue.value.splice(insertIndex, 0, config)
      }
    }
  }

  /**
   * 取消加载
   */
  function cancelLoad(url: string): void {
    const state = activeLoads.value.get(url)
    if (state) {
      state.loading = false
      state.error = new Error('加载被取消')
      activeLoads.value.delete(url)
    }

    // 从队列中移除
    const queueIndex = loadQueue.value.findIndex(config => config.url === url)
    if (queueIndex > -1) {
      loadQueue.value.splice(queueIndex, 1)
    }
  }

  /**
   * 取消所有加载
   */
  function cancelAllLoads(): void {
    activeLoads.value.clear()
    loadQueue.value = []
  }

  /**
   * 更新统计信息
   */
  function updateStats(size: number, duration: number): void {
    stats.value.totalLoaded++
    stats.value.totalSize += size
    stats.value.averageLoadTime =
      (stats.value.averageLoadTime * (stats.value.totalLoaded - 1) + duration) /
      stats.value.totalLoaded
  }

  /**
   * 获取加载状态
   */
  function getLoadState(url: string): LoadState | null {
    return activeLoads.value.get(url) || null
  }

  /**
   * 清理缓存
   */
  function clearCache(): void {
    cache.clear()
  }

  return {
    // 状态
    isLoading,
    activeLoadCount,
    queueLength,
    stats,

    // 加载方法
    loadResource,
    loadImage,
    loadScript,
    loadStyle,
    loadFont,
    loadJSON,

    // 批量加载
    loadResources,
    loadResourceGroup,
    preloadResources,

    // 队列管理
    queueResource,
    cancelLoad,
    cancelAllLoads,

    // 状态查询
    getLoadState,

    // 缓存管理
    cache,
    clearCache,

    // 配置
    maxConcurrent,

    // 清理
    cleanup: () => {
      cancelAllLoads()
      cache.cleanup()
    }
  }
}

/**
 * 图片懒加载组合式API
 */
export function useImageLazyLoading() {
  const { loadImage } = useResourceLoader()
  const observer = ref<IntersectionObserver | null>(null)
  const loadedImages = shallowRef<Set<string>>(new Set())

  /**
   * 初始化懒加载观察器
   */
  function initObserver(options: IntersectionObserverInit = {}) {
    observer.value = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src

          if (src && !loadedImages.value.has(src)) {
            loadImage(src, { cache: true })
              .then(_result => {
                img.src = src
                img.classList.add('loaded')
                loadedImages.value.add(src)
                observer.value?.unobserve(img)
              })
              .catch(error => {
                img.classList.add('error')
                console.error('懒加载图片失败:', src, error)
              })
          }
        }
      })
    }, {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    })
  }

  /**
   * 观察图片元素
   */
  function observe(img: HTMLImageElement): void {
    if (!observer.value) {
      initObserver()
    }
    observer.value?.observe(img)
  }

  /**
   * 停止观察
   */
  function unobserve(img: HTMLImageElement): void {
    observer.value?.unobserve(img)
  }

  /**
   * 销毁观察器
   */
  function disconnect(): void {
    observer.value?.disconnect()
    observer.value = null
  }

  return {
    observe,
    unobserve,
    disconnect,
    loadedImages,
    initObserver
  }
}