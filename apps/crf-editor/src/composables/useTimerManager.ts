import { ref, onUnmounted, computed } from 'vue'

/**
 * 定时器管理组合式API
 * 自动清理定时器，防止内存泄漏
 */
export function useTimerManager() {
  const timers = ref<Set<number>>(new Set())
  const intervals = ref<Set<number>>(new Set())
  
  /**
   * 安全的 setTimeout
   */
  const safeSetTimeout = (callback: () => void, delay: number): number => {
    const id = window.setTimeout(() => {
      callback()
      timers.value.delete(id)
    }, delay)
    
    timers.value.add(id)
    return id
  }
  
  /**
   * 安全的 setInterval
   */
  const safeSetInterval = (callback: () => void, delay: number): number => {
    const id = window.setInterval(callback, delay)
    intervals.value.add(id)
    return id
  }
  
  /**
   * 清除特定定时器
   */
  const clearSafeTimeout = (id: number): void => {
    if (timers.value.has(id)) {
      window.clearTimeout(id)
      timers.value.delete(id)
    }
  }
  
  /**
   * 清除特定间隔器
   */
  const clearSafeInterval = (id: number): void => {
    if (intervals.value.has(id)) {
      window.clearInterval(id)
      intervals.value.delete(id)
    }
  }
  
  /**
   * 清除所有定时器
   */
  const clearAllTimers = (): void => {
    // 清除所有setTimeout
    timers.value.forEach(id => {
      window.clearTimeout(id)
    })
    timers.value.clear()
    
    // 清除所有setInterval
    intervals.value.forEach(id => {
      window.clearInterval(id)
    })
    intervals.value.clear()
  }
  
  /**
   * 防抖函数（带定时器管理）
   */
  const debouncedCall = (callback: (...args: unknown[]) => void, delay: number) => {
    let debounceTimer: number | null = null
    
    return () => {
      if (debounceTimer) {
        clearSafeTimeout(debounceTimer)
      }
      
      debounceTimer = safeSetTimeout(callback, delay)
    }
  }
  
  /**
   * 节流函数（带定时器管理）
   */
  const throttledCall = (callback: (...args: unknown[]) => void, delay: number) => {
    let isThrottled = false
    
    return () => {
      if (isThrottled) return
      
      isThrottled = true
      callback()
      
      safeSetTimeout(() => {
        isThrottled = false
      }, delay)
    }
  }
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    clearAllTimers()
  })
  
  return {
    safeSetTimeout,
    safeSetInterval,
    clearSafeTimeout,
    clearSafeInterval,
    clearAllTimers,
    debouncedCall,
    throttledCall,
    // 只读的统计信息
    timerCount: computed(() => timers.value.size),
    intervalCount: computed(() => intervals.value.size)
  }
}

/**
 * 全局事件监听器管理
 */
export function useEventManager() {
  const listeners = ref<Map<string, { element: EventTarget; type: string; listener: EventListener }>>(new Map())
  
  /**
   * 安全添加事件监听器
   */
  const addEventListener = (
    element: EventTarget,
    type: string,
    listener: EventListener,
    options?: boolean | AddEventListenerOptions
  ): string => {
    const id = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    element.addEventListener(type, listener, options)
    listeners.value.set(id, { element, type, listener })
    
    return id
  }
  
  /**
   * 移除特定事件监听器
   */
  const removeEventListener = (id: string): void => {
    const listener = listeners.value.get(id)
    if (listener) {
      listener.element.removeEventListener(listener.type, listener.listener)
      listeners.value.delete(id)
    }
  }
  
  /**
   * 清除所有事件监听器
   */
  const clearAllEventListeners = (): void => {
    listeners.value.forEach(({ element, type, listener }) => {
      element.removeEventListener(type, listener)
    })
    listeners.value.clear()
  }
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    clearAllEventListeners()
  })
  
  return {
    addEventListener,
    removeEventListener,
    clearAllEventListeners,
    listenerCount: computed(() => listeners.value.size)
  }
}

/**
 * 内存监控工具（开发环境）
 */
export function useMemoryMonitor() {
  const isSupported = typeof performance !== 'undefined' && 'memory' in performance
  
  const getMemoryUsage = () => {
    if (!isSupported) {
      return null
    }
    
    const memory = (performance as Performance & { memory: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)
    }
  }
  
  const logMemoryUsage = (label = 'Memory Usage') => {
    const usage = getMemoryUsage()
    if (usage && process.env.NODE_ENV === 'development') {
      console.group(label)
      console.log(`Used: ${(usage.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
      console.log(`Total: ${(usage.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
      console.log(`Limit: ${(usage.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`)
      console.log(`Usage: ${usage.usagePercentage}%`)
      console.groupEnd()
    }
  }
  
  const startMemoryMonitoring = (interval = 30000) => {
    if (!isSupported || process.env.NODE_ENV !== 'development') {
      return null
    }
    
    const { safeSetInterval, clearSafeInterval } = useTimerManager()
    
    const intervalId = safeSetInterval(() => {
      const usage = getMemoryUsage()
      if (usage && parseFloat(usage.usagePercentage) > 80) {
        console.warn('⚠️ Memory usage is high:', usage.usagePercentage + '%')
      }
    }, interval)
    
    return () => clearSafeInterval(intervalId)
  }
  
  return {
    isSupported,
    getMemoryUsage,
    logMemoryUsage,
    startMemoryMonitoring
  }
}