import { ref, computed, watch, nextTick } from 'vue'
import { useNaive } from '@/composables/useNaive'
import { usePreview } from './usePreview'
import { instanceAPI, templateAPI } from '@/api'

interface FormDataEntryOptions {
    templateId: string
    instanceId?: string
    mode?: 'create' | 'edit' | 'view'
    autoSave?: boolean
    autoSaveInterval?: number
    enableValidation?: boolean
    enableSubmit?: boolean
}

interface FormInstance {
    id: string
    template_id: string
    form_data: Record<string, unknown>
    status: 'draft' | 'submitted' | 'approved' | 'rejected'
    created_at: string
    updated_at: string
}

export function useFormDataEntry(options: FormDataEntryOptions) {
    const {
        templateId,
        instanceId,
        mode = 'create',
        autoSave = true,
        autoSaveInterval = 5000,
        enableValidation = true,
        enableSubmit = true
    } = options

    // 获取 Naive UI 实例
    const { message } = useNaive()

    // 状态管理
    const isLoading = ref(false)
    const isSaving = ref(false)
    const isSubmitting = ref(false)
    const lastSaveTime = ref<Date | null>(null)
    const hasUnsavedChanges = ref(false)
    const currentInstance = ref<FormInstance | null>(null)
    const templateData = ref<Record<string, unknown> | null>(null)

    // 复用预览功能
    const preview = usePreview({
        mode: 'interactive',
        showValidation: enableValidation,
        enableSubmit: enableSubmit,
        theme: 'clinical'
    })

    // 计算属性
    const isEditMode = computed(() => mode === 'edit')
    const isViewMode = computed(() => mode === 'view')
    const canSubmit = computed(() =>
        enableSubmit &&
        !isViewMode.value &&
        preview.completionPercentage.value > 0
    )

    // 初始化表单数据
    const initializeForm = async () => {
        try {
            isLoading.value = true

            // 1. 获取模板数据
            const templateResponse = await templateAPI.getTemplate(templateId)
            if (!templateResponse.success) {
                throw new Error('获取模板数据失败')
            }

            templateData.value = templateResponse.data.template

            // 2. 获取或创建实例
            if (instanceId) {
                // 编辑现有实例
                const instanceResponse = await instanceAPI.getInstance(instanceId)
                if (instanceResponse.success) {
                    currentInstance.value = instanceResponse.data.instance
                } else {
                    throw new Error('获取表单实例失败')
                }
            } else {
                // 创建新实例
                const createResponse = await instanceAPI.createInstance({
                    template_id: templateId
                })
                if (createResponse.success) {
                    currentInstance.value = createResponse.data.instance
                } else {
                    throw new Error('创建表单实例失败')
                }
            }

            // 3. 初始化预览数据
            if (templateData.value?.template_data) {
                const previewData = {
                    sections: templateData.value.template_data.form_structure || [],
                    pageConfig: templateData.value.template_data.page_config || {},
                    formData: currentInstance.value?.form_data || {},
                    validationResults: templateData.value.template_data.validation_rules || {}
                } as Record<string, unknown>

                // 设置预览数据
                preview.previewData.value = previewData
                preview.currentFormData.value = { ...previewData.formData }

                // 进入预览模式
                preview.enterPreview()
            }

            hasUnsavedChanges.value = false
            lastSaveTime.value = new Date()

        } catch (error) {
            console.error('初始化表单失败:', error)
            message.error('初始化表单失败')
            throw error
        } finally {
            isLoading.value = false
        }
    }

    // 自动保存
    let autoSaveTimer: NodeJS.Timeout | null = null
    const startAutoSave = () => {
        if (!autoSave || isViewMode.value) return

        autoSaveTimer = setInterval(async () => {
            if (hasUnsavedChanges.value && !isSaving.value) {
                await saveFormData()
            }
        }, autoSaveInterval)
    }

    const stopAutoSave = () => {
        if (autoSaveTimer) {
            clearInterval(autoSaveTimer)
            autoSaveTimer = null
        }
    }

    // 保存表单数据
    const saveFormData = async (showMessage = false) => {
        if (!currentInstance.value || isViewMode.value) return false

        try {
            isSaving.value = true

            const response = await instanceAPI.updateInstance(currentInstance.value.id, {
                form_data: preview.currentFormData.value
            })

            if (response.success) {
                lastSaveTime.value = new Date()
                hasUnsavedChanges.value = false

                if (showMessage) {
                    message.success('表单已保存')
                }

                return true
            } else {
                throw new Error('保存失败')
            }
        } catch (error) {
            console.error('保存表单数据失败:', error)
            if (showMessage) {
                message.error('保存失败')
            }
            return false
        } finally {
            isSaving.value = false
        }
    }

    // 提交表单
    const submitForm = async () => {
        if (!canSubmit.value) {
            message.warning('无法提交表单')
            return false
        }

        try {
            isSubmitting.value = true

            // 先保存数据
            const saved = await saveFormData()
            if (!saved) {
                throw new Error('保存数据失败')
            }

            // 验证表单
            const isValid = preview.validateForm()
            if (!isValid) {
                message.error('请先修正表单错误')
                return false
            }

            // 提交表单
            const response = await instanceAPI.submitInstance(currentInstance.value!.id)

            if (response.success) {
                message.success('表单提交成功')
                return true
            } else {
                throw new Error('提交失败')
            }
        } catch (error) {
            console.error('提交表单失败:', error)
            message.error('提交失败')
            return false
        } finally {
            isSubmitting.value = false
        }
    }

    // 重置表单
    const resetForm = async () => {
        if (!currentInstance.value) return

        try {
            // 重置为初始数据
            preview.currentFormData.value = { ...currentInstance.value.form_data }
            preview.validationErrors.value = {}
            hasUnsavedChanges.value = false

            message.success('表单已重置')
        } catch (error) {
            console.error('重置表单失败:', error)
            message.error('重置失败')
        }
    }

    // 监听表单数据变化
    watch(
        () => preview.currentFormData.value,
        () => {
            hasUnsavedChanges.value = true
        },
        { deep: true }
    )

    // 监听验证错误变化
    watch(
        () => preview.validationErrors.value,
        (errors) => {
            // 可以在这里处理验证错误的变化
            console.log('验证错误变化:', errors)
        },
        { deep: true }
    )

    // 导出表单数据
    const exportFormData = (format: 'json' | 'csv' = 'json') => {
        return preview.exportFormData(format)
    }

    // 获取表单状态
    const getFormStatus = () => {
        if (!currentInstance.value) return 'unknown'
        return currentInstance.value.status
    }

    // 获取完成度
    const getCompletionPercentage = () => {
        return preview.completionPercentage.value
    }

    // 获取验证错误
    const getValidationErrors = () => {
        return preview.validationErrors.value
    }

    // 检查是否有验证错误
    const hasValidationErrors = computed(() => {
        return Object.keys(preview.validationErrors.value).length > 0
    })

    // 清理资源
    const cleanup = () => {
        stopAutoSave()
        preview.exitPreview()
    }

    return {
        // 状态
        isLoading,
        isSaving,
        isSubmitting,
        lastSaveTime,
        hasUnsavedChanges,
        currentInstance,
        templateData,
        hasValidationErrors,

        // 预览相关
        previewData: preview.previewData,
        currentFormData: preview.currentFormData,
        validationErrors: preview.validationErrors,
        completionPercentage: preview.completionPercentage,

        // 计算属性
        isEditMode,
        isViewMode,
        canSubmit,

        // 方法
        initializeForm,
        saveFormData,
        submitForm,
        resetForm,
        exportFormData,
        getFormStatus,
        getCompletionPercentage,
        getValidationErrors,
        startAutoSave,
        stopAutoSave,
        cleanup,

        // 预览方法
        updateField: preview.updateField,
        validateField: preview.validateField,
        validateForm: preview.validateForm,
        enterPreview: preview.enterPreview,
        exitPreview: preview.exitPreview
    }
}