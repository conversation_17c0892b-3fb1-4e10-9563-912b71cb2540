import { ref, computed, watch, onUnmounted } from 'vue'
import { EditorMode } from '@crf/types/core'
import { useEditorStore } from '@/stores/editor-store'
import { historyAPI } from '@/api'
import { cloneEditorState } from '@/utils/deep-clone'

interface UndoRedoEntry {
  id: string
  action: string
  description: string
  timestamp: number
  beforeState: unknown
  afterState: unknown
  canUndo: boolean
  canRedo: boolean
}

interface UndoRedoOptions {
  maxHistorySize?: number
  templateId: string
  enableServerSync?: boolean // 是否同步到服务器
  autoTrack?: boolean // 是否自动跟踪变化
}

export function useUndoRedo(options: UndoRedoOptions) {
  const editorStore = useEditorStore()

  const {
    maxHistorySize = 50,
    templateId,
    enableServerSync = true,
    autoTrack = true
  } = options

  // 历史记录堆栈
  const history = ref<UndoRedoEntry[]>([])
  const currentIndex = ref(-1)

  // 计算属性
  const canUndo = computed(() => currentIndex.value >= 0)
  const canRedo = computed(() => currentIndex.value < history.value.length - 1)
  const currentEntry = computed(() =>
    currentIndex.value >= 0 ? history.value[currentIndex.value] : null
  )

  // 获取当前编辑器状态
  const getCurrentState = () => {
    return {
      sections: cloneEditorState(editorStore.sections),
      pageConfig: cloneEditorState(editorStore.pageConfig),
      formData: cloneEditorState(editorStore.formData),
      selectedComponent: editorStore.selectedComponent ? cloneEditorState(editorStore.selectedComponent) : null
    }
  }

  // 应用状态到编辑器
  const applyState = (state: unknown) => {
    // 暂停自动跟踪，避免循环
    stopAutoTrack?.()

    try {
      // 恢复sections
      if (state.sections) {
        editorStore.sections.splice(0, editorStore.sections.length, ...state.sections)
      }

      // 恢复pageConfig
      if (state.pageConfig) {
        Object.assign(editorStore.pageConfig, state.pageConfig)
      }

      // 恢复formData
      if (state.formData) {
        Object.assign(editorStore.formData, state.formData)
      }

      // 恢复选中的组件
      if (state.selectedComponent) {
        editorStore.selectComponent(state.selectedComponent)
      } else {
        editorStore.selectComponent(null)
      }
    } finally {
      // 重新启动自动跟踪
      setTimeout(() => {
        if (autoTrack) {
          startAutoTrack()
        }
      }, 100)
    }
  }

  // 同步到服务器
  const syncToServer = async (entry: UndoRedoEntry) => {
    if (!enableServerSync || !templateId) {
      console.log('跳过服务器同步: enableServerSync =', enableServerSync, ', templateId =', templateId)
      return
    }

    try {
      await historyAPI.createHistoryEntry({
        resource_type: 'template',
        resource_id: templateId,
        action: entry.action,
        change_data: {
          before: entry.beforeState,
          after: entry.afterState,
          timestamp: entry.timestamp,
          description: entry.description
        },
        description: entry.description
      })
      console.log('✅ 历史记录已同步到服务器')
    } catch (error) {
      console.warn('⚠️ 同步历史记录到服务器失败:', error)
    }
  }

  // 添加历史记录条目
  const addEntry = (action: string, description: string, beforeState?: unknown, afterState?: unknown) => {
    const entry: UndoRedoEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      action,
      description,
      timestamp: Date.now(),
      beforeState: beforeState || getCurrentState(),
      afterState: afterState || getCurrentState(),
      canUndo: true,
      canRedo: true
    }

    // 移除当前位置之后的所有历史记录（用户执行了新操作）
    if (currentIndex.value < history.value.length - 1) {
      history.value.splice(currentIndex.value + 1)
    }

    // 添加新条目
    history.value.push(entry)
    currentIndex.value = history.value.length - 1

    // 限制历史记录大小
    if (history.value.length > maxHistorySize) {
      const removeCount = history.value.length - maxHistorySize
      history.value.splice(0, removeCount)
      currentIndex.value -= removeCount
    }

    // 同步到服务器
    if (enableServerSync) {
      syncToServer(entry)
    }

    console.log(`📝 添加历史记录: ${description}`)
    return entry.id
  }

  // 监听编辑器模式变化，处理历史记录
  watch(() => editorStore.mode, (newMode, oldMode) => {
    // 从预览模式返回编辑模式时，清除所有可重做的历史记录
    // 防止用户通过“重做”恢复到预览时输入的数据状态
    if (newMode === EditorMode.EDIT && oldMode === EditorMode.PREVIEW) {
      clear()
      console.log('🔄 切换到编辑模式，已清除所有历史记录')
    }
  })

  // 撤销操作
  const undo = () => {
    if (!canUndo.value) {
      console.warn('⚠️ 没有可撤销的操作')
      return false
    }

    const entry = history.value[currentIndex.value]
    if (!entry) {
      console.warn('⚠️ 历史记录条目不存在')
      return false
    }

    applyState(entry.beforeState)
    currentIndex.value--

    console.log(`↶ 撤销: ${entry.description}`)
    return true
  }

  // 重做操作
  const redo = () => {
    if (!canRedo.value) {
      console.warn('⚠️ 没有可重做的操作')
      return false
    }

    currentIndex.value++
    const entry = history.value[currentIndex.value]
    if (!entry) {
      console.warn('⚠️ 历史记录条目不存在')
      currentIndex.value-- // 回退索引
      return false
    }

    applyState(entry.afterState)

    console.log(`↷ 重做: ${entry.description}`)
    return true
  }

  // 清除历史记录
  const clear = () => {
    history.value = []
    currentIndex.value = -1
    console.log('🗑️ 历史记录已清除')
  }

  // 获取历史记录列表（用于UI显示）
  const getHistory = () => {
    return history.value.map((entry, index) => ({
      ...entry,
      isCurrent: index === currentIndex.value,
      canSelect: true
    }))
  }

  // 跳转到指定历史记录
  const goToEntry = (entryId: string) => {
    const index = history.value.findIndex(entry => entry.id === entryId)
    if (index === -1) {
      console.warn('⚠️ 未找到指定的历史记录条目')
      return false
    }

    const entry = history.value[index]
    if (!entry) {
      console.warn('⚠️ 历史记录条目不存在')
      return false
    }

    applyState(entry.afterState)
    currentIndex.value = index

    console.log(`🎯 跳转到历史记录: ${entry.description}`)
    return true
  }

  // 自动跟踪编辑器变化
  let stopAutoTrack: (() => void) | null = null

  const startAutoTrack = () => {
    if (!autoTrack || stopAutoTrack) return

    let lastState = getCurrentState()
    let changeTimeout: number | null = null

    const trackChange = (action: string, description: string) => {
      const currentState = getCurrentState()

      // 防抖处理，避免频繁记录
      if (changeTimeout) {
        clearTimeout(changeTimeout)
      }

      changeTimeout = window.setTimeout(() => {
        addEntry(action, description, lastState, currentState)
        lastState = currentState
      }, 500) // 500ms防抖
    }

    // 监听sections变化
    const stopWatchSections = watch(
      () => editorStore.sections,
      () => trackChange('sections_change', '修改表单结构'),
      { deep: true }
    )

    // 监听pageConfig变化
    const stopWatchPageConfig = watch(
      () => editorStore.pageConfig,
      () => trackChange('page_config_change', '修改页面配置'),
      { deep: true }
    )

    // 监听formData变化
    const stopWatchFormData = watch(
      () => editorStore.formData,
      () => trackChange('form_data_change', '修改表单数据'),
      { deep: true }
    )

    stopAutoTrack = () => {
      stopWatchSections()
      stopWatchPageConfig()
      stopWatchFormData()
      if (changeTimeout) {
        clearTimeout(changeTimeout)
      }
      stopAutoTrack = null
    }

    console.log('👁️ 自动跟踪已启动')
  }

  // 手动记录操作
  const recordOperation = (action: string, description: string, operation: () => void) => {
    const beforeState = getCurrentState()

    try {
      operation()
    } catch (error) {
      console.error('❌ 操作执行失败:', error)
      throw error
    }

    const afterState = getCurrentState()
    addEntry(action, description, beforeState, afterState)
  }

  // 键盘快捷键处理
  const handleKeyboardShortcut = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'z') {
      event.preventDefault()
      if (event.shiftKey) {
        redo() // Ctrl+Shift+Z 或 Cmd+Shift+Z
      } else {
        undo() // Ctrl+Z 或 Cmd+Z
      }
    } else if ((event.ctrlKey || event.metaKey) && event.key === 'y') {
      event.preventDefault()
      redo() // Ctrl+Y 或 Cmd+Y
    }
  }

  // 清理函数
  const cleanup = () => {
    stopAutoTrack?.()
    document.removeEventListener('keydown', handleKeyboardShortcut)
  }

  // 在组件销毁时清理
  onUnmounted(() => {
    cleanup()
  })

  // 初始化
  const init = () => {
    // 添加初始状态
    addEntry('init', '初始化编辑器')

    // 启动自动跟踪
    if (autoTrack) {
      startAutoTrack()
    }

    // 监听键盘快捷键
    document.addEventListener('keydown', handleKeyboardShortcut)

    console.log('✅ 撤销/重做功能已初始化')
  }

  return {
    // 状态
    history: computed(() => history.value),
    currentIndex: computed(() => currentIndex.value),
    canUndo,
    canRedo,
    currentEntry,

    // 方法
    undo,
    redo,
    clear,
    addEntry,
    recordOperation,
    getHistory,
    goToEntry,
    init,
    cleanup,

    // 控制
    startAutoTrack,
    stopAutoTrack: () => stopAutoTrack?.()
  }
}