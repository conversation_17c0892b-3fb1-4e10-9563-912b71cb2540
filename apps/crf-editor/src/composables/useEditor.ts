import { computed, ref, watch } from 'vue'
import { useEditorStore } from '@/stores/editor-store'
import type { BaseBlock } from '@/types/editor'
import type { FormSection } from '@/components/edit/render/types'
import { getComponent } from '@crf/components'

/**
 * 编辑器核心逻辑组合式API
 * 统一管理物料区、渲染区、配置区的交互逻辑
 */
export function useEditor() {
    const editorStore = useEditorStore()
    // 表单分组管理
    const sections = ref<FormSection[]>([
        {
            id: 'default',
            name: '默认分组',
            blocks: []
        }
    ])

    // 当前激活的分组
    const activeSectionId = ref('default')


    // 计算属性
    const currentSection = computed(() =>
        sections.value.find(s => s.id === activeSectionId.value)
    )

    const hasMultipleSections = computed(() => sections.value.length > 1)

    // 组件选择逻辑
    const selectComponent = (component: BaseBlock, sectionId?: string) => {
        if (!component.id) return

        editorStore.selectComponent(component)
        if (sectionId) {
            activeSectionId.value = sectionId
        }
        editorStore.showConfigPanel()
    }

    // 添加组件到指定分组
    const addComponentToSection = (component: BaseBlock, sectionId: string) => {
        const section = sections.value.find(s => s.id === sectionId)
        if (section) {
            section.blocks.push(component)
            selectComponent(component, sectionId)
        }
    }

    // 删除组件
    const removeComponent = (componentId: string, sectionId: string) => {
        const section = sections.value.find(s => s.id === sectionId)
        if (section) {
            const index = section.blocks.findIndex((block: Record<string, unknown>) => block.id === componentId)
            if (index > -1) {
                section.blocks.splice(index, 1)
                // 如果删除的是当前选中组件，清空选择
                if (editorStore.selectedComponent?.id === componentId) {
                    editorStore.selectComponent(null)
                    editorStore.hideConfigPanel()
                }
            }
        }
    }

    // 分组管理
    const addSection = () => {
        const newSectionId = `section-${Date.now()}`
        const newSection: FormSection = {
            id: newSectionId,
            name: `分组 ${sections.value.length}`,
            blocks: []
        }
        sections.value.push(newSection)
        activeSectionId.value = newSectionId
        return newSectionId
    }

    const removeSection = (sectionId: string) => {
        if (sections.value.length <= 1) return false

        const index = sections.value.findIndex(s => s.id === sectionId)
        if (index > -1) {
            sections.value.splice(index, 1)
            // 如果删除的是当前激活分组，切换到第一个分组
            if (activeSectionId.value === sectionId) {
                if (sections.value[0]) {
                    activeSectionId.value = sections.value[0].id
                }
            }
            return true
        }
        return false
    }

    const updateSectionName = (sectionId: string, name: string) => {
        const section = sections.value.find(s => s.id === sectionId)
        if (section && name.trim()) {
            section.name = name.trim()
        }
    }

    // 同步到全局状态
    const syncToStore = () => {
        const allBlocks = sections.value.flatMap(section => section.blocks)
        // 转换为新的组件格式并添加到Store
        allBlocks.forEach(block => {
            // 确保block有必需的属性
            if (!block.id) return

            const componentConfig = {
                id: block.id,
                type: block.code || 'text',
                title: block.title || block.props?.title || '组件',
                ...block.props
            }

            // 检查组件是否已存在
            const existingComponent = editorStore.getComponent(block.id)
            if (!existingComponent) {
                editorStore.addComponent(componentConfig)
            }
        })
    }

    // 监听变化，自动同步
    watch(sections, syncToStore, { deep: true, immediate: true })

    return {
        // 状态
        sections,
        activeSectionId,
        currentSection,
        hasMultipleSections,

        // 方法
        selectComponent,
        addComponentToSection,
        removeComponent,
        addSection,
        removeSection,
        updateSectionName,
        syncToStore,

        // Store 状态和方法的便捷访问
        currentSelect: computed(() => editorStore.selectedComponent),
        configPanelShow: computed(() => editorStore.editorConfig.showConfigPanel),
        setCurrentSelect: (component: Record<string, unknown>) => editorStore.selectComponent(component),
        setConfigPanelShow: (show: boolean) => {
            if (show) {
                editorStore.showConfigPanel()
            } else {
                editorStore.hideConfigPanel()
            }
        }
    }
}

/**
 * 组件注册管理
 */
export function useComponentRegistry() {
    // 获取组件名称
    const getComponentNameByCode = (code: string): string => {
        return getComponent(code)?.name || code
    }

    return {
        getComponentName: getComponentNameByCode
    }
}

/**
 * 拖拽管理
 */
export function useDragDrop() {
    const { addComponentToSection } = useEditor()

    // 处理拖拽结束事件
    const handleDrop = (event: DragEvent, sectionId: string) => {
        const { added } = event
        if (added && added.element) {
            addComponentToSection(added.element, sectionId)
        }
    }

    return {
        handleDrop
    }
}