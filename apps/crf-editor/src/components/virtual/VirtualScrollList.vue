<template>
  <div 
    ref="containerRef"
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- 总高度占位 -->
    <div 
      class="virtual-scroll-spacer"
      :style="{ height: totalHeight + 'px' }"
    ></div>
    
    <!-- 可见项目容器 -->
    <div 
      class="virtual-scroll-content"
      :style="{ transform: `translateY(${offsetY}px)` }"
    >
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item.item, item.index)"
        class="virtual-scroll-item"
        :style="{ 
          height: getItemHeight(item.item, item.index) + 'px',
          'min-height': getItemHeight(item.item, item.index) + 'px'
        }"
        :data-index="item.index"
      >
        <slot 
          :item="item.item" 
          :index="item.index"
          :visible="item.visible"
        />
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div 
      v-if="loading && visibleItems.length > 0"
      class="virtual-scroll-loading"
    >
      <n-spin size="small">
        <template #description>
          加载中...
        </template>
      </n-spin>
    </div>
    
    <!-- 空状态 -->
    <div 
      v-if="items.length === 0 && !loading"
      class="virtual-scroll-empty"
    >
      <slot name="empty">
        <n-empty 
          description="暂无数据"
          size="large"
        />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  nextTick,
  defineEmits
} from 'vue'
import { useTimerManager } from '@/composables/useTimerManager'
import { useErrorHandler } from '@/utils/error-handler'

// Props
export interface VirtualScrollProps<T = unknown> {
  items: T[]
  itemHeight?: number | ((item: T, index: number) => number)
  containerHeight?: number
  buffer?: number
  loading?: boolean
  keyField?: keyof T | ((item: T, index: number) => string | number)
  threshold?: number // 滚动到底部多少像素时触发加载更多
  enableInfiniteScroll?: boolean
  estimatedItemHeight?: number // 动态高度时的预估高度
  overscan?: number // 预渲染的项目数量
}

const props = withDefaults(defineProps<VirtualScrollProps>(), {
  itemHeight: 50,
  containerHeight: 400,
  buffer: 5,
  loading: false,
  threshold: 100,
  enableInfiniteScroll: false,
  estimatedItemHeight: 50,
  overscan: 5
})

// Emits
const emit = defineEmits<{
  'load-more': []
  'scroll': [{ scrollTop: number; scrollHeight: number; clientHeight: number }]
  'item-rendered': [{ item: unknown; index: number; element: HTMLElement }]
}>()

// Composables
const { safeSetTimeout } = useTimerManager()
const { handleError } = useErrorHandler()

// Refs
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const isScrolling = ref(false)
const itemHeightCache = ref<Map<number, number>>(new Map())

// 获取项目高度
const getItemHeight = (item: unknown, index: number): number => {
  // 如果有缓存，使用缓存的高度
  if (itemHeightCache.value.has(index)) {
    return itemHeightCache.value.get(index)!
  }
  
  // 如果itemHeight是函数，调用函数获取高度
  if (typeof props.itemHeight === 'function') {
    const height = props.itemHeight(item, index)
    itemHeightCache.value.set(index, height)
    return height
  }
  
  // 使用固定高度
  return props.itemHeight as number
}

// 计算总高度
const totalHeight = computed(() => {
  if (typeof props.itemHeight === 'number') {
    return props.items.length * props.itemHeight
  }
  
  // 动态高度情况下，计算所有项目的高度
  let total = 0
  for (let i = 0; i < props.items.length; i++) {
    const item = props.items[i]
    if (item !== undefined) {
      total += getItemHeight(item, i)
    }
  }
  return total
})

// 计算可见范围
const visibleRange = computed(() => {
  const containerHeight = props.containerHeight
  let start = 0
  let end = props.items.length
  
  if (typeof props.itemHeight === 'number') {
    // 固定高度的简单计算
    start = Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.buffer)
    end = Math.min(
      props.items.length,
      Math.ceil((scrollTop.value + containerHeight) / props.itemHeight) + props.buffer
    )
  } else {
    // 动态高度的复杂计算
    let currentHeight = 0
    
    // 寻找开始索引
    for (let i = 0; i < props.items.length; i++) {
      const item = props.items[i]
      if (item === undefined) continue
      const itemHeight = getItemHeight(item, i)
      if (currentHeight + itemHeight > scrollTop.value - props.buffer * props.estimatedItemHeight) {
        start = Math.max(0, i)
        break
      }
      currentHeight += itemHeight
    }
    
    // 寻找结束索引
    currentHeight = 0
    for (let i = 0; i < props.items.length; i++) {
      const item = props.items[i]
      if (item === undefined) continue
      
      if (i < start) {
        currentHeight += getItemHeight(item, i)
        continue
      }
      
      const itemHeight = getItemHeight(item, i)
      if (currentHeight > containerHeight + props.buffer * props.estimatedItemHeight) {
        end = Math.min(props.items.length, i)
        break
      }
      currentHeight += itemHeight
    }
  }
  
  // 添加overscan
  start = Math.max(0, start - props.overscan)
  end = Math.min(props.items.length, end + props.overscan)
  
  return { start, end }
})

// 计算偏移量
const offsetY = computed(() => {
  const { start } = visibleRange.value
  
  if (typeof props.itemHeight === 'number') {
    return start * props.itemHeight
  }
  
  // 动态高度情况下计算累积偏移
  let offset = 0
  for (let i = 0; i < start; i++) {
    const item = props.items[i]
    if (item !== undefined) {
      offset += getItemHeight(item, i)
    }
  }
  return offset
})

// 可见项目
const visibleItems = computed(() => {
  const { start, end } = visibleRange.value
  
  return props.items.slice(start, end).map((item, index) => ({
    item,
    index: start + index,
    visible: true
  }))
})

// 获取项目唯一键
const getItemKey = (item: unknown, index: number): string | number => {
  if (typeof props.keyField === 'function') {
    return props.keyField(item, index)
  }
  
  if (props.keyField && typeof props.keyField === 'string') {
    return (item as Record<string, unknown>)[props.keyField] || index
  }
  
  return index
}

// 滚动处理
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  const newScrollTop = target.scrollTop
  
  scrollTop.value = newScrollTop
  isScrolling.value = true
  
  // 发出滚动事件
  emit('scroll', {
    scrollTop: newScrollTop,
    scrollHeight: target.scrollHeight,
    clientHeight: target.clientHeight
  })
  
  // 无限滚动检查
  if (props.enableInfiniteScroll) {
    const { scrollHeight, clientHeight } = target
    const distanceFromBottom = scrollHeight - (newScrollTop + clientHeight)
    
    if (distanceFromBottom <= props.threshold && !props.loading) {
      emit('load-more')
    }
  }
  
  // 重置滚动状态
  safeSetTimeout(() => {
    isScrolling.value = false
  }, 150)
}

// 滚动到指定项目
const scrollToItem = (index: number, behavior: ScrollBehavior = 'smooth') => {
  if (!containerRef.value || index < 0 || index >= props.items.length) {
    return
  }
  
  let targetScrollTop = 0
  
  if (typeof props.itemHeight === 'number') {
    targetScrollTop = index * props.itemHeight
  } else {
    // 动态高度情况下计算目标位置
    for (let i = 0; i < index; i++) {
      const item = props.items[i]
      if (item !== undefined) {
        targetScrollTop += getItemHeight(item, i)
      }
    }
  }
  
  containerRef.value.scrollTo({
    top: targetScrollTop,
    behavior
  })
}

// 滚动到顶部
const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  if (containerRef.value) {
    containerRef.value.scrollTo({
      top: 0,
      behavior
    })
  }
}

// 滚动到底部
const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
  if (containerRef.value) {
    containerRef.value.scrollTo({
      top: containerRef.value.scrollHeight,
      behavior
    })
  }
}

// 更新项目高度缓存
const updateItemHeight = (index: number, height: number) => {
  if (height > 0) {
    itemHeightCache.value.set(index, height)
  }
}

// 清除高度缓存
const clearHeightCache = () => {
  itemHeightCache.value.clear()
}

// 获取可见项目范围
const getVisibleRange = () => {
  return visibleRange.value
}

// 监听项目变化，清除缓存
watch(() => props.items.length, () => {
  if (typeof props.itemHeight !== 'number') {
    clearHeightCache()
  }
})

// 响应式高度测量
const measureItemHeights = async () => {
  if (typeof props.itemHeight === 'number' || !containerRef.value) {
    return
  }
  
  await nextTick()
  
  try {
    const itemElements = containerRef.value.querySelectorAll('.virtual-scroll-item')
    
    itemElements.forEach((element) => {
      const index = parseInt(element.getAttribute('data-index') || '0')
      const height = element.getBoundingClientRect().height
      
      if (height > 0) {
        updateItemHeight(index, height)
      }
    })
  } catch (error) {
    handleError(error as Error)
  }
}

// 监听可见项目变化，测量高度
watch(visibleItems, () => {
  if (typeof props.itemHeight !== 'number') {
    safeSetTimeout(measureItemHeights, 0)
  }
}, { flush: 'post' })

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  updateItemHeight,
  clearHeightCache,
  getVisibleRange,
  containerRef
})
</script>

<style scoped lang="scss">
.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  
  // 优化滚动性能
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--n-bg-color-page);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--n-border-color);
    border-radius: 4px;
    
    &:hover {
      background: var(--n-border-color-dark);
    }
  }
}

.virtual-scroll-spacer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  pointer-events: none;
}

.virtual-scroll-content {
  position: relative;
  z-index: 1;
}

.virtual-scroll-item {
  position: relative;
  box-sizing: border-box;
  
  // 防止内容溢出
  overflow: hidden;
  
  // 提升渲染性能
  will-change: transform;
  contain: layout style paint;
}

.virtual-scroll-loading {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--n-bg-color);
  border-top: 1px solid var(--n-border-color-light);
}

.virtual-scroll-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

// 性能优化：减少重绘
.virtual-scroll-item {
  transform: translateZ(0);
}

// 响应式设计
@media (max-width: 768px) {
  .virtual-scroll-container {
    &::-webkit-scrollbar {
      width: 4px;
    }
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .virtual-scroll-container {
    scroll-behavior: auto !important;
  }
}
</style>