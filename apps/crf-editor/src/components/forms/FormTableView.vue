<template>
  <div class="forms-table-container">
    <n-data-table
      :columns="columns"
      :data="forms"
      :loading="loading"
      :row-key="(row) => row.id"
      :checked-row-keys="Array.from(selectedFormIds)"
      @update:checked-row-keys="(keys: string[]) => $emit('selection-change', keys)"
      size="medium"
      striped
      :scroll-x="1400"
      :max-height="600"
    />
    
    <!-- 分页组件 - 始终显示在底部，即使只有一页 -->
    <div v-if="pagination" class="table-pagination">
      <!-- 分页组件 -->
      <n-pagination
        :page="pagination.current"
        :page-size="20"
        :page-count="Math.ceil(pagination.total / 20)"
        :item-count="pagination.total"
        :on-update:page="(page: number) => $emit('page-change', page)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { NDataTable, NPagination } from 'naive-ui'

interface FormItem {
  id: string
  [key: string]: any
}

interface PaginationInfo {
  current: number
  size: number
  total: number
}

interface Props {
  forms: FormItem[]
  columns: any[]
  selectedFormIds: Set<string>
  loading?: boolean
  pagination?: PaginationInfo
}

defineProps<Props>()

defineEmits<{
  'selection-change': [checkedKeys: string[]]
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
}>()
</script>

<style lang="scss" scoped>
.forms-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  min-height: 400px; // 确保有最小高度
}

.table-pagination {
  padding: 16px 20px;
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fafafa;
  margin-top: auto; // 确保分页始终在底部
}

// 分页的响应式调整
.table-pagination {
  @media (max-width: 768px) {
    padding: 12px 16px;
    
    :deep(.n-pagination) {
      .n-pagination-item {
        min-width: 32px;
        height: 32px;
      }
      
      .n-pagination-quick-jumper {
        display: none;
      }
    }
  }
  
  @media (max-width: 480px) {
    padding: 8px 12px;
    
    :deep(.n-pagination) {
      .n-pagination-size-picker {
        display: none;
      }
      
      .n-pagination-item {
        min-width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }
  }
}

// 表格样式优化
:deep(.n-data-table) {
  .n-data-table-th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
  }
  
  .n-data-table-td {
    border-bottom: 1px solid #f3f4f6;
  }
  
  .table-form-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .table-form-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    color: white;
  }
  
  .table-form-details {
    flex: 1;
    min-width: 0;
  }
  
  .table-form-title {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .table-form-description {
    font-size: 12px;
    color: #6b7280;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .table-time {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: #6b7280;
    
    .time-icon {
      font-size: 12px;
    }
  }
  
  .table-actions {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    min-width: 200px; // 确保有足够空间
  }
  
  .table-action-button {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f3f4f6;
      transform: translateY(-1px);
    }
    
    // 不同类型按钮的悬停效果
    &.n-button--primary-type:hover {
      background-color: #dbeafe;
    }
  }
  
  .version-tag {
    background: #dcfce7 !important;
    color: #166534 !important;
    border: none !important;
  }
}
</style>