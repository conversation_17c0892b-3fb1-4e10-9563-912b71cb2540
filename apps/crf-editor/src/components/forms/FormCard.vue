<template>
  <div 
    :key="`form-${form.id}`" 
    :data-form-id="form.id" 
    class="form-card" 
    :class="{ 
      'is-duplicating': isDuplicating, 
      'is-deleting': isDeleting, 
      'is-selected': isSelected 
    }"
  >
    <!-- 复选框 -->
    <div class="form-checkbox-container" @click.stop>
      <n-checkbox
          :checked="isSelected"
          @update:checked="handleCheckboxClick"
          size="medium"
          class="form-card-checkbox"
        />
    </div>
    
    <div class="form-card-body">
      <div class="form-card-content">
        <div class="form-icon-container">
          <div class="form-icon" :style="iconStyle">
            <n-icon class="icon" size="24">
              <component :is="safeGetIconComponent(form.icon)" />
            </n-icon>
          </div>
        </div>
        
        <div class="form-info">
          <div class="form-header">
            <n-tooltip>
              <template #trigger>
                <h3 class="form-title">{{ displayName }}</h3>
              </template>
              {{ displayName }}
            </n-tooltip>
          </div>
          
          <n-tooltip
            :delay="200"
            trigger="hover"
            style="max-width: 300px"
          >
            <template #trigger>
              <p class="form-description">
                {{ form.description || '暂无' }}
              </p>
            </template>
            {{ form.description || '暂无' }}
          </n-tooltip>
          
          <!-- 版本号和编辑时间 -->
          <div class="form-meta m-b-10px m-t-10px">
            <!-- 编辑时间 -->
            <div class="edit-time">
              <n-icon class="time-icon"><Edit /></n-icon>
              {{ formattedTime }}
            </div>
            
            <!-- 版本号 -->
            <n-tag 
              v-if="form.version" 
              size="small" 
              class="version-tag"
              :bordered="false"
            >
              v{{ form.version }}
            </n-tag>
            
            <!-- 发布状态标签 -->
            <n-tag 
              size="small" 
              :type="statusType" 
              class="form-status"
            >
              {{ statusText }}
            </n-tag>
          </div>
        </div>
      </div>
    </div>
      
    <!-- 操作按钮 -->
    <div class="form-actions" @click.stop>
      <div class="actions-row">
        <div class="action-buttons">
          <n-button
            text
            @click="$emit('edit', form)"
          >
            <n-icon><Edit /></n-icon>
            编辑
          </n-button>
          
          <n-tooltip>
            <template #trigger>
              <n-button
                text
                @click="$emit('fill', form)"
                :disabled="fillButtonState.disabled"
                :type="fillButtonState.type"
                :class="{ 'unpublished-fill-btn': !isPublished }"
              >
                <n-icon><Document /></n-icon>
                填写
                <n-icon v-if="!isPublished" class="warning-icon"><Warning /></n-icon>
              </n-button>
            </template>
            {{ fillButtonState.tooltip }}
          </n-tooltip>
          
          <n-button
            text
            @click="$emit('view-data', form)"
          >
            <n-icon><DataLine /></n-icon>
            数据
          </n-button>
        </div>
        
        <n-dropdown
          trigger="click"
          :options="dropdownOptions"
          @select="(cmd, option) => $emit('command', cmd, form)"
          class="more-dropdown"
          v-if="dropdownOptions.length > 0"
        >
          <n-button text class="more-button">
            <n-icon><More /></n-icon>
          </n-button>
        </n-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NButton, NIcon, NTag, NTooltip, NCheckbox, NDropdown } from 'naive-ui'
import { 
  CreateOutline as Edit,
  DocumentTextOutline as Document,
  EllipsisHorizontalOutline as More,
  StatsChartOutline as DataLine,
  WarningOutline as Warning
} from '@vicons/ionicons5'

// 从常量文件导入图标相关工具
import { getIconComponent, adjustColorBrightness, DEFAULT_FORM_COLOR } from '@/constants/icons'
// 从工具函数导入通用函数
import { formatDetailTime, getStatusType, getStatusText } from '@/utils/form'

type FormStatus = 'draft' | 'published' | 'deleted'

interface FormData {
  id: string
  name?: string
  title?: string
  description?: string
  icon?: string
  iconColor?: string
  version?: string
  status?: FormStatus
  created_at?: string
  updated_at?: string
  [key: string]: any
}

interface Props {
  form: FormData
  isSelected: boolean
  isDuplicating: boolean
  isDeleting: boolean
  loading?: boolean
  creating?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  select: [formId: string, checked: boolean]
  edit: [form: FormData]
  fill: [form: FormData]
  'view-data': [form: FormData]
  command: [command: string, form: FormData]
}>()

// 计算属性：表单显示名称
const displayName = computed(() => props.form.name || props.form.title || '未命名表单')

// 计算属性：表单状态相关
const isPublished = computed(() => props.form.status === 'published')
const isDeleted = computed(() => props.form.status === 'deleted')
const statusText = computed(() => getStatusText(props.form.status || 'draft'))
const statusType = computed(() => getStatusType(props.form.status || 'draft'))

// 计算属性：图标样式
const iconStyle = computed(() => {
  const color = props.form.iconColor || DEFAULT_FORM_COLOR
  return {
    background: `linear-gradient(135deg, ${color}, ${adjustColorBrightness(color, -20)})`
  }
})

// 计算属性：下拉菜单选项
const dropdownOptions = computed(() => {
  const isOperationDisabled = props.loading || props.creating
  
  // 如果是已删除的表单，只显示恢复选项
  if (isDeleted.value) {
    return [
      {
        key: 'restore',
        label: '恢复',
        disabled: isOperationDisabled || props.isDuplicating
      }
    ]
  }
  
  // 正常表单的选项
  return [
    {
      key: 'duplicate',
      label: '复制',
      disabled: isOperationDisabled || props.isDuplicating
    },
    { type: 'divider' },
    {
      key: 'delete',
      label: '删除',
      disabled: isOperationDisabled || props.isDeleting
    }
  ]
})

// 计算属性：填写按钮状态
const fillButtonState = computed(() => ({
  disabled: !isPublished.value || props.isDuplicating || props.isDeleting,
  type: (isPublished.value ? 'primary' : 'warning') as 'primary' | 'warning',
  tooltip: isPublished.value ? '填写表单' : '⚠️ 表单需要发布后才能填写'
}))

// 格式化时间显示
const formattedTime = computed(() => {
  const time = props.form.updated_at || props.form.created_at
  return time ? formatDetailTime(time) : '-'
})

// 处理复选框点击
const handleCheckboxClick = (checked: boolean) => {
  if (!props.form?.id) {
    console.warn('FormCard: 无效的表单ID，无法选择')
    return
  }
  emit('select', props.form.id, checked)
}

// 安全的图标获取
const safeGetIconComponent = (iconName?: string) => {
  try {
    return getIconComponent(iconName)
  } catch (error) {
    console.warn('FormCard: 获取图标组件失败，使用默认图标', error)
    return getIconComponent()
  }
}
</script>

<style lang="scss" scoped>
// CSS 变量定义
.form-card {
  --form-card-width: 350px;
  --form-card-height: 250px;
  --form-card-border-radius: 12px;
  --form-card-border-color: #e5e7eb;
  --form-card-hover-border-color: #d1d5db;
  --form-card-selected-border-color: #3b82f6;
  --form-card-transition: all 0.2s ease;
  --form-card-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.08);
  --form-card-shadow-selected: 0 0 0 2px rgba(59, 130, 246, 0.1);
  
  position: relative;
  background: white;
  border-radius: var(--form-card-border-radius);
  border: 1px solid var(--form-card-border-color);
  transition: var(--form-card-transition);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: var(--form-card-width);
  height: var(--form-card-height);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--form-card-shadow-hover);
    border-color: var(--form-card-hover-border-color);
  }
  
  &.is-selected {
    border-color: var(--form-card-selected-border-color);
    box-shadow: var(--form-card-shadow-selected);
  }
  
  &.is-duplicating,
  &.is-deleting {
    opacity: 0.6;
    pointer-events: none;
  }
}

.form-checkbox-container {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  padding: 6px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  // 确保复选框样式正确
  :deep(.form-card-checkbox) {
    .n-checkbox-box {
      border: 2px solid #d1d5db;
      border-radius: 4px;
      background: white;
      transition: all 0.2s ease;
    }
    
    .n-checkbox-box__border {
      border: none;
    }
    
    .n-checkbox-box__check {
      color: white;
    }
    
    &.n-checkbox--checked {
      .n-checkbox-box {
        background: #3b82f6;
        border-color: #3b82f6;
      }
    }
    
    &:hover .n-checkbox-box {
      border-color: #9ca3af;
    }
    
    &.n-checkbox--checked:hover .n-checkbox-box {
      background: #2563eb;
      border-color: #2563eb;
    }
  }
}

.form-card-body {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-card-content {
  display: flex;
  gap: 16px;
  flex: 1;
}

.form-icon-container {
  flex-shrink: 0;
}

.form-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
  
  .icon {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

.form-info {
  flex: 1;
  min-width: 0;
}

.form-header {
  margin-bottom: 8px;
}

.form-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-description {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 60px; // 固定高度，确保描述区域一致
}

.form-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 12px;
  margin-bottom: 0;
}

.edit-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
  
  .time-icon {
    font-size: 12px;
  }
}

.version-tag {
  background: #dcfce7 !important;
  color: #166534 !important;
  border: none !important;
}

.form-status {
  margin-left: auto;
}

.form-actions {
  padding: 16px 20px;
  background: #f9fafb;
  border-top: 1px solid #f3f4f6;
  margin-top: auto; // 确保操作区域始终在底部
}

.actions-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.more-button {
  padding: 6px;
  border-radius: 6px;
  
  &:hover {
    background: #f3f4f6;
  }
}

.unpublished-fill-btn {
  .warning-icon {
    margin-left: 4px;
    color: #f59e0b;
  }
}

// 高亮新创建的表单
.highlight-new-form {
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-card-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .form-icon-container {
    align-self: flex-start;
  }
  
  .actions-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
</style>