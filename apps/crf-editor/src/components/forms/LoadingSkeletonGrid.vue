<template>
  <div class="forms-grid-container">
    <div class="forms-grid">
      <div v-for="n in skeletonCount" :key="n" class="form-card form-card-skeleton">
        <!-- 复选框占位 -->
        <div class="skeleton-checkbox">
          <n-skeleton circle :width="20" :height="20" />
        </div>
        
        <div class="form-card-body">
          <div class="form-card-content">
            <div class="form-icon-container">
              <n-skeleton circle :width="48" :height="48" class="skeleton-circle-line" />
            </div>
            <div class="form-info">
              <div class="form-header">
                <n-skeleton :width="randomWidth(120, 180)" :height="20" class="skeleton-line" />
                <n-skeleton :width="randomWidth(60, 80)" :height="24" class="skeleton-line" />
              </div>
              <n-skeleton :width="randomWidth(200, 250)" :height="14" style="margin-top: 8px" class="skeleton-line" />
              <n-skeleton :width="randomWidth(150, 200)" :height="14" style="margin-top: 6px" class="skeleton-line" />
            </div>
          </div>
          <div class="form-meta">
            <n-skeleton :width="randomWidth(120, 150)" :height="12" class="skeleton-line" />
            <n-skeleton :width="randomWidth(50, 70)" :height="20" class="skeleton-line" />
          </div>
        </div>
        <div class="form-actions">
          <div class="actions-row">
            <div class="action-buttons">
              <n-skeleton :width="randomWidth(48, 60)" :height="28" class="skeleton-line" />
              <n-skeleton :width="randomWidth(48, 60)" :height="28" class="skeleton-line" />
              <n-skeleton :width="randomWidth(48, 60)" :height="28" class="skeleton-line" />
            </div>
            <n-skeleton circle :width="28" :height="28" class="skeleton-circle-line" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NSkeleton } from 'naive-ui'

interface Props {
  skeletonCount?: number
}

withDefaults(defineProps<Props>(), {
  skeletonCount: 6
})

// 随机宽度生成函数
const randomWidth = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min + 'px'
}
</script>

<style lang="scss" scoped>
.forms-grid-container {
  margin-top: 24px;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  padding: 4px;
}

.form-card-skeleton {
  position: relative;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  min-height: 200px;
  
  .skeleton-checkbox {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 2;
  }
  
  .form-card-body {
    padding: 20px;
  }
  
  .form-card-content {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
  }
  
  .form-icon-container {
    flex-shrink: 0;
  }
  
  .form-info {
    flex: 1;
    min-width: 0;
  }
  
  .form-header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .form-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
  }
  
  .form-actions {
    padding: 16px 20px;
    background: #f9fafb;
    border-top: 1px solid #f3f4f6;
  }
  
  .actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
  }
  
  .skeleton-line {
    border-radius: 4px;
  }
  
  .skeleton-circle-line {
    border-radius: 50%;
  }
}

// 骨架屏动画
.form-card-skeleton {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
</style>