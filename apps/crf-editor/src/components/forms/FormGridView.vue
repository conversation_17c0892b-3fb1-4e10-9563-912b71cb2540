<template>
  <div class="forms-grid">
    <TransitionGroup
      name="form-card-fade"
      tag="div"
      class="forms-grid-inner"
      appear
    >
      <FormCard
        v-for="form in forms"
        :key="`form-${form.id}`"
        :form="form"
        :is-selected="selectedFormIds.has(form.id)"
        :is-duplicating="duplicatingFormIds.has(form.id)"
        :is-deleting="deletingFormIds.has(form.id)"
        :loading="loading"
        :creating="creating"
        @select="(formId, checked) => $emit('select', formId, checked)"
        @edit="$emit('edit', $event)"
        @fill="$emit('fill', $event)"
        @view-data="$emit('view-data', $event)"
        @command="(cmd, form) => $emit('command', cmd, form)"
      />
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import FormCard from './FormCard.vue'

interface FormItem {
  id: string
  [key: string]: any
}

interface Props {
  forms: FormItem[]
  selectedFormIds: Set<string>
  duplicatingFormIds: Set<string>
  deletingFormIds: Set<string>
  loading?: boolean
  creating?: boolean
}

defineProps<Props>()

defineEmits<{
  select: [formId: string, checked: boolean]
  edit: [form: FormItem]
  fill: [form: FormItem]
  'view-data': [form: FormItem]
  command: [command: string, form: FormItem]
}>()
</script>

<style lang="scss" scoped>
.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 350px);
  gap: 20px;
  padding: 4px;
  justify-content: start;
}

.forms-grid-inner {
  display: contents;
}

// 表单卡片过渡动画
.form-card-fade-enter-active,
.form-card-fade-leave-active {
  transition: all 0.3s ease;
}

.form-card-fade-enter-from,
.form-card-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.form-card-fade-move {
  transition: transform 0.3s ease;
}
</style>