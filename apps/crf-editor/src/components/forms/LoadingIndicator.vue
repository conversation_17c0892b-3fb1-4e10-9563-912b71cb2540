<template>
  <Transition name="loading-indicator">
    <div v-if="visible" class="top-loading-indicator">
      <n-icon class="is-loading"><Loading /></n-icon>
      <span>{{ message }}</span>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { NIcon } from 'naive-ui'
import { RefreshOutline as Loading } from '@vicons/ionicons5'

interface Props {
  visible: boolean
  message?: string
}

withDefaults(defineProps<Props>(), {
  message: '正在更新...'
})
</script>

<style lang="scss" scoped>
.top-loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  color: #0369a1;
  font-size: 14px;
  font-weight: 500;
  
  .is-loading {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 加载指示器过渡动画
.loading-indicator-enter-active,
.loading-indicator-leave-active {
  transition: all 0.3s ease;
}

.loading-indicator-enter-from,
.loading-indicator-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>