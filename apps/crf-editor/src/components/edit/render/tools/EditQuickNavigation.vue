<template>
  <div v-if="show" class="quick-nav-container" :class="{ 'preview-mode': previewMode }">
    <!-- 目录面板 -->
    <div v-if="showPanel" class="nav-panel">
      <div class="panel-header">
        <h3>文档目录</h3>
        <button class="close-btn" @click="closePanel">
          <crf-icon icon="material-symbols:close" size="20" />
        </button>
      </div>
      <div class="panel-body">
        <div class="section-tree">
          <edit-quick-nav-item
            v-for="(section, index) in sectionTree"
            :key="section.id"
            :section="section"
            :active-section-id="activeSectionId"
            :section-number="sectionNumbers.get(section.id) || `${index + 1}`"
            :section-number-map="sectionNumbers"
            @scroll-to-section="handleScrollToSection"
          />
        </div>
      </div>
    </div>

    <!-- 浮动按钮 -->
    <div class="quick-nav-fab" @click="togglePanel">
      <crf-icon icon="material-symbols:menu-book" size="20" />
<!--      <span class="fab-text">目录</span>-->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { CrfIcon } from '@crf/components'
import EditQuickNavItem from './EditQuickNavItem.vue'

interface FormSection {
  id: string
  name: string
  blocks: Record<string, unknown>[]
  level?: number
  parentId?: string
  children?: FormSection[]
  order?: number
}

const props = defineProps<{
  show: boolean
  sections: FormSection[]
  activeSectionId: string
  previewMode?: boolean
}>()

const emit = defineEmits<{
  'scroll-to-section': [sectionId: string]
}>()

const sectionTree = computed(() => {
  // 数据本身已经是层级结构，直接使用
  return props.sections;
});

// 为了生成正确的编号，我们需要扁平化的sections列表
// const flatSections = computed(() => {
//   const flatten = (sections: FormSection[]): FormSection[] => {
//     const result: FormSection[] = [];
//     sections.forEach(section => {
//       result.push(section);
//       if (section.children && section.children.length > 0) {
//         result.push(...flatten(section.children));
//       }
//     });
//     return result;
//   };
//   return flatten(props.sections);
// });

// 生成章节编号映射
const sectionNumbers = computed(() => {
  const numbers = new Map<string, string>();
  const generateNumbers = (sections: FormSection[], prefix = '') => {
    sections.forEach((section, index) => {
      const currentNumber = prefix ? `${prefix}.${index + 1}` : `${index + 1}`;
      numbers.set(section.id, currentNumber);
      if (section.children && section.children.length > 0) {
        generateNumbers(section.children, currentNumber);
      }
    });
  };
  generateNumbers(props.sections);
  return numbers;
});

const showPanel = ref(false)

const togglePanel = () => {
  showPanel.value = !showPanel.value
}

const closePanel = () => {
  showPanel.value = false
}

const handleScrollToSection = (sectionId: string) => {
  emit('scroll-to-section', sectionId)
  closePanel() // 点击章节后关闭面板
}
</script>

<style lang="scss" scoped>
.quick-nav-container {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

// 目录面板
.nav-panel {
  background: white;
  border-radius: 10px;
  min-width: 260px;
  max-width: 400px;
  width: max-content;
  max-height: 450px;
  margin-bottom: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  animation: panelSlideUp 0.3s ease-out;
  transform-origin: bottom left;
}

@keyframes panelSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  background: #fafbfc;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
  }
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
}

.panel-body {
  max-height: 380px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f8fafc;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;

    &:hover {
      background: #94a3b8;
    }
  }
}

.section-tree {
  padding: 8px 12px;
}

// 右下角浮动按钮
.quick-nav-fab {
  background: #3b82f6;
  color: white;
  border-radius: 50px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(59, 130, 246, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.fab-text {
  white-space: nowrap;
}

// 预览模式样式调整
.quick-nav-container.preview-mode {
  .quick-nav-fab {
    background: #10b981; // 预览模式下使用绿色
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
    
    &:hover {
      background: #059669;
      box-shadow: 0 6px 25px rgba(16, 185, 129, 0.4);
    }
  }
  
  .nav-panel {
    .panel-header {
      background: #f0fdf4; // 预览模式下使用绿色主题
      
      h3 {
        color: #065f46;
      }
    }
  }
}
</style>
