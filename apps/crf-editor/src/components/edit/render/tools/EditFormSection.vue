<template>
  <div ref="formSectionEl"
       :data-section-id="section?.id" :data-section-name="section?.name"
       :data-section-level="section?.level"
       class="form-section">
    <!-- 章节连接线 - 仅在同名章节之间显示 -->
    <div v-if="showConnector" class="section-connector" :class="`tag-level-${section?.level || 0}`" :style="connectorStyle"></div>
    
    <!-- 分组头部 -->
    <div class="section-divider">
      <div class="section-title-row" :style="sectionStyle">
        <div class="section-title-container">
          <template v-if="editing && !previewMode">
            <span :class="['section-number-tag', tagColorClass]">{{ sectionNumber }}</span>
            <input
              ref="nameInput"
              :value="section?.name"
              :class="['section-name-input', inputColorClass]"
              @blur="handleSaveName"
              @focus="($event.target as HTMLInputElement).select()"
              @keyup.enter="handleSaveName"
            />
          </template>
          <template v-else>
            <span :class="['section-number-tag', tagColorClass]">{{ sectionNumber }}</span>
            <div
              :class="[
                'section-title-area',
                { 'animating': props.animating }
              ]"
            >
                              <h3
                  :class="['section-title', titleColorClass]"
                  @dblclick="!previewMode ? handleStartEdit : null"
                  @mouseenter="!previewMode ? (showDeleteBtn = true) : null"
                  @mouseleave="!previewMode ? (showDeleteBtn = false) : null"
                >
                  {{ section?.name }}
                </h3>
              <!-- 编辑按钮 - 预览模式下隐藏 -->
              <button
                v-if="!previewMode"
                class="edit-section-btn"
                title="编辑分组名称"
                @click.stop="handleStartEdit"
              >
                <crf-icon icon="material-symbols:edit-outline" size="16" />
              </button>
              <!-- 添加子章节按钮 - 预览模式下隐藏 -->
              <button
                v-if="!previewMode"
                class="add-subsection-btn"
                title="新建子章节"
                @click.stop="handleAddSubSection"
              >
                <crf-icon icon="material-symbols:add-circle-outline-rounded" size="16" />
              </button>
              <!-- 删除按钮 - 预览模式下隐藏 -->
              <button
                v-if="canDelete && !previewMode"
                :class="['delete-section-btn', { 'show': showDeleteBtn }]"
                title="删除分组"
                @click.stop="handleDelete"
              >
                <crf-icon icon="material-symbols:delete-outline" size="16" />
              </button>
            </div>
          </template>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="divider-line"></div>
    </div>

    <!-- 分组拖拽区域 -->
    <div class="section-content">
      <edit-render-drag
        :group="dragGroup"
        :list="section?.blocks"
        :section-id="section?.id"
        class="section-drop-zone"
        @update:list="handleListUpdate"
      />
      
      <!-- 空状态提示 -->
      <div v-if="!section?.blocks || section?.blocks.length === 0" class="drop-zone-hint">
        <div class="hint-text">
          <h4>拖拽组件到此处</h4>
          <p>从左侧物料区拖拽组件到此区域，或双击组件快速添加</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, nextTick, computed, onUpdated, watch } from 'vue'
import { useDialog } from 'naive-ui'
import { CrfIcon } from '@crf/components'
import { dragGroup } from '@/components/edit/nested.ts'
import EditRenderDrag from '../EditRenderDrag.vue'

interface FormSection {
  id: string
  name: string
  blocks: Record<string, unknown>[]
  level: number
}

const props = defineProps<{
  section: FormSection
  number: string
  editing: boolean
  animating: boolean
  canDelete: boolean
  previewMode?: boolean
}>()

const section = computed(() => props.section)
const previewMode = computed(() => props.previewMode || false)

// 添加dialog实例
const dialog = useDialog()



const emit = defineEmits<{
  (e: 'start-edit', id: string): void
  (e: 'save-name', payload: { id: string; name: string }): void
  (e: 'delete', id: string): void
  (e: 'add-subsection', parentId: string): void
  (e: 'update:section', section: FormSection): void
}>()

// 明确定义变量和函数类型

// 定义响应式变量
const showDeleteBtn = ref<boolean>(false)
const nameInput = ref<HTMLInputElement | null>(null)
const formSectionEl = ref<HTMLElement | null>(null)

// 计算样式属性
const sectionStyle = computed<{ marginLeft: string }>(() => ({
  // 所有章节标题都靠近最左侧
  marginLeft: '0px'
}))

// 判断是否显示连接线
const showConnector = ref<boolean>(false);

// 更新连接线状态
const updateConnectorStatus = () => {
  try {
    if (!props.section) return;
    
    // 获取章节名称和层级
    const sectionName = props.section.name;
    const sectionLevel = props.section.level || 0;
    const sectionId = props.section.id;
    
    // 只有非顶级章节才可能需要连接线
    if (sectionLevel === 0 || !sectionName || sectionName.trim() === '') {
      showConnector.value = false;
      return;
    }
    
    // 查找同名章节
    const allSections = document.querySelectorAll('.form-section');
    if (!allSections || allSections.length === 0) {
      // DOM可能还没有完全渲染
      showConnector.value = false;
      return;
    }
    
    // 收集所有相同名称和层级的章节ID
    const sameSections = [];
    for (const section of allSections) {
      const name = section.getAttribute('data-section-name');
      const level = parseInt(section.getAttribute('data-section-level') || '0');
      const id = section.getAttribute('data-section-id');
      
      // 只收集相同名称和层级的章节
      if (name === sectionName && level === sectionLevel && id) {
        sameSections.push(id);
      }
    }
    
    // 如果只有一个章节，不显示连接线
    if (sameSections.length <= 1) {
      showConnector.value = false;
      return;
    }
    
    // 找到当前章节在列表中的位置
    const currentIndex = sameSections.indexOf(sectionId);
    
    // 如果不是第一个章节，则显示连接线
    showConnector.value = currentIndex > 0;
  } catch (error) {
    console.error('更新连接线状态时出错:', error);
    showConnector.value = false;
  }
};

// 连接线样式
const connectorStyle = computed<{ [key: string]: string }>(() => {
  // 连接线样式不需要根据章节级别调整
  return {
    height: 'calc(100% + 48px)', // 连接到下一个章节
    top: '-24px', // 从上一个章节连接过来
    left: '11px', // 对齐章节标签
    opacity: '0.6', // 降低不透明度，使其不那么显眼
    borderLeftWidth: '2px', // 稍微粗一点的线
    zIndex: '1' // 确保在内容之下
  };
});

// 在组件挂载后和更新后检查连接线状态
onMounted(() => {
  // 延迟执行，确保所有DOM元素都已渲染完成
  setTimeout(() => {
    updateConnectorStatus();
  }, 300);
});

onUpdated(() => {
  // 延迟执行，确保所有DOM元素都已渲染完成
  setTimeout(() => {
    updateConnectorStatus();
  }, 100);
});

// 监听章节名称变化，更新连接线状态
watch(() => props.section?.name, () => {
  nextTick(() => {
    updateConnectorStatus();
  });
});

// 监听章节层级变化，更新连接线状态
watch(() => props.section?.level, () => {
  nextTick(() => {
    updateConnectorStatus();
  });
});

// 计算章节编号
const sectionNumber = computed<string>(() => props.number);

// 根据章节级别设置不同的标签颜色
const tagColorClass = computed<string>(() => {
  const level = props.section?.level || 0;
  switch (level) {
    case 0: return 'tag-level-0'; // 顶级章节 - 蓝色
    case 1: return 'tag-level-1'; // 一级子章节 - 绿色
    case 2: return 'tag-level-2'; // 二级子章节 - 紫色
    case 3: return 'tag-level-3'; // 三级子章节 - 橙色
    default: return 'tag-level-4'; // 更深层级 - 红色
  }
});

// 根据章节级别设置不同的标题颜色
const titleColorClass = computed<string>(() => {
  const level = props.section?.level || 0;
  switch (level) {
    case 0: return 'title-level-0'; // 顶级章节 - 蓝色
    case 1: return 'title-level-1'; // 一级子章节 - 绿色
    case 2: return 'title-level-2'; // 二级子章节 - 紫色
    case 3: return 'title-level-3'; // 三级子章节 - 橙色
    default: return 'title-level-4'; // 更深层级 - 红色
  }
});

// 根据章节级别设置不同的输入框颜色
const inputColorClass = computed<string>(() => {
  const level = props.section?.level || 0;
  switch (level) {
    case 0: return 'input-level-0'; // 顶级章节 - 蓝色
    case 1: return 'input-level-1'; // 一级子章节 - 绿色
    case 2: return 'input-level-2'; // 二级子章节 - 紫色
    case 3: return 'input-level-3'; // 三级子章节 - 橙色
    default: return 'input-level-4'; // 更深层级 - 红色
  }
});

// 暴露根元素给父组件
defineExpose({
  $el: formSectionEl
})

// 保存章节名称
const handleSaveName = (event: Event): void => {
  const target = event.target as HTMLInputElement
  if (props.section) {
    emit('save-name', { id: props.section.id, name: target.value })
  }
}

// 开始编辑章节名称
const handleStartEdit = (): void => {
  if (props.section) {
    emit('start-edit', props.section.id)
    
    // 等待DOM更新后，将光标定位到文本末尾
    nextTick(() => {
      if (nameInput.value) {
        nameInput.value.focus()
        // 将光标定位到文本末尾
        const length = nameInput.value.value.length
        nameInput.value.setSelectionRange(length, length)
      }
    })
  }
}

// 删除章节
const handleDelete = async (): Promise<void> => {
  if (!props.section) return
  
  try {
    await new Promise<void>((resolve, reject) => {
      dialog.warning({
        title: '删除确认',
        content: `确定要删除分组"${props.section.name}"吗？此操作不可撤销。`,
        positiveText: '确定删除',
        negativeText: '取消',
        onPositiveClick: () => {
          resolve()
        },
        onNegativeClick: () => {
          reject('cancel')
        }
      })
    })
    emit('delete', props.section.id)
  } catch {
    // 用户取消删除，不需要处理
  }
}

// 添加子章节
const handleAddSubSection = (): void => {
  if (props.section) {
    emit('add-subsection', props.section.id)
  }
}

// 处理列表更新事件
const handleListUpdate = (event: Record<string, unknown>[]): void => {
  if (props.section) {
    // 创建更新后的章节
    const updatedSection: FormSection = {
      ...props.section,
      blocks: event
    }
    emit('update:section', updatedSection)
  }
}



// 处理点击外部事件
const handleClickOutside = (event: MouseEvent): void => {
  if (props.editing && nameInput.value && !nameInput.value.contains(event.target as Node)) {
    handleSaveName({ target: nameInput.value } as unknown as Event)
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 24px;  // 减少章节间距：从48px改为24px
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

// 章节连接线
.section-connector {
  position: absolute;
  left: 0;
  border-left: 2px dashed;
  z-index: 1;
  opacity: 0.6;
  pointer-events: none; // 避免干扰鼠标事件
  transition: opacity 0.3s ease;
  
  // 不同级别的连接线颜色
  &.tag-level-0 {
    border-left-color: #3b82f6; // 蓝色
  }
  
  &.tag-level-1 {
    border-left-color: #10b981; // 绿色
  }
  
  &.tag-level-2 {
    border-left-color: #8b5cf6; // 紫色
  }
  
  &.tag-level-3 {
    border-left-color: #f97316; // 橙色
  }
  
  &.tag-level-4 {
    border-left-color: #ef4444; // 红色
  }
  
  // 当鼠标悬停在章节上时，使连接线更明显
  .form-section:hover & {
    opacity: 0.8;
  }
}

.section-divider {
  margin-bottom: 16px;  // 减少章节标题与内容的间距：从24px改为16px
}

.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;  // 减少标题行的下边距：从12px改为8px
}

.section-title-container {
  display: flex;
  align-items: center;
  gap: 10px;

  &:hover .section-number-tag {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
    color: #2563eb;
  }
}

.section-title-area {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(243, 244, 246, 0.3);  // 减少悬浮背景的透明度
  }

  // 动画状态样式
  &.animating {
    animation: simpleHighlight 0.8s ease-out;
    
    // 根据不同级别使用不同的标题动画
    .title-level-0 {
      animation: simpleTitleBlue 0.8s ease-out;
    }
    
    .title-level-1 {
      animation: simpleTitleGreen 0.8s ease-out;
    }
    
    .title-level-2 {
      animation: simpleTitlePurple 0.8s ease-out;
    }
    
    .title-level-3 {
      animation: simpleTitleOrange 0.8s ease-out;
    }
    
    .title-level-4 {
      animation: simpleTitleRed 0.8s ease-out;
    }
    
    // 根据不同级别使用不同的标签动画
    .tag-level-0 {
      animation: simpleTagBlue 0.8s ease-out;
    }
    
    .tag-level-1 {
      animation: simpleTagGreen 0.8s ease-out;
    }
    
    .tag-level-2 {
      animation: simpleTagPurple 0.8s ease-out;
    }
    
    .tag-level-3 {
      animation: simpleTagOrange 0.8s ease-out;
    }
    
    .tag-level-4 {
      animation: simpleTagRed 0.8s ease-out;
    }
  }
}

.section-number-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 22px;
  height: 20px;
  padding: 0 6px;
  font-size: 11px;
  font-weight: 600;
  border-radius: 3px;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

// 顶级章节 - 蓝色
.tag-level-0 {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
  
  &:hover {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
    color: #2563eb;
  }
}

// 一级子章节 - 绿色
.tag-level-1 {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
  
  &:hover {
    background: rgba(16, 185, 129, 0.15);
    border-color: rgba(16, 185, 129, 0.3);
    color: #059669;
  }
}

// 二级子章节 - 紫色
.tag-level-2 {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.2);
  
  &:hover {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
    color: #7c3aed;
  }
}

// 三级子章节 - 橙色
.tag-level-3 {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
  border: 1px solid rgba(249, 115, 22, 0.2);
  
  &:hover {
    background: rgba(249, 115, 22, 0.15);
    border-color: rgba(249, 115, 22, 0.3);
    color: #ea580c;
  }
}

// 更深层级 - 红色
.tag-level-4 {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
  
  &:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  cursor: pointer;
  transition: color 0.2s ease;
}

// 顶级章节标题 - 蓝色，大字体
.title-level-0 {
  color: #3b82f6;
  font-size: 20px;
  font-weight: 600;
  
  &:hover {
    color: #2563eb;
  }
}

// 一级子章节标题 - 绿色，中等字体
.title-level-1 {
  color: #10b981;
  font-size: 18px;
  font-weight: 500;
  
  &:hover {
    color: #059669;
  }
}

// 二级子章节标题 - 紫色，较小字体
.title-level-2 {
  color: #8b5cf6;
  font-size: 16px;
  font-weight: 500;
  
  &:hover {
    color: #7c3aed;
  }
}

// 三级子章节标题 - 橙色，小字体
.title-level-3 {
  color: #f97316;
  font-size: 14px;
  font-weight: 500;
  
  &:hover {
    color: #ea580c;
  }
}

// 更深层级标题 - 红色，最小字体
.title-level-4 {
  color: #ef4444;
  font-size: 12px;
  font-weight: 500;
  
  &:hover {
    color: #dc2626;
  }
}

.section-name-input {
  font-size: 18px;
  font-weight: 600;
  padding: 6px 12px;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  background: white;
  outline: none;
  transition: all 0.2s ease;
}

// 蓝色输入框
.input-level-0 {
  color: #3b82f6;
  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
}

// 绿色输入框
.input-level-1 {
  color: #10b981;
  &:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }
}

// 紫色输入框
.input-level-2 {
  color: #8b5cf6;
  &:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }
}

// 橙色输入框
.input-level-3 {
  color: #f97316;
  &:focus {
    border-color: #f97316;
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
  }
}

// 红色输入框
.input-level-4 {
  color: #ef4444;
  &:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }
}

.add-subsection-btn,
.edit-section-btn,
.delete-section-btn {
  padding: 6px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.1s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  opacity: 0;
}

.add-subsection-btn:hover,
.edit-section-btn:hover {
  background: #f3f4f6;
  color: #3b82f6;
  opacity: 1;
}

.delete-section-btn {
  color: #ef4444;
  opacity: 0;

  &.show {
    opacity: 1;
  }

  &:hover {
    background: #fef2f2;
    color: #dc2626;
    opacity: 1;
  }
}

// 当悬浮在整个标题区域时显示所有按钮
.section-title-row:hover {
  .edit-section-btn {
    opacity: 1;
  }
  
  .delete-section-btn {
    opacity: 1;
  }
  
  .add-subsection-btn {
    opacity: 1;
  }
}

.divider-line {
  height: 1px;
  border: 1px dashed #e8e8e8;  // 更浅的分割线颜色
  position: relative;
  opacity: 0.6;  // 降低分割线的透明度
}

.section-content {
  position: relative;
  min-height: 120px;
  padding: 16px;

  .section-drop-zone {
    min-height: 80px;
    border-radius: 8px;
    background: #fff;
    transition: all 0.3s ease;
    
    &:empty {
      background: #f1f5f9;
      border: 1px dashed #cbd5e1;
    }
  }

  .drop-zone-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    pointer-events: none;
    z-index: 1;
    
    .hint-icon {
      color: #94a3b8;
      margin-bottom: 12px;
      animation: gentleFloat 3s ease-in-out infinite;
    }
    
    .hint-text {
      h4 {
        margin: 0 0 4px;
        color: #334155;
        font-size: 14px;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: #64748b;
        font-size: 13px;
        line-height: 1.5;
        max-width: 600px;
      }
    }
  }
}

// 蓝色标题动画
@keyframes simpleTitleBlue {
  0% {
    color: #3b82f6;
  }
  50% {
    color: #2563eb;
    font-weight: 700;
  }
  100% {
    color: #3b82f6;
    font-weight: 600;
  }
}

// 绿色标题动画
@keyframes simpleTitleGreen {
  0% {
    color: #10b981;
  }
  50% {
    color: #059669;
    font-weight: 700;
  }
  100% {
    color: #10b981;
    font-weight: 600;
  }
}

// 紫色标题动画
@keyframes simpleTitlePurple {
  0% {
    color: #8b5cf6;
  }
  50% {
    color: #7c3aed;
    font-weight: 700;
  }
  100% {
    color: #8b5cf6;
    font-weight: 600;
  }
}

// 橙色标题动画
@keyframes simpleTitleOrange {
  0% {
    color: #f97316;
  }
  50% {
    color: #ea580c;
    font-weight: 700;
  }
  100% {
    color: #f97316;
    font-weight: 600;
  }
}

// 红色标题动画
@keyframes simpleTitleRed {
  0% {
    color: #ef4444;
  }
  50% {
    color: #dc2626;
    font-weight: 700;
  }
  100% {
    color: #ef4444;
    font-weight: 600;
  }
}

// 蓝色标签动画
@keyframes simpleTagBlue {
  0% {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    transform: scale(1);
  }
  50% {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    transform: scale(1.1);
  }
  100% {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    transform: scale(1);
  }
}

// 绿色标签动画
@keyframes simpleTagGreen {
  0% {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
    transform: scale(1);
  }
  50% {
    background: #10b981;
    border-color: #10b981;
    color: white;
    transform: scale(1.1);
  }
  100% {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
    transform: scale(1);
  }
}

// 紫色标签动画
@keyframes simpleTagPurple {
  0% {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    transform: scale(1);
  }
  50% {
    background: #8b5cf6;
    border-color: #8b5cf6;
    color: white;
    transform: scale(1.1);
  }
  100% {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    transform: scale(1);
  }
}

// 橙色标签动画
@keyframes simpleTagOrange {
  0% {
    background: rgba(249, 115, 22, 0.1);
    border-color: rgba(249, 115, 22, 0.2);
    color: #f97316;
    transform: scale(1);
  }
  50% {
    background: #f97316;
    border-color: #f97316;
    color: white;
    transform: scale(1.1);
  }
  100% {
    background: rgba(249, 115, 22, 0.1);
    border-color: rgba(249, 115, 22, 0.2);
    color: #f97316;
    transform: scale(1);
  }
}

// 红色标签动画
@keyframes simpleTagRed {
  0% {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    transform: scale(1);
  }
  50% {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transform: scale(1.1);
  }
  100% {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    transform: scale(1);
  }
}

// 简洁主体动画
@keyframes simpleHighlight {
  0% {
    background-color: transparent;
    transform: scale(1);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1.01);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: none;
  }
}

// 空状态提示样式
.empty-section-hint {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  pointer-events: none;
  border-radius: 8px;
  background: #f1f5f9;
  border: 1px dashed #cbd5e1;
  transition: all 0.3s ease-in-out;
  opacity: 0.5;

  .empty-hint-content {
    text-align: center;
    padding: 20px;
    opacity: 0.8;
    transition: all 0.3s ease;
    
    .empty-hint-icon {
      margin-bottom: 12px;
      color: #94a3b8;
      animation: gentleFloat 3s ease-in-out infinite;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }
    
    .empty-hint-text {
      h4 {
        margin: 0 0 6px 0;
        color: #334155;
        font-size: 15px;
        font-weight: 600;
        letter-spacing: -0.025em;
      }
      
      p {
        margin: 0;
        color: #64748b;
        font-size: 13px;
        line-height: 1.5;
        max-width: 200px;
        font-weight: 400;
      }
    }
  }
}

// 拖拽提示动画
@keyframes dragHint {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-3px);
  }
  75% {
    transform: translateX(3px);
  }
}

// 改进章节内容区域的拖拽反馈
.section-content {
  .section-drop-zone {
    min-height: 80px;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
    
    &.drag-over {
      background: rgba(59, 130, 246, 0.05);
      border: 2px dashed #3b82f6;
      transform: scale(1.005);
      
      // 隐藏空状态提示
      + .empty-section-hint {
        opacity: 0;
        transform: scale(0.95);
      }
    }
    
    // 当拖拽进入时的特殊样式
    &.dragging-over {
      .empty-section-hint {
        opacity: 0.3;
        transform: scale(0.9);
      }
    }
  }
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}
</style>