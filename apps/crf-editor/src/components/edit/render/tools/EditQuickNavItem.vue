<template>
  <div class="quick-nav-item-container">
    <div :class="['quick-nav-item', `level-${section.level || 0}`]" @click="scrollToSection">
      <button v-if="hasChildren" class="expand-btn" @click.stop="toggleExpand">
        <crf-icon :icon="isExpanded ? 'material-symbols:expand-more' : 'material-symbols:chevron-right'" size="16" />
      </button>
      <div v-else class="item-indent"></div>
      <div
        :class="[
          'quick-nav-dot',
          { 'active': activeSectionId === section.id }
        ]"
      >
        {{ sectionNumber }}
      </div>
      <div class="section-title" :class="{ 'active': activeSectionId === section.id }">
        {{ section.name }}
      </div>
    </div>
    <div 
      v-if="hasChildren" 
      :class="[
        'children-container',
        { 
          'expanding': isExpanding,
          'collapsing': isCollapsing
        }
      ]"
      :style="{ maxHeight: isExpanded ? '500px' : '0px' }"
    >
      <template v-if="sectionNumberMap">
        <edit-quick-nav-item
          v-for="child in section.children"
          :key="child.id"
          :section="child"
          :active-section-id="activeSectionId"
          :section-number="getChildSectionNumber(child)"
          :section-number-map="sectionNumberMap"
          @scroll-to-section="$emit('scroll-to-section', $event)"
        />
      </template>
      <template v-else>
        <edit-quick-nav-item
          v-for="child in section.children"
          :key="child.id"
          :section="child"
          :active-section-id="activeSectionId"
          :section-number="getChildSectionNumber(child)"
          @scroll-to-section="$emit('scroll-to-section', $event)"
        />
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { CrfIcon } from '@crf/components'

interface FormSection {
  id: string
  name: string
  blocks: Record<string, unknown>[]
  level?: number
  parentId?: string
  children?: FormSection[]
  order?: number
}

const props = defineProps<{
  section: FormSection
  activeSectionId: string
  sectionNumber: string
  sectionNumberMap?: Map<string, string>
}>()

const emit = defineEmits<{
  'scroll-to-section': [sectionId: string]
}>()

// 默认展开所有节点
const isExpanded = ref(true)
const isExpanding = ref(false)
const isCollapsing = ref(false)

const hasChildren = computed(() => props.section.children && props.section.children.length > 0)

const toggleExpand = () => {
  if (isExpanded.value) {
    // 收起动画
    isCollapsing.value = true
    setTimeout(() => {
      isExpanded.value = false
      isCollapsing.value = false
    }, 300)
  } else {
    // 展开动画
    isExpanded.value = true
    isExpanding.value = true
    setTimeout(() => {
      isExpanding.value = false
    }, 300)
  }
}

const scrollToSection = () => {
  emit('scroll-to-section', props.section.id)
}

const getChildSectionNumber = (child: FormSection) => {
  // 使用父组件传递的编号映射，如果没有则使用fallback逻辑
  if (props.sectionNumberMap) {
    return props.sectionNumberMap.get(child.id) || '';
  }
  
  // Fallback逻辑
  const childIndex = props.section.children?.findIndex(c => c.id === child.id) ?? -1;
  if (childIndex !== -1) {
    return `${props.sectionNumber}.${childIndex + 1}`;
  }
  return '';
};
</script>

<style lang="scss" scoped>
.quick-nav-item-container {
  display: flex;
  flex-direction: column;
}

.quick-nav-item {
  display: flex;
  align-items: center;
  position: relative;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease;
  
  &:hover {
    background: #f8fafc;
  }
  
  // 所有层级都使用相同的样式，不再有递增边距
  .section-title { 
    font-size: 11px;
    font-weight: 500;
  }
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 1px;
  margin-right: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  transition: all 0.3s ease;
  border-radius: 50%;
  flex-shrink: 0;
  width: 16px;
  height: 16px;

  &:hover {
    color: #334155;
    background: #e2e8f0;
    transform: scale(1.1);
  }
}

.item-indent {
  width: 16px;
  margin-right: 3px;
  flex-shrink: 0;
}

.quick-nav-dot {
  min-width: 16px;
  height: 16px;
  padding: 0 3px;
  border-radius: 2px;
  background: #f1f5f9;
  color: #64748b;
  font-size: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  flex-shrink: 0;
  border: 1px solid #e2e8f0;
  transition: all 0.15s ease;
  white-space: nowrap;

  &.active {
    color: white;
    border-color: transparent;
  }

  // 未选中时使用统一的灰色
  &:not(.active) {
    background: #f1f5f9;
    color: #64748b;
  }
}

// 选中时根据层级使用不同颜色
.quick-nav-item.level-0 .quick-nav-dot.active { background: #3b82f6; }
.quick-nav-item.level-1 .quick-nav-dot.active { background: #10b981; }
.quick-nav-item.level-2 .quick-nav-dot.active { background: #8b5cf6; }
.quick-nav-item.level-3 .quick-nav-dot.active { background: #f97316; }
.quick-nav-item.level-4 .quick-nav-dot.active { background: #ef4444; }
.quick-nav-item.level-5 .quick-nav-dot.active { background: #06b6d4; }
.quick-nav-item.level-6 .quick-nav-dot.active { background: #84cc16; }
.quick-nav-item.level-7 .quick-nav-dot.active { background: #f59e0b; }
.quick-nav-item.level-8 .quick-nav-dot.active { background: #ec4899; }
.quick-nav-item.level-9 .quick-nav-dot.active { background: #6366f1; }

.section-title {
  flex: 1;
  color: #374151;
  font-weight: 500;
  transition: color 0.15s ease;
  line-height: 1.2;
  word-break: break-word;
  
  &.active {
    color: #3b82f6;
    font-weight: 600;
  }
}

.children-container {
  margin-top: 1px;
  padding-left: 12px;
  border-left: 1px solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  // 添加展开/收起动画
  &.expanding {
    animation: expandDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  &.collapsing {
    animation: collapseUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 展开动画
@keyframes expandDown {
  from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
  }
}

// 收起动画
@keyframes collapseUp {
  from {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
  }
  to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
}
</style>