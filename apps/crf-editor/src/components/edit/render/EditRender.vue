<script lang="ts" setup>
import { nextTick, onMounted, onUnmounted, ref, computed, provide } from 'vue'
import { useTimerManager, useEventManager } from '@/composables/useTimerManager'
import EditQuickNavigation from './tools/EditQuickNavigation.vue'
import EditFormSectionComponent from './tools/EditFormSection.vue'
import EditAddsectionButton from './tools/EditAddSectionButton.vue'
import EditRenderHover from './EditRenderHover.vue'
import { useEditorStore } from '@/stores/editor-store'
import { CrfIcon } from '@crf/components'
import { useMessage } from 'naive-ui'
import type { ComponentInstance } from '@crf/types/editor'
import { EditorMode } from '@crf/types/core'
import { getComponentName } from '@/utils/component-registry'

// 使用新的统一编辑器Store
const editorStore = useEditorStore()

// Naive UI 实例
const message = useMessage()

// 提供editStore给子组件使用
provide('editStore', editorStore)

// 使用定时器和事件管理器
const { safeSetTimeout, debouncedCall } = useTimerManager()
const { addEventListener } = useEventManager()

// 标题编辑相关状态
const isEditingTitle = ref(false)
const tempTitle = ref('')
const titleInputRef = ref()

// 使用新的Store中的章节数据
const sections = computed(() => editorStore.sections)

const sectionNumbers = computed(() => {
  const numbers = new Map<string, string>()
  const generateNumbers = (sections: Record<string, unknown>[], prefix = '') => {
    sections.forEach((section, index) => {
      const currentNumber = prefix ? `${prefix}.${index + 1}` : `${index + 1}`
      numbers.set(section.id, currentNumber)
      if (section.children && section.children.length > 0) {
        generateNumbers(section.children, currentNumber)
      }
    })
  }
  generateNumbers(editorStore.sections)
  return numbers
})

// 当前激活的分组
const activeSectionId = ref('default')

// 快速导航显示状态
const showQuickNav = ref(true)

// 分组引用，用于滚动定位
const sectionRefs = ref<Record<string, HTMLElement>>({})

// 编辑状态
const editingSectionId = ref('')

// 动画状态
const animatingSectionId = ref('')

// 防抖滚动处理器
const debouncedScrollHandler = debouncedCall(() => {
  updateActiveSectionFromScroll()
}, 100)

// 新增分组 - 使用新的编辑器Store
const addNewSection = () => {
  const newSection = editorStore.addSection('新章节')
  activeSectionId.value = newSection.id

  // 如果分组数量超过1个，显示快速导航
  if (sections.value.length > 1) {
    showQuickNav.value = true
  }

  // 等待 DOM 更新后再滚动到新分组
  nextTick(() => {
    setTimeout(() => {
      scrollToSection(newSection.id)
    }, 100)
  })
}

// 新增子章节 - 使用新的编辑器Store
const addSubSection = (parentId: string) => {
  const parent = editorStore.findSectionById(parentId)
  if (!parent) return
  
  const childrenCount = parent.children?.length || 0
  const parentNumber = sectionNumbers.value.get(parentId) || '1'
  const newSectionNumber = `${parentNumber}.${childrenCount + 1}`
  
  // 生成更好的默认名称
  const defaultName = `子章节 ${newSectionNumber}`
  
  const newSubSection = editorStore.addSubSection(parentId, defaultName)
  
  if (newSubSection) {
    activeSectionId.value = newSubSection.id
    
    nextTick(() => {
      setTimeout(() => {
        scrollToSection(newSubSection.id)
      }, 100)
    })
  }
}

// 滚动到指定分组
const scrollToSection = (sectionId: string) => {
  const element = document.querySelector(`[data-section-id="${sectionId}"]`) as HTMLElement
  if (!element) {
    console.warn(`找不到分组DOM元素: ${sectionId}`)
    const componentInstance = sectionRefs.value[sectionId]
    if (componentInstance) {
      const fallbackElement = (componentInstance as Record<string, unknown>).$el?.value || (componentInstance as Record<string, unknown>).$el
      if (fallbackElement) {
        scrollToElement(sectionId, fallbackElement)
        return
      }
    }
    return
  }

  scrollToElement(sectionId, element)
}

const scrollToElement = (sectionId: string, element: HTMLElement) => {
  try {
    activeSectionId.value = sectionId

    const scrollContainer = document.querySelector('.edit-render') as HTMLElement
    if (!scrollContainer) {
      console.warn('找不到滚动容器')
      return
    }

    const headerHeight = 60
    const containerRect = scrollContainer.getBoundingClientRect()
    const elementRect = element.getBoundingClientRect()
    const targetPosition = elementRect.top - containerRect.top + scrollContainer.scrollTop - headerHeight - 20

    scrollContainer.scrollTo({
      top: Math.max(0, targetPosition),
      behavior: 'smooth'
    })

    setTimeout(() => {
      triggerSectionAnimation(sectionId)
    }, 300)
  } catch (error) {
    console.error('滚动到章节失败:', error)
  }
}

// 触发章节动画效果
const triggerSectionAnimation = (sectionId: string) => {
  animatingSectionId.value = sectionId
  safeSetTimeout(() => {
    animatingSectionId.value = ''
  }, 1000)
}

// 监听滚动，更新激活状态
const handleScroll = () => {
  debouncedScrollHandler()
}

// 根据滚动位置更新激活章节
const updateActiveSectionFromScroll = () => {
  const scrollContainer = document.querySelector('.edit-render') as HTMLElement
  if (!scrollContainer) return

  const scrollTop = scrollContainer.scrollTop
  const sections = document.querySelectorAll('[data-section-id]')
  
  let activeSection = null
  sections.forEach((section) => {
    const element = section as HTMLElement
    const rect = element.getBoundingClientRect()
    const containerRect = scrollContainer.getBoundingClientRect()
    const elementTop = rect.top - containerRect.top + scrollTop
    
    if (elementTop <= scrollTop + 100) {
      activeSection = element.getAttribute('data-section-id')
    }
  })
  
  if (activeSection && activeSection !== activeSectionId.value) {
    activeSectionId.value = activeSection
  }
}

// 删除章节 - 使用新的编辑器Store
const deleteSection = (sectionId: string) => {
  editorStore.deleteSection(sectionId)
  
  // 如果删除的是当前激活章节，切换到第一个章节
  if (activeSectionId.value === sectionId && sections.value.length > 0) {
    activeSectionId.value = sections.value[0].id
  }
  
  // 如果只剩一个章节，隐藏快速导航
  if (sections.value.length <= 1) {
    showQuickNav.value = false
  }
}

// 更新章节名称 - 使用新的编辑器Store
const updateSectionName = (sectionId: string, newName: string) => {
  editorStore.updateSectionName(sectionId, newName)
}

// 处理保存章节名称事件
const handleSaveNameWrapper = (payload: { id: string; name: string }) => {
  updateSectionName(payload.id, payload.name)
  editingSectionId.value = '' // 结束编辑状态
}

// 处理章节更新事件
const handleSectionUpdate = (section: Record<string, unknown>) => {
  // 这里可以处理来自拖拽等操作的章节更新
  // 由于我们使用新的编辑器Store，这些更新应该通过 editorStore 来处理
  console.log('章节更新:', section)
  
  // 如果是块更新，我们需要同步到新的编辑器Store
  if (section.blocks) {
    // 找到对应的章节并更新其块
    const targetSection = editorStore.findSectionById(section.id)
    if (targetSection) {
      targetSection.blocks = section.blocks
    }
  }
}

// 获取所有章节的扁平化列表（用于递归渲染）
const allSections = computed(() => {
  const result: Record<string, unknown>[] = []
  
  const flattenSections = (sections: Record<string, unknown>[], level = 0) => {
    sections.forEach(section => {
      result.push({ ...section, level })
      if (section.children && section.children.length > 0) {
        flattenSections(section.children, level + 1)
      }
    })
  }
  
  flattenSections(sections.value)
  return result
})

// 标题编辑相关方法
const handleTitleClick = () => {
  // 点击标题加载页面配置
  editorStore.selectPageConfig()
}

const startEditTitle = () => {
  isEditingTitle.value = true
  tempTitle.value = editorStore.pageConfig.title
  nextTick(() => {
    titleInputRef.value?.focus()
  })
}

const saveTitle = () => {
  if (tempTitle.value.trim()) {
    editorStore.updatePageConfig({ title: tempTitle.value.trim() })
  }
  isEditingTitle.value = false
}

const cancelEditTitle = () => {
  isEditingTitle.value = false
  tempTitle.value = ''
}

// 复制组件
const handleComponentCopy = (componentId: string) => {
  try {
    const component = editorStore.getComponent(componentId)
    if (!component) {
      throw new Error('找不到要复制的组件')
    }
    
    // 创建组件副本
    const copiedComponent = {
      ...component,
      id: `${component.type}-${Date.now()}` // 生成新的ID
    }
    
    // 添加到当前章节
    editorStore.addComponent(copiedComponent)
    message.success('复制成功')
  } catch (error) {
    console.error('复制组件失败:', error)
    message.error('复制失败')
  }
}

// 删除组件
const handleComponentDelete = (componentId: string) => {
  try {
    const component = editorStore.getComponent(componentId)
    if (!component) {
      throw new Error('找不到要删除的组件')
    }
    
    // 从章节中删除组件
    if (editorStore.deleteComponent(componentId)) {
      message.success('删除成功')
    } else {
      throw new Error('删除组件失败')
    }
  } catch (error) {
    console.error('删除组件失败:', error)
    message.error('删除失败')
  }
}

// 选中的组件ID
const selectedComponentId = ref<string | null>(null)

// 更新组件值
const updateComponentValue = (componentId: string, value: unknown) => {
  console.log('更新组件值:', componentId, value)
  
  // 更新到store的formData中
  editorStore.updateFormData(componentId, value)
  
  // 同时也更新组件的props（用于编辑模式）
  const updateData: Partial<ComponentInstance> = {
    props: {
      value: value as unknown,
      modelValue: value as unknown // 确保modelValue也被更新
    }
  }
  editorStore.updateComponent(componentId, updateData)
}

// 更新组件选项
const updateComponentOptions = (componentId: string, options: Record<string, unknown>[]) => {
  console.log('更新组件选项:', componentId, options)
  const component = editorStore.getComponent(componentId)
  if (!component) {
    console.warn('找不到组件:', componentId)
    return
  }
  
  const updateData: Partial<ComponentInstance> = {
    props: {
      ...component.props,
      options
    }
  }
  editorStore.updateComponent(componentId, updateData)
  console.log('组件选项更新完成:', updateData)
}

// 选中组件
const selectComponent = (componentId: string | null) => {
  selectedComponentId.value = componentId
}

// 获取组件属性 - 根据模式返回不同属性
const getComponentProps = (block: Record<string, unknown>) => {
  const baseProps = { ...block.props }
  
  if (editorStore.mode === EditorMode.PREVIEW) {
    // 预览模式下的属性调整
    return {
      ...baseProps,
      // 确保表单组件在预览模式下可以交互
      disabled: false,
      readonly: false,
      // 从formData中获取值
      modelValue: editorStore.formData[block.id] || baseProps.modelValue || baseProps.defaultValue
    }
  } else {
    // 编辑模式下的属性 - 强制使用默认值，不显示预览数据
    return {
      ...baseProps,
      // 编辑模式下使用默认值，不显示预览时输入的数据
      modelValue: baseProps.modelValue || baseProps.defaultValue || ''
    }
  }
}

// 生命周期
onMounted(() => {
  const scrollContainer = document.querySelector('.edit-render')
  if (scrollContainer) {
    addEventListener(scrollContainer, 'scroll', handleScroll, { passive: true })
  }

  // 初始化时检查是否有多个章节，如果有就显示快速导航
  if (sections.value.length > 1) {
    showQuickNav.value = true
  }

  // 监听组件点击事件，用于选中组件（仅编辑模式）
  const handleComponentClick = (event: MouseEvent) => {
    // 预览模式下不处理组件选择
    if (editorStore.mode === EditorMode.PREVIEW) {
      return
    }
    
    const target = event.target as HTMLElement
    const componentWrapper = target.closest('.component-wrapper')
    if (componentWrapper) {
      const componentId = componentWrapper.getAttribute('data-component-id')
      selectComponent(componentId)
    } else {
      selectComponent(null)
    }
  }

  document.addEventListener('click', handleComponentClick)

  // 清理事件监听
  onUnmounted(() => {
    document.removeEventListener('click', handleComponentClick)
  })
})

// onUnmounted中的清理由useTimerManager和useEventManager自动处理

// 暴露给模板的方法和状态
defineExpose({
  addNewSection,
  addSubSection,
  scrollToSection,
  deleteSection,
  updateSectionName,
  handleSaveNameWrapper,
  handleSectionUpdate
})
</script>

<template>
  <div class="edit-render" :class="{ 'preview-mode': editorStore.mode === EditorMode.PREVIEW }">
    <!-- 快速导航组件 - 预览模式下也显示 -->
    <EditQuickNavigation
      :show="showQuickNav"
      :sections="sections"
      :active-section-id="activeSectionId"
      :preview-mode="editorStore.mode === EditorMode.PREVIEW"
      @scroll-to-section="scrollToSection"
    />

    <div class="edit-render-container">
      <div class="edit-render-container__canvas">
        <!-- 可编辑标题区域 -->
        <div class="page-title-container">
          <div v-if="!isEditingTitle || editorStore.mode === EditorMode.PREVIEW" class="page-title-display" @click="editorStore.mode === EditorMode.EDIT ? handleTitleClick : null">
            <h1 class="page-title">{{ editorStore.pageConfig.title }}</h1>
            <button 
              v-if="editorStore.mode === EditorMode.EDIT" 
              class="title-edit-btn" 
              @click.stop="startEditTitle"
            >
              <crf-icon icon="material-symbols:edit" size="16" />
            </button>
          </div>
          <div v-else-if="editorStore.mode === EditorMode.EDIT" class="page-title-edit">
            <n-input
              ref="titleInputRef"
              v-model:value="tempTitle"
              class="title-input"
              placeholder="请输入页面标题"
              @blur="saveTitle"
              @keyup.enter="saveTitle"
              @keyup.esc="cancelEditTitle"
            />
            <div class="title-edit-actions">
              <n-button size="small" @click="saveTitle">保存</n-button>
              <n-button size="small" @click="cancelEditTitle">取消</n-button>
            </div>
          </div>
        </div>

        <!-- 分组渲染区域 -->
        <div class="sections-container">
          <template v-for="section in allSections" :key="section.id">
            <EditFormSectionComponent
              :ref="(el: Record<string, unknown>) => sectionRefs[section.id] = el"
              :section="section"
              :number="sectionNumbers.get(section.id) || ''"
              :editing="editorStore.mode === EditorMode.EDIT ? (editingSectionId === section.id) : false"
              :animating="animatingSectionId === section.id"
              :can-delete="section.level === 0 ? sections.length > 1 : true"
              :data-section-id="section.id"
              :preview-mode="editorStore.mode === EditorMode.PREVIEW || editorStore.mode === 'fill'"
              @start-edit="editingSectionId = $event"
              @save-name="handleSaveNameWrapper"
              @delete="deleteSection"
              @add-subsection="addSubSection"
              @update:section="handleSectionUpdate"
            >
              <!-- 组件渲染 -->
              <template v-for="block in section.blocks" :key="`${block.id}-${editorStore.mode}`">
                <div class="component-wrapper" :class="{ 'preview-component': editorStore.mode === EditorMode.PREVIEW }" :data-component-id="block.id">
                  <component
                    :is="getComponentName(block.type)"
                    v-bind="getComponentProps(block)"
                    @update:modelValue="(value: unknown) => updateComponentValue(block.id, value)"
                    @update-options="(options: Record<string, unknown>[]) => updateComponentOptions(block.id, options)"
                  />
                  <!-- 编辑悬停效果 - 仅编辑模式显示 -->
                  <edit-render-hover
                    v-if="editorStore.mode === EditorMode.EDIT"
                    :is-selected="selectedComponentId === block.id"
                    @copy="handleComponentCopy(block.id)"
                    @delete="handleComponentDelete(block.id)"
                  />
                </div>
              </template>
            </EditFormSectionComponent>
          </template>
        </div>

        <!-- 新增分组按钮 - 仅编辑模式显示 -->
        <EditAddsectionButton 
          v-if="editorStore.mode === EditorMode.EDIT" 
          class="m-t-[-20px]" 
          @add-section="addNewSection" 
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 主容器
.edit-render {
  width: 100%;
  height: calc(100vh - var(--edit-header-height));
  margin-left: var(--edit-block-width);
  margin-top: var(--edit-header-height);
  margin-right: var(--edit-config-width);
  background-color: var(--edit-background-color);
  overflow-y: auto;
}

.edit-render-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  max-width: 1200px;
  min-width: 360px;
  margin: 0 auto;
  padding: 16px 20px;  // 减少顶部和底部的外边距
}

// 渲染容器 - 移除背景色和边框，让表单更简洁
.edit-render-container__canvas {
  border: 1px dashed #cccccc;  // 移除边框
  background-color: white;     // 移除白色背景
  border-radius: 8px;
  position: relative;
  padding: 20px 16px;  // 减少内边距
  z-index: 1;
  width: 100%;
}

// 页面标题样式
.page-title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  
  .page-title-display {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f5f5f5;
      
      .title-edit-btn {
        opacity: 1;
      }
    }
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      color: #1f2937;
      text-align: center;
    }
    
    .title-edit-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      opacity: 0;
      transition: all 0.2s ease;
      border-radius: 4px;
      color: #6b7280;
      
      &:hover {
        background-color: #e5e7eb;
        color: #374151;
      }
    }
  }
  
  .page-title-edit {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
    max-width: 500px;
    
    .title-input {
      width: 100%;
      
      :deep(.n-input__input-el) {
        font-size: 24px;
        font-weight: 600;
        text-align: center;
        padding: 12px 16px;
      }
    }
    
    .title-edit-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.component-wrapper {
  position: relative;
  margin: 8px 0;
  
  &:hover {
    > .edit-render-hover {
      display: block !important;
    }
  }
  
  // 预览模式样式
  &.preview-component {
    margin: 12px 0;
    
    // 移除悬停效果
    &:hover {
      > .edit-render-hover {
        display: none !important;
      }
    }
  }
}

// 预览模式样式 - 仅修改交互行为，保持视觉样式一致
.edit-render.preview-mode {  
  .page-title-container .page-title-display {
    cursor: default; // 预览模式下标题不可点击
    
    &:hover {
      background-color: transparent;
    }
  }
}
</style>