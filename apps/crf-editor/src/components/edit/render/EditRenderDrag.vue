<template>
  <draggable
    :group="props.group"
    :list="props.list"
    :move="move"
    :sort="props.sort"
    :disabled="editorStore.mode === 'preview' || editorStore.mode === 'fill'"
    animation="200"
    class="edit-render-drag"
    item-key="id"
  >
    <template #item="{ element }">
      <div class="component-wrapper">
        <div
          :class="[activeClass(element), { 
            'preview-mode': editorStore.mode === 'preview',
            'fill-mode': editorStore.mode === 'fill'
          }]"
          class="block-render"
          @mouseenter="(editorStore.mode !== 'preview' && editorStore.mode !== 'fill') && (hoverId = element.id)"
          @mouseleave="(editorStore.mode !== 'preview' && editorStore.mode !== 'fill') && (hoverId = '')"
          @click.stop="handleComponentClick(element)"
        >
          <edit-render-hover 
            v-if="editorStore.mode !== 'fill'"
            :is-selected="shouldShowHover(element)"
            @copy="handleComponentCopy(element)"
            @delete="handleComponentDelete(element.id)"
          />
          <component
            :is="renderComponentCode(element)"
            :key="element.id"
            :config="getComponentConfig(element)"
            :component-id="element.id"
            v-bind="getComponentProps(element)"
          />
        </div>
      </div>
    </template>
  </draggable>
</template>

<script lang="ts" setup>
import { computed, ref, provide, watch } from 'vue'
// import { dragProps } from '@/components/edit/render/edit-render-drag.ts'
import { move } from '@/components/edit/nested.ts'
import EditRenderHover from './EditRenderHover.vue'
import { getComponentName } from '@crf/editor-core'
import { useEditorStore } from '../../../stores/editor-store'
import draggable from 'vuedraggable'
import { env } from '@/utils/env'
import { useMessage } from 'naive-ui'

interface Props {
  group: string | Record<string, unknown>
  list: Record<string, unknown>[]
  sort?: boolean
  sectionId?: string
}

const props = withDefaults(defineProps<Props>(), {
  sort: true
})

const editorStore = useEditorStore()
const message = useMessage()
const hoverId = ref('')

// 提供统一store给子组件
provide('editStore', editorStore)

/**
 * 计算渲染组件的函数
 */
const renderComponentCode = computed(() => {
  return (element: { code: string }) => {
    const componentName = getComponentName(element.code)

    if (componentName === 'div' && env.isDev && env.enableConsoleLog) {
      console.warn(`[ComponentRender] 未找到组件映射: ${element.code}`)
    }

    return componentName
  }
})

// 使用computed确保组件配置的响应式更新
const getComponentConfig = computed(() => {
  return (element: Record<string, unknown>) => {
    // 获取组件的默认标题
    const getDefaultTitle = (code: string) => {
      // 处理不同的组件code格式
      const normalizedCode = code ? code.replace('crf-', '') : ''
      
      let title = '组件'
      switch (normalizedCode) {
        case 'text':
          title = '文本输入'
          break
        case 'textarea':
          title = '多行文本'
          break
        case 'card':
          title = '卡片标题'
          break
        default:
          title = '组件'
      }
      
      return title
    }
    
    // 获取有效的标题值（非空字符串）
    const getValidTitle = (title: unknown) => {
      return title && String(title).trim() ? String(title).trim() : null
    }
    
    // 如果是选中的组件，优先使用store中的最新数据
    if (editorStore.selectedComponent?.id === element.id) {
      const selectedConfig = editorStore.selectedComponent
      const validTitle = getValidTitle(selectedConfig.title) || 
                         getValidTitle(selectedConfig.props?.title) || 
                         getValidTitle(element.title) || 
                         getValidTitle(element.props?.title) || 
                         getDefaultTitle(element.code)
      
      return {
        id: element.id,
        type: element.code || 'text',
        title: validTitle,
        ...selectedConfig.props,
        ...selectedConfig
      }
    }
    
    // 否则使用element中的数据，确保有默认值
    const validTitle = getValidTitle(element.title) || 
                       getValidTitle(element.props?.title) || 
                       getDefaultTitle(element.code)
    
    return {
      id: element.id,
      type: element.code || 'text',
      title: validTitle,
      ...element.props
    }
  }
})

// 使用computed确保组件props的响应式更新
const getComponentProps = computed(() => {
  return (element: Record<string, unknown>) => {
    // 获取组件的默认标题
    const getDefaultTitle = (code: string) => {
      // 处理不同的组件code格式
      const normalizedCode = code ? code.replace('crf-', '') : ''
      
      let title = '组件'
      switch (normalizedCode) {
        case 'text':
          title = '文本输入'
          break
        case 'textarea':
          title = '多行文本'
          break
        case 'card':
          title = '卡片标题'
          break
        default:
          title = '组件'
      }
      
      return title
    }
    
    // 获取有效的标题值（非空字符串）
    const getValidTitle = (title: unknown) => {
      return title && String(title).trim() ? String(title).trim() : null
    }
    
    // 如果是选中的组件，优先使用store中的最新数据
    if (editorStore.selectedComponent?.id === element.id) {
      const selectedConfig = editorStore.selectedComponent
      const validTitle = getValidTitle(selectedConfig.title) || 
                         getValidTitle(selectedConfig.props?.title) || 
                         getValidTitle(element.title) || 
                         getValidTitle(element.props?.title) || 
                         getDefaultTitle(element.code)
      
      return {
        ...(element.props || {}),
        ...(selectedConfig.props || {}),
        title: validTitle
      }
    }
    
    // 否则使用element中的数据，确保有默认值
    const props = element.props || {}
    const validTitle = getValidTitle(element.title) || 
                       getValidTitle(props.title) || 
                       getDefaultTitle(element.code)
    
    return {
      ...props,
      title: validTitle
    }
  }
})

// 判断该组件是否选中
const activeClass = computed(() => {
  return (element: { id: string }) => {
    // 预览模式和填写模式下不显示选中状态
    if (editorStore.mode === 'preview' || editorStore.mode === 'fill') {
      return {}
    }
    
    const id = editorStore.selectedComponent?.id || ''
    return { 'is-active': element.id === id }
  }
})

// 判断组件是否应该显示悬浮工具栏
const shouldShowHover = computed(() => {
  return (element: { id: string }) => {
    // 在预览模式和填写模式下不显示悬浮工具栏
    if (editorStore.mode === 'preview' || editorStore.mode === 'fill') {
      return false
    }
    
    const isHovering = element.id === hoverId.value
    const isSelected = element.id === (editorStore.selectedComponent?.id || '')
    return isHovering || isSelected
  }
})

// 处理组件点击事件
const handleComponentClick = (element: Record<string, unknown>) => {
  // 预览模式和填写模式下不执行选中操作
  if (editorStore.mode === 'preview' || editorStore.mode === 'fill') {
    return
  }
  
  // 使用统一状态管理选择组件
  editorStore.selectComponent(element)
}

// 复制组件
const handleComponentCopy = (element: Record<string, unknown>) => {
  try {
    // 首先选中当前组件，确保store知道当前操作的组件
    editorStore.selectComponent(element)
    
    // 创建组件副本
    const copiedComponent = {
      ...element,
      id: `${element.code || 'component'}-${Date.now()}` // 生成新的ID
    }
    
    // 使用当前章节ID
    const targetSectionId = props.sectionId
    
    // 添加到当前章节
    editorStore.addComponent(copiedComponent, targetSectionId)
    message.success('复制成功')
  } catch (error) {
    console.error('复制组件失败:', error)
    message.error('复制失败')
  }
}

// 删除组件
const handleComponentDelete = (componentId: string) => {
  try {
    // 从章节中删除组件
    if (editorStore.deleteComponent(componentId)) {
      message.success('删除成功')
    } else {
      throw new Error('删除组件失败')
    }
  } catch (error) {
    console.error('删除组件失败:', error)
    message.error('删除失败')
  }
}

// 监听模式变化，预览模式和填写模式下清除选中状态
watch(
  () => editorStore.mode,
  (newMode) => {
    if (newMode === 'preview' || newMode === 'fill') {
      // 预览模式和填写模式下清除选中状态
      editorStore.selectComponent(null)
      // 清除悬浮状态
      hoverId.value = ''
    }
  }
)
</script>

<style lang="scss" scoped>
.block-render {
  cursor: grab;
  position: relative;
  border-radius: 4px;
  width: 100%; /* 确保所有组件100%宽度 */

  &:active {
    cursor: grabbing;
  }

  /* 预览模式和填写模式下取消拖拽光标和所有编辑相关的视觉效果 */
  &.preview-mode,
  &.fill-mode {
    cursor: default;
    
    &:active {
      cursor: default;
    }
    
    /* 预览模式和填写模式下不显示选中状态的边框和标识 */
    &.is-active {
      &::before {
        display: none;
      }
      
      &::after {
        display: none;
      }
    }
    
    /* 预览模式下不显示悬浮效果 */
    &:hover {
      background-color: transparent;
    }
  }

  // 选中状态样式 - 虚线边框 + 左侧6个点标签
  &.is-active {
    // 虚线边框
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border: 1px dashed #1890ff;
      border-radius: 4px;
      pointer-events: none;
      z-index: 1;
    }

    // 左侧现代简洁的选中标识
    &::after {
      content: '';
      position: absolute;
      left: -3px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
      border-radius: 2px;
      pointer-events: none;
      z-index: 2;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);

      // 添加微妙的脉冲动画
      animation: selectedPulse 2s ease-in-out infinite;
    }
  }
}

.edit-render-drag {
  width: 100%;
  height: 100%;
}

.component-wrapper {
  position: relative;
  min-height: 20px;
  margin-bottom: 16px; /* 增加组件间距 */
  width: 100%; /* 确保100%宽度 */
}

.component-wrapper:last-child {
  margin-bottom: 0; /* 最后一个组件不需要底部间距 */
}

// 选中状态的脉冲动画
@keyframes selectedPulse {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1.05);
  }
}

// 嵌套渲染样式
.nested-render {
  .nested-item {
    position: relative;
    margin-bottom: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .nested-item:last-child {
    margin-bottom: 0;
  }

  .nested-item:hover {
    background-color: rgba(24, 144, 255, 0.02);
  }
  
  /* 预览模式下的嵌套组件不显示悬浮效果 */
  .preview-mode .nested-item:hover {
    background-color: transparent;
  }
  
  /* 预览模式下的嵌套组件不显示选中状态 */
  .preview-mode .nested-item.is-nested-selected {
    background-color: transparent;
    
    &::before {
      display: none;
    }
    
    &::after {
      display: none;
    }
  }

  .nested-item.is-nested-selected {
    background-color: rgba(24, 144, 255, 0.04);

    /* 嵌套组件选中状态的边框 */
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border: 1px dashed #52c41a;
      border-radius: 4px;
      pointer-events: none;
      z-index: 1;
    }

    /* 嵌套组件选中标识 */
    &::after {
      content: '';
      position: absolute;
      left: -3px;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 10px;
      background: linear-gradient(180deg, #52c41a 0%, #73d13d 100%);
      border-radius: 2px;
      z-index: 2;
      animation: nestedSelectedPulse 2s ease-in-out infinite;
    }
  }
}

/* 嵌套组件悬停指示器 */
.nested-hover-indicator {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  pointer-events: none;
  z-index: 15;
  border-radius: 4px;
}

.nested-toolbar {
  position: absolute;
  top: -12px;
  right: 2px;
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d9f7be;
  border-radius: 4px;
  padding: 1px;
  gap: 1px;
  pointer-events: auto;
  box-shadow: 0 1px 4px rgba(82, 196, 26, 0.2);
  animation: slideInDown 0.15s ease-out;
}

.nested-toolbar .toolbar-item {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #52c41a;
}

.nested-toolbar .toolbar-item:hover {
  background: #f6ffed;
  transform: translateY(-1px);
}

.nested-toolbar .toolbar-item:first-child:hover {
  color: #389e0d;
}

.nested-toolbar .toolbar-item:last-child:hover {
  color: #cf1322;
  background: #fff2f0;
}

/* 嵌套组件选中动画 */
@keyframes nestedSelectedPulse {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1.1);
  }
}
</style>