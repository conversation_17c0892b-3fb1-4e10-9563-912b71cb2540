<template>
  <draggable
    :clone="clone"
    :group="group"
    :list="list"
    :sort="sort"
    animation="200"
    class="edit-block-drag"
    item-key="id"
  >
    <template #item="{ element }">
      <crf-card
        :icon="element.icon"
        :iconColor="element.iconColor"
        :info="element.desc"
        :tag="element.tag"
        :title="element.label"
        compact
        @dblclick="handleDoubleClick(element)"
        class="draggable-component-card"
      />
    </template>
  </draggable>
</template>

<script lang="ts" setup>
import draggable from 'vuedraggable'
import { clone } from '@/components/edit/nested.ts'
import { useEditorStore } from '@/stores/editor-store'
import { generateId } from '@/utils/id'
import { useMessage } from 'naive-ui'

defineProps({
  list: {
    type: Array,
    required: true,
    default: () => []
  },
  group: {
    type: [String, Object],
    default: 'group'
  },
  sort: {
    type: Boolean,
    default: true
  }
})

const editorStore = useEditorStore()

// 添加message实例
const message = useMessage()

/**
 * 处理双击事件 - 自动添加组件到最后章节的最后位置
 */
const handleDoubleClick = (element: Record<string, unknown>) => {
  try {
    // 获取所有章节
    const sections = editorStore.sections
    if (!sections || sections.length === 0) {
      message.warning('请先创建章节')
      return
    }

    // 找到最后一个章节（包括子章节）
    const findLastSection = (sectionList: Record<string, unknown>[]): Record<string, unknown> | null => {
      if (sectionList.length === 0) return null
      
      const lastSection = sectionList[sectionList.length - 1]
      if (lastSection.children && lastSection.children.length > 0) {
        return findLastSection(lastSection.children)
      }
      return lastSection
    }

    const lastSection = findLastSection(sections)
    if (!lastSection) {
      message.warning('未找到可用的章节')
      return
    }

    // 创建新的组件实例
    const newComponent = {
      id: generateId(),
      code: element.code,
      name: element.name,
      title: element.label,
      icon: element.icon,
      iconColor: element.iconColor,
      desc: element.desc,
      tag: element.tag,
      props: {
        title: element.label,
        ...element.props
      },
      sectionId: lastSection.id
    }

    // 添加到最后章节
    lastSection.blocks.push(newComponent)
    
    // 选中新添加的组件
    editorStore.selectComponent(newComponent)
    editorStore.showConfigPanel()

    // 显示成功提示
    message.success(`"${element.label}" 已添加到 "${lastSection.name}"`)

  } catch (error) {
    console.error('双击添加组件失败:', error)
    message.error('添加组件失败，请稍后重试')
  }
}
</script>

<style lang="scss" scoped>
.draggable-component-card {
  position: relative;
  transition: all 0.2s ease;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}
</style>