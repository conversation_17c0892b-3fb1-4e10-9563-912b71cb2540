<script lang="ts" setup>
import { ref } from 'vue'
import { CrfIcon } from '@crf/components'
import { componentList, menuList } from './edit-block'
import { dragGroup } from '@/components/edit/nested.ts'

import EditBlockDrag from './EditBlockDrag.vue'

const activeMenu = ref(0)
const ml = ref(menuList)
const cL = componentList
</script>

<template>
  <div
    class="fixed w-[var(--edit-block-width)] top-[var(--edit-header-height)] left-0 flex items-start flex-shrink-0 bg-white border-r-solid border-r-1px border-r-[var(--edit-border-color)]"
  >
    <!--  左边工具条  -->
    <div
      class="left border-r-[1px] border-r-solid border-r-[var(--edit-border-color)]">
      <div
        v-for="(item, index) in ml"
        :key="index"
        :class="{ active: index === activeMenu }"
        class="menu-item flex flex-col items-center justify-center text-align-center cursor-pointer"
        @click="activeMenu = index"
      >
        <crf-icon :color="index === activeMenu ? item.activeColor :item.color" :icon="item.icon" size="1.3rem" />
        <div :style="{color: index === activeMenu ? item.activeColor: item.color}" class="mt-1 text-center">
          <span class="text-size-sm">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- 右边组件栏   -->
    <div class="right flex-1">
      <div v-show="activeMenu === 0">

        <div class="h-[calc(100vh-var(--edit-header-height))] overflow-y-auto bg-white m-t-5px">
          <div v-for="(item, index) in cL" :key="index">
            <div class="sticky top-0 bg-white z-10 py-5 px-4 font-medium text-lg border-b border-gray-200">
              <div class="flex items-center space-x-2">
                <crf-icon :icon="item.icon" class="flex-shrink-0" size="1.25rem" />
                <span class="leading-none font-semibold">{{ item.title }}</span>
              </div>
            </div>

            <div class="p-4">
              <edit-block-drag
                :group="{ name: dragGroup, pull: 'clone', put: false }"
                :list="item.components"
                :sort="false"
                class="component-grid"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-show="activeMenu === 1">套件页面</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.left {
  width: var(--edit-block-toolbox-width);
  height: calc(100vh - var(--edit-header-height));

  .menu-item {
    padding: 6px 0;
    margin: 17px 5px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 3.6rem;
      height: 3.6rem;
      border-radius: 8px;
      background-color: transparent;
      transition: background-color 0.2s ease;
      z-index: 0;
    }

    &:hover::before {
      background-color: rgba(37, 99, 235, 0.1);
    }

    &.active::before {
      background-color: rgba(37, 99, 235, 0.1);
    }

    .crf-icon, div {
      position: relative;
      z-index: 1;
    }
  }
}

.right {
  height: calc(100vh - var(--edit-header-height));
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;  // 减小网格间距
  padding: 4px;  // 减小内边距
}

// 修改分类标题样式
.sticky {
  top: 0;
  background-color: white;
  z-index: 10;
  padding: 8px 12px;  // 减小内边距
  font-size: 14px;  // 减小字号
  border-bottom: 1px solid var(--gray-200);
  
  .flex {
    gap: 6px;  // 减小图标和文字的间距
    
    .crf-icon {
      font-size: 16px;  // 减小图标尺寸
    }
    
    span {
      font-weight: 500;  // 减小字重
    }
  }
}

// 修改组件列表容器样式
.p-4 {
  padding: 8px;  // 减小内边距
}

.component-card {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
  padding: 10px 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #fafbfc;
  cursor: grab;
  transition: box-shadow 0.2s, background 0.2s;
  position: relative;
  min-height: 40px;

  &:hover {
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
    background: #f0f6ff;
  }

  .card-row {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;
    position: relative;
  }

  .card-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  .component-title {
    font-size: 13px;
    color: #333;
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .component-desc {
    font-size: 11px;
    color: #888;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
  }

  .component-tag {
    font-size: 10px;
    color: #fff;
    background: #2463EB;
    border-radius: 4px;
    padding: 1px 6px;
    text-align: center;
    margin-left: 6px;
    white-space: nowrap;
    flex-shrink: 0;
  }
}
</style>



