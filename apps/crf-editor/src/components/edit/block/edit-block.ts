import { getComponentBlocks } from '@crf/components'

// 从组件配置获取组件块
export const baseBlocks: ReturnType<typeof getComponentBlocks> = getComponentBlocks()

export const componentList: Array<{
  title: string
  icon: string
  components: typeof baseBlocks
}> = [
  {
    title: '基础组件',
    icon: 'material-symbols:widgets',
    components: baseBlocks
  }
]

export const menuList = [
  {
    icon: 'uiw:component',
    color: '#6B7280',
    activeColor: '#2463EB',
    name: '组件',
    active: true
  },
  {
    icon: 'fluent-mdl2:web-components',
    color: '#6B7280',
    activeColor: '#2463EB',
    name: '套件',
    active: false
  }
]

