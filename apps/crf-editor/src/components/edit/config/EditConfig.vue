<template>
  <div class="edit-config">
    <div class="config-content">
      <div class="config-form">
        
        <div v-if="editorStore.selectedComponent?.id" class="p-14px">
          <div class="flex items-center gap-2 mb-3">
            <crf-icon
              :icon="getComponentIcon(editorStore.selectedComponent.code)"
              :style="{ color: getComponentIconColor(editorStore.selectedComponent.code) }"
              size="20"
            />
            <span class="text-size-2xl font-medium">{{ getComponentName }}</span>
          </div>
        </div>
        <edit-config-block v-if="editorStore.selectedComponent?.id" />
        <edit-config-page v-else />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useEditorStore } from '@/stores/editor-store'
import { COMPONENTS, CrfIcon } from '@crf/components'
import EditConfigBlock from '@/components/edit/config/EditConfigBlock.vue'
import EditConfigPage from '@/components/edit/config/EditConfigPage.vue'

// 使用新的统一编辑器Store
const editorStore = useEditorStore()

// 根据组件code获取图标
const getComponentIcon = (code?: string) => {
  if (!code) return 'material-symbols:extension'
  const component = COMPONENTS.find(comp => comp.code === code)
  return component?.icon || 'material-symbols:extension'
}

// 根据组件code获取图标颜色
const getComponentIconColor = (code?: string) => {
  if (!code) return '#666'
  const component = COMPONENTS.find(comp => comp.code === code)
  return component?.iconColor || '#666'
}

// 获取组件名称 - 使用computed确保响应式更新
const getComponentName = computed(() => {
  const selectedComponent = editorStore.selectedComponent
  if (!selectedComponent) return '组件配置'
  
  // 优先使用组件配置中的 label（组件名称）
  const component = COMPONENTS.find(comp => comp.code === selectedComponent.code)
  if (component?.label) return component.label
  
  // 其次使用组件的 name 属性
  if (selectedComponent.name) return selectedComponent.name
  
  // 最后使用组件code或默认值
  return selectedComponent.code || '组件'
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables/_color.scss';

.edit-config {
  position: fixed;
  z-index: 200;
  top: var(--edit-header-height);
  right: 0;
  width: var(--edit-config-width);
  height: calc(100vh - var(--edit-header-height));
  background: white;
  border-left: 1px solid var(--edit-border-color);
  transition: transform 0.3s ease;

  .config-content {
    height: 100%;
    overflow-y: auto;
  }
  
  .config-form {
    padding: 0;
  }

  // 配置头部区域
  .p-14px {
    background: #ffffff;
    border-bottom: 1px solid var(--edit-border-color);
    margin-bottom: 0;
    padding: 10px !important;
    
    .flex {
      margin-bottom: 12px !important;
    }
  }
}
</style>

