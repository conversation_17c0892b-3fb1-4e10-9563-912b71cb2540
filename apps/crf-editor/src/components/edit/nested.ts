import type { VueDraggableMoveEvent } from '@/types/draggable.ts'
import { cloneDeep } from 'lodash'
import { nanoid } from '@crf/utils'

/**
 * 判断组件是否包含该类名，如果包含该类名则可以进行嵌套
 */
export const nestedClass: string = 'nested-container'

/**
 * 多个draggable组件的group名
 * 相同名可以相互拖拽
 */
export const dragGroup = 'blocks'

/**
 * draggable移动事件
 * 判断是否可以拖入，防止嵌套元素被拖拽到其他嵌套容器中
 * @param e - vuedraggable move 事件参数
 * @returns 是否允许拖拽操作
 */
export const move = (e: VueDraggableMoveEvent): boolean => {
  // 将 e.to 的 classList 转换为数组并赋值给 classList 变量
  const classList = Array.from(e?.to?.classList)
  // 检查 classList 是否包含 nestedClass，结果赋值给 isRelatedNested 变量
  const isRelatedNested = classList?.includes(nestedClass)
  // 如果拖拽的元素是嵌套的并且目标容器是相关的嵌套容器，则返回 false
  if (e?.draggedContext?.element?.nested && isRelatedNested) return false
  // 否则返回 true，允许拖拽操作
  return true
}

/**
 * 获取组件默认属性
 */
function getDefaultProps(componentCode: string) {
  // 处理不同的组件code格式
  const normalizedCode = componentCode.replace('crf-', '')
  
  switch (normalizedCode) {
    case 'text':
      return {
        title: '文本输入',
        placeholder: '请输入内容',
        description: '请输入文本内容',
        required: false,
        disabled: false,
        readonly: false,
        format: 'text',
        minLength: 0,
        maxLength: 200,
        enableCustomError: false,
        customErrorMessage: '',
        medicalType: 'general',
        fieldCode: ''
      }
    case 'textarea':
      return {
        title: '多行文本',
        placeholder: '请输入内容',
        description: '请输入多行文本内容',
        required: false,
        disabled: false,
        readonly: false,
        minLength: 0,
        maxLength: 1000,
        rows: 4,
        autosize: false,
        resize: 'vertical',
        showCount: false,
        enableCustomError: false,
        customErrorMessage: '',
        medicalType: 'general',
        fieldCode: ''
      }
    case 'card':
      return {
        title: '卡片标题',
        shadow: 'always'
      }
    case 'container':
      return {
        title: '容器标题',
        width: '100%',
        height: 'auto',
        padding: '16px',
        backgroundColor: '#ffffff',
        borderStyle: 'solid',
        borderWidth: '1px',
        borderColor: '#d9d9d9',
        borderRadius: '4px',
        blocks: []
      }
    default:
      return {}
  }
}

interface ComponentElement {
  code: string
  label?: string
  title?: string
  id?: string
  props?: Record<string, unknown>
  [key: string]: unknown
}

/**
 * draggable 克隆事件，拖拽组件时调用该方案为组件生成唯一 id 和默认属性
 */
export const clone = (e: ComponentElement) => {
  const componentCode = e.code
  const defaultProps = getDefaultProps(componentCode)

  const result = cloneDeep({
    ...e,
    id: nanoid(8),
    title: defaultProps.title || e.label || e.title || '组件', // 优先使用默认标题，然后是label
    props: defaultProps
  })

  return result
}