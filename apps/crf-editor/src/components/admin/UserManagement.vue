<!-- 用户管理页面 -->
<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">用户管理</h2>
        <p class="page-description">管理系统用户，分配角色和权限</p>
      </div>
      <div class="header-actions">
        <n-button 
          type="primary" 
          @click="showCreateDialog = true"
          :disabled="!canManageUsers"
        >
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          创建用户
        </n-button>
        <n-button 
          @click="refreshData"
          :loading="loading"
        >
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <n-grid cols="24" :x-gap="20">
        <n-grid-item span="8">
          <n-input
            v-model:value="search"
            placeholder="搜索用户（用户名、邮箱、姓名）"
            clearable
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </n-grid-item>
        <n-grid-item span="6">
          <n-select
            v-model:value="roleFilter"
            placeholder="筛选角色"
            clearable
            @update:value="handleRoleFilter"
            :options="roleOptions"
          />
        </n-grid-item>
        <n-grid-item span="4">
          <n-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            clearable
            @update:value="handleStatusFilter"
            :options="statusOptions"
          />
        </n-grid-item>
        <n-grid-item span="6">
          <div class="batch-actions">
            <n-button
              v-if="selectedUsers.length > 0"
              type="warning"
              @click="batchActivateUsers"
              :disabled="!canManageUsers"
            >
              批量激活
            </n-button>
            <n-button
              v-if="selectedUsers.length > 0"
              type="error"
              @click="batchDeactivateUsers"
              :disabled="!canManageUsers"
            >
              批量停用
            </n-button>
          </div>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 用户列表 -->
    <div class="user-table-container">
      <n-data-table
        :columns="columns"
        :data="filteredUsers"
        :loading="loading"
        :row-key="rowKey"
        @update:checked-row-keys="handleSelectionChange"
        striped
        bordered
        flex-height
        style="min-height: 400px"
      />

      <!-- 分页 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :item-count="total"
          show-size-picker
          show-quick-jumper
          @update:page="handleCurrentChange"
          @update:page-size="handleSizeChange"
        >
          <template #prefix="{ itemCount }">
            共 {{ itemCount }} 项
          </template>
        </n-pagination>
      </div>
    </div>

    <!-- 创建用户对话框 -->
    <CreateUserDialog 
      v-model="showCreateDialog"
      @created="onUserCreated"
    />

    <!-- 编辑用户对话框 -->
    <EditUserDialog 
      v-model="showEditDialog"
      :user="selectedUser"
      @updated="onUserUpdated"
    />

    <!-- 用户角色管理对话框 -->
    <UserRoleDialog 
      v-model="showRoleDialog"
      :user="selectedUser"
      @updated="onUserRolesUpdated"
    />

    <!-- 用户详情对话框 -->
    <UserDetailDialog 
      v-model="showDetailDialog"
      :user="selectedUser"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, h } from 'vue'
import { storeToRefs } from 'pinia'
import { 
  useMessage, useDialog, 
  NButton, NIcon, NTag, NAvatar, NDropdown, 
  type DataTableColumns, type DropdownOption
} from 'naive-ui'
import { 
  AddOutline, RefreshOutline, SearchOutline, CreateOutline, 
  PersonOutline, PeopleOutline, EllipsisHorizontalOutline 
} from '@vicons/ionicons5'
import { useUserStore } from '@/stores/user-store'
import { usePermissionStore } from '@/stores/permission-store'
import { userManagementAPI } from '@/api/rbac'
import type { User, Role } from '@/types/rbac'
import CreateUserDialog from './CreateUserDialog.vue'
import EditUserDialog from './EditUserDialog.vue'
import UserRoleDialog from './UserRoleDialog.vue'
import UserDetailDialog from './UserDetailDialog.vue'

// UI 组件
const message = useMessage()
const dialog = useDialog()

// Stores
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// Store状态
const { user: currentUser } = storeToRefs(userStore)

// 响应式数据
const users = ref<User[]>([])
const userRoles = ref<Record<string, Role[]>>({})
const availableRoles = ref<Role[]>([])
const loading = ref(false)
const search = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const selectedUsers = ref<string[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showRoleDialog = ref(false)
const showDetailDialog = ref(false)
const selectedUser = ref<User | null>(null)

// 权限检查
const canManageUsers = computed(() => 
  userStore.hasPermission('user', 'create') ||
  userStore.hasPermission('user', 'update')
)

const canAssignRoles = computed(() => 
  userStore.hasPermission('user', 'assign_role')
)

// 选项数据
const roleOptions = computed(() => [
  ...availableRoles.value.map(role => ({
    label: role.name,
    value: role.code
  }))
])

const statusOptions = [
  { label: '激活', value: 'active' },
  { label: '停用', value: 'inactive' }
]

// 表格配置
const rowKey = (row: User) => row.id

const columns: DataTableColumns<User> = [
  {
    type: 'selection',
    disabled: (row: User) => row.username === currentUser.value?.username
  },
  {
    title: '用户信息',
    key: 'userInfo',
    minWidth: 200,
    render: (row: User) => {
      return h('div', { class: 'user-info' }, [
        h(NAvatar, {
          size: 40,
          class: 'user-avatar',
          src: row.avatar_url || undefined
        }, {
          default: () => row.avatar_url ? null : h(NIcon, { component: PersonOutline })
        }),
        h('div', { class: 'user-details' }, [
          h('div', { class: 'user-name' }, row.full_name || row.username),
          h('div', { class: 'user-email' }, row.email),
          h('div', { class: 'user-username' }, `@${row.username}`)
        ])
      ])
    }
  },
  {
    title: '角色',
    key: 'roles',
    minWidth: 160,
    render: (row: User) => {
      const roles = getUserRoles(row.id)
      return h('div', { class: 'role-tags' }, [
        ...roles.map(role => 
          h(NTag, {
            size: 'small',
            type: getRoleTagType(role.code),
            class: 'role-tag'
          }, { default: () => role.name })
        ),
        roles.length === 0 ? h(NTag, {
          size: 'small',
          type: 'default',
          class: 'role-tag'
        }, { default: () => '无角色' }) : null
      ].filter(Boolean))
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row: User) => {
      return h(NTag, {
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      }, { default: () => row.is_active ? '激活' : '停用' })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render: (row: User) => formatDate(row.created_at)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: User) => {
      const dropdownOptions: DropdownOption[] = [
        {
          label: '查看详情',
          key: 'view'
        },
        {
          label: '活动日志',
          key: 'activity'
        },
        {
          label: row.is_active ? '停用' : '激活',
          key: 'toggle-status',
          disabled: !canManageUsers.value
        },
        {
          type: 'divider',
          key: 'divider1'
        },
        {
          label: '重置密码',
          key: 'reset-password',
          disabled: !canManageUsers.value
        },
        {
          label: '删除用户',
          key: 'delete',
          disabled: !canManageUsers.value || row.username === currentUser.value?.username,
          props: {
            style: 'color: #d03050'
          }
        }
      ]

      return h('div', { class: 'action-buttons' }, [
        h(NButton, {
          size: 'small',
          onClick: () => editUser(row),
          disabled: !canManageUsers.value
        }, {
          default: () => '编辑',
          icon: () => h(NIcon, { component: CreateOutline })
        }),
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => manageUserRoles(row),
          disabled: !canAssignRoles.value
        }, {
          default: () => '角色',
          icon: () => h(NIcon, { component: PeopleOutline })
        }),
        h(NDropdown, {
          options: dropdownOptions,
          onSelect: (key: string) => handleCommand(key, row)
        }, {
          default: () => h(NButton, {
            size: 'small'
          }, {
            icon: () => h(NIcon, { component: EllipsisHorizontalOutline })
          })
        })
      ])
    }
  }
]

// 计算属性
const filteredUsers = computed(() => {
  let filtered = users.value

  // 搜索过滤
  if (search.value) {
    const searchLower = search.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.username.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchLower))
    )
  }

  // 角色过滤
  if (roleFilter.value) {
    filtered = filtered.filter(user => {
      const roles = getUserRoles(user.id)
      return roles.some(role => role.code === roleFilter.value)
    })
  }

  // 状态过滤
  if (statusFilter.value) {
    const isActive = statusFilter.value === 'active'
    filtered = filtered.filter(user => user.is_active === isActive)
  }

  return filtered
})

// 方法
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await userManagementAPI.getUsers({
      page: currentPage.value,
      limit: pageSize.value,
      search: search.value,
      role: roleFilter.value
    })
    
    if (response.success) {
      users.value = response.data.users
      total.value = response.data.total
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const fetchUserRoles = async () => {
  try {
    for (const user of users.value) {
      const response = await userManagementAPI.getUserDetail(user.id)
      if (response.success) {
        userRoles.value[user.id] = response.data.roles || []
      }
    }
  } catch (error) {
    console.error('获取用户角色失败:', error)
  }
}

const fetchAvailableRoles = async () => {
  try {
    const response = await permissionStore.fetchAllRoles()
    availableRoles.value = response.roles || []
  } catch (error) {
    console.error('获取可用角色失败:', error)
  }
}

const getUserRoles = (userId: string): Role[] => {
  return userRoles.value[userId] || []
}

const getRoleTagType = (roleCode: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' => {
  const typeMap: Record<string, 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'> = {
    'super_admin': 'error',
    'admin': 'warning',
    'researcher': 'primary',
    'data_entry': 'success',
    'reviewer': 'info',
    'viewer': 'default'
  }
  return typeMap[roleCode] || 'primary'
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const refreshData = async () => {
  await fetchUsers()
  await fetchUserRoles()
}

const handleSelectionChange = (rowKeys: Array<string | number>) => {
  selectedUsers.value = rowKeys as string[]
}

const handleRoleFilter = () => {
  currentPage.value = 1
  fetchUsers()
}

const handleStatusFilter = () => {
  currentPage.value = 1
  fetchUsers()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUsers()
}

const editUser = (user: User) => {
  selectedUser.value = user
  showEditDialog.value = true
}

const manageUserRoles = (user: User) => {
  selectedUser.value = user
  showRoleDialog.value = true
}

const viewUserDetails = (user: User) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

const viewUserActivity = (user: User) => {
  message.info('用户活动日志功能待实现')
}

const toggleUserStatus = async (user: User) => {
  try {
    const action = user.is_active ? '停用' : '激活'
    
    dialog.warning({
      title: '确认操作',
      content: `确定要${action}用户 ${user.username} 吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          const apiCall = user.is_active 
            ? userManagementAPI.deactivateUser(user.id)
            : userManagementAPI.activateUser(user.id)

          const response = await apiCall
          if (response.success) {
            message.success(`用户${action}成功`)
            await refreshData()
          } else {
            message.error(`用户${action}失败`)
          }
        } catch (error: unknown) {
          console.error('切换用户状态失败:', error)
          message.error('操作失败')
        }
      }
    })
  } catch (error: unknown) {
    console.error('切换用户状态失败:', error)
    message.error('操作失败')
  }
}

const resetPassword = async (user: User) => {
  try {
    dialog.warning({
      title: '确认重置密码',
      content: `确定要重置用户 ${user.username} 的密码吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          const newPassword = Math.random().toString(36).slice(-8) // 生成8位随机密码
          const response = await userManagementAPI.resetPassword(user.id, newPassword)
          
          if (response.success) {
            message.success(`密码重置成功，新密码：${newPassword}`)
          } else {
            message.error('密码重置失败')
          }
        } catch (error: unknown) {
          console.error('重置密码失败:', error)
          message.error('重置密码失败')
        }
      }
    })
  } catch (error: unknown) {
    console.error('重置密码失败:', error)
    message.error('重置密码失败')
  }
}

const deleteUser = async (user: User) => {
  try {
    dialog.error({
      title: '确认删除',
      content: `确定要删除用户 ${user.username} 吗？此操作不可恢复！`,
      positiveText: '删除',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          // 这里应该调用软删除API
          message.success('用户删除成功')
          await refreshData()
        } catch (error: unknown) {
          console.error('删除用户失败:', error)
          message.error('删除用户失败')
        }
      }
    })
  } catch (error: unknown) {
    console.error('删除用户失败:', error)
    message.error('删除用户失败')
  }
}

const batchActivateUsers = async () => {
  try {
    const userIds = selectedUsers.value
    const response = await userManagementAPI.batchUpdateUsers(userIds, 'activate')
    
    if (response.success) {
      message.success('批量激活成功')
      selectedUsers.value = []
      await refreshData()
    } else {
      message.error('批量激活失败')
    }
  } catch (error) {
    console.error('批量激活失败:', error)
    message.error('批量激活失败')
  }
}

const batchDeactivateUsers = async () => {
  try {
    const userIds = selectedUsers.value
    const response = await userManagementAPI.batchUpdateUsers(userIds, 'deactivate')
    
    if (response.success) {
      message.success('批量停用成功')
      selectedUsers.value = []
      await refreshData()
    } else {
      message.error('批量停用失败')
    }
  } catch (error) {
    console.error('批量停用失败:', error)
    message.error('批量停用失败')
  }
}

const handleCommand = (command: string, user: User) => {
  switch (command) {
    case 'view':
      viewUserDetails(user)
      break
    case 'activity':
      viewUserActivity(user)
      break
    case 'toggle-status':
      toggleUserStatus(user)
      break
    case 'reset-password':
      resetPassword(user)
      break
    case 'delete':
      deleteUser(user)
      break
  }
}

// 事件处理
const onUserCreated = () => {
  refreshData()
}

const onUserUpdated = () => {
  refreshData()
}

const onUserRolesUpdated = () => {
  fetchUserRoles()
}

// 生命周期
onMounted(() => {
  fetchAvailableRoles()
  refreshData()
})

// 监听搜索框变化
let searchTimer: ReturnType<typeof setTimeout> | null = null
watch(search, () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    fetchUsers()
  }, 300)
})
</script>

<style scoped>
.user-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  .page-title {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
  
  .page-description {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.user-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .user-avatar {
    flex-shrink: 0;
  }
  
  .user-details {
    flex: 1;
    
    .user-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .user-email {
      color: #606266;
      font-size: 13px;
      margin-bottom: 2px;
    }
    
    .user-username {
      color: #909399;
      font-size: 12px;
    }
  }
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  
  .role-tag {
    margin: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ebeef5;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table .n-data-table-td) {
  padding: 12px 0;
}

:deep(.danger-item) {
  color: #d03050;
}
</style>