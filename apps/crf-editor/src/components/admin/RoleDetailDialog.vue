<!-- 角色详情对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="700px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        角色详情: {{ role?.display_name }}
      </v-card-title>
      
      <v-card-text>
        <v-container>
          <!-- 基本信息 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">基本信息</h3>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="4" class="d-flex justify-center">
              <v-avatar size="120" :color="getRoleColor(role?.name)" class="elevation-4">
                <v-icon size="60" color="white">mdi-shield-account</v-icon>
              </v-avatar>
            </v-col>
            
            <v-col cols="12" md="8">
              <v-list density="compact">
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis">角色名称</v-list-item-title>
                  <v-list-item-subtitle class="text-h6">{{ role?.name }}</v-list-item-subtitle>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis">显示名称</v-list-item-title>
                  <v-list-item-subtitle class="text-body-1">{{ role?.display_name }}</v-list-item-subtitle>
                </v-list-item>
                
                <v-list-item v-if="role?.description">
                  <v-list-item-title class="text-caption text-medium-emphasis">描述</v-list-item-title>
                  <v-list-item-subtitle class="text-body-1">{{ role?.description }}</v-list-item-subtitle>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis">类型</v-list-item-title>
                  <v-list-item-subtitle>
                    <v-chip
                      :color="role?.is_system ? 'orange' : 'blue'"
                      size="small"
                    >
                      {{ role?.is_system ? '系统角色' : '自定义角色' }}
                    </v-chip>
                  </v-list-item-subtitle>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis">状态</v-list-item-title>
                  <v-list-item-subtitle>
                    <v-chip
                      :color="role?.is_active ? 'success' : 'error'"
                      size="small"
                    >
                      {{ role?.is_active ? '激活' : '停用' }}
                    </v-chip>
                  </v-list-item-subtitle>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis">创建时间</v-list-item-title>
                  <v-list-item-subtitle class="text-body-1">{{ formatDate(role?.created_at) }}</v-list-item-subtitle>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis">最后更新</v-list-item-title>
                  <v-list-item-subtitle class="text-body-1">{{ formatDate(role?.updated_at) }}</v-list-item-subtitle>
                </v-list-item>
              </v-list>
            </v-col>
          </v-row>
          
          <v-divider class="my-4" />
          
          <!-- 角色权限 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">角色权限 ({{ rolePermissions.length }} 个)</h3>
              
              <div v-if="rolePermissions.length > 0">
                <div class="permission-groups">
                  <div
                    v-for="(group, resource) in groupedPermissions"
                    :key="resource"
                    class="permission-group mb-3"
                  >
                    <h4 class="text-subtitle-1 mb-2 d-flex align-center">
                      <v-icon :color="getPermissionColor(resource)" class="mr-2">
                        {{ getPermissionIcon(resource) }}
                      </v-icon>
                      {{ getResourceDisplayName(resource) }} ({{ group.length }})
                    </h4>
                    <div class="permission-chips">
                      <v-chip
                        v-for="permission in group"
                        :key="permission.id"
                        :color="getPermissionColor(permission.resource)"
                        size="small"
                        class="ma-1"
                      >
                        {{ permission.action }}:{{ permission.scope }}
                      </v-chip>
                    </div>
                  </div>
                </div>
              </div>
              
              <v-alert
                v-else
                type="info"
                variant="tonal"
              >
                该角色尚未分配任何权限
              </v-alert>
            </v-col>
          </v-row>
          
          <v-divider class="my-4" />
          
          <!-- 统计信息 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">统计信息</h3>
              
              <v-row>
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="primary">
                    <v-icon size="24" class="mb-2">mdi-account-group</v-icon>
                    <div class="text-h5">{{ roleStats.userCount }}</div>
                    <div class="text-caption">用户数量</div>
                  </v-card>
                </v-col>
                
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="success">
                    <v-icon size="24" class="mb-2">mdi-key</v-icon>
                    <div class="text-h5">{{ rolePermissions.length }}</div>
                    <div class="text-caption">权限数量</div>
                  </v-card>
                </v-col>
                
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="warning">
                    <v-icon size="24" class="mb-2">mdi-folder</v-icon>
                    <div class="text-h5">{{ resourceCount }}</div>
                    <div class="text-caption">资源类型</div>
                  </v-card>
                </v-col>
                
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="info">
                    <v-icon size="24" class="mb-2">mdi-clock</v-icon>
                    <div class="text-h5">{{ daysSinceCreated }}</div>
                    <div class="text-caption">创建天数</div>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="closeDialog"
        >
          关闭
        </v-btn>
        <v-btn
          color="warning"
          variant="elevated"
          @click="managePermissions"
          :disabled="!canManageRoles"
        >
          管理权限
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="editRole"
          :disabled="!canManageRoles"
        >
          编辑角色
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { Role, Permission } from '@/types/rbac'

interface Props {
  modelValue: boolean
  role: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit-role', role: Role): void
  (e: 'manage-permissions', role: Role): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const rolePermissions = ref<Permission[]>([])
const roleStats = ref({
  userCount: 0
})

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canManageRoles = computed(() => 
  permissionStore.hasPermission('role', 'update')
)

const groupedPermissions = computed(() => {
  return rolePermissions.value.reduce((groups, permission) => {
    if (!groups[permission.resource]) {
      groups[permission.resource] = []
    }
    groups[permission.resource].push(permission)
    return groups
  }, {} as Record<string, Permission[]>)
})

const resourceCount = computed(() => {
  return new Set(rolePermissions.value.map(p => p.resource)).size
})

const daysSinceCreated = computed(() => {
  if (!props.role?.created_at) return 0
  const created = new Date(props.role.created_at)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - created.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

// 方法
const getRoleColor = (roleName?: string) => {
  if (!roleName) return 'primary'
  const colorMap: Record<string, string> = {
    'super_admin': 'purple',
    'admin': 'red',
    'researcher': 'blue',
    'data_entry': 'green',
    'reviewer': 'orange',
    'viewer': 'grey'
  }
  return colorMap[roleName] || 'primary'
}

const getPermissionColor = (resource: string) => {
  const colorMap: Record<string, string> = {
    'user': 'purple',
    'role': 'red',
    'project': 'blue',
    'template': 'green',
    'instance': 'orange',
    'data': 'cyan',
    'system': 'grey'
  }
  return colorMap[resource] || 'primary'
}

const getPermissionIcon = (resource: string) => {
  const iconMap: Record<string, string> = {
    'user': 'mdi-account',
    'role': 'mdi-shield-account',
    'project': 'mdi-folder',
    'template': 'mdi-file-document',
    'instance': 'mdi-form-select',
    'data': 'mdi-database',
    'system': 'mdi-cog'
  }
  return iconMap[resource] || 'mdi-key'
}

const getResourceDisplayName = (resource: string) => {
  const nameMap: Record<string, string> = {
    'user': '用户管理',
    'role': '角色管理',
    'project': '项目管理',
    'template': '模板管理',
    'instance': '实例管理',
    'data': '数据管理',
    'system': '系统管理'
  }
  return nameMap[resource] || resource
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const fetchRoleData = async () => {
  if (!props.role) return
  
  try {
    // 获取角色权限
    const roles = await permissionStore.fetchAllRoles(true)
    const currentRole = roles.find(r => r.id === props.role!.id)
    rolePermissions.value = currentRole?.permissions || []
    
    // 获取角色统计
    // 这里可以添加API调用获取统计数据
    roleStats.value = {
      userCount: 0 // 模拟数据
    }
    
  } catch (error) {
    console.error('获取角色数据失败:', error)
  }
}

const editRole = () => {
  if (props.role) {
    emit('edit-role', props.role)
    closeDialog()
  }
}

const managePermissions = () => {
  if (props.role) {
    emit('manage-permissions', props.role)
    closeDialog()
  }
}

const closeDialog = () => {
  isOpen.value = false
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.role) {
    fetchRoleData()
  }
})

watch(() => props.role, () => {
  if (props.modelValue && props.role) {
    fetchRoleData()
  }
})
</script>

<style scoped>
.permission-groups {
  max-height: 300px;
  overflow-y: auto;
}

.permission-group {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background-color: #fafafa;
}

.permission-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.v-card {
  overflow: visible;
}
</style>