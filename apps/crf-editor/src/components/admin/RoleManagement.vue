<!-- 角色管理页面 -->
<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">角色管理</h2>
        <p class="page-description">管理系统角色和权限分配</p>
      </div>
      <div class="header-actions">
        <n-button 
          type="primary" 
          @click="showCreateDialog = true"
          :disabled="!canManageRoles"
        >
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          创建角色
        </n-button>
        <n-button 
          @click="refreshData"
          :loading="loading"
        >
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <n-grid cols="24" :x-gap="20">
        <n-grid-item span="8">
          <n-input
            v-model:value="search"
            placeholder="搜索角色（角色名、显示名）"
            clearable
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
        </n-grid-item>
        <n-grid-item span="6">
          <n-select
            v-model:value="typeFilter"
            placeholder="筛选类型"
            clearable
            @update:value="handleTypeFilter"
            :options="typeOptions"
          />
        </n-grid-item>
        <n-grid-item span="4">
          <n-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            clearable
            @update:value="handleStatusFilter"
            :options="statusOptions"
          />
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 角色列表 -->
    <div class="role-table-container">
      <n-data-table
        :columns="columns"
        :data="filteredRoles"
        :loading="loading"
        :row-key="rowKey"
        striped
        bordered
        flex-height
        style="min-height: 400px"
      />

      <!-- 分页 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :item-count="total"
          show-size-picker
          show-quick-jumper
          @update:page="handleCurrentChange"
          @update:page-size="handleSizeChange"
        >
          <template #prefix="{ itemCount }">
            共 {{ itemCount }} 项
          </template>
        </n-pagination>
      </div>
    </div>

    <!-- 创建角色对话框 -->
    <CreateRoleDialog 
      v-model="showCreateDialog"
      @created="onRoleCreated"
    />

    <!-- 编辑角色对话框 -->
    <EditRoleDialog 
      v-model="showEditDialog"
      :role="selectedRole"
      @updated="onRoleUpdated"
    />

    <!-- 角色权限管理对话框 -->
    <RolePermissionDialog 
      v-model="showPermissionDialog"
      :role="selectedRole"
      @updated="onRolePermissionsUpdated"
    />

    <!-- 角色用户管理对话框 -->
    <RoleUsersDialog 
      v-model="showUsersDialog"
      :role="selectedRole"
      @updated="onRoleUsersUpdated"
    />

    <!-- 角色详情对话框 -->
    <RoleDetailDialog 
      v-model="showDetailDialog"
      :role="selectedRole"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { storeToRefs } from 'pinia'
import { 
  useMessage, useDialog, 
  NButton, NIcon, NTag, NDropdown, 
  type DataTableColumns, type DropdownOption
} from 'naive-ui'
import { 
  AddOutline, RefreshOutline, SearchOutline, CreateOutline, 
  KeyOutline, PeopleOutline, EllipsisHorizontalOutline, EyeOutline
} from '@vicons/ionicons5'
import { useUserStore } from '@/stores/user-store'
import { usePermissionStore } from '@/stores/permission-store'
import { roleManagementAPI } from '@/api/rbac'
import type { Role } from '@/types/rbac'
import CreateRoleDialog from './CreateRoleDialog.vue'
import EditRoleDialog from './EditRoleDialog.vue'
import RolePermissionDialog from './RolePermissionDialog.vue'
import RoleUsersDialog from './RoleUsersDialog.vue'
import RoleDetailDialog from './RoleDetailDialog.vue'

// UI 组件
const message = useMessage()
const dialog = useDialog()

// Stores
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// Store状态
const { user: currentUser } = storeToRefs(userStore)

// 响应式数据
const roles = ref<Role[]>([])
const rolePermissions = ref<Record<string, Record<string, unknown>[]>>({})
const roleUsers = ref<Record<string, Record<string, unknown>[]>>({})
const loading = ref(false)
const search = ref('')
const typeFilter = ref('')
const statusFilter = ref('')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showPermissionDialog = ref(false)
const showUsersDialog = ref(false)
const showDetailDialog = ref(false)
const selectedRole = ref<Role | null>(null)

// 数据表格配置
const rowKey = (row: Role) => row.id

// 选择器选项
const typeOptions = [
  { label: '系统角色', value: 'system' },
  { label: '自定义角色', value: 'custom' }
]

const statusOptions = [
  { label: '激活', value: 'active' },
  { label: '停用', value: 'inactive' }
]

// 创建下拉菜单
const createDropdownOptions = (role: Role): DropdownOption[] => [
  {
    label: '编辑角色',
    key: 'edit',
    disabled: !canManageRoles.value || role.is_system
  },
  {
    label: role.is_active ? '停用' : '激活',
    key: 'toggle-status',
    disabled: !canManageRoles.value || role.is_system
  },
  {
    type: 'divider'
  },
  {
    label: '删除角色',
    key: 'delete',
    disabled: !canManageRoles.value || role.is_system,
    props: {
      style: 'color: #e74c3c;'
    }
  }
]

// 数据表格列定义
const columns: DataTableColumns<Role> = [
  {
    title: '角色信息',
    key: 'role_info',
    minWidth: 200,
    render(row) {
      return h('div', { class: 'role-info' }, [
        h('div', { class: 'role-details' }, [
          h('div', { class: 'role-name' }, row.display_name),
          h('div', { class: 'role-code' }, row.code),
          h('div', { class: 'role-description' }, row.description || '暂无描述')
        ])
      ])
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_system ? 'warning' : 'primary',
        size: 'small'
      }, { default: () => row.is_system ? '系统' : '自定义' })
    }
  },
  {
    title: '权限数量',
    key: 'permission_count',
    width: 100,
    render(row) {
      return h('div', { class: 'permission-badge', style: 'text-align: center;' }, [
        h(NIcon, { size: 16, style: 'margin-right: 4px;' }, { default: () => h(KeyOutline) }),
        h('span', { class: 'badge-text' }, getRolePermissionCount(row.id))
      ])
    }
  },
  {
    title: '用户数量',
    key: 'user_count',
    width: 100,
    render(row) {
      return h('div', { class: 'user-badge', style: 'text-align: center;' }, [
        h(NIcon, { size: 16, style: 'margin-right: 4px;' }, { default: () => h(PeopleOutline) }),
        h('span', { class: 'badge-text' }, getRoleUserCount(row.id))
      ])
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render(row) {
      return h(NTag, {
        type: row.is_active ? 'success' : 'error',
        size: 'small'
      }, { default: () => row.is_active ? '激活' : '停用' })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render(row) {
      return formatDate(row.created_at)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 280,
    fixed: 'right',
    render(row) {
      return h('div', { class: 'action-buttons' }, [
        h(NButton, {
          size: 'small',
          onClick: () => viewRole(row)
        }, {
          default: () => '查看',
          icon: () => h(NIcon, null, { default: () => h(EyeOutline) })
        }),
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => managePermissions(row),
          disabled: !canManageRoles.value
        }, {
          default: () => '权限',
          icon: () => h(NIcon, null, { default: () => h(KeyOutline) })
        }),
        h(NButton, {
          size: 'small',
          type: 'warning',
          onClick: () => manageUsers(row),
          disabled: !canAssignRoles.value
        }, {
          default: () => '用户',
          icon: () => h(NIcon, null, { default: () => h(PeopleOutline) })
        }),
        h(NDropdown, {
          options: createDropdownOptions(row),
          onSelect: (key: string) => handleCommand(key, row)
        }, {
          default: () => h(NButton, { size: 'small' }, {
            icon: () => h(NIcon, null, { default: () => h(EllipsisHorizontalOutline) })
          })
        })
      ])
    }
  }
]

// 权限检查
const canManageRoles = computed(() => 
  userStore.hasPermission('role', 'create') ||
  userStore.hasPermission('role', 'update')
)

const canAssignRoles = computed(() => 
  userStore.hasPermission('role', 'assign_permission')
)

// 计算属性
const filteredRoles = computed(() => {
  let filtered = roles.value

  // 搜索过滤
  if (search.value) {
    const searchLower = search.value.toLowerCase()
    filtered = filtered.filter(role => 
      role.code.toLowerCase().includes(searchLower) ||
      role.display_name.toLowerCase().includes(searchLower) ||
      (role.description && role.description.toLowerCase().includes(searchLower))
    )
  }

  // 类型过滤
  if (typeFilter.value) {
    const isSystem = typeFilter.value === 'system'
    filtered = filtered.filter(role => role.is_system === isSystem)
  }

  // 状态过滤
  if (statusFilter.value) {
    const isActive = statusFilter.value === 'active'
    filtered = filtered.filter(role => role.is_active === isActive)
  }

  return filtered
})

// 方法
const fetchRoles = async () => {
  loading.value = true
  try {
    const response = await roleManagementAPI.getRoles({
      page: currentPage.value,
      limit: pageSize.value,
      search: search.value,
      type: typeFilter.value
    })
    
    if (response.success) {
      roles.value = response.data.roles
      total.value = response.data.total
    }
  } catch (error: unknown) {
    console.error('获取角色列表失败:', error)
    message.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await fetchRoles()
}

const getRolePermissionCount = (roleId: string): number => {
  return rolePermissions.value[roleId]?.length || 0
}

const getRoleUserCount = (roleId: string): number => {
  return roleUsers.value[roleId]?.length || 0
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleTypeFilter = () => {
  currentPage.value = 1
  fetchRoles()
}

const handleStatusFilter = () => {
  currentPage.value = 1
  fetchRoles()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchRoles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchRoles()
}

const viewRole = (role: Role) => {
  selectedRole.value = role
  showDetailDialog.value = true
}

const managePermissions = (role: Role) => {
  selectedRole.value = role
  showPermissionDialog.value = true
}

const manageUsers = (role: Role) => {
  selectedRole.value = role
  showUsersDialog.value = true
}

const editRole = (role: Role) => {
  selectedRole.value = role
  showEditDialog.value = true
}

const toggleRoleStatus = async (role: Role) => {
  const action = role.is_active ? '停用' : '激活'
  
  dialog.warning({
    title: '确认操作',
    content: `确定要${action}角色 ${role.display_name} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const apiCall = role.is_active 
          ? roleManagementAPI.deactivateRole(role.id)
          : roleManagementAPI.activateRole(role.id)

        const response = await apiCall
        if (response.success) {
          message.success(`角色${action}成功`)
          await refreshData()
        } else {
          message.error(`角色${action}失败`)
        }
      } catch (error: unknown) {
        console.error('切换角色状态失败:', error)
        message.error('操作失败')
      }
    }
  })
}

const deleteRole = async (role: Role) => {
  dialog.error({
    title: '确认删除',
    content: `确定要删除角色 ${role.display_name} 吗？此操作不可恢复！`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const response = await roleManagementAPI.deleteRole(role.id)
        if (response.success) {
          message.success('角色删除成功')
          await refreshData()
        } else {
          message.error('角色删除失败')
        }
      } catch (error: unknown) {
        console.error('删除角色失败:', error)
        message.error('删除角色失败')
      }
    }
  })
}

const handleCommand = (command: string, role: Role) => {
  switch (command) {
    case 'edit':
      editRole(role)
      break
    case 'toggle-status':
      toggleRoleStatus(role)
      break
    case 'delete':
      deleteRole(role)
      break
  }
}

// 事件处理
const onRoleCreated = () => {
  refreshData()
}

const onRoleUpdated = () => {
  refreshData()
}

const onRolePermissionsUpdated = () => {
  // 权限更新后刷新数据
  refreshData()
}

const onRoleUsersUpdated = () => {
  // 用户分配更新后刷新数据
  refreshData()
}

// 生命周期
onMounted(() => {
  refreshData()
})

// 监听搜索框变化
let searchTimer: ReturnType<typeof setTimeout> | null = null
watch(search, () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    fetchRoles()
  }, 300)
})
</script>

<style scoped>
.role-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  .page-title {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
  
  .page-description {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.role-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .role-details {
    flex: 1;
    
    .role-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .role-code {
      color: #606266;
      font-size: 13px;
      margin-bottom: 2px;
    }
    
    .role-description {
      color: #909399;
      font-size: 12px;
    }
  }
}

.permission-badge,
.user-badge {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ebeef5;
}
</style>