<!-- 角色用户对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="800px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        角色用户: {{ role?.display_name }}
      </v-card-title>
      
      <v-card-text>
        <v-container>
          <!-- 用户列表 -->
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-space-between align-center mb-3">
                <h3 class="text-h6">拥有该角色的用户 ({{ roleUsers.length }})</h3>
                <v-text-field
                  v-model="search"
                  label="搜索用户"
                  prepend-inner-icon="mdi-magnify"
                  variant="outlined"
                  density="compact"
                  style="max-width: 300px"
                  hide-details
                />
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <v-data-table
                :headers="headers"
                :items="filteredUsers"
                :loading="loading"
                :search="search"
                density="compact"
                class="elevation-1"
              >
                <!-- 用户信息 -->
                <template v-slot:item.user="{ item }">
                  <div class="d-flex align-center">
                    <v-avatar size="32" class="mr-3">
                      <v-img 
                        v-if="item.avatar_url" 
                        :src="item.avatar_url"
                        :alt="item.full_name"
                      />
                      <v-icon v-else>mdi-account</v-icon>
                    </v-avatar>
                    <div>
                      <div class="font-weight-medium">{{ item.full_name || item.username }}</div>
                      <div class="text-caption text-medium-emphasis">{{ item.email }}</div>
                    </div>
                  </div>
                </template>
                
                <!-- 项目范围 -->
                <template v-slot:item.scope="{ item }">
                  <div v-if="item.project_name">
                    <v-chip size="small" color="blue" variant="outlined">
                      <v-icon start>mdi-folder</v-icon>
                      {{ item.project_name }}
                    </v-chip>
                  </div>
                  <div v-else>
                    <v-chip size="small" color="purple">
                      <v-icon start>mdi-earth</v-icon>
                      全局
                    </v-chip>
                  </div>
                </template>
                
                <!-- 分配时间 -->
                <template v-slot:item.assigned_at="{ item }">
                  {{ formatDate(item.assigned_at) }}
                </template>
                
                <!-- 过期时间 -->
                <template v-slot:item.expires_at="{ item }">
                  <div v-if="item.expires_at">
                    <v-chip
                      :color="isExpired(item.expires_at) ? 'error' : 'warning'"
                      size="small"
                    >
                      {{ formatDate(item.expires_at) }}
                    </v-chip>
                  </div>
                  <span v-else class="text-medium-emphasis">永不过期</span>
                </template>
                
                <!-- 状态 -->
                <template v-slot:item.status="{ item }">
                  <v-chip
                    :color="getRoleUserStatus(item).color"
                    size="small"
                  >
                    {{ getRoleUserStatus(item).text }}
                  </v-chip>
                </template>
                
                <!-- 操作 -->
                <template v-slot:item.actions="{ item }">
                  <div class="action-buttons">
                    <v-btn
                      icon="mdi-eye"
                      size="small"
                      variant="text"
                      @click="viewUser(item)"
                    />
                    <v-btn
                      icon="mdi-pencil"
                      size="small"
                      variant="text"
                      @click="editUserRole(item)"
                      :disabled="!canManageRoles"
                    />
                    <v-btn
                      icon="mdi-delete"
                      size="small"
                      variant="text"
                      color="error"
                      @click="removeUserRole(item)"
                      :disabled="!canManageRoles"
                    />
                  </div>
                </template>
              </v-data-table>
              
              <v-alert
                v-if="roleUsers.length === 0 && !loading"
                type="info"
                variant="tonal"
                class="mt-4"
              >
                暂无用户拥有该角色
              </v-alert>
            </v-col>
          </v-row>
          
          <v-divider class="my-4" />
          
          <!-- 统计信息 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">统计信息</h3>
              
              <v-row>
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="primary">
                    <v-icon size="24" class="mb-2">mdi-account-group</v-icon>
                    <div class="text-h5">{{ roleUsers.length }}</div>
                    <div class="text-caption">总用户数</div>
                  </v-card>
                </v-col>
                
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="success">
                    <v-icon size="24" class="mb-2">mdi-check-circle</v-icon>
                    <div class="text-h5">{{ activeUsersCount }}</div>
                    <div class="text-caption">有效用户</div>
                  </v-card>
                </v-col>
                
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="warning">
                    <v-icon size="24" class="mb-2">mdi-clock-alert</v-icon>
                    <div class="text-h5">{{ expiredUsersCount }}</div>
                    <div class="text-caption">已过期</div>
                  </v-card>
                </v-col>
                
                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="info">
                    <v-icon size="24" class="mb-2">mdi-earth</v-icon>
                    <div class="text-h5">{{ globalUsersCount }}</div>
                    <div class="text-caption">全局权限</div>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="closeDialog"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { Role } from '@/types/rbac'
import { api } from '@/api/rbac'

interface Props {
  modelValue: boolean
  role: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const loading = ref(false)
const search = ref('')
const roleUsers = ref<Record<string, unknown>[]>([])

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canManageRoles = computed(() => 
  permissionStore.hasPermission('user', 'assign_role')
)

const filteredUsers = computed(() => {
  if (!search.value) return roleUsers.value
  const searchLower = search.value.toLowerCase()
  return roleUsers.value.filter(user => 
    user.username?.toLowerCase().includes(searchLower) ||
    user.email?.toLowerCase().includes(searchLower) ||
    user.full_name?.toLowerCase().includes(searchLower)
  )
})

const activeUsersCount = computed(() => {
  return roleUsers.value.filter(user => {
    const status = getRoleUserStatus(user)
    return status.text === '有效'
  }).length
})

const expiredUsersCount = computed(() => {
  return roleUsers.value.filter(user => {
    return user.expires_at && isExpired(user.expires_at)
  }).length
})

const globalUsersCount = computed(() => {
  return roleUsers.value.filter(user => !user.project_id).length
})

// 表格列定义
const headers = [
  { title: '用户', key: 'user', sortable: false },
  { title: '用户名', key: 'username' },
  { title: '作用范围', key: 'scope', sortable: false },
  { title: '分配时间', key: 'assigned_at' },
  { title: '过期时间', key: 'expires_at' },
  { title: '状态', key: 'status' },
  { title: '操作', key: 'actions', sortable: false }
]

// 方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const isExpired = (dateString: string) => {
  return new Date(dateString) < new Date()
}

const getRoleUserStatus = (userRole: Record<string, unknown>) => {
  if (userRole.expires_at && isExpired(userRole.expires_at)) {
    return { color: 'error', text: '已过期' }
  }
  return { color: 'success', text: '有效' }
}

const fetchRoleUsers = async () => {
  if (!props.role) return
  
  loading.value = true
  try {
    // 获取拥有该角色的用户列表
    // const response = await api.get(`/roles/${props.role.id}/users`)
    // roleUsers.value = response.data || []
    
    // 模拟数据
    roleUsers.value = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        full_name: '系统管理员',
        project_id: null,
        project_name: null,
        assigned_at: '2024-01-01T00:00:00Z',
        expires_at: null
      },
      {
        id: '2',
        username: 'researcher1',
        email: '<EMAIL>',
        full_name: '研究员一',
        project_id: 'proj1',
        project_name: '项目一',
        assigned_at: '2024-02-01T00:00:00Z',
        expires_at: '2024-12-31T23:59:59Z'
      }
    ]
  } catch (error) {
    console.error('获取角色用户失败:', error)
  } finally {
    loading.value = false
  }
}

const viewUser = (userRole: Record<string, unknown>) => {
  // 查看用户详情
  console.log('查看用户:', userRole)
}

const editUserRole = (userRole: Record<string, unknown>) => {
  // 编辑用户角色
  console.log('编辑用户角色:', userRole)
}

const removeUserRole = async (userRole: Record<string, unknown>) => {
  if (!confirm(`确定要移除用户 ${userRole.full_name || userRole.username} 的该角色吗？`)) return
  
  try {
    await permissionStore.removeRole(userRole.id, props.role!.id, userRole.project_id)
    await fetchRoleUsers()
  } catch (error) {
    console.error('移除用户角色失败:', error)
  }
}

const closeDialog = () => {
  isOpen.value = false
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.role) {
    fetchRoleUsers()
  }
})

watch(() => props.role, () => {
  if (props.modelValue && props.role) {
    fetchRoleUsers()
  }
})
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons .v-btn {
  min-width: 32px;
}

.v-card {
  overflow: visible;
}
</style>