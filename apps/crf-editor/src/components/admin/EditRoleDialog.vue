<!-- 编辑角色对话框 -->
<template>
  <n-modal v-model:show="isOpen" preset="dialog" title="编辑角色" style="width: 600px;" :mask-closable="false">
    <div class="edit-role-dialog">
      <h3 class="dialog-title">编辑角色: {{ role?.display_name }}</h3>
      
      <n-form ref="formRef" :model="roleForm" :rules="formRules">
        <n-form-item label="角色名称" path="name">
          <n-input
            v-model:value="roleForm.name"
            placeholder="角色的系统标识符"
            :disabled="role?.is_system"
          />
          <template #feedback>
            {{ role?.is_system ? '系统角色不能修改名称' : '角色的系统标识符' }}
          </template>
        </n-form-item>
        
        <n-form-item label="显示名称" path="display_name">
          <n-input
            v-model:value="roleForm.display_name"
            placeholder="角色的显示名称，用户友好的名称"
          />
        </n-form-item>
        
        <n-form-item label="角色描述">
          <n-input
            v-model:value="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="详细描述角色的职责和用途"
          />
        </n-form-item>
        
        <n-form-item v-if="!role?.is_system" label="激活角色">
          <n-switch v-model:value="roleForm.is_active" />
        </n-form-item>
        
        <n-alert v-if="role?.is_system" type="info" style="margin-bottom: 16px;">
          此为系统角色，只能修改显示名称和描述。要管理权限，请使用"权限管理"功能。
        </n-alert>
      </n-form>
    </div>
    
    <template #action>
      <n-button @click="closeDialog">取消</n-button>
      <n-button type="primary" :loading="loading" @click="updateRole">
        保存更改
      </n-button>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { NModal, NForm, NFormItem, NInput, NSwitch, NAlert, NButton } from 'naive-ui'
import { usePermissionStore } from '@/stores/permission-store'
import type { Role } from '@/types/rbac'
import type { FormInst, FormRules } from 'naive-ui'

interface Props {
  modelValue: boolean
  role: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const loading = ref(false)

// 表单数据
const roleForm = ref({
  name: '',
  display_name: '',
  description: '',
  is_active: true
})

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '角色名称是必填项', trigger: 'blur' },
    { min: 2, message: '角色名称至少2个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '角色名称只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '显示名称是必填项', trigger: 'blur' },
    { min: 2, message: '显示名称至少2个字符', trigger: 'blur' }
  ]
}

// 方法
const loadRoleData = () => {
  if (props.role) {
    roleForm.value = {
      name: props.role.name,
      display_name: props.role.display_name,
      description: props.role.description || '',
      is_active: props.role.is_active
    }
  }
}

const resetForm = () => {
  roleForm.value = {
    name: '',
    display_name: '',
    description: '',
    is_active: true
  }
  formRef.value?.restoreValidation()
}

const updateRole = async () => {
  if (!formRef.value || !props.role) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }
  
  loading.value = true
  try {
    const updateData: Partial<Role> = {
      display_name: roleForm.value.display_name,
      description: roleForm.value.description || undefined
    }
    
    // 非系统角色可以修改更多属性
    if (!props.role.is_system) {
      updateData.name = roleForm.value.name
      updateData.is_active = roleForm.value.is_active
    }
    
    await permissionStore.updateRole(props.role.id, updateData)
    
    emit('updated')
    closeDialog()
    
  } catch (error: unknown) {
    console.error('更新角色失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  isOpen.value = false
  resetForm()
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.role) {
    loadRoleData()
  }
})

watch(() => props.role, () => {
  if (props.modelValue && props.role) {
    loadRoleData()
  }
})
</script>

<style scoped>
.v-card {
  overflow: visible;
}
</style>