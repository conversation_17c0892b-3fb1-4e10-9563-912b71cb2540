<!-- 创建用户对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="600px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        创建新用户
      </v-card-title>
      
      <v-card-text>
        <v-form ref="form" v-model="valid" @submit.prevent="createUser">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="userForm.username"
                  label="用户名"
                  :rules="usernameRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-account"
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="userForm.email"
                  label="邮箱"
                  :rules="emailRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-email"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="userForm.full_name"
                  label="全名"
                  :rules="nameRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-account-circle"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="userForm.password"
                  label="密码"
                  :rules="passwordRules"
                  required
                  variant="outlined"
                  :type="showPassword ? 'text' : 'password'"
                  prepend-inner-icon="mdi-lock"
                  :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showPassword = !showPassword"
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="userForm.confirmPassword"
                  label="确认密码"
                  :rules="confirmPasswordRules"
                  required
                  variant="outlined"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  prepend-inner-icon="mdi-lock-check"
                  :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showConfirmPassword = !showConfirmPassword"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-autocomplete
                  v-model="userForm.roles"
                  label="分配角色"
                  :items="availableRoles"
                  item-title="display_name"
                  item-value="id"
                  multiple
                  chips
                  variant="outlined"
                  prepend-inner-icon="mdi-shield-account"
                  :disabled="!canAssignRoles"
                >
                  <template v-slot:chip="{ props, item }">
                    <v-chip
                      v-bind="props"
                      :color="getRoleColor(item.raw.name)"
                      size="small"
                    >
                      {{ item.raw.display_name }}
                    </v-chip>
                  </template>
                </v-autocomplete>
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-switch
                  v-model="userForm.is_active"
                  label="激活用户"
                  color="primary"
                  inset
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="userForm.notes"
                  label="备注"
                  variant="outlined"
                  rows="3"
                  prepend-inner-icon="mdi-note-text"
                />
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="closeDialog"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :loading="loading"
          :disabled="!valid"
          @click="createUser"
        >
          创建用户
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { Role } from '@/types/rbac'
import { api } from '@/api/rbac'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const form = ref()
const valid = ref(false)
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const availableRoles = ref<Role[]>([])

// 表单数据
const userForm = ref({
  username: '',
  email: '',
  full_name: '',
  password: '',
  confirmPassword: '',
  roles: [] as string[],
  is_active: true,
  notes: ''
})

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canAssignRoles = computed(() => 
  permissionStore.hasPermission('user', 'assign_role')
)

// 表单验证规则
const usernameRules = [
  (v: string) => !!v || '用户名是必填项',
  (v: string) => v.length >= 3 || '用户名至少3个字符',
  (v: string) => /^[a-zA-Z0-9_]+$/.test(v) || '用户名只能包含字母、数字和下划线'
]

const emailRules = [
  (v: string) => !!v || '邮箱是必填项',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址'
]

const nameRules = [
  (v: string) => !!v || '全名是必填项',
  (v: string) => v.length >= 2 || '全名至少2个字符'
]

const passwordRules = [
  (v: string) => !!v || '密码是必填项',
  (v: string) => v.length >= 8 || '密码至少8个字符',
  (v: string) => /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(v) || '密码必须包含大写字母、小写字母和数字'
]

const confirmPasswordRules = [
  (v: string) => !!v || '请确认密码',
  (v: string) => v === userForm.value.password || '两次密码输入不一致'
]

// 方法
const getRoleColor = (roleName: string) => {
  const colorMap: Record<string, string> = {
    'super_admin': 'purple',
    'admin': 'red',
    'researcher': 'blue',
    'data_entry': 'green',
    'reviewer': 'orange',
    'viewer': 'grey'
  }
  return colorMap[roleName] || 'primary'
}

const fetchRoles = async () => {
  try {
    const roles = await permissionStore.fetchAllRoles(false)
    availableRoles.value = roles.filter((role: Role) => role.is_active)
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

const resetForm = () => {
  userForm.value = {
    username: '',
    email: '',
    full_name: '',
    password: '',
    confirmPassword: '',
    roles: [],
    is_active: true,
    notes: ''
  }
  form.value?.resetValidation()
}

const createUser = async () => {
  if (!form.value?.validate()) return
  
  loading.value = true
  try {
    // 创建用户
    const userData = {
      username: userForm.value.username,
      email: userForm.value.email,
      full_name: userForm.value.full_name,
      password: userForm.value.password,
      is_active: userForm.value.is_active
    }
    
    const response = await api.post('/users', userData)
    const newUser = response.data
    
    // 分配角色
    if (userForm.value.roles.length > 0 && canAssignRoles.value) {
      for (const roleId of userForm.value.roles) {
        await permissionStore.assignRole(newUser.id, roleId)
      }
    }
    
    emit('created')
    closeDialog()
    
  } catch (error: unknown) {
    console.error('创建用户失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  isOpen.value = false
  resetForm()
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    fetchRoles()
    resetForm()
  }
})
</script>

<style scoped>
.v-card {
  overflow: visible;
}
</style>