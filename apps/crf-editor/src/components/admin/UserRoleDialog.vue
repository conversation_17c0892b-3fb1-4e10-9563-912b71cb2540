<!-- 用户角色管理对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="800px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        管理用户角色: {{ user?.username }}
      </v-card-title>
      
      <v-card-text>
        <v-container>
          <!-- 当前角色 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">当前角色</h3>
              <div v-if="userRoles.length > 0" class="role-chips">
                <v-chip
                  v-for="roleInfo in userRoles"
                  :key="roleInfo.id"
                  :color="getRoleColor(roleInfo.name)"
                  closable
                  @click:close="removeRole(roleInfo.id, roleInfo.project_id)"
                  class="ma-1"
                >
                  <v-icon start>mdi-shield-account</v-icon>
                  {{ roleInfo.display_name }}
                  <span v-if="roleInfo.project_name" class="ml-2 text-caption">
                    ({{ roleInfo.project_name }})
                  </span>
                </v-chip>
              </div>
              <v-alert
                v-else
                type="info"
                variant="tonal"
                class="mt-2"
              >
                该用户尚未分配任何角色
              </v-alert>
            </v-col>
          </v-row>
          
          <v-divider class="my-4" />
          
          <!-- 分配新角色 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">分配新角色</h3>
              
              <v-form ref="form" v-model="valid">
                <v-row>
                  <v-col cols="12" md="6">
                    <v-autocomplete
                      v-model="newRoleForm.role_id"
                      label="选择角色"
                      :items="availableRoles"
                      item-title="display_name"
                      item-value="id"
                      variant="outlined"
                      prepend-inner-icon="mdi-shield-account"
                      :rules="roleRules"
                      :disabled="!canAssignRoles"
                    >
                      <template v-slot:item="{ props, item }">
                        <v-list-item v-bind="props">
                          <template v-slot:prepend>
                            <v-avatar size="24" :color="getRoleColor(item.raw.name)">
                              <v-icon size="16" color="white">mdi-shield-account</v-icon>
                            </v-avatar>
                          </template>
                          <v-list-item-title>{{ item.raw.display_name }}</v-list-item-title>
                          <v-list-item-subtitle>{{ item.raw.description }}</v-list-item-subtitle>
                        </v-list-item>
                      </template>
                    </v-autocomplete>
                  </v-col>
                  
                  <v-col cols="12" md="6">
                    <v-autocomplete
                      v-model="newRoleForm.project_id"
                      label="选择项目 (可选)"
                      :items="availableProjects"
                      item-title="name"
                      item-value="id"
                      variant="outlined"
                      prepend-inner-icon="mdi-folder"
                      clearable
                      :disabled="!canAssignRoles"
                    />
                  </v-col>
                </v-row>
                
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="newRoleForm.expires_at"
                      label="过期时间 (可选)"
                      type="datetime-local"
                      variant="outlined"
                      prepend-inner-icon="mdi-calendar-clock"
                      :disabled="!canAssignRoles"
                    />
                  </v-col>
                  
                  <v-col cols="12" md="6" class="d-flex align-center">
                    <v-btn
                      color="primary"
                      variant="elevated"
                      :loading="loading"
                      :disabled="!valid || !canAssignRoles"
                      @click="assignRole"
                      block
                    >
                      <v-icon start>mdi-plus</v-icon>
                      分配角色
                    </v-btn>
                  </v-col>
                </v-row>
              </v-form>
            </v-col>
          </v-row>
          
          <v-divider class="my-4" />
          
          <!-- 角色历史 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">角色历史</h3>
              
              <v-data-table
                :headers="historyHeaders"
                :items="roleHistory"
                :loading="historyLoading"
                density="compact"
                class="elevation-1"
              >
                <template v-slot:item.role="{ item }">
                  <v-chip
                    :color="getRoleColor(item.role_name)"
                    size="small"
                  >
                    {{ item.role_display_name }}
                  </v-chip>
                </template>
                
                <template v-slot:item.project="{ item }">
                  <span v-if="item.project_name">
                    {{ item.project_name }}
                  </span>
                  <span v-else class="text-medium-emphasis">
                    全局
                  </span>
                </template>
                
                <template v-slot:item.status="{ item }">
                  <v-chip
                    :color="item.is_active ? 'success' : 'error'"
                    size="small"
                  >
                    {{ item.is_active ? '激活' : '停用' }}
                  </v-chip>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="closeDialog"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { User, Role, RoleInfo } from '@/types/rbac'
import { api } from '@/api/rbac'

interface Props {
  modelValue: boolean
  user: User | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const form = ref()
const valid = ref(false)
const loading = ref(false)
const historyLoading = ref(false)

const userRoles = ref<RoleInfo[]>([])
const availableRoles = ref<Role[]>([])
const availableProjects = ref<Record<string, unknown>[]>([])
const roleHistory = ref<Record<string, unknown>[]>([])

// 新角色表单
const newRoleForm = ref({
  role_id: '',
  project_id: null as string | null,
  expires_at: ''
})

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canAssignRoles = computed(() => 
  permissionStore.hasPermission('user', 'assign_role')
)

// 表单验证规则
const roleRules = [
  (v: string) => !!v || '请选择角色'
]

// 表格列定义
const historyHeaders = [
  { title: '角色', key: 'role' },
  { title: '项目', key: 'project' },
  { title: '分配时间', key: 'assigned_at' },
  { title: '过期时间', key: 'expires_at' },
  { title: '状态', key: 'status' }
]

// 方法
const getRoleColor = (roleName: string) => {
  const colorMap: Record<string, string> = {
    'super_admin': 'purple',
    'admin': 'red',
    'researcher': 'blue',
    'data_entry': 'green',
    'reviewer': 'orange',
    'viewer': 'grey'
  }
  return colorMap[roleName] || 'primary'
}

const fetchUserRoles = async () => {
  if (!props.user) return
  
  try {
    const response = await permissionStore.fetchUserRoles(props.user.id)
    // 将UserRole数据映射为RoleInfo格式
    userRoles.value = (response.roles || []).map((userRole: Record<string, unknown>): RoleInfo => ({
      id: userRole.role?.id || userRole.role_id,
      name: userRole.role?.name || userRole.role?.code || 'unknown',
      display_name: userRole.role?.display_name || userRole.role?.name || 'Unknown Role',
      project_id: userRole.project_id,
      project_name: userRole.project?.name,
      assigned_at: userRole.assigned_at,
      expires_at: userRole.expires_at
    }))
  } catch (error) {
    console.error('获取用户角色失败:', error)
  }
}

const fetchAvailableRoles = async () => {
  try {
    const roles = await permissionStore.fetchAllRoles(false)
    availableRoles.value = roles.filter((role: Role) => role.is_active)
  } catch (error) {
    console.error('获取可用角色失败:', error)
  }
}

const fetchAvailableProjects = async () => {
  try {
    const response = await api.get('/projects')
    availableProjects.value = response.data || []
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

const fetchRoleHistory = async () => {
  if (!props.user) return
  
  historyLoading.value = true
  try {
    // 这里可以获取用户的角色历史
    // const response = await api.get(`/user-roles/${props.user.id}/history`)
    // roleHistory.value = response.data || []
    
    // 模拟数据
    roleHistory.value = []
  } catch (error) {
    console.error('获取角色历史失败:', error)
  } finally {
    historyLoading.value = false
  }
}

const assignRole = async () => {
  if (!form.value?.validate() || !props.user) return
  
  loading.value = true
  try {
    await permissionStore.assignRole(
      props.user.id,
      newRoleForm.value.role_id,
      newRoleForm.value.project_id || undefined,
      newRoleForm.value.expires_at || undefined
    )
    
    // 重新获取用户角色
    await fetchUserRoles()
    
    // 重置表单
    resetForm()
    
    emit('updated')
    
  } catch (error: unknown) {
    console.error('分配角色失败:', error)
  } finally {
    loading.value = false
  }
}

const removeRole = async (roleId: string, projectId?: string) => {
  if (!props.user) return
  
  if (!confirm('确定要移除该角色吗？')) return
  
  try {
    await permissionStore.removeRole(props.user.id, roleId, projectId)
    
    // 重新获取用户角色
    await fetchUserRoles()
    
    emit('updated')
    
  } catch (error: unknown) {
    console.error('移除角色失败:', error)
  }
}

const resetForm = () => {
  newRoleForm.value = {
    role_id: '',
    project_id: null,
    expires_at: ''
  }
  form.value?.resetValidation()
}

const loadData = async () => {
  if (!props.user) return
  
  await Promise.all([
    fetchUserRoles(),
    fetchAvailableRoles(),
    fetchAvailableProjects(),
    fetchRoleHistory()
  ])
}

const closeDialog = () => {
  isOpen.value = false
  resetForm()
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.user) {
    loadData()
  }
})

watch(() => props.user, () => {
  if (props.modelValue && props.user) {
    loadData()
  }
})
</script>

<style scoped>
.role-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.v-card {
  overflow: visible;
}
</style>