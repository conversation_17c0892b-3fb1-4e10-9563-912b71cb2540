<!-- 编辑用户对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="600px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        编辑用户: {{ user?.username }}
      </v-card-title>
      
      <v-card-text>
        <v-form ref="form" v-model="valid" @submit.prevent="updateUser">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="userForm.username"
                  label="用户名"
                  :rules="usernameRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-account"
                  disabled
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="userForm.email"
                  label="邮箱"
                  :rules="emailRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-email"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="userForm.full_name"
                  label="全名"
                  :rules="nameRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-account-circle"
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-switch
                  v-model="userForm.is_active"
                  label="激活用户"
                  color="primary"
                  inset
                />
              </v-col>
            </v-row>
            
            <v-divider class="my-4" />
            
            <v-row>
              <v-col cols="12">
                <h3 class="text-h6 mb-3">更改密码</h3>
                <v-checkbox
                  v-model="changePassword"
                  label="更改用户密码"
                  color="primary"
                />
              </v-col>
            </v-row>
            
            <template v-if="changePassword">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="userForm.password"
                    label="新密码"
                    :rules="passwordRules"
                    variant="outlined"
                    :type="showPassword ? 'text' : 'password'"
                    prepend-inner-icon="mdi-lock"
                    :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showPassword = !showPassword"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="userForm.confirmPassword"
                    label="确认新密码"
                    :rules="confirmPasswordRules"
                    variant="outlined"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    prepend-inner-icon="mdi-lock-check"
                    :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showConfirmPassword = !showConfirmPassword"
                  />
                </v-col>
              </v-row>
            </template>
            
            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="userForm.notes"
                  label="备注"
                  variant="outlined"
                  rows="3"
                  prepend-inner-icon="mdi-note-text"
                />
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="closeDialog"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :loading="loading"
          :disabled="!valid"
          @click="updateUser"
        >
          保存更改
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { User } from '@/types/rbac'
import { api } from '@/api/rbac'

interface Props {
  modelValue: boolean
  user: User | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const form = ref()
const valid = ref(false)
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const changePassword = ref(false)

// 表单数据
const userForm = ref({
  username: '',
  email: '',
  full_name: '',
  password: '',
  confirmPassword: '',
  is_active: true,
  notes: ''
})

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canManageUsers = computed(() => 
  permissionStore.hasPermission('user', 'update')
)

// 表单验证规则
const usernameRules = [
  (v: string) => !!v || '用户名是必填项'
]

const emailRules = [
  (v: string) => !!v || '邮箱是必填项',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址'
]

const nameRules = [
  (v: string) => !!v || '全名是必填项',
  (v: string) => v.length >= 2 || '全名至少2个字符'
]

const passwordRules = computed(() => {
  if (!changePassword.value) return []
  return [
    (v: string) => !!v || '密码是必填项',
    (v: string) => v.length >= 8 || '密码至少8个字符',
    (v: string) => /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(v) || '密码必须包含大写字母、小写字母和数字'
  ]
})

const confirmPasswordRules = computed(() => {
  if (!changePassword.value) return []
  return [
    (v: string) => !!v || '请确认密码',
    (v: string) => v === userForm.value.password || '两次密码输入不一致'
  ]
})

// 方法
const loadUserData = () => {
  if (props.user) {
    userForm.value = {
      username: props.user.username,
      email: props.user.email,
      full_name: props.user.full_name,
      password: '',
      confirmPassword: '',
      is_active: props.user.is_active,
      notes: ''
    }
  }
}

const resetForm = () => {
  userForm.value = {
    username: '',
    email: '',
    full_name: '',
    password: '',
    confirmPassword: '',
    is_active: true,
    notes: ''
  }
  changePassword.value = false
  form.value?.resetValidation()
}

const updateUser = async () => {
  if (!form.value?.validate() || !props.user) return
  
  loading.value = true
  try {
    const updateData: Record<string, unknown> = {
      email: userForm.value.email,
      full_name: userForm.value.full_name,
      is_active: userForm.value.is_active
    }
    
    // 如果需要更改密码
    if (changePassword.value && userForm.value.password) {
      updateData.password = userForm.value.password
    }
    
    await api.put(`/users/${props.user.id}`, updateData)
    
    emit('updated')
    closeDialog()
    
  } catch (error: unknown) {
    console.error('更新用户失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  isOpen.value = false
  resetForm()
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.user) {
    loadUserData()
  }
})

watch(() => props.user, () => {
  if (props.modelValue && props.user) {
    loadUserData()
  }
})

watch(changePassword, (newVal) => {
  if (!newVal) {
    userForm.value.password = ''
    userForm.value.confirmPassword = ''
  }
})
</script>

<style scoped>
.v-card {
  overflow: visible;
}
</style>