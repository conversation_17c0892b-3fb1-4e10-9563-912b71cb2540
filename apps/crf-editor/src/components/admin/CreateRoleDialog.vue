<!-- 创建角色对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="600px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        创建新角色
      </v-card-title>
      
      <v-card-text>
        <v-form ref="form" v-model="valid" @submit.prevent="createRole">
          <v-container>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="roleForm.name"
                  label="角色名称"
                  :rules="nameRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-shield-account"
                  hint="角色的系统标识符，只能包含字母、数字和下划线"
                  persistent-hint
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="roleForm.display_name"
                  label="显示名称"
                  :rules="displayNameRules"
                  required
                  variant="outlined"
                  prepend-inner-icon="mdi-rename-box"
                  hint="角色的显示名称，用户友好的名称"
                  persistent-hint
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="roleForm.description"
                  label="角色描述"
                  variant="outlined"
                  rows="3"
                  prepend-inner-icon="mdi-text"
                  hint="详细描述角色的职责和用途"
                  persistent-hint
                />
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-switch
                  v-model="roleForm.is_active"
                  label="激活角色"
                  color="primary"
                  inset
                />
              </v-col>
            </v-row>
            
            <v-divider class="my-4" />
            
            <v-row>
              <v-col cols="12">
                <h3 class="text-h6 mb-3">初始权限 (可选)</h3>
                <v-autocomplete
                  v-model="roleForm.permissions"
                  label="选择权限"
                  :items="availablePermissions"
                  :item-title="getPermissionTitle"
                  item-value="id"
                  multiple
                  chips
                  variant="outlined"
                  prepend-inner-icon="mdi-key"
                  closable-chips
                >
                  <template v-slot:chip="{ props, item }">
                    <v-chip
                      v-bind="props"
                      :color="getPermissionColor(item.raw.resource)"
                      size="small"
                    >
                      {{ getPermissionTitle(item.raw) }}
                    </v-chip>
                  </template>
                  
                  <template v-slot:item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template v-slot:prepend>
                        <v-icon :color="getPermissionColor(item.raw.resource)">
                          {{ getPermissionIcon(item.raw.resource) }}
                        </v-icon>
                      </template>
                      <v-list-item-title>{{ getPermissionTitle(item.raw) }}</v-list-item-title>
                      <v-list-item-subtitle>{{ item.raw.description }}</v-list-item-subtitle>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="closeDialog"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :loading="loading"
          :disabled="!valid"
          @click="createRole"
        >
          创建角色
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { Permission } from '@/types/rbac'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const form = ref()
const valid = ref(false)
const loading = ref(false)
const availablePermissions = ref<Permission[]>([])

// 表单数据
const roleForm = ref({
  name: '',
  display_name: '',
  description: '',
  permissions: [] as string[],
  is_active: true
})

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const nameRules = [
  (v: string) => !!v || '角色名称是必填项',
  (v: string) => v.length >= 2 || '角色名称至少2个字符',
  (v: string) => /^[a-zA-Z0-9_]+$/.test(v) || '角色名称只能包含字母、数字和下划线'
]

const displayNameRules = [
  (v: string) => !!v || '显示名称是必填项',
  (v: string) => v.length >= 2 || '显示名称至少2个字符'
]

// 方法
const getPermissionTitle = (permission: Permission) => {
  return `${permission.resource}:${permission.action}:${permission.scope}`
}

const getPermissionColor = (resource: string) => {
  const colorMap: Record<string, string> = {
    'user': 'purple',
    'role': 'red',
    'project': 'blue',
    'template': 'green',
    'instance': 'orange',
    'data': 'cyan',
    'system': 'grey'
  }
  return colorMap[resource] || 'primary'
}

const getPermissionIcon = (resource: string) => {
  const iconMap: Record<string, string> = {
    'user': 'mdi-account',
    'role': 'mdi-shield-account',
    'project': 'mdi-folder',
    'template': 'mdi-file-document',
    'instance': 'mdi-form-select',
    'data': 'mdi-database',
    'system': 'mdi-cog'
  }
  return iconMap[resource] || 'mdi-key'
}

const fetchPermissions = async () => {
  try {
    const permissions = await permissionStore.fetchAllPermissions()
    availablePermissions.value = permissions.filter(p => !p.is_system)
  } catch (error) {
    console.error('获取权限列表失败:', error)
  }
}

const resetForm = () => {
  roleForm.value = {
    name: '',
    display_name: '',
    description: '',
    permissions: [],
    is_active: true
  }
  form.value?.resetValidation()
}

const createRole = async () => {
  if (!form.value?.validate()) return
  
  loading.value = true
  try {
    const roleData = {
      name: roleForm.value.name,
      display_name: roleForm.value.display_name,
      description: roleForm.value.description || undefined
    }
    
    const newRole = await permissionStore.createRole(roleData)
    
    // 分配权限
    if (roleForm.value.permissions.length > 0) {
      await permissionStore.assignPermissionsToRole(newRole.id, roleForm.value.permissions)
    }
    
    emit('created')
    closeDialog()
    
  } catch (error: unknown) {
    console.error('创建角色失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  isOpen.value = false
  resetForm()
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    fetchPermissions()
    resetForm()
  }
})
</script>

<style scoped>
.v-card {
  overflow: visible;
}
</style>