<template>
  <div class="form-item">
    <label class="form-label">{{ fieldLabel }}</label>
    <div class="options-config">
      <div class="options-header">
        <n-button 
          type="primary" 
          size="small" 
          @click="addOption"
        >
          <template #icon><n-icon><Plus /></n-icon></template>
          添加选项
        </n-button>
      </div>
      
      <div class="options-list">
        <div 
          v-for="(option, index) in optionsList" 
          :key="index"
          class="option-item"
        >
          <div class="option-inputs">
            <n-input
              v-model:value="option.label"
              placeholder="选项标签"
              size="small"
              @update:value="handleOptionChange"
            />
            <n-input
              v-model:value="option.value"
              placeholder="选项值"
              size="small"
              @update:value="handleOptionChange"
            />
          </div>
          <div class="option-actions">
            <n-button 
              type="error" 
              size="small" 
              text
              @click="removeOption(index)"
              :disabled="optionsList.length <= 1"
            >
              <template #icon><n-icon><Delete /></n-icon></template>
            </n-button>
          </div>
        </div>
      </div>
      
      <div v-if="optionsList.length === 0" class="empty-state">
        <n-empty description="暂无选项，点击上方按钮添加" size="small" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useFormConfig } from './mixins/useFormConfig'
import { NButton, NInput, NEmpty, NIcon } from 'naive-ui'
import { AddOutline as Plus, TrashOutline as Delete } from '@vicons/ionicons5'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  handleChange
} = useFormConfig(props, emit)

// 选项列表
const optionsList = ref(initOptions())

// 初始化选项
function initOptions() {
  const value = fieldValue.value
  if (Array.isArray(value) && value.length > 0) {
    return value.map(option => ({
      value: option.value || '',
      label: option.label || ''
    }))
  }
  return [
    { value: 'yes', label: '是' },
    { value: 'no', label: '否' }
  ]
}

// 添加选项
const addOption = () => {
  const newIndex = optionsList.value.length + 1
  const commonOptions = ['是', '否', '其他', '不确定', '男', '女', '轻度', '中度', '重度']
  const defaultLabel = commonOptions[newIndex - 1] || `选项${newIndex}`
  const defaultValue = `option${newIndex}`
  
  optionsList.value.push({
    value: defaultValue,
    label: defaultLabel
  })
  handleOptionChange()
}

// 删除选项
const removeOption = (index: number) => {
  optionsList.value.splice(index, 1)
  handleOptionChange()
}



// 处理选项变化
const handleOptionChange = () => {
  const validOptions = optionsList.value.filter(option => option.value && option.label)
  handleChange(validOptions)
}

// 监听外部数据变化
watch(() => props.data?.formData?.value, (newValue) => {
  if (newValue && Array.isArray(newValue)) {
    optionsList.value = newValue.map(option => ({
      value: option.value || '',
      label: option.label || ''
    }))
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.options-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.options-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 12px;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.option-item:hover {
  border-color: #c0c4cc;
}

.option-inputs {
  flex: 1;
  display: flex;
  gap: 8px;
  align-items: center;
}

.options-inputs .n-input {
  flex: 1;
}

.option-actions {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 16px;
}

/* 添加通用样式类 */
.form-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}
</style>