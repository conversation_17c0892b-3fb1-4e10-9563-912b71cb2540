<template>
  <n-form-item :label="data.label || '图标配置'" :path="data.key">
    <div class="config-icon-container">
      <!-- 图标输入框 -->
      <n-input
        v-model:value="iconValue"
        :placeholder="data.placeholder || '请输入图标名称（如：mdi:account）'"
        :clearable="data.clearable !== false"
        :disabled="data.disabled"
        :readonly="data.readonly"
        :size="data.size || 'medium'"
        @input="handleInput"
        @change="handleChange"
        @blur="handleBlur"
        @focus="handleFocus"
      >
        <!-- 图标预览插槽 -->
        <template #prefix>
          <div class="icon-preview">
            <crf-icon 
              v-if="iconValue && isValidIcon" 
              :icon="iconValue" 
              :size="16"
              :color="'#666'"
            />
            <span v-else class="icon-placeholder">🎨</span>
          </div>
        </template>
      </n-input>
      
      <!-- 图标说明 -->
      <div class="icon-help-text">
        <span class="help-text">支持 Iconify 格式，如：mdi:account、lucide:settings、heroicons:home</span>
      </div>
      
      <!-- 常用图标快捷选择 -->
      <div v-if="showQuickSelect" class="quick-select">
        <div class="quick-select-label">常用图标：</div>
        <div class="quick-select-icons">
          <div 
            v-for="quickIcon in quickIcons" 
            :key="quickIcon.value"
            class="quick-icon-item"
            :class="{ active: iconValue === quickIcon.value }"
            @click="selectQuickIcon(quickIcon.value)"
          >
            <crf-icon :icon="quickIcon.value" :size="18" />
            <span class="quick-icon-label">{{ quickIcon.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </n-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, watch } from 'vue'
import { CrfIcon } from '@crf/components'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const { data } = toRefs(props)

// 计算属性：从data中获取key
const fieldKey = computed(() => data.value?.key || '')

// 是否显示快捷选择
const showQuickSelect = computed(() => data.value?.showQuickSelect !== false)

// 常用图标列表
const quickIcons = ref([
  { value: 'mdi:account', label: '用户' },
  { value: 'mdi:email', label: '邮箱' },
  { value: 'mdi:lock', label: '锁定' },
  { value: 'mdi:eye', label: '查看' },
  { value: 'mdi:eye-off', label: '隐藏' },
  { value: 'mdi:search', label: '搜索' },
  { value: 'mdi:settings', label: '设置' },
  { value: 'mdi:home', label: '首页' },
  { value: 'mdi:phone', label: '电话' },
  { value: 'mdi:calendar', label: '日历' },
  { value: 'mdi:file-document', label: '文档' },
  { value: 'mdi:heart', label: '心脏' },
  { value: 'mdi:hospital', label: '医院' },
  { value: 'mdi:pill', label: '药品' },
  { value: 'mdi:stethoscope', label: '听诊器' },
  { value: 'mdi:thermometer', label: '体温计' }
])

// 获取初始值
const getInitialValue = () => {
  const formValue = data.value.formData?.value
  const defaultValue = data.value.default
  
  // 优先使用表单值
  if (formValue !== undefined && formValue !== null) {
    return String(formValue)
  }
  
  // 其次使用默认值
  if (defaultValue !== undefined && defaultValue !== null) {
    return String(defaultValue)
  }
  
  // 返回空字符串
  return ''
}

// 初始化图标值
const iconValue = ref(getInitialValue())

// 验证图标是否有效（简单验证格式）
const isValidIcon = computed(() => {
  if (!iconValue.value) return false
  
  // 检查是否符合 iconify 格式：prefix:name
  const iconFormat = /^[a-zA-Z][a-zA-Z0-9-]*:[a-zA-Z0-9-_]+$/
  return iconFormat.test(iconValue.value)
})

// 处理输入事件
const handleInput = (value: string) => {
  iconValue.value = value
  // 立即触发更新
  emit('update', {
    [fieldKey.value]: value
  })
}

// 处理变化事件
const handleChange = (_value: string) => {
  // change事件主要用于失焦时的最终确认
  // 输入时主要依赖input事件
}

// 处理失焦事件
const handleBlur = (_event: FocusEvent) => {
  // 失焦时确保最终值已更新
  emit('update', {
    [fieldKey.value]: iconValue.value
  })
}

// 处理聚焦事件
const handleFocus = (_event: FocusEvent) => {
  // 可以在这里添加聚焦逻辑
}

// 选择快捷图标
const selectQuickIcon = (icon: string) => {
  iconValue.value = icon
  emit('update', {
    [fieldKey.value]: icon
  })
}

// 监听data变化，更新内部值
watch(
  () => data.value.formData?.value,
  (newValue) => {
    if (newValue !== undefined && newValue !== null) {
      iconValue.value = String(newValue)
    }
  },
  { immediate: true }
)

// 监听默认值变化
watch(
  () => data.value.default,
  (newDefault) => {
    // 只有在当前值为空且有新默认值时才更新
    if (!iconValue.value && newDefault !== undefined && newDefault !== null) {
      iconValue.value = String(newDefault)
      emit('update', {
        [fieldKey.value]: String(newDefault)
      })
    }
  }
)
</script>

<style scoped>
.config-icon-container {
  width: 100%;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 28px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-right: 8px;
}

.icon-placeholder {
  font-size: 16px;
  color: #ccc;
}

.icon-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.help-text {
  display: block;
}

.quick-select {
  margin-top: 12px;
  padding: 8px 0;
  border-top: 1px solid #eee;
}

.quick-select-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.quick-select-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
}

.quick-icon-item:hover {
  border-color: #409eff;
  background: #f0f8ff;
}

.quick-icon-item.active {
  border-color: #409eff;
  background: #409eff;
  color: white;
}

.quick-icon-item.active .quick-icon-label {
  color: white;
}

.quick-icon-label {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
  text-align: center;
  line-height: 1.2;
}

:deep(.n-input-group__prepend) {
  background: #f5f5f5;
  border-color: #dcdfe6;
}

:deep(.n-input-group__prepend) .icon-preview {
  margin: 0;
}
</style>