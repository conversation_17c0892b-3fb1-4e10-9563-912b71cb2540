<template>
  <n-form-item :label="fieldLabel" :path="fieldKey" :rule="validationRules">
    <n-input
      v-model:value="fieldValue"
      type="textarea"
      :placeholder="data.placeholder || '请输入内容'"
      :disabled="data.disabled"
      :readonly="data.readonly"
      :rows="data.rows || 4"
      :minlength="data.minLength"
      :maxlength="data.maxLength"
      :show-count="data.showWordLimit || data.showCount"
      :autosize="data.autosize"
      :resizable="data.resize !== 'none'"
      @input="handleInput"
      @update:value="handleChange"
      @blur="handleBlur"
    />
  </n-form-item>
</template>

<script lang="ts" setup>
// Vue 3 编译器宏不需要导入
import { useFormConfig } from './mixins/useFormConfig'

const emit = defineEmits(['update', 'input', 'change', 'blur', 'focus'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  handleInput,
  handleChange,
  handleBlur
} = useFormConfig(props, emit)
</script>

<style lang="scss" scoped>
.config-textarea-count {
  margin-top: 4px;
  text-align: right;
  color: #6b7280;
  font-size: 12px;
}
</style>