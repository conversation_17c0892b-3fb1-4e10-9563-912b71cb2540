<template>
  <n-form-item :label="fieldLabel" :path="fieldKey" :rule="validationRules">
    <n-checkbox-group v-model:value="fieldValue" @update:value="handleChange">
      <n-checkbox
        v-for="option in options"
        :key="option.value"
        :value="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </n-checkbox>
    </n-checkbox-group>
  </n-form-item>
</template>

<script lang="ts" setup>
// Vue 3 编译器宏不需要导入
import { useFormConfig } from './mixins/useFormConfig'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  options,
  handleChange
} = useFormConfig(props, emit)
</script>