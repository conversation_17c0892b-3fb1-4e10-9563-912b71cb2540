<template>
  <n-form-item :label="data.label || '滑块'" :path="data.key">
    <div class="config-slider-wrapper">
      <n-slider
        v-model:value="sliderValue"
        :min="data.min || 0"
        :max="data.max || 100"
        :step="data.step || 1"
        :disabled="data.disabled"
        :marks="data.marks"
        :range="data.range"
        :vertical="data.vertical"
        :tooltip="data.showTooltip !== false"
        :format-tooltip="formatTooltip"
        @update:value="handleChange"
      />
      <div v-if="data.showInput" class="config-slider-input">
        <n-input-number
          v-model:value="sliderValue"
          :min="data.min || 0"
          :max="data.max || 100"
          :step="data.step || 1"
          :disabled="data.disabled"
          :size="data.size || 'small'"
          :show-button="false"
          style="width: 80px"
          @update:value="handleChange"
        />
      </div>
    </div>
  </n-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, watch } from 'vue'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const { data } = toRefs(props)

// 计算属性：从data中获取key
const fieldKey = computed(() => data.value?.key || '')

// 获取初始值
const getInitialValue = () => {
  const formValue = data.value.formData?.value
  const defaultValue = data.value.default
  const min = data.value.min || 0
  const isRange = data.value.range || false
  
  // 如果有表单值
  if (formValue !== undefined) {
    if (isRange) {
      return Array.isArray(formValue) ? formValue : [min, formValue]
    }
    return Number(formValue) || min
  }
  
  // 如果有默认值
  if (defaultValue !== undefined) {
    if (isRange) {
      return Array.isArray(defaultValue) ? defaultValue : [min, defaultValue]
    }
    return Number(defaultValue) || min
  }
  
  // 返回默认值
  return isRange ? [min, min] : min
}

// 初始化滑块值
const sliderValue = ref(getInitialValue())

// 格式化提示文本
const formatTooltip = (value: number) => {
  if (data.value.formatter && typeof data.value.formatter === 'function') {
    return data.value.formatter(value)
  }
  
  // 默认格式化
  if (data.value.unit) {
    return `${value}${data.value.unit}`
  }
  
  return String(value)
}

// 处理滑块输入事件（拖拽中）
const handleInput = (value: number | number[]) => {
  sliderValue.value = value
  // 可选：在拖拽中也触发更新
  if (data.value.realtime) {
    emit('update', {
      [fieldKey.value]: value
    })
  }
}

// 处理滑块变化事件（拖拽结束）
const handleChange = (value: number | number[]) => {
  sliderValue.value = value
  emit('update', {
    [fieldKey.value]: value
  })
}

// 监听data变化，更新内部值
watch(
  () => data.value.formData?.value,
  (newValue) => {
    if (newValue !== undefined) {
      const isRange = data.value.range || false
      const min = data.value.min || 0
      
      if (isRange) {
        sliderValue.value = Array.isArray(newValue) ? newValue : [min, newValue]
      } else {
        sliderValue.value = Number(newValue) || min
      }
    }
  },
  { immediate: true }
)

// 监听默认值变化
watch(
  () => data.value?.default,
  (newValue) => {
    if (newValue !== undefined && data.value.formData?.value === undefined) {
      const isRange = data.value.range || false
      const min = data.value.min || 0
      
      if (isRange) {
        sliderValue.value = Array.isArray(newValue) ? newValue : [min, newValue]
      } else {
        sliderValue.value = Number(newValue) || min
      }
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.config-slider-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;

  .config-slider-input {
    flex-shrink: 0;
  }
}


</style>