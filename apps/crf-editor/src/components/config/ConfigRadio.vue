<template>
  <n-form-item :label="fieldLabel" :path="fieldKey" :rule="validationRules">
    <n-radio-group v-model:value="fieldValue" @update:value="handleChange">
      <n-radio
        v-for="option in options"
        :key="option.value"
        :value="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </n-radio>
    </n-radio-group>
  </n-form-item>
</template>

<script lang="ts" setup>
// Vue 3 编译器宏不需要导入
import { useFormConfig } from './mixins/useFormConfig'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  options,
  handleChange
} = useFormConfig(props, emit)
</script>