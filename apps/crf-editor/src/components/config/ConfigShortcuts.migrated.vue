<template>
  <div class="form-item">
    <label class="form-label">{{ fieldLabel }}</label>
    <div class="shortcuts-config">
      <div class="flex justify-between items-center mb-4">
        <span class="font-medium text-gray-800">快捷选项配置</span>
        <n-button 
          type="primary" 
          size="small" 
          @click="addShortcut"
        >
          <template #icon><n-icon><Plus /></n-icon></template>
          添加快捷选项
        </n-button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(shortcut, index) in shortcutsList" 
          :key="index"
          class="flex items-center gap-3 p-3 bg-white border border-gray-200 rounded"
        >
          <div class="flex-1 grid grid-cols-3 gap-2 items-center">
            <n-input
              v-model:value="shortcut.text"
              placeholder="快捷选项文本"
              size="small"
              @update:value="handleShortcutChange"
            />
            <n-input
              v-model:value="shortcut.value"
              placeholder="快捷选项值"
              size="small"
              @update:value="handleShortcutChange"
            />
            <n-input
              v-model:value="shortcut.description"
              placeholder="选项描述（可选）"
              size="small"
              @update:value="handleShortcutChange"
            />
          </div>
          <div class="flex gap-1">
            <n-button 
              size="small" 
              text
              @click="moveUp(index)"
              :disabled="index === 0"
            >
              <template #icon><n-icon><ArrowUp /></n-icon></template>
            </n-button>
            <n-button 
              size="small" 
              text
              @click="moveDown(index)"
              :disabled="index === shortcutsList.length - 1"
            >
              <template #icon><n-icon><ArrowDown /></n-icon></template>
            </n-button>
            <n-button 
              type="error" 
              size="small" 
              text
              @click="removeShortcut(index)"
            >
              <template #icon><n-icon><Delete /></n-icon></template>
            </n-button>
          </div>
        </div>
      </div>
      
      <div v-if="shortcutsList.length === 0" class="text-center py-5">
        <n-empty description="暂无快捷选项，点击上方按钮添加" size="small" />
      </div>
      
      <div class="mt-4">
        <n-divider title-placement="left">预设快捷选项</n-divider>
        <div class="flex flex-wrap gap-2">
          <n-button size="small" @click="addPreset('today')">今天</n-button>
          <n-button size="small" @click="addPreset('yesterday')">昨天</n-button>
          <n-button size="small" @click="addPreset('lastWeek')">最近一周</n-button>
          <n-button size="small" @click="addPreset('lastMonth')">最近一月</n-button>
          <n-button size="small" @click="addPreset('lastThreeMonths')">最近三月</n-button>
          <n-button size="small" @click="addPreset('lastYear')">最近一年</n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useFormConfig } from './mixins/useFormConfig'
import { NButton, NInput, NEmpty, NDivider, NIcon } from 'naive-ui'
import { 
  AddOutline as Plus, 
  ChevronUpOutline as ArrowUp, 
  ChevronDownOutline as ArrowDown, 
  TrashOutline as Delete 
} from '@vicons/ionicons5'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  handleChange
} = useFormConfig(props, emit)

// 快捷选项列表
const shortcutsList = ref(initShortcuts())

// 预设快捷选项
const presetShortcuts = {
  today: { text: '今天', value: '', description: '选择今天的日期' },
  yesterday: { text: '昨天', value: '', description: '选择昨天的日期' },
  lastWeek: { text: '最近一周', value: '', description: '选择最近一周的日期范围' },
  lastMonth: { text: '最近一月', value: '', description: '选择最近一个月的日期范围' },
  lastThreeMonths: { text: '最近三月', value: '', description: '选择最近三个月的日期范围' },
  lastYear: { text: '最近一年', value: '', description: '选择最近一年的日期范围' }
}

// 初始化快捷选项
function initShortcuts() {
  const value = fieldValue.value
  if (Array.isArray(value) && value.length > 0) {
    return value.map(shortcut => ({
      text: shortcut.text || '',
      value: shortcut.value || '',
      description: shortcut.description || ''
    }))
  }
  return [
    { text: '今天', value: '', description: '选择今天的日期' },
    { text: '最近一周', value: '', description: '选择最近一周的日期范围' }
  ]
}

// 添加快捷选项
const addShortcut = () => {
  const newIndex = shortcutsList.value.length + 1
  shortcutsList.value.push({
    text: `快捷选项${newIndex}`,
    value: '',
    description: ''
  })
  handleShortcutChange()
}

// 添加预设快捷选项
const addPreset = (presetKey: string) => {
  const preset = presetShortcuts[presetKey as keyof typeof presetShortcuts]
  if (preset) {
    // 检查是否已存在相同的快捷选项
    const exists = shortcutsList.value.some(shortcut => shortcut.text === preset.text)
    if (!exists) {
      shortcutsList.value.push({ ...preset })
      handleShortcutChange()
    }
  }
}

// 删除快捷选项
const removeShortcut = (index: number) => {
  shortcutsList.value.splice(index, 1)
  handleShortcutChange()
}

// 上移快捷选项
const moveUp = (index: number) => {
  if (index > 0 && shortcutsList.value[index] && shortcutsList.value[index - 1]) {
    const temp = shortcutsList.value[index]
    shortcutsList.value[index] = shortcutsList.value[index - 1]!
    shortcutsList.value[index - 1] = temp
    handleShortcutChange()
  }
}

// 下移快捷选项
const moveDown = (index: number) => {
  if (index < shortcutsList.value.length - 1 && shortcutsList.value[index] && shortcutsList.value[index + 1]) {
    const temp = shortcutsList.value[index]
    shortcutsList.value[index] = shortcutsList.value[index + 1]!
    shortcutsList.value[index + 1] = temp
    handleShortcutChange()
  }
}

// 处理快捷选项变化
const handleShortcutChange = () => {
  const validShortcuts = shortcutsList.value.filter(shortcut => shortcut.text)
  handleChange(validShortcuts)
}

// 监听外部数据变化
watch(() => props.data?.formData?.value, (newValue) => {
  if (newValue && Array.isArray(newValue)) {
    shortcutsList.value = newValue.map(shortcut => ({
      text: shortcut.text || '',
      value: shortcut.value || '',
      description: shortcut.description || ''
    }))
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
/* 保留复杂的自定义样式 */
.shortcuts-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

/* 保留表单项的基础样式 */
.form-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}
</style>