import { computed, ref, watch, toRefs } from 'vue'
import { useFormValidation, useOptionsParser } from '@crf/composables'

/**
 * 表单配置组件通用逻辑
 */
export function useFormConfig(props: Record<string, unknown>, emit: (event: string, ...args: unknown[]) => void) {
  const { data } = toRefs(props)

  // 使用表单验证组合式函数
  const { commonValidators } = useFormValidation()

  // 处理选项数据，正确处理enumNames
  const options = computed(() => {
    if (!data?.value) return []

    const configValue = data.value

    // 如果直接提供了options数组
    if (configValue.options && Array.isArray(configValue.options)) {
      return configValue.options.map((item: Record<string, unknown>) => ({
        value: item.value,
        label: item.label || item.value,
        disabled: item.disabled || false
      }))
    }

    // 处理Union类型的枚举值（如direction: 'horizontal' | 'vertical'）
    if (configValue.anyOf && Array.isArray(configValue.anyOf)) {
      return configValue.anyOf.map((item: Record<string, unknown>, index: number) => ({
        value: item.const,
        label: configValue.enumNames?.[index] || item.const,
        disabled: false
      }))
    }

    // 如果提供了enum和enumNames
    if (configValue.enum && Array.isArray(configValue.enum)) {
      return configValue.enum.map((value: unknown, index: number) => ({
        value,
        label: configValue.enumNames?.[index] || value,
        disabled: false
      }))
    }

    return []
  })

  // 使用选项解析组合式函数的其他功能
  const { currentValue, isValidValue } = data ? useOptionsParser(data) : { currentValue: ref(undefined), isValidValue: () => false }

  // 计算字段键名
  const fieldKey = computed(() => data?.value?.key || '')

  // 计算字段标签
  const fieldLabel = computed(() => data?.value?.label || '字段')

  // 获取初始值
  const getInitialValue = () => {
    const formValue = data?.value?.formData?.value
    const defaultValue = data?.value?.default

    // 优先使用表单值
    if (formValue !== undefined && formValue !== null) {
      return formValue
    }

    // 其次使用默认值
    if (defaultValue !== undefined && defaultValue !== null) {
      return defaultValue
    }

    // 返回空值
    return ''
  }

  // 初始化字段值
  const fieldValue = ref(getInitialValue())

  // 计算Element Plus验证规则
  const validationRules = computed(() => {
    const rules = []
    const validation = data?.value?.validation || {}

    // 必填验证
    if (validation.required) {
      rules.push({
        required: true,
        message: validation.message || `${fieldLabel.value}不能为空`,
        trigger: 'blur'
      })
    }

    // 长度验证
    if (validation.minLength && validation.minLength > 0) {
      rules.push({
        min: validation.minLength,
        message: `${fieldLabel.value}最少需要${validation.minLength}个字符`,
        trigger: 'blur'
      })
    }

    if (validation.maxLength) {
      rules.push({
        max: validation.maxLength,
        message: `${fieldLabel.value}最多允许${validation.maxLength}个字符`,
        trigger: 'blur'
      })
    }

    // 数值范围验证
    if (validation.min !== undefined) {
      rules.push({
        validator: (_rule: unknown, value: unknown, callback: (error?: Error) => void) => {
          const num = Number(value)
          if (value && (isNaN(num) || num < validation.min)) {
            callback(new Error(`${fieldLabel.value}不能小于${validation.min}`))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      })
    }

    if (validation.max !== undefined) {
      rules.push({
        validator: (_rule: unknown, value: unknown, callback: (error?: Error) => void) => {
          const num = Number(value)
          if (value && (isNaN(num) || num > validation.max)) {
            callback(new Error(`${fieldLabel.value}不能大于${validation.max}`))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      })
    }

    // 正则验证
    if (validation.pattern) {
      rules.push({
        pattern: new RegExp(validation.pattern),
        message: validation.patternMessage || `${fieldLabel.value}格式不正确`,
        trigger: 'blur'
      })
    }

    // 预设验证类型
    if (validation.type) {
      let validator = null
      switch (validation.type) {
        case 'email':
          validator = commonValidators.email
          break
        case 'phone':
          validator = commonValidators.phone
          break
        case 'url':
          validator = commonValidators.url
          break
        case 'idCard':
          validator = commonValidators.idCard
          break
        case 'number':
          validator = commonValidators.number
          break
        case 'integer':
          validator = commonValidators.integer
          break
        case 'positiveNumber':
          validator = commonValidators.positiveNumber
          break
      }

      if (validator) {
        rules.push({
          validator: (_rule: unknown, value: unknown, callback: (error?: Error) => void) => {
            if (value) {
              const error = validator(value)
              if (error) {
                callback(new Error(error))
              } else {
                callback()
              }
            } else {
              callback()
            }
          },
          trigger: 'blur'
        })
      }
    }

    return rules
  })

  // 处理字段值变化
  const handleFieldChange = (value: unknown) => {
    fieldValue.value = value
    emit('update', {
      [fieldKey.value]: value
    })
  }

  // 处理输入事件
  const handleInput = (value: unknown) => {
    fieldValue.value = value
    emit('update', {
      [fieldKey.value]: value
    })
  }

  // 处理变化事件
  const handleChange = (value: unknown) => {
    fieldValue.value = value
    emit('update', {
      [fieldKey.value]: value
    })
  }

  // 处理失焦事件
  const handleBlur = () => {
    emit('update', {
      [fieldKey.value]: fieldValue.value
    })
  }

  // 监听表单数据变化
  watch(
    () => data?.value?.formData?.value,
    (newValue) => {
      if (newValue !== undefined && newValue !== null) {
        fieldValue.value = newValue
      }
    },
    { immediate: true }
  )

  // 监听默认值变化
  watch(
    () => data?.value?.default,
    (newDefault) => {
      if (!fieldValue.value && newDefault !== undefined && newDefault !== null) {
        fieldValue.value = newDefault
        emit('update', {
          [fieldKey.value]: newDefault
        })
      }
    }
  )

  return {
    fieldKey,
    fieldLabel,
    fieldValue,
    validationRules,
    options,
    currentValue,
    isValidValue,
    handleFieldChange,
    handleInput,
    handleChange,
    handleBlur
  }
}