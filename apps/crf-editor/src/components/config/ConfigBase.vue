<template>
  <div class="config-base">
    <!-- 基础配置分组 -->
    <n-divider title-placement="left">
      <span class="config-group-title">
        <n-icon><Setting /></n-icon>
        基础配置
      </span>
    </n-divider>

    <!-- 标题配置 -->
    <div class="form-item">
      <label class="form-label">标题</label>
      <n-input
        v-model:value="formData.title"
        placeholder="请输入组件标题"
        clearable
        @update:value="handleFieldChange('title', $event)"
      />
    </div>

    <!-- 描述配置 -->
    <div class="form-item">
      <label class="form-label">描述信息</label>
      <n-input
        v-model:value="formData.description"
        type="textarea"
        placeholder="请输入组件描述信息"
        :rows="2"
        clearable
        @update:value="handleFieldChange('description', $event)"
      />
    </div>

    <!-- 必填配置 -->
    <div class="form-item">
      <label class="form-label">必填项</label>
      <n-switch
        v-model:value="formData.required"
        @update:value="handleFieldChange('required', $event)"
      />
    </div>

    <!-- 帮助文本配置 -->
    <div class="form-item">
      <label class="form-label">帮助文本</label>
      <n-input
        v-model:value="formData.helpText"
        placeholder="请输入帮助文本"
        clearable
        @update:value="handleFieldChange('helpText', $event)"
      />
    </div>

    <!-- 状态配置分组 -->
    <n-divider title-placement="left">
      <span class="config-group-title">
        <n-icon><MoreFilled /></n-icon>
        状态配置
      </span>
    </n-divider>

    <n-space size="large">
      <!-- 禁用状态 -->
      <div class="form-item">
        <label class="form-label">禁用</label>
        <n-switch
          v-model:value="formData.disabled"
          @update:value="handleFieldChange('disabled', $event)"
        />
      </div>

      <!-- 只读状态 -->
      <div class="form-item">
        <label class="form-label">只读</label>
        <n-switch
          v-model:value="formData.readonly"
          @update:value="handleFieldChange('readonly', $event)"
        />
      </div>

      <!-- 显示状态 -->
      <div class="form-item">
        <label class="form-label">显示</label>
        <n-switch
          v-model:value="formData.visible"
          @update:value="handleFieldChange('visible', $event)"
        />
      </div>
    </n-space>

    <!-- 实时预览 -->
    <div v-if="showPreview" class="config-preview">
      <n-divider title-placement="left">
        <span class="config-group-title">
          <n-icon><EyeOutline /></n-icon>
          实时预览
        </span>
      </n-divider>
      
      <div class="preview-container">
        <slot name="preview" :formData="formData">
          <div class="preview-placeholder">
            组件预览区域
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch } from 'vue'
import { NDivider, NInput, NSwitch, NSpace, NIcon } from 'naive-ui'
import { SettingsOutline as Setting, EllipsisHorizontalOutline as MoreFilled, EyeOutline } from '@vicons/ionicons5'
// import type { BaseComponentSchema } from '@crf/components'

interface ComponentData {
  title?: string
  description?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  visible?: boolean
  helpText?: string
  [key: string]: unknown
}

interface Props {
  // 组件数据
  componentData?: ComponentData
  // 是否显示预览
  showPreview?: boolean
  // 配置分组过滤
  configGroups?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  componentData: () => ({}),
  showPreview: true,
  configGroups: () => ['basic', 'state', 'medical']
})

const emit = defineEmits<{
  'update:componentData': [data: ComponentData]
  'field-change': [field: string, value: unknown]
}>()

// 表单数据
const formData = reactive<ComponentData>({
  title: '',
  description: '',
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  helpText: '',
  ...props.componentData
})

// 字段变化处理
const handleFieldChange = (field: string, value: unknown) => {
  (formData as Record<string, unknown>)[field] = value
  emit('field-change', field, value)
  
  // 更新完整数据
  emit('update:componentData', { ...formData })
}

// 监听外部数据变化
watch(
  () => props.componentData,
  (newData) => {
    Object.assign(formData, newData)
  },
  { deep: true }
)

// 暴露方法
defineExpose({
  formData,
  validate: () => {
    // 可以在这里添加表单验证逻辑
    return true
  },
  resetFields: () => {
    Object.assign(formData, {
      title: '',
      description: '',
      required: false,
      disabled: false,
      readonly: false,
      visible: true,
      helpText: ''
    })
  }
})
</script>

<style lang="scss" scoped>
.config-base {
  padding: 16px;
}

.config-group-title {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.preview-container {
  padding: 16px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 100px;
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  height: 80px;
}
</style>