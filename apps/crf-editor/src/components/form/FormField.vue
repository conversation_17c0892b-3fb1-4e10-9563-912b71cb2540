<template>
  <div class="form-field" :class="{ 'has-error': !!error, 'readonly': readonly }">
    <!-- 字段标签 -->
    <div class="field-label">
      <label :for="fieldId">
        {{ field.label || field.name }}
        <span v-if="field.required" class="required">*</span>
      </label>
      <span v-if="field.description" class="field-description">
        {{ field.description }}
      </span>
    </div>
    
    <!-- 字段内容 -->
    <div class="field-content">
      <!-- 文本输入 -->
      <n-input
        v-if="field.type === 'text' || field.type === 'email' || field.type === 'url'"
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder"
        :disabled="readonly"
        :type="field.type"
        clearable
        @input="handleInput"
        @change="handleChange"
      />
      
      <!-- 数字输入 -->
      <n-input-number
        v-else-if="field.type === 'number'"
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder"
        :disabled="readonly"
        :min="field.validation?.min"
        :max="field.validation?.max"
        :step="field.validation?.step || 1"
        :precision="field.validation?.precision"
        style="width: 100%"
        @update:value="handleInput"
        @change="handleChange"
      />
      
      <!-- 多行文本 -->
      <n-input
        v-else-if="field.type === 'textarea'"
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder"
        :disabled="readonly"
        :rows="field.rows || 3"
        :maxlength="field.validation?.maxLength"
        type="textarea"
        show-count
        @input="handleInput"
        @change="handleChange"
      />
      
      <!-- 选择框 -->
      <n-select
        v-else-if="field.type === 'select'"
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder || '请选择'"
        :disabled="readonly"
        :multiple="field.multiple"
        :clearable="!field.required"
        style="width: 100%"
        :options="field.options"
        @update:value="handleChange"
      />
      
      <!-- 单选框 -->
      <n-radio-group
        v-else-if="field.type === 'radio'"
        :id="fieldId"
        :value="value"
        :disabled="readonly"
        @update:value="handleChange"
      >
        <n-radio
          v-for="option in field.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </n-radio>
      </n-radio-group>
      
      <!-- 复选框 -->
      <n-checkbox-group
        v-else-if="field.type === 'checkbox'"
        :id="fieldId"
        :value="value || []"
        :disabled="readonly"
        @update:value="handleChange"
      >
        <n-checkbox
          v-for="option in field.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </n-checkbox>
      </n-checkbox-group>
      
      <!-- 日期选择 -->
      <n-date-picker
        v-else-if="field.type === 'date'"
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder || '请选择日期'"
        :disabled="readonly"
        :type="field.dateType || 'date'"
        :format="field.format || 'yyyy-MM-dd'"
        :value-format="field.valueFormat || 'yyyy-MM-dd'"
        style="width: 100%"
        @update:value="handleChange"
      />
      
      <!-- 时间选择 -->
      <n-time-picker
        v-else-if="field.type === 'time'"
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder || '请选择时间'"
        :disabled="readonly"
        :format="field.format || 'HH:mm:ss'"
        :value-format="field.valueFormat || 'HH:mm:ss'"
        style="width: 100%"
        @update:value="handleChange"
      />
      
      <!-- 开关 -->
      <n-switch
        v-else-if="field.type === 'switch'"
        :id="fieldId"
        :value="value"
        :disabled="readonly"
        @update:value="handleChange"
      >
        <template #checked>{{ field.activeText }}</template>
        <template #unchecked>{{ field.inactiveText }}</template>
      </n-switch>
      
      <!-- 滑块 -->
      <n-slider
        v-else-if="field.type === 'slider'"
        :id="fieldId"
        :value="value"
        :disabled="readonly"
        :min="field.validation?.min || 0"
        :max="field.validation?.max || 100"
        :step="field.validation?.step || 1"
        @update:value="handleChange"
      />
      
      <!-- 评分 -->
      <n-rate
        v-else-if="field.type === 'rate'"
        :id="fieldId"
        :value="value"
        :readonly="readonly"
        :count="field.max || 5"
        :allow-half="field.allowHalf"
        @update:value="handleChange"
      />
      
      <!-- 文件上传 -->
      <n-upload
        v-else-if="field.type === 'upload'"
        :id="fieldId"
        :file-list="value || []"
        :disabled="readonly"
        :multiple="field.multiple"
        :accept="field.accept"
        :max="field.limit"
        action="#"
        @change="handleUploadChange"
      >
        <n-button type="primary" :disabled="readonly">
          <template #icon>
            <crf-icon icon="material-symbols:upload" size="16px" />
          </template>
          选择文件
        </n-button>
      </n-upload>
      
      <!-- 默认文本输入 -->
      <n-input
        v-else
        :id="fieldId"
        :value="value"
        :placeholder="field.placeholder"
        :disabled="readonly"
        clearable
        @input="handleInput"
        @change="handleChange"
      />
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="field-error">
      <crf-icon icon="material-symbols:error" size="16px" />
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CrfIcon } from '@crf/components'

// 类型定义
enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview'
}

interface FieldOption {
  label: string
  value: unknown
}

interface FormField {
  id: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  description?: string
  options?: FieldOption[]
  limit?: number
  rows?: number
  min?: number
  max?: number
}

interface Props {
  field: FormField
  value: unknown
  mode: FormMode
  readonly?: boolean
  error?: string
}

interface Emits {
  (e: 'input', value: unknown): void
  (e: 'change', value: unknown): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()

// 字段ID
const fieldId = computed(() => `field-${props.field.id}`)

// 处理输入
const handleInput = (value: unknown) => {
  emit('input', value)
}

// 处理变更
const handleChange = (value: unknown) => {
  emit('change', value)
}

// 处理文件上传变更
const handleUploadChange = (fileList: unknown) => {
  emit('change', fileList)
}
</script>

<style lang="scss" scoped>
.form-field {
  .field-label {
    margin-bottom: 8px;
    
    label {
      display: block;
      font-weight: 500;
      color: #374151;
      font-size: 14px;
      line-height: 1.5;
      
      .required {
        color: #ef4444;
        margin-left: 2px;
      }
    }
    
    .field-description {
      display: block;
      font-size: 12px;
      color: #6b7280;
      margin-top: 4px;
      line-height: 1.4;
    }
  }
  
  .field-content {
    :deep(.n-input__wrapper) {
      border-radius: 6px;
    }
    
    :deep(.n-select) {
      .n-select__wrapper {
        border-radius: 6px;
      }
    }
    
    :deep(.n-input__textarea-el) {
      border-radius: 6px;
    }
    
    :deep(.n-radio-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    :deep(.n-checkbox-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    :deep(.n-upload) {
      width: 100%;
      
      .n-upload__tip {
        margin-top: 8px;
        color: #6b7280;
        font-size: 12px;
      }
    }
  }
  
  .field-error {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 6px;
    color: #ef4444;
    font-size: 12px;
    
    .crf-icon {
      flex-shrink: 0;
    }
  }
  
  &.has-error {
    .field-content {
      :deep(.n-input__wrapper) {
        border-color: #ef4444;
        box-shadow: 0 0 0 1px #ef4444;
      }
      
      :deep(.n-select__wrapper) {
        border-color: #ef4444;
        box-shadow: 0 0 0 1px #ef4444;
      }
      
      :deep(.n-input__textarea-el) {
        border-color: #ef4444;
        box-shadow: 0 0 0 1px #ef4444;
      }
    }
  }
  
  &.readonly {
    .field-content {
      :deep(.n-input__wrapper) {
        background-color: #f9fafb;
        border-color: #e5e7eb;
      }
      
      :deep(.n-select__wrapper) {
        background-color: #f9fafb;
        border-color: #e5e7eb;
      }
      
      :deep(.n-input__textarea-el) {
        background-color: #f9fafb;
        border-color: #e5e7eb;
      }
    }
  }
}
</style>