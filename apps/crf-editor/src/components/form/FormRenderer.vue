<template>
  <div class="form-renderer">
    <div v-if="!templateData || !templateData.sections" class="empty-form">
      <n-empty description="暂无表单内容" />
    </div>
    
    <div v-else class="form-sections">
      <div 
        v-for="(section, sectionIndex) in templateData.sections" 
        :key="section.id || sectionIndex"
        class="form-section"
      >
        <!-- 章节标题 -->
        <div v-if="section.title" class="section-header">
          <h3 class="section-title">{{ section.title }}</h3>
          <p v-if="section.description" class="section-description">
            {{ section.description }}
          </p>
        </div>
        
        <!-- 表单字段 -->
        <div class="section-content">
          <form-field
            v-for="(field, fieldIndex) in section.fields"
            :key="field.id || fieldIndex"
            :field="field"
            :value="formData[field.id]"
            :mode="mode"
            :readonly="readonly"
            :error="validationErrors[field.id]"
            @input="handleFieldInput(field.id, $event)"
            @change="handleFieldChange(field.id, $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import FormField from './FormField.vue'

// 临时类型定义
enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview'
}

interface Props {
  templateData: Record<string, unknown>
  formData: Record<string, unknown>
  mode: FormMode
  readonly?: boolean
}

interface Emits {
  (e: 'data-change', value: Record<string, unknown>): void
  (e: 'validation-change', errors: Record<string, string>): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()

// 验证错误
const validationErrors = ref<Record<string, string>>({})

// 处理字段输入
const handleFieldInput = (fieldId: string, value: unknown) => {
  const newFormData = { ...props.formData }
  newFormData[fieldId] = value
  emit('data-change', newFormData)
}

// 处理字段变更
const handleFieldChange = (fieldId: string, value: unknown) => {
  // 验证字段
  validateField(fieldId, value)
  
  // 触发数据变更
  handleFieldInput(fieldId, value)
}

// 验证字段
const validateField = (fieldId: string, value: unknown) => {
  const errors = { ...validationErrors.value }
  
  // 查找字段配置
  const field = findFieldById(fieldId)
  if (!field) return
  
  // 必填验证
  if (field.required && (!value || value === '')) {
    errors[fieldId] = `${field.label || field.name}是必填项`
  } else {
    delete errors[fieldId]
  }
  
  // 类型验证
  if (value && field.type) {
    switch (field.type) {
      case 'email':
        if (!isValidEmail(value as string)) {
          errors[fieldId] = '请输入有效的邮箱地址'
        }
        break
      case 'number':
        if (isNaN(Number(value))) {
          errors[fieldId] = '请输入有效的数字'
        }
        break
      case 'url':
        if (!isValidUrl(value as string)) {
          errors[fieldId] = '请输入有效的URL'
        }
        break
    }
  }
  
  // 长度验证
  if (value && field.validation) {
    const valueStr = value as string
    if (field.validation.minLength && valueStr.length < field.validation.minLength) {
      errors[fieldId] = `最少需要${field.validation.minLength}个字符`
    }
    if (field.validation.maxLength && valueStr.length > field.validation.maxLength) {
      errors[fieldId] = `最多允许${field.validation.maxLength}个字符`
    }
  }
  
  validationErrors.value = errors
  emit('validation-change', errors)
}

// 查找字段
const findFieldById = (fieldId: string): Record<string, unknown> | null => {
  if (!props.templateData?.sections) return null
  
  for (const section of props.templateData.sections as Record<string, unknown>[]) {
    if (section.fields) {
      const field = (section.fields as Record<string, unknown>[]).find((f: Record<string, unknown>) => f.id === fieldId)
      if (field) return field
    }
  }
  return null
}

// 验证邮箱
const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证URL
const isValidUrl = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 监听表单数据变化，进行全量验证
watch(() => props.formData, (newFormData) => {
  if (!props.templateData?.sections) return
  
  for (const section of props.templateData.sections as Record<string, unknown>[]) {
    if (section.fields) {
      for (const field of section.fields as Record<string, unknown>[]) {
        validateField(field.id as string, newFormData[field.id as string])
      }
    }
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.form-renderer {
  .empty-form {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
  
  .form-sections {
    .form-section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-header {
        margin-bottom: 20px;
        
        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 8px 0;
        }
        
        .section-description {
          color: #6b7280;
          margin: 0;
          line-height: 1.6;
        }
      }
      
      .section-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }
  }
}
</style>