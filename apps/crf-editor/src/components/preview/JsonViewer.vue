<template>
  <div class="json-viewer" :class="`theme-${theme}`">
    <!-- 工具栏 -->
    <div v-if="showCopy || showDownload" class="json-toolbar">
      <n-button-group>
        <n-button v-if="showCopy" @click="copyToClipboard">
          <template #icon>
            <DocumentCopy />
          </template>
          复制
        </n-button>
        <n-button v-if="showDownload" @click="downloadJson">
          <template #icon>
            <Download />
          </template>
          下载
        </n-button>
      </n-button-group>
    </div>
    
    <!-- JSON内容 -->
    <div class="json-content">
      <pre class="json-code">{{ formattedJson }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMessage } from 'naive-ui'
import { CopyOutline as DocumentCopy, DownloadOutline as Download } from '@vicons/ionicons5'

// Props定义
interface Props {
  data: unknown
  theme?: 'light' | 'dark'
  showCopy?: boolean
  showDownload?: boolean
  indent?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
  theme: 'light',
  showCopy: false,
  showDownload: false,
  indent: 2
})

const message = useMessage()

// 格式化JSON
const formattedJson = computed(() => {
  try {
    return JSON.stringify(props.data, null, props.indent)
  } catch (error) {
    return '无效的JSON数据'
  }
})

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedJson.value)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 下载JSON文件
const downloadJson = () => {
  try {
    const blob = new Blob([formattedJson.value], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `data-${Date.now()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    message.success('下载成功')
  } catch (error) {
    message.error('下载失败')
  }
}
</script>

<style scoped>
.json-viewer {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.json-toolbar {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.json-content {
  max-height: 400px;
  overflow: auto;
}

.json-code {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #2c3e50;
  background-color: #fff;
  white-space: pre-wrap;
  word-break: break-all;
}

.theme-dark .json-code {
  color: #e4e7ed;
  background-color: #2d3748;
}

.theme-dark .json-toolbar {
  background-color: #4a5568;
  border-bottom-color: #718096;
}

.theme-dark {
  border-color: #718096;
}
</style>