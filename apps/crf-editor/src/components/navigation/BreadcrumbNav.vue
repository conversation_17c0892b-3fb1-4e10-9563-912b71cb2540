<template>
  <div class="breadcrumb-nav" v-if="breadcrumbs.length > 0">
    <!-- 返回按钮 -->
    <n-button 
      v-if="showBackButton"
      text 
      class="back-button"
      @click="handleBack"
    >
      <template #icon>
        <n-icon><ChevronBackOutline /></n-icon>
      </template>
    </n-button>
    
    <!-- 面包屑导航 -->
    <n-breadcrumb class="breadcrumb">
      <n-breadcrumb-item 
        v-for="(item, index) in breadcrumbs" 
        :key="item.path"
        :clickable="item.clickable && index < breadcrumbs.length - 1"
        @click="item.clickable && index < breadcrumbs.length - 1 ? handleBreadcrumbClick(item) : null"
      >
        <template #icon v-if="item.icon">
          <n-icon><component :is="item.icon" /></n-icon>
        </template>
        {{ item.label }}
      </n-breadcrumb-item>
    </n-breadcrumb>
    
    <!-- 动态操作按钮 -->
    <div class="action-buttons" v-if="actionButtons.length > 0">
      <n-button
        v-for="button in actionButtons"
        :key="button.key"
        :type="button.type || 'default'"
        :size="button.size || 'small'"
        @click="button.onClick"
      >
        <template #icon v-if="button.icon">
          <n-icon><component :is="button.icon" /></n-icon>
        </template>
        {{ button.label }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ChevronBackOutline,
  HomeOutline,
  FolderOutline,
  DocumentTextOutline,
  GridOutline,
  LayersOutline
} from '@vicons/ionicons5'

interface BreadcrumbItem {
  label: string
  path: string
  icon?: any
  clickable?: boolean
}

interface ActionButton {
  key: string
  label: string
  type?: 'primary' | 'default' | 'success' | 'warning' | 'error'
  size?: 'small' | 'medium' | 'large'
  icon?: any
  onClick: () => void
}

interface Props {
  customBreadcrumbs?: BreadcrumbItem[]
  customActions?: ActionButton[]
  showBackButton?: boolean
  projectInfo?: {
    id: string
    name: string
  } | null
}

const props = withDefaults(defineProps<Props>(), {
  customBreadcrumbs: () => [],
  customActions: () => [],
  showBackButton: true,
  projectInfo: null
})

const emit = defineEmits<{
  back: []
  breadcrumbClick: [item: BreadcrumbItem]
}>()

const route = useRoute()
const router = useRouter()

// 根据路由自动生成面包屑
const autoBreadcrumbs = computed((): BreadcrumbItem[] => {
  const path = route.path
  const breadcrumbs: BreadcrumbItem[] = []
  
  // 工作台
  breadcrumbs.push({
    label: '工作台',
    path: '/dashboard',
    icon: HomeOutline,
    clickable: true
  })
  
  if (path.startsWith('/projects')) {
    breadcrumbs.push({
      label: '项目管理',
      path: '/projects',
      icon: FolderOutline,
      clickable: true
    })
    
    // 项目详情
    if (route.params.id) {
      breadcrumbs.push({
        label: '项目详情',
        path: `/projects/${route.params.id}`,
        clickable: true
      })
      
      // 模板管理
      if (path.includes('/templates')) {
        breadcrumbs.push({
          label: '模板管理',
          path: `/projects/${route.params.id}/templates`,
          clickable: false
        })
      }
    }
  } else if (path.startsWith('/forms')) {
    // 检查是否有项目ID参数
    const projectId = route.query.project_id as string

    if (projectId) {
      // 如果有项目ID，显示项目层级结构
      breadcrumbs.push({
        label: '项目管理',
        path: '/dashboard',
        icon: FolderOutline,
        clickable: true
      })

      // 使用项目信息显示项目名称
      const projectName = props.projectInfo?.name || '项目详情'
      breadcrumbs.push({
        label: projectName,
        path: `/projects/${projectId}`,
        clickable: true
      })

      breadcrumbs.push({
        label: '表单管理',
        path: `/forms?project_id=${projectId}`,
        clickable: false
      })
    } else {
      // 全局表单管理
      breadcrumbs.push({
        label: '模板管理',
        path: '/forms',
        icon: DocumentTextOutline,
        clickable: true
      })
    }

    // 表单数据
    if (path.includes('/data')) {
      breadcrumbs.push({
        label: '表单数据',
        path: route.path,
        clickable: false
      })
    }
  } else if (path.startsWith('/instances')) {
    breadcrumbs.push({
      label: '实例管理',
      path: '/instances',
      icon: LayersOutline,
      clickable: true
    })
  } else if (path.startsWith('/editor')) {
    breadcrumbs.push({
      label: '表单编辑器',
      path: '/editor',
      icon: DocumentTextOutline,
      clickable: false
    })
  } else if (path.startsWith('/admin')) {
    breadcrumbs.push({
      label: '系统管理',
      path: '/admin',
      icon: GridOutline,
      clickable: true
    })
    
    if (path.includes('/users')) {
      breadcrumbs.push({
        label: '用户管理',
        path: '/admin/users',
        clickable: false
      })
    } else if (path.includes('/roles')) {
      breadcrumbs.push({
        label: '角色管理',
        path: '/admin/roles',
        clickable: false
      })
    }
  }
  
  return breadcrumbs
})

// 最终面包屑（优先使用自定义）
const breadcrumbs = computed(() => {
  return props.customBreadcrumbs.length > 0 ? props.customBreadcrumbs : autoBreadcrumbs.value
})

// 根据路由自动生成操作按钮
const autoActionButtons = computed((): ActionButton[] => {
  const path = route.path
  const buttons: ActionButton[] = []
  
  // 根据不同页面添加相应的操作按钮
  if (path === '/projects') {
    buttons.push({
      key: 'create-project',
      label: '创建项目',
      type: 'primary',
      onClick: () => router.push('/projects?action=create')
    })
  } else if (path === '/forms') {
    buttons.push({
      key: 'create-template',
      label: '创建模板',
      type: 'primary',
      onClick: () => router.push('/forms?action=create')
    })
  }
  
  return buttons
})

// 最终操作按钮（优先使用自定义）
const actionButtons = computed(() => {
  return props.customActions.length > 0 ? props.customActions : autoActionButtons.value
})

// 处理返回
const handleBack = () => {
  emit('back')
  router.back()
}

// 处理面包屑点击
const handleBreadcrumbClick = (item: BreadcrumbItem) => {
  emit('breadcrumbClick', item)
  if (item.clickable && item.path) {
    router.push(item.path)
  }
}
</script>

<style lang="scss" scoped>
.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--color-border-primary);
  margin-bottom: var(--spacing-4);
}

.back-button {
  color: var(--color-text-secondary);
  
  &:hover {
    color: var(--color-primary);
  }
}

.breadcrumb {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .breadcrumb-nav {
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }
  
  .action-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
