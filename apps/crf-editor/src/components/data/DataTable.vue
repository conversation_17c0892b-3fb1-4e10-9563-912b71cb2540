<template>
  <div class="data-table">
    <!-- 工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <n-input
          v-model:value="searchValue"
          placeholder="搜索数据..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon><MagnifyingGlassIcon /></n-icon>
          </template>
        </n-input>
        
        <!-- 版本筛选 -->
        <n-select 
          v-if="availableVersions.length > 1"
          v-model:value="versionFilter"
          placeholder="筛选版本"
          clearable
          class="version-filter"
          @update:value="handleVersionFilter"
        >
          <n-option
            v-for="version in availableVersions"
            :key="version"
            :label="`版本 ${version}`"
            :value="version"
          />
        </n-select>
        
        <!-- 章节筛选 -->
        <n-select 
          v-if="availableSections.length > 1"
          v-model:value="sectionFilter"
          placeholder="筛选章节"
          clearable
          class="section-filter"
          @update:value="handleSectionFilter"
        >
          <n-option
            v-for="section in availableSections"
            :key="section.id"
            :label="section.title"
            :value="section.id"
          />
        </n-select>
        
        <!-- 显示模式切换 -->
        <n-button-group>
          <n-button 
            :type="viewMode === 'table' ? 'primary' : 'default'"
            @click="viewMode = 'table'"
          >
            <template #icon>
              <n-icon><Squares2X2Icon /></n-icon>
            </template>
            横表视图
          </n-button>
          <n-button 
            :type="viewMode === 'raw' ? 'primary' : 'default'"
            @click="viewMode = 'raw'"
          >
            <template #icon>
              <n-icon><DocumentTextIcon /></n-icon>
            </template>
            原始数据
          </n-button>
          <n-button 
            :type="viewMode === 'diff' ? 'primary' : 'default'"
            @click="viewMode = 'diff'"
            :disabled="!enableVersionDiff"
          >
            <template #icon>
              <n-icon><ArrowsRightLeftIcon /></n-icon>
            </template>
            版本对比
          </n-button>
        </n-button-group>
      </div>
      
      <div class="toolbar-right">
        <!-- 表格操作 -->
        <n-button @click="refreshData">
          <template #icon>
            <n-icon><ArrowPathIcon /></n-icon>
          </template>
          刷新
        </n-button>
        
        <!-- 导出功能 -->
        <n-dropdown :options="exportOptions" @select="handleExport" trigger="click">
          <template #trigger>
            <n-button type="primary">
              导出数据
              <template #icon>
                <n-icon><ChevronDownIcon /></n-icon>
              </template>
            </n-button>
          </template>
        </n-dropdown>
        
        <!-- 全屏切换 -->
        <n-button @click="toggleFullscreen">
          <template #icon>
            <n-icon><XMarkIcon /></n-icon>
          </template>
        </n-button>
      </div>
    </div>

    <!-- 表格内容区域 -->
    <div class="table-content" :class="{ 'fullscreen': isFullscreen }">
      
      <!-- 横表视图 -->
      <div v-if="viewMode === 'table'" class="table-view">
        <n-data-table
          ref="dataTable"
          :data="tableData"
          :columns="tableColumns"
          :max-height="tableHeight"
          striped
          :bordered="true"
          :loading="loading"
          @update:sorter="handleSortChange"
          :row-props="rowProps"
        />
        
        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <n-pagination
            v-model:page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :item-count="totalRows"
            show-size-picker
            show-quick-jumper
            @update:page="handlePageChange"
            @update:page-size="handlePageChange"
          />
        </div>
      </div>

      <!-- 原始数据视图 -->
      <div v-else-if="viewMode === 'raw'" class="raw-view">
        <div class="raw-controls">
          <n-select
            v-model:value="selectedInstanceId"
            placeholder="选择实例查看原始数据"
            class="instance-selector"
            @update:value="loadInstanceDetail"
          >
            <n-option
              v-for="instance in instances"
              :key="instance.id"
              :label="`${instance.instance_name || '未命名实例'} (v${instance.template_version || '1.0'})`"
              :value="instance.id"
            />
          </n-select>
          <n-button @click="copyRawData" v-if="selectedInstanceData" type="primary">
            <template #icon>
              <n-icon><DocumentDuplicateIcon /></n-icon>
            </template>
            复制数据
          </n-button>
        </div>

        <div v-if="selectedInstanceData" class="raw-data-content">
          <n-tabs v-model:value="activeRawTab" class="raw-tabs">
            <n-tab-pane name="formatted" tab="格式化数据">
              <div class="json-viewer">
                <pre><code>{{ formatJsonData(selectedInstanceData) }}</code></pre>
              </div>
            </n-tab-pane>
            <n-tab-pane name="form_data" tab="表单数据">
              <div class="json-viewer">
                <pre><code>{{ JSON.stringify(selectedInstanceData.form_data, null, 2) }}</code></pre>
              </div>
            </n-tab-pane>
            <n-tab-pane name="meta" tab="实例信息">
              <div class="instance-meta-info">
                <n-descriptions :column="2" bordered>
                  <n-descriptions-item label="实例ID">
                    {{ selectedInstanceData.id }}
                  </n-descriptions-item>
                  <n-descriptions-item label="实例名称">
                    {{ selectedInstanceData.instance_name || '未命名实例' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="模板版本">
                    v{{ selectedInstanceData.template_version || '1.0' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="完成度">
                    {{ selectedInstanceData.completion_percentage || 0 }}%
                  </n-descriptions-item>
                  <n-descriptions-item label="状态">
                    <n-tag :type="getStatusType(selectedInstanceData.status)">
                      {{ getStatusText(selectedInstanceData.status) }}
                    </n-tag>
                  </n-descriptions-item>
                  <n-descriptions-item label="创建时间">
                    {{ formatDateTime(selectedInstanceData.created_at) }}
                  </n-descriptions-item>
                </n-descriptions>
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
        
        <div v-else class="raw-placeholder">
          <n-empty description="请选择一个实例查看原始数据" />
        </div>
      </div>

      <!-- 版本对比视图 -->
      <div v-else-if="viewMode === 'diff'" class="diff-view">
        <div class="diff-controls">
          <div class="version-selectors">
            <div class="selector-group">
              <label>基准版本:</label>
              <n-select v-model:value="baseVersionForDiff" placeholder="选择基准版本">
                <n-option
                  v-for="version in availableVersions"
                  :key="version"
                  :label="`版本 ${version}`"
                  :value="version"
                />
              </n-select>
            </div>
            <div class="selector-group">
              <label>对比版本:</label>
              <n-select v-model:value="compareVersionForDiff" placeholder="选择对比版本">
                <n-option
                  v-for="version in availableVersions"
                  :key="version"
                  :label="`版本 ${version}`"
                  :value="version"
                />
              </n-select>
            </div>
            <n-button 
              type="primary" 
              @click="generateVersionDiff"
              :disabled="!baseVersionForDiff || !compareVersionForDiff"
            >
              生成对比
            </n-button>
          </div>
        </div>

        <div v-if="versionDiffData.length > 0" class="diff-table">
          <n-data-table
            :data="versionDiffData"
            :columns="diffTableColumns"
            :max-height="tableHeight - 60"
            striped
            :bordered="true"
          />
        </div>
        
        <div v-else class="diff-placeholder">
          <n-empty description="请选择版本进行对比分析" />
        </div>
      </div>
    </div>

    <!-- 实例详情弹窗 -->
    <n-modal
      v-model:show="detailDialogVisible"
      :title="`实例详情 - ${selectedDetailInstance?.instance_name || '未命名实例'}`"
      style="width: 80%"
      preset="dialog"
      @close="handleCloseDetail"
    >
      <div v-if="selectedDetailInstance" class="instance-detail-content">
        <n-descriptions :column="2" bordered class="detail-descriptions">
          <n-descriptions-item label="实例ID">
            {{ selectedDetailInstance.id }}
          </n-descriptions-item>
          <n-descriptions-item label="实例名称">
            {{ selectedDetailInstance.instance_name || '未命名实例' }}
          </n-descriptions-item>
          <n-descriptions-item label="模板版本">
            <n-tag type="info">v{{ selectedDetailInstance.template_version || '1.0' }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="getStatusType(selectedDetailInstance.status)">
              {{ getStatusText(selectedDetailInstance.status) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="完成度">
            <n-progress
              :percentage="selectedDetailInstance.completion_percentage || 0"
              :stroke-width="8"
            />
          </n-descriptions-item>
          <n-descriptions-item label="受试者ID">
            {{ selectedDetailInstance.subject_id || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="访问ID">
            {{ selectedDetailInstance.visit_id || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">
            {{ formatDateTime(selectedDetailInstance.created_at) }}
          </n-descriptions-item>
        </n-descriptions>
        
        <div class="form-data-detail">
          <h3>表单数据详情</h3>
          <n-data-table
            :data="detailFormData"
            :columns="detailTableColumns"
            :max-height="300"
            striped
            :bordered="true"
          />
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { useMessage } from 'naive-ui'
import { 
  SearchOutline as MagnifyingGlassIcon, 
  GridOutline as Squares2X2Icon, 
  DocumentOutline as DocumentTextIcon, 
  SwapHorizontalOutline as ArrowsRightLeftIcon, 
  RefreshOutline as ArrowPathIcon, 
  ChevronDownOutline as ChevronDownIcon, 
  CloseOutline as XMarkIcon,
  ServerOutline as CircleStackIcon, 
  CopyOutline as DocumentDuplicateIcon 
} from '@vicons/ionicons5'
import { 
  createSchemaFieldMapper, 
  extractFieldsFromInstances, 
  type FieldInfo, 
  type FormSchema,
  type SchemaFieldMapper 
} from '@/utils/schema-field-mapper'
import * as XLSX from 'xlsx'
import { 
  NDataTable, NButton, NIcon, NInput, NSelect, NPagination, NSpace,
  NButtonGroup, NDropdown, NTag, NProgress, NTabs, NTabPane, NEmpty, NDescriptions,
  NDescriptionsItem, NModal
} from 'naive-ui'

interface Props {
  instances: Record<string, unknown>[]
  templateId?: string
  templateSchema?: FormSchema | string | null
  height?: number
  enableVersionDiff?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 600,
  enableVersionDiff: true,
  templateSchema: null
})

const emit = defineEmits<{
  'instance-selected': [instance: Record<string, unknown>]
  'export-requested': [format: string, data: Record<string, unknown>[]]
  'version-compare': [instances: Record<string, unknown>[]]
  'refresh': []
}>()

// 使用 Naive UI 的消息功能
const message = useMessage()

// 表格引用
const dataTable = ref()

// Schema映射器
const schemaMapper = ref<SchemaFieldMapper | null>(null)

// 基础状态
const loading = ref(false)
const viewMode = ref<'table' | 'raw' | 'diff'>('table')
const isFullscreen = ref(false)

// 搜索和筛选
const searchValue = ref('')
const versionFilter = ref('')
const sectionFilter = ref('')
const availableVersions = ref<string[]>([])
const availableSections = ref<Record<string, unknown>[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalRows = computed(() => filteredInstances.value.length)

// 表格数据
const tableData = ref<Record<string, unknown>[]>([])
const extractedFields = ref<FieldInfo[]>([])
const displayFields = computed(() => {
  if (sectionFilter.value) {
    // 根据章节筛选字段
    return extractedFields.value.filter(field => 
      field.section === sectionFilter.value
    )
  }
  return extractedFields.value
})

// 原始数据相关
const selectedInstanceId = ref('')
const selectedInstanceData = ref<Record<string, unknown> | null>(null)
const activeRawTab = ref('formatted')

// 版本对比相关
const baseVersionForDiff = ref('')
const compareVersionForDiff = ref('')
const versionDiffData = ref<Record<string, unknown>[]>([])

// 详情弹窗
const detailDialogVisible = ref(false)
const selectedDetailInstance = ref<Record<string, unknown> | null>(null)

// 表格列定义
const tableColumns = computed(() => {
  const columns: Record<string, unknown>[] = [
    {
      title: '实例名称',
      key: 'instance_name',
      width: 200,
      fixed: 'left',
      ellipsis: { tooltip: true },
      render: (row: Record<string, unknown>) => {
        return h('div', { class: 'instance-name-cell' }, [
          h('span', { class: 'name' }, row.instance_name || '未命名实例'),
          row.template_version && row.template_version !== '1.0' 
            ? h(NTag, { size: 'small', type: 'info', class: 'version-tag' }, { default: () => `v${row.template_version}` })
            : null
        ])
      }
    },
    {
      title: '受试者ID',
      key: 'subject_id',
      width: 150,
      ellipsis: { tooltip: true }
    },
    {
      title: '访问ID',
      key: 'visit_id',
      width: 150,
      ellipsis: { tooltip: true }
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (row: Record<string, unknown>) => {
        return h(NTag, { type: getStatusType(row.status), size: 'small' }, { default: () => getStatusText(row.status) })
      }
    },
    {
      title: '完成度',
      key: 'completion_percentage',
      width: 120,
      align: 'center',
      sorter: true,
      render: (row: Record<string, unknown>) => {
        return h('div', {}, [
          h(NProgress, {
            percentage: row.completion_percentage || 0,
            'stroke-width': 6,
            'show-text': false,
            class: 'mini-progress'
          }),
          h('span', { class: 'progress-text' }, `${row.completion_percentage || 0}%`)
        ])
      }
    }
  ]

  // 添加动态字段列
  displayFields.value.forEach(field => {
    columns.push({
      title: field.label,
      key: `form_data.${field.id}`,
      width: getFieldWidth(field.type),
      sorter: isFieldSortable(field.type),
      ellipsis: { tooltip: true },
      render: (row: Record<string, unknown>) => {
        return h('div', { class: ['field-cell', getFieldCellClass(row, field.id)] }, [
          h('span', { class: 'field-value' }, formatFieldValue(row.form_data?.[field.id], field)),
          shouldShowVersionTag(row, field.id) ? h('div', { class: 'version-indicators' }, [
            isFieldAdded(row, field.id) ? h(NTag, { size: 'small', type: 'success', class: 'version-tag' }, { default: () => '新增' }) : null,
            isFieldModified(row, field.id) ? h(NTag, { size: 'small', type: 'warning', class: 'version-tag' }, { default: () => '修改' }) : null,
            isFieldRemoved(row, field.id) ? h(NTag, { size: 'small', type: 'error', class: 'version-tag' }, { default: () => '删除' }) : null
          ]) : null
        ])
      }
    })
  })

  // 添加创建时间列
  columns.push({
    title: '创建时间',
    key: 'created_at',
    width: 160,
    sorter: true,
    render: (row: Record<string, unknown>) => formatDateTime(row.created_at)
  })

  // 添加操作列
  columns.push({
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    align: 'center',
    render: (row: Record<string, unknown>) => {
      return h(NButton, {
        type: 'primary',
        size: 'small',
        onClick: (e: Event) => {
          e.stopPropagation()
          showInstanceDetail(row)
        }
      }, { default: () => '详情' })
    }
  })

  return columns
})

// 版本对比表格列定义
const diffTableColumns = computed(() => {
  const columns: Record<string, unknown>[] = [
    { title: '实例名称', key: 'instanceName', width: 200, fixed: 'left' },
    { title: '受试者ID', key: 'subjectId', width: 150 },
    { title: '访问ID', key: 'visitId', width: 150 }
  ]

  displayFields.value.forEach(field => {
    columns.push({
      title: field.label,
      key: field.id,
      width: Math.max(getFieldWidth(field.type), 200),
      render: (row: Record<string, unknown>) => {
        return h('div', { class: ['diff-cell', getDiffCellClass(row, field.id)] }, [
          h('div', { class: 'base-value' }, [
            h('span', { class: 'value-label' }, '基准:'),
            h('span', { class: 'value-content' }, getFieldValue(row.baseData, field.id, field))
          ]),
          h('div', { class: 'compare-value' }, [
            h('span', { class: 'value-label' }, '对比:'),
            h('span', { class: 'value-content' }, getFieldValue(row.compareData, field.id, field))
          ])
        ])
      }
    })
  })

  return columns
})

// 详情表格列定义
const detailTableColumns = [
  { title: '字段名称', key: 'fieldLabel', width: 200 },
  { title: '字段类型', key: 'fieldType', width: 120 },
  {
    title: '填写内容',
    key: 'fieldValue',
    ellipsis: { tooltip: true },
    render: (row: Record<string, unknown>) => {
      return h('span', { class: 'field-value-display' }, row.fieldValue)
    }
  }
]

// 行属性
const rowProps = (row: Record<string, unknown>) => {
  return {
    onClick: () => handleRowClick(row)
  }
}

// 计算属性
const tableHeight = computed(() => isFullscreen.value ? window.innerHeight - 200 : props.height)

const filteredInstances = computed(() => {
  let filtered = props.instances

  // 搜索过滤
  if (searchValue.value) {
    const keyword = searchValue.value.toLowerCase()
    filtered = filtered.filter(instance => 
      (instance.instance_name || '').toLowerCase().includes(keyword) ||
      (instance.subject_id || '').toLowerCase().includes(keyword) ||
      (instance.visit_id || '').toLowerCase().includes(keyword)
    )
  }

  // 版本过滤
  if (versionFilter.value) {
    filtered = filtered.filter(instance => 
      (instance.template_version || '1.0') === versionFilter.value
    )
  }

  return filtered
})

const detailFormData = computed(() => {
  if (!selectedDetailInstance.value?.form_data) return []
  
  const formData = selectedDetailInstance.value.form_data
  return Object.entries(formData).map(([key, value]) => {
    const field = extractedFields.value.find(f => f.id === key)
    return {
      fieldId: key,
      fieldLabel: field?.label || key,
      fieldType: field?.type || 'text',
      fieldValue: formatFieldValue(value, field)
    }
  })
})

// 方法
const extractVersions = () => {
  const versions = new Set<string>()
  props.instances.forEach(instance => {
    const version = instance.template_version || '1.0'
    versions.add(version)
  })
  availableVersions.value = Array.from(versions).sort((a, b) => parseFloat(b) - parseFloat(a))
}

const extractSections = () => {
  // 从schema中提取章节信息
  const sections = new Set<Record<string, unknown>>()
  if (props.templateSchema && schemaMapper.value) {
    // 这里可以根据实际的schema结构来提取章节
    // 暂时使用简单的分组逻辑
    sections.add({ id: 'basic', title: '基础信息' })
    sections.add({ id: 'medical', title: '医疗信息' })
    sections.add({ id: 'follow', title: '随访信息' })
  }
  availableSections.value = Array.from(sections)
}

const extractFields = () => {
  console.log('开始解析字段，使用schema:', !!props.templateSchema)
  
  // 优先使用schema解析
  if (props.templateSchema && schemaMapper.value) {
    try {
      schemaMapper.value.parseSchema(props.templateSchema)
      const schemaFields = schemaMapper.value.getAllFields()
      console.log('从schema解析到字段:', schemaFields.length, '个')
      
      if (schemaFields.length > 0) {
        extractedFields.value = schemaFields
        return
      }
    } catch (error) {
      console.warn('Schema解析失败，回退到实例数据解析:', error)
    }
  }
  
  // 回退到从实例数据中提取字段
  console.log('从实例数据提取字段')
  extractedFields.value = extractFieldsFromInstances(props.instances)
  console.log('从实例数据解析到字段:', extractedFields.value.length, '个')
}

const generateTableData = () => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  
  tableData.value = filteredInstances.value.slice(startIndex, endIndex).map(instance => ({
    ...instance,
    form_data: instance.form_data || {}
  }))
}

const generateVersionDiff = () => {
  if (!baseVersionForDiff.value || !compareVersionForDiff.value) {
    message.warning('请选择两个版本进行对比')
    return
  }

  const baseVersionInstances = props.instances.filter(
    instance => (instance.template_version || '1.0') === baseVersionForDiff.value
  )
  
  const compareVersionInstances = props.instances.filter(
    instance => (instance.template_version || '1.0') === compareVersionForDiff.value
  )

  // 生成对比数据
  const diffData: Record<string, unknown>[] = []
  const processedInstanceIds = new Set()

  // 处理基准版本的实例
  baseVersionInstances.forEach(baseInstance => {
    const compareInstance = compareVersionInstances.find(
      instance => instance.subject_id === baseInstance.subject_id && 
                 instance.visit_id === baseInstance.visit_id
    )

    diffData.push({
      instanceId: baseInstance.id,
      instanceName: baseInstance.instance_name,
      subjectId: baseInstance.subject_id,
      visitId: baseInstance.visit_id,
      baseData: baseInstance.form_data || {},
      compareData: compareInstance?.form_data || {},
      hasBaseData: true,
      hasCompareData: !!compareInstance
    })

    if (compareInstance) {
      processedInstanceIds.add(compareInstance.id)
    }
  })

  // 处理对比版本中独有的实例
  compareVersionInstances.forEach(compareInstance => {
    if (!processedInstanceIds.has(compareInstance.id)) {
      diffData.push({
        instanceId: compareInstance.id,
        instanceName: compareInstance.instance_name,
        subjectId: compareInstance.subject_id,
        visitId: compareInstance.visit_id,
        baseData: {},
        compareData: compareInstance.form_data || {},
        hasBaseData: false,
        hasCompareData: true
      })
    }
  })

  versionDiffData.value = diffData
  
  message.success(`生成版本对比数据成功，共 ${diffData.length} 条记录`)
}

const getDiffCellClass = (row: Record<string, unknown>, fieldId: string) => {
  const baseValue = getFieldValue(row.baseData, fieldId)
  const compareValue = getFieldValue(row.compareData, fieldId)
  
  if (!row.hasBaseData) return 'diff-added'
  if (!row.hasCompareData) return 'diff-removed'
  if (baseValue !== compareValue) return 'diff-changed'
  return 'diff-same'
}

const getFieldValue = (data: Record<string, unknown>, fieldId: string, field?: FieldInfo): string => {
  const value = data[fieldId]
  if (field) {
    return formatFieldValue(value, field)
  }
  return formatFieldValue(value)
}

const formatFieldValue = (value: unknown, field?: FieldInfo): string => {
  if (value === null || value === undefined) return '-'
  
  if (field && schemaMapper.value) {
    return schemaMapper.value.formatFieldValue(field.id, value)
  }
  
  if (Array.isArray(value)) {
    return value.join(', ')
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  return String(value)
}

const getFieldWidth = (type: string): number => {
  const widthMap: Record<string, number> = {
    text: 200,
    textarea: 300,
    number: 120,
    date: 150,
    datetime: 180,
    time: 120,
    select: 180,
    radio: 150,
    checkbox: 250,
    boolean: 100,
    email: 200,
    phone: 150,
    file: 150,
    static: 200
  }
  return widthMap[type] || 200
}

const isFieldSortable = (type: string): boolean => {
  const sortableTypes = ['text', 'number', 'date', 'datetime', 'time']
  return sortableTypes.includes(type)
}

const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    in_progress: 'warning',
    completed: 'success',
    submitted: 'primary'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    in_progress: '进行中',
    completed: '已完成',
    submitted: '已提交'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateString: string): string => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatJsonData = (data: unknown): string => {
  return JSON.stringify(data, null, 2)
}

// 版本管理相关方法
const shouldShowVersionTag = (row: Record<string, unknown>, fieldId: string): boolean => {
  // 如果有多个版本，显示版本标识
  return availableVersions.value.length > 1
}

const isFieldAdded = (row: Record<string, unknown>, fieldId: string): boolean => {
  // 检查字段是否为新增字段
  const currentVersion = row.template_version || '1.0'
  const baseVersion = availableVersions.value[availableVersions.value.length - 1]
  
  if (currentVersion === baseVersion) return false
  
  // 简单逻辑：如果字段在当前版本有值，在基础版本中没有定义，则认为是新增
  return row.form_data?.[fieldId] !== undefined
}

const isFieldModified = (row: Record<string, unknown>, fieldId: string): boolean => {
  // 检查字段是否被修改
  // 这里可以根据实际需求实现更复杂的逻辑
  return false
}

const isFieldRemoved = (row: Record<string, unknown>, fieldId: string): boolean => {
  // 检查字段是否被删除
  const currentVersion = row.template_version || '1.0'
  const latestVersion = availableVersions.value[0]
  
  if (currentVersion === latestVersion) return false
  
  // 简单逻辑：如果字段在当前版本中没有值，但在字段定义中存在，则认为是删除
  return row.form_data?.[fieldId] === undefined && 
         extractedFields.value.some(f => f.id === fieldId)
}

const getFieldCellClass = (row: Record<string, unknown>, fieldId: string): string => {
  const classes = ['field-cell']
  
  if (isFieldAdded(row, fieldId)) classes.push('field-added')
  if (isFieldModified(row, fieldId)) classes.push('field-modified')
  if (isFieldRemoved(row, fieldId)) classes.push('field-removed')
  
  return classes.join(' ')
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
  generateTableData()
}

const handleVersionFilter = () => {
  currentPage.value = 1
  generateTableData()
  emit('version-compare', filteredInstances.value)
}

const handleSectionFilter = () => {
  currentPage.value = 1
  generateTableData()
}

const handleRowClick = (row: Record<string, unknown>) => {
  selectedDetailInstance.value = row
  detailDialogVisible.value = true
  emit('instance-selected', row)
}

const handleSortChange = () => {
  generateTableData()
}

const handlePageChange = () => {
  generateTableData()
}

const refreshData = () => {
  emit('refresh')
  message.success('数据刷新成功')
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const handleExport = async (format: string) => {
  try {
    let exportData: Record<string, unknown>[] = []
    
    switch (format) {
      case 'csv':
        exportData = generateExportData()
        downloadCSV(exportData)
        break
        
      case 'excel':
        exportData = generateExportData()
        downloadExcel(exportData)
        break
        
      case 'json':
        exportData = filteredInstances.value
        downloadJSON(exportData)
        break
    }
    
    emit('export-requested', format, exportData)
    message.success(`${format.toUpperCase()}导出成功`)
  } catch (error) {
    console.error('导出失败:', error)
    message.error(`${format.toUpperCase()}导出失败`)
  }
}

const generateExportData = () => {
  return filteredInstances.value.map(instance => {
    const row: Record<string, unknown> = {
      '实例名称': instance.instance_name || '未命名实例',
      '受试者ID': instance.subject_id || '-',
      '访问ID': instance.visit_id || '-',
      '模板版本': instance.template_version || '1.0',
      '状态': getStatusText(instance.status),
      '完成度': `${instance.completion_percentage || 0}%`,
      '创建时间': formatDateTime(instance.created_at)
    }
    
    // 添加动态字段
    displayFields.value.forEach(field => {
      row[field.label] = formatFieldValue(instance.form_data?.[field.id], field)
    })
    
    return row
  })
}

const downloadCSV = (data: Record<string, unknown>[]) => {
  const ws = XLSX.utils.json_to_sheet(data)
  const csv = XLSX.utils.sheet_to_csv(ws)
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `表单数据_${new Date().toISOString().split('T')[0]}.csv`
  link.click()
  URL.revokeObjectURL(link.href)
}

const downloadExcel = (data: Record<string, unknown>[]) => {
  const ws = XLSX.utils.json_to_sheet(data)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '表单数据')
  XLSX.writeFile(wb, `表单数据_${new Date().toISOString().split('T')[0]}.xlsx`)
}

const downloadJSON = (data: Record<string, unknown>[]) => {
  const dataStr = JSON.stringify(data, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `表单数据_${new Date().toISOString().split('T')[0]}.json`
  link.click()
  URL.revokeObjectURL(url)
}

const loadInstanceDetail = async () => {
  const instance = props.instances.find(i => i.id === selectedInstanceId.value)
  if (instance) {
    selectedInstanceData.value = instance
  }
}

const copyRawData = async () => {
  if (!selectedInstanceData.value) return
  
  try {
    const dataText = formatJsonData(selectedInstanceData.value)
    await navigator.clipboard.writeText(dataText)
    message.success('数据已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const showInstanceDetail = (row: Record<string, unknown>) => {
  selectedDetailInstance.value = row
  detailDialogVisible.value = true
  emit('instance-selected', row)
}

const handleCloseDetail = () => {
  detailDialogVisible.value = false
  selectedDetailInstance.value = null
}

// 导出选项
const exportOptions = [
  { label: '导出 CSV', key: 'csv' },
  { label: '导出 Excel', key: 'excel' },
  { label: '导出 JSON', key: 'json' }
]

// 监听器
watch(() => props.instances, () => {
  extractVersions()
  extractSections()
  extractFields()
  generateTableData()
}, { immediate: true })

watch(() => props.templateSchema, () => {
  if (props.templateSchema && schemaMapper.value) {
    console.log('模板schema更新，重新解析字段')
    extractFields()
    generateTableData()
  }
}, { immediate: true })

watch([currentPage, pageSize], () => {
  generateTableData()
})

// 生命周期
onMounted(() => {
  // 创建schema映射器
  schemaMapper.value = createSchemaFieldMapper()
  
  extractVersions()
  extractSections()
  extractFields()
  generateTableData()
})

// 暴露方法
defineExpose({
  refreshData,
  exportData: handleExport,
  getTableData: () => tableData.value,
  getFilteredData: () => filteredInstances.value
})
</script>

<style lang="scss" scoped>
.data-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #fafafa;

  .toolbar-left {
    display: flex;
    gap: 12px;
    align-items: center;

    .search-input {
      width: 280px;
    }

    .version-filter,
    .section-filter {
      width: 150px;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.table-content {
  flex: 1;
  overflow: hidden;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: #fff;
  }
}

.table-view {
  height: 100%;
  display: flex;
  flex-direction: column;

  .pagination-wrapper {
    padding: 16px 20px;
    border-top: 1px solid #e5e7eb;
    background: #fafafa;
    text-align: center;
  }
}

// 实例名称单元格
.instance-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .name {
    flex: 1;
    font-weight: 500;
  }

  .version-tag {
    font-size: 10px;
  }
}

// 进度条样式
.mini-progress {
  margin-bottom: 4px;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

// 字段单元格
.field-cell {
  position: relative;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  .field-value {
    display: block;
    margin-bottom: 4px;
  }

  .version-indicators {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .version-tag {
      font-size: 10px;
      padding: 1px 4px;
      line-height: 1.2;
    }
  }

  // 版本变更样式
  &.field-added {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-left: 3px solid #22c55e;
  }

  &.field-modified {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-left: 3px solid #f59e0b;
  }

  &.field-removed {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-left: 3px solid #ef4444;
    opacity: 0.7;
  }
}

.raw-view {
  padding: 20px;
  height: 100%;
  overflow: auto;

  .raw-controls {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 20px;

    .instance-selector {
      width: 400px;
    }
  }

  .raw-data-content {
    .raw-tabs {
      :deep(.n-tabs__nav-wrap) {
        &::after {
          background-color: #f1f5f9;
        }
      }

      .json-viewer {
        background: #f8fafc;
        border-radius: 8px;
        padding: 16px;
        max-height: 500px;
        overflow: auto;

        pre {
          margin: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
          line-height: 1.5;
          color: #1f2937;

          code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
          }
        }

        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;

          &:hover {
            background: #94a3b8;
          }
        }
      }

      .instance-meta-info {
        padding: 16px;
        background: #f8fafc;
        border-radius: 8px;
      }
    }
  }

  .raw-placeholder {
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.diff-view {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .diff-controls {
    margin-bottom: 20px;

    .version-selectors {
      display: flex;
      gap: 20px;
      align-items: center;
      flex-wrap: wrap;

      .selector-group {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          font-weight: 500;
          color: #374151;
          white-space: nowrap;
        }

        .n-select {
          width: 150px;
        }
      }
    }
  }

  .diff-table {
    flex: 1;
    overflow: hidden;
  }

  .diff-placeholder {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 版本对比单元格样式
.diff-cell {
  padding: 4px;
  border-radius: 4px;

  &.diff-added {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
  }

  &.diff-removed {
    background: #fef2f2;
    border: 1px solid #fecaca;
  }

  &.diff-changed {
    background: #fffbeb;
    border: 1px solid #fed7aa;
  }

  &.diff-same {
    background: #f8fafc;
  }

  .base-value,
  .compare-value {
    display: flex;
    gap: 8px;
    margin: 2px 0;
    font-size: 12px;

    .value-label {
      color: #6b7280;
      min-width: 40px;
      font-weight: 500;
    }

    .value-content {
      flex: 1;
      color: #1f2937;
    }
  }
}

.empty-data {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;

  p {
    margin: 8px 0 0 0;
    font-size: 14px;
  }
}

.instance-detail-content {
  .detail-descriptions {
    margin-bottom: 24px;
  }

  .form-data-detail {
    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }

    .field-value-display {
      max-width: 400px;
      word-break: break-all;
    }
  }
}

// Element Plus Table 样式覆盖
:deep(.n-data-table) {
  .n-data-table__header {
    th {
      background: #f8fafc;
      font-weight: 600;
      color: #374151;
    }
  }

  .n-data-table__row {
    &:hover {
      background: #f0f9ff;
    }
  }

  .n-data-table__cell {
    padding: 8px 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .toolbar-left,
    .toolbar-right {
      justify-content: center;

      .search-input {
        width: 100%;
      }
    }
  }

  .diff-controls {
    .version-selectors {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .selector-group {
        .n-select {
          width: 100%;
        }
      }
    }
  }

  .instance-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>