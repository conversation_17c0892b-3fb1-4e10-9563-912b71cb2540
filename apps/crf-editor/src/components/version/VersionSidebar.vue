<template>
  <div class="version-sidebar" @click.stop>
    <div class="sidebar-header">
      <h3>版本管理</h3>
      <button class="close-btn" @click="$emit('close')">
        <crf-icon icon="material-symbols:close" size="20px" />
      </button>
    </div>

    <div class="sidebar-content">
      <!-- 当前版本信息 -->
      <div class="current-version-section">
        <div class="section-title">当前版本</div>
        <div class="current-version-card">
          <div class="version-info">
            <span class="version-number">V{{ currentVersion || '1.0.0' }}</span>
            <n-tag 
              :type="currentStatus === 'published' ? 'success' : 'warning'" 
              size="small"
            >
              {{ currentStatus === 'published' ? '已发布' : '草稿' }}
            </n-tag>
          </div>
          <div class="version-time">
            {{ formatTime(currentTime) }}
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <n-button 
          type="primary" 
          @click="handleCreateVersion"
          :loading="createVersionLoading"
          class="action-btn"
        >
          <crf-icon icon="material-symbols:add" size="16px" />
          创建新版本
        </n-button>
        
        <n-button 
          @click="handleRefresh"
          :loading="refreshLoading"
          class="action-btn"
        >
          <crf-icon icon="material-symbols:refresh" size="16px" />
          刷新列表
        </n-button>
      </div>

      <!-- 版本列表 -->
      <div class="version-list-section">
        <div class="section-title">历史版本</div>
        
        <div v-if="loading" class="loading-state">
          <n-skeleton text :repeat="3" />
        </div>
        
        <div v-else-if="versions.length === 0" class="empty-state">
          <div class="empty-content">
            <crf-icon icon="material-symbols:history" size="48px" class="empty-icon" />
            <h4>暂无版本记录</h4>
            <p>表单尚未发布，暂无版本记录。</p>
            <p>发布表单后会自动创建版本记录。</p>
            <n-button type="primary" @click="$emit('close')">
              去发布表单
            </n-button>
          </div>
        </div>
        
        <div v-else class="version-list">
          <div 
            v-for="version in versions" 
            :key="version.id"
            class="version-item"
            :class="{ 
              'is-current': version.version === currentVersion,
              'is-published': version.status === 'published'
            }"
          >
            <div class="version-header">
              <div class="version-meta">
                <span class="version-name">V{{ version.version }}</span>
                <n-tag 
                  :type="getVersionTagType(version)" 
                  size="small"
                >
                  {{ getVersionStatusText(version) }}
                </n-tag>
              </div>
              
              <div class="version-actions">
                <n-button 
                  text 
                  size="small"
                  @click="handlePreview(version)"
                  title="预览"
                >
                  <crf-icon icon="material-symbols:visibility" size="14px" />
                </n-button>
                
                <n-button 
                  v-if="version.version !== currentVersion"
                  text 
                  size="small"
                  @click="handleRollback(version)"
                  title="回滚"
                >
                  <crf-icon icon="material-symbols:history" size="14px" />
                </n-button>
              </div>
            </div>
            
            <div class="version-details">
              <div class="version-publisher">
                发布人: {{ version.creator?.username || version.publisher?.username || 'admin' }}
              </div>
              <div class="version-time">
                {{ formatTime(version.published_at || version.created_at) }}
              </div>
              <div v-if="version.change_log || version.description" class="version-description">
                {{ version.change_log || version.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建版本对话框 -->
    <n-modal 
      v-model:show="showCreateVersionDialog" 
      preset="dialog"
      title="创建新版本" 
      style="width: 400px"
    >
      <n-form ref="createVersionFormRef" :model="createVersionForm" :rules="createVersionRules">
        <n-form-item label="版本号" path="version">
          <n-input 
            v-model:value="createVersionForm.version" 
            placeholder="如: 1.0.1"
          />
          <div class="version-suggestion">
            <n-button @click="autoSetVersion('patch')" size="small" text>
              补丁版本 ({{ getNextVersion('patch') }})
            </n-button>
            <n-button @click="autoSetVersion('minor')" size="small" text>
              次版本 ({{ getNextVersion('minor') }})
            </n-button>
            <n-button @click="autoSetVersion('major')" size="small" text>
              主版本 ({{ getNextVersion('major') }})
            </n-button>
          </div>
        </n-form-item>
        
        <n-form-item label="描述">
          <n-input 
            v-model:value="createVersionForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="版本描述（可选）"
          />
        </n-form-item>
        
        <n-form-item label="变更日志">
          <n-input 
            v-model:value="createVersionForm.changeLog" 
            type="textarea" 
            :rows="3"
            placeholder="详细变更记录（可选）"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-button @click="showCreateVersionDialog = false">取消</n-button>
        <n-button 
          type="primary" 
          @click="confirmCreateVersion"
          :loading="createVersionLoading"
        >
          创建
        </n-button>
      </template>
    </n-modal>

    <!-- 版本预览对话框 -->
    <n-modal 
      v-model:show="showPreviewDialog" 
      preset="dialog"
      :title="`版本详情 - V${selectedVersion?.version}`"
      style="width: 80%"
    >
      <div v-if="selectedVersion" class="version-preview">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="版本号">
            V{{ selectedVersion.version }}
          </n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="getVersionTagType(selectedVersion)">
              {{ getVersionStatusText(selectedVersion) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="发布人">
            {{ selectedVersion.creator?.username || selectedVersion.publisher?.username || 'admin' }}
          </n-descriptions-item>
          <n-descriptions-item label="发布时间">
            {{ formatTime(selectedVersion.published_at || selectedVersion.created_at) }}
          </n-descriptions-item>
          <n-descriptions-item v-if="selectedVersion.description" label="描述" :span="2">
            {{ selectedVersion.description }}
          </n-descriptions-item>
          <n-descriptions-item v-if="selectedVersion.change_log" label="变更日志" :span="2">
            {{ selectedVersion.change_log }}
          </n-descriptions-item>
        </n-descriptions>

        <div class="snapshot-section">
          <h4>表单结构快照</h4>
          <n-scrollbar style="max-height: 300px">
            <pre class="json-content">{{ formatJSON(selectedVersion.snapshot_data) }}</pre>
          </n-scrollbar>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'
import { CrfIcon } from '@crf/components'
import { templateAPI } from '@/api'
import type { CRFVersion } from '@/types/api'

interface Props {
  templateId: string
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'rollback', version: CRFVersion): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 添加message和dialog实例
const message = useMessage()
const dialog = useDialog()

// 状态
const versions = ref<CRFVersion[]>([])
const currentVersion = ref<string>('')
const currentStatus = ref<string>('draft')
const currentTime = ref<string>('')
const refreshLoading = ref(false)
const createVersionLoading = ref(false)
const showCreateVersionDialog = ref(false)
const showPreviewDialog = ref(false)
const selectedVersion = ref<CRFVersion | null>(null)

// 表单相关
const createVersionFormRef = ref<FormInst>()
const createVersionForm = ref({
  version: '',
  description: '',
  changeLog: ''
})

const createVersionRules: FormRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为 x.y.z', trigger: 'blur' }
  ]
}

// 加载版本列表
const loadVersions = async () => {
  try {
    refreshLoading.value = true
    const response = await templateAPI.getVersions(props.templateId)
    if (response.success && response.data) {
      versions.value = response.data.versions || []
      
      // 获取当前模板信息
      const templateResponse = await templateAPI.getTemplate(props.templateId)
      if (templateResponse.success && templateResponse.data?.template) {
        const template = templateResponse.data.template
        currentVersion.value = template.version || '1.0.0'
        currentStatus.value = template.status || 'draft'
        currentTime.value = template.updated_at || template.created_at
      }
    }
  } catch (error) {
    console.error('加载版本列表失败:', error)
    message.error('加载版本列表失败')
  } finally {
    refreshLoading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '未知'
  return new Date(timeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取版本状态标签类型
const getVersionTagType = (version: CRFVersion) => {
  switch (version.status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取版本状态文本
const getVersionStatusText = (version: CRFVersion) => {
  switch (version.status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

// 格式化JSON
const formatJSON = (obj: unknown) => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch {
    return '无效的JSON数据'
  }
}

// 获取下一个版本号
const getNextVersion = (type: 'major' | 'minor' | 'patch') => {
  const current = currentVersion.value || '1.0.0'
  const parts = current.split('.').map(Number)
  
  switch (type) {
    case 'major':
      return `${parts[0] + 1}.0.0`
    case 'minor':
      return `${parts[0]}.${parts[1] + 1}.0`
    case 'patch':
      return `${parts[0]}.${parts[1]}.${parts[2] + 1}`
    default:
      return current
  }
}

// 自动设置版本号
const autoSetVersion = (type: 'major' | 'minor' | 'patch') => {
  createVersionForm.value.version = getNextVersion(type)
}

// 处理创建版本
const handleCreateVersion = () => {
  createVersionForm.value.version = getNextVersion('patch')
  createVersionForm.value.description = ''
  createVersionForm.value.changeLog = ''
  showCreateVersionDialog.value = true
}

// 确认创建版本
const confirmCreateVersion = async () => {
  if (!createVersionFormRef.value) return
  
  try {
    await createVersionFormRef.value.validate()
    createVersionLoading.value = true
    
    const response = await templateAPI.createVersion({
      template_id: props.templateId,
      version: createVersionForm.value.version,
      description: createVersionForm.value.description,
      change_log: createVersionForm.value.changeLog
    })
    
    if (response.success) {
      message.success('版本创建成功')
      showCreateVersionDialog.value = false
      await loadVersions()
    }
  } catch (error) {
    console.error('创建版本失败:', error)
    message.error('创建版本失败')
  } finally {
    createVersionLoading.value = false
  }
}

// 处理刷新
const handleRefresh = () => {
  loadVersions()
}

// 处理预览
const handlePreview = (version: CRFVersion) => {
  selectedVersion.value = version
  showPreviewDialog.value = true
}

// 处理回滚
const handleRollback = async (version: CRFVersion) => {
  try {
    await new Promise<void>((resolve, reject) => {
      dialog.warning({
        title: '确认回滚',
        content: `确定要回滚到版本 V${version.version} 吗？此操作不可逆。`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          resolve()
        },
        onNegativeClick: () => {
          reject('cancel')
        }
      })
    })
    
    emit('rollback', version)
  } catch (error) {
    // 用户取消操作
  }
}

// 初始化
onMounted(() => {
  loadVersions()
})
</script>

<style lang="scss" scoped>
.version-sidebar {
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
    
    .close-btn {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      color: #909399;
      
      &:hover {
        background: #f5f7fa;
        color: #606266;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 12px;
    }

    .current-version-section {
      margin-bottom: 24px;

      .current-version-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;

        .version-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .version-number {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }

        .version-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .action-section {
      margin-bottom: 24px;

      .action-btn {
        width: 100%;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
      }
    }

    .version-list-section {
      .loading-state, .empty-state {
        padding: 40px 20px;
        text-align: center;
      }

      .empty-content {
        .empty-icon {
          color: #c0c4cc;
          margin-bottom: 16px;
        }

        h4 {
          margin: 0 0 8px 0;
          color: #606266;
          font-size: 16px;
        }

        p {
          margin: 4px 0;
          color: #909399;
          font-size: 14px;
          line-height: 1.4;
        }

        .n-button {
          margin-top: 16px;
        }
      }

      .version-list {
        .version-item {
          border: 1px solid #ebeef5;
          border-radius: 8px;
          margin-bottom: 12px;
          overflow: hidden;
          transition: all 0.2s;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.is-current {
            border-color: #409eff;
            background: #f0f9ff;
          }

          &.is-published {
            border-left: 4px solid #67c23a;
          }

          .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #fafafa;
            border-bottom: 1px solid #ebeef5;

            .version-meta {
              display: flex;
              align-items: center;
              gap: 8px;

              .version-name {
                font-weight: 600;
                color: #303133;
              }
            }

            .version-actions {
              display: flex;
              gap: 4px;
            }
          }

          .version-details {
            padding: 12px 16px;

            .version-publisher, .version-time {
              font-size: 12px;
              color: #909399;
              margin-bottom: 4px;
            }

            .version-description {
              font-size: 13px;
              color: #606266;
              line-height: 1.4;
              margin-top: 8px;
            }
          }
        }
      }
    }
  }

  .version-suggestion {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .version-preview {
    .snapshot-section {
      margin-top: 24px;

      h4 {
        margin-bottom: 12px;
        color: #303133;
      }

      .json-content {
        background: #f5f7fa;
        padding: 16px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #606266;
        white-space: pre-wrap;
      }
    }
  }
}
</style>