<template>
  <div class="version-list">
    <div v-if="versions.length === 0" class="empty-state">
      <n-empty description="暂无版本记录" />
    </div>
    
    <div v-else class="version-items">
      <div 
        v-for="version in versions" 
        :key="version.id"
        class="version-item"
        :class="{ 
          'is-current': version.version === currentVersion,
          'is-published': version.status === 'published',
          'is-draft': version.status === 'draft'
        }"
      >
        <div class="version-header">
          <div class="version-info">
            <h4 class="version-number">v{{ version.version }}</h4>
            <n-tag 
              :type="getVersionTagType(version)" 
              size="small"
            >
              {{ getVersionStatusText(version) }}
            </n-tag>
            <span v-if="version.version === currentVersion" class="current-badge">
              当前版本
            </span>
          </div>
          
          <div class="version-actions">
            <n-button-group size="small">
              <n-button 
                @click="$emit('view-details', version)"
                title="查看详情"
              >
                <template #icon>
                  <n-icon><EyeIcon /></n-icon>
                </template>
              </n-button>
              <n-button 
                @click="$emit('rollback', version)"
                :disabled="version.version === currentVersion"
                title="回滚到此版本"
              >
                <template #icon>
                  <n-icon><RefreshIcon /></n-icon>
                </template>
              </n-button>
              <n-button 
                @click="addToCompare(version)"
                :disabled="compareList.length >= 2"
                title="添加到比较"
              >
                <template #icon>
                  <n-icon><RankIcon /></n-icon>
                </template>
              </n-button>
            </n-button-group>
          </div>
        </div>
        
        <div class="version-content">
          <div class="version-meta">
            <span class="version-title">{{ version.title }}</span>
            <span class="version-time">{{ formatTime(version.created_at) }}</span>
          </div>
          
          <div v-if="version.description" class="version-description">
            {{ version.description }}
          </div>
          
          <div v-if="version.change_log" class="version-changelog">
            <details>
              <summary>变更日志</summary>
              <pre>{{ version.change_log }}</pre>
            </details>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 比较工具栏 -->
    <div v-if="compareList.length > 0" class="compare-toolbar">
      <div class="compare-info">
        已选择 {{ compareList.length }} 个版本进行比较
      </div>
      
      <div class="compare-actions">
        <n-button 
          @click="handleCompare"
          type="primary"
          :disabled="compareList.length !== 2"
        >
          <template #icon>
            <n-icon><RankIcon /></n-icon>
          </template>
          比较版本
        </n-button>
        <n-button @click="clearCompare">
          <template #icon>
            <n-icon><CloseIcon /></n-icon>
          </template>
          清除选择
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Eye as EyeIcon, Refresh as RefreshIcon, BarChart as RankIcon, Close as CloseIcon } from '@vicons/ionicons5'
import type { CRFVersion } from '@/types/api'

interface Props {
  versions: CRFVersion[]
  currentVersion: string
}

defineProps<Props>()

const emit = defineEmits<{
  rollback: [version: CRFVersion]
  compare: [versions: CRFVersion[]]
  'view-details': [version: CRFVersion]
}>()

// 比较列表
const compareList = ref<CRFVersion[]>([])

// 获取版本状态标签类型
const getVersionTagType = (version: CRFVersion) => {
  switch (version.status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取版本状态文本
const getVersionStatusText = (version: CRFVersion) => {
  switch (version.status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 添加到比较列表
const addToCompare = (version: CRFVersion) => {
  if (compareList.value.length < 2 && !compareList.value.find(v => v.id === version.id)) {
    compareList.value.push(version)
  }
}

// 处理比较
const handleCompare = () => {
  if (compareList.value.length === 2) {
    emit('compare', [...compareList.value])
    clearCompare()
  }
}

// 清除比较选择
const clearCompare = () => {
  compareList.value = []
}
</script>

<style lang="scss" scoped>
.version-list {
  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }
  
  .version-items {
    .version-item {
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;
      transition: all 0.2s ease;
      
      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.is-current {
        border-color: #409eff;
        background: #f0f9ff;
      }
      
      &.is-published {
        border-left: 4px solid #67c23a;
      }
      
      &.is-draft {
        border-left: 4px solid #909399;
      }
      
      .version-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        
        .version-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .version-number {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .current-badge {
            font-size: 12px;
            color: #409eff;
            background: #ecf5ff;
            padding: 2px 8px;
            border-radius: 12px;
          }
        }
      }
      
      .version-content {
        padding: 16px;
        
        .version-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .version-title {
            font-weight: 500;
            color: #303133;
          }
          
          .version-time {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .version-description {
          color: #606266;
          margin-bottom: 8px;
          line-height: 1.5;
        }
        
        .version-changelog {
          details {
            summary {
              cursor: pointer;
              color: #409eff;
              font-size: 12px;
              margin-bottom: 8px;
              
              &:hover {
                text-decoration: underline;
              }
            }
            
            pre {
              background: #f5f7fa;
              padding: 8px;
              border-radius: 4px;
              font-size: 12px;
              line-height: 1.4;
              white-space: pre-wrap;
              margin: 0;
            }
          }
        }
      }
    }
  }
  
  .compare-toolbar {
    position: sticky;
    bottom: 0;
    background: white;
    border-top: 1px solid #e8e8e8;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .compare-info {
      font-size: 14px;
      color: #606266;
    }
    
    .compare-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>