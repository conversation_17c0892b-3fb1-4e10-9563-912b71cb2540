<template>
  <div class="version-details">
    <div class="details-header">
      <h3>版本详情</h3>
      <n-tag :type="getStatusTagType()" size="large">
        {{ getStatusText() }}
      </n-tag>
    </div>
    
    <div class="details-content">
      <n-descriptions :column="2" bordered>
        <n-descriptions-item label="版本号">
          <code>{{ version.version }}</code>
        </n-descriptions-item>
        
        <n-descriptions-item label="状态">
          <n-tag :type="getStatusTagType()">{{ getStatusText() }}</n-tag>
        </n-descriptions-item>
        
        <n-descriptions-item label="标题" :span="2">
          {{ version.title }}
        </n-descriptions-item>
        
        <n-descriptions-item label="创建时间">
          {{ formatTime(version.created_at) }}
        </n-descriptions-item>
        
        <n-descriptions-item label="创建者">
          {{ version.creator?.full_name || version.creator?.username || '未知' }}
        </n-descriptions-item>
        
        <n-descriptions-item v-if="version.published_at" label="发布时间">
          {{ formatTime(version.published_at) }}
        </n-descriptions-item>
        
        <n-descriptions-item v-if="version.publisher" label="发布者">
          {{ version.publisher.full_name || version.publisher.username }}
        </n-descriptions-item>
        
        <n-descriptions-item v-if="version.description" label="描述" :span="2">
          {{ version.description }}
        </n-descriptions-item>
      </n-descriptions>
      
      <div v-if="version.change_log" class="change-log-section">
        <h4>变更日志</h4>
        <div class="change-log-content">
          <pre>{{ version.change_log }}</pre>
        </div>
      </div>
      
      <div v-if="version.snapshot_data" class="snapshot-section">
        <h4>版本快照</h4>
        <div class="snapshot-content">
          <n-tabs type="card">
            <n-tab-pane name="structure" tab="表单结构">
              <json-viewer :data="version.snapshot_data" />
            </n-tab-pane>
            
            <n-tab-pane name="stats" tab="统计信息">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-label">章节数量</div>
                  <div class="stat-value">{{ getSectionCount() }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">组件数量</div>
                  <div class="stat-value">{{ getComponentCount() }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">数据大小</div>
                  <div class="stat-value">{{ getDataSize() }}</div>
                </div>
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import type { CRFVersion, CRFTemplate } from '@/types/api'
import JsonViewer from '@/components/preview/JsonViewer.vue'

interface Props {
  version: CRFVersion
  template?: CRFTemplate | null
}

const props = defineProps<Props>()

// 获取状态标签类型
const getStatusTagType = () => {
  switch (props.version.status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = () => {
  switch (props.version.status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取章节数量
const getSectionCount = () => {
  try {
    const data = props.version.snapshot_data
    if (Array.isArray(data)) {
      return data.length
    }
    return 0
  } catch {
    return 0
  }
}

// 获取组件数量
const getComponentCount = () => {
  try {
    const data = props.version.snapshot_data
    if (Array.isArray(data)) {
      let count = 0
      const countComponents = (sections: Record<string, unknown>[]) => {
        sections.forEach(section => {
          count += section.blocks?.length || 0
          if (section.children) {
            countComponents(section.children)
          }
        })
      }
      countComponents(data)
      return count
    }
    return 0
  } catch {
    return 0
  }
}

// 获取数据大小
const getDataSize = () => {
  try {
    const dataStr = JSON.stringify(props.version.snapshot_data)
    const bytes = new Blob([dataStr]).size
    
    if (bytes < 1024) {
      return `${bytes} B`
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
    }
  } catch {
    return '未知'
  }
}
</script>

<style lang="scss" scoped>
.version-details {
  .details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .details-content {
    .change-log-section {
      margin-top: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
      
      .change-log-content {
        background: #f5f7fa;
        border-radius: 4px;
        padding: 12px;
        
        pre {
          margin: 0;
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.5;
          white-space: pre-wrap;
          color: #606266;
        }
      }
    }
    
    .snapshot-section {
      margin-top: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
      
      .snapshot-content {
        :deep(.n-tabs__content) {
          padding: 16px;
        }
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 16px;
          
          .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            
            .stat-label {
              font-size: 12px;
              color: #6c757d;
              margin-bottom: 8px;
            }
            
            .stat-value {
              font-size: 24px;
              font-weight: 600;
              color: #495057;
            }
          }
        }
      }
    }
  }
}
</style>