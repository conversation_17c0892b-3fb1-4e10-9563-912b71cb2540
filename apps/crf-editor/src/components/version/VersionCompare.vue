<template>
  <div class="version-compare">
    <div class="compare-header">
      <h3>版本比较</h3>
      <div class="version-info">
        <div class="version-tag">
          <n-tag type="primary">{{ version1.version }}</n-tag>
          <span>{{ formatTime(version1.created_at) }}</span>
        </div>
        <span class="vs">VS</span>
        <div class="version-tag">
          <n-tag type="success">{{ version2.version }}</n-tag>
          <span>{{ formatTime(version2.created_at) }}</span>
        </div>
      </div>
    </div>
    
    <div class="compare-content">
      <n-tabs type="card">
        <n-tab-pane name="info" tab="基本信息">
          <div class="info-compare">
            <n-data-table :data="getInfoComparison()" :bordered="true" :striped="true" :columns="infoColumns" />
          </div>
        </n-tab-pane>
        
        <n-tab-pane name="structure" tab="表单结构">
          <div class="structure-compare">
            <div class="compare-panels">
              <div class="compare-panel">
                <h4>版本 {{ version1.version }}</h4>
                <div class="structure-tree">
                  <json-viewer :data="version1.snapshot_data" />
                </div>
              </div>
              
              <div class="compare-panel">
                <h4>版本 {{ version2.version }}</h4>
                <div class="structure-tree">
                  <json-viewer :data="version2.snapshot_data" />
                </div>
              </div>
            </div>
          </div>
        </n-tab-pane>
        
        <n-tab-pane name="stats" tab="差异统计">
          <div class="stats-compare">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-label">章节数量变化</div>
                <div class="stat-value">
                  {{ getSectionCount(version1) }} → {{ getSectionCount(version2) }}
                  <span :class="getSectionCountChangeClass()">
                    ({{ getSectionCountChange() }})
                  </span>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-label">组件数量变化</div>
                <div class="stat-value">
                  {{ getComponentCount(version1) }} → {{ getComponentCount(version2) }}
                  <span :class="getComponentCountChangeClass()">
                    ({{ getComponentCountChange() }})
                  </span>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-label">数据大小变化</div>
                <div class="stat-value">
                  {{ getDataSize(version1) }} → {{ getDataSize(version2) }}
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-label">时间间隔</div>
                <div class="stat-value">
                  {{ getTimeDifference() }}
                </div>
              </div>
            </div>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { NDataTable, NTag } from 'naive-ui'
import type { CRFVersion } from '@/types/api'
import JsonViewer from '@/components/preview/JsonViewer.vue'

interface Props {
  version1: CRFVersion
  version2: CRFVersion
}

const props = defineProps<Props>()

// 表格列配置
const infoColumns = [
  {
    key: 'field',
    title: '字段',
    width: 120
  },
  {
    key: 'value1',
    title: `版本 ${props.version1.version}`,
    width: 300,
    render: (row: Record<string, unknown>) => {
      return h('span', {
        class: row.isDifferent ? 'text-diff' : ''
      }, row.value1)
    }
  },
  {
    key: 'value2',
    title: `版本 ${props.version2.version}`,
    width: 300,
    render: (row: Record<string, unknown>) => {
      return h('span', {
        class: row.isDifferent ? 'text-diff' : ''
      }, row.value2)
    }
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    render: (row: Record<string, unknown>) => {
      return h(NTag, {
        type: row.isDifferent ? 'warning' : 'success',
        size: 'small'
      }, {
        default: () => row.isDifferent ? '已更改' : '相同'
      })
    }
  }
]

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取基本信息比较
const getInfoComparison = () => {
  return [
    {
      field: '标题',
      value1: props.version1.title,
      value2: props.version2.title,
      isDifferent: props.version1.title !== props.version2.title
    },
    {
      field: '描述',
      value1: props.version1.description || '无',
      value2: props.version2.description || '无',
      isDifferent: props.version1.description !== props.version2.description
    },
    {
      field: '状态',
      value1: props.version1.status,
      value2: props.version2.status,
      isDifferent: props.version1.status !== props.version2.status
    },
    {
      field: '创建者',
      value1: props.version1.creator?.username || '未知',
      value2: props.version2.creator?.username || '未知',
      isDifferent: props.version1.created_by !== props.version2.created_by
    }
  ]
}

// 获取章节数量
const getSectionCount = (version: CRFVersion) => {
  try {
    const data = version.snapshot_data
    if (Array.isArray(data)) {
      return data.length
    }
    return 0
  } catch {
    return 0
  }
}

// 获取组件数量
const getComponentCount = (version: CRFVersion) => {
  try {
    const data = version.snapshot_data
    if (Array.isArray(data)) {
      let count = 0
      const countComponents = (sections: Record<string, unknown>[]) => {
        sections.forEach(section => {
          count += section.blocks?.length || 0
          if (section.children) {
            countComponents(section.children)
          }
        })
      }
      countComponents(data)
      return count
    }
    return 0
  } catch {
    return 0
  }
}

// 获取数据大小
const getDataSize = (version: CRFVersion) => {
  try {
    const dataStr = JSON.stringify(version.snapshot_data)
    const bytes = new Blob([dataStr]).size
    
    if (bytes < 1024) {
      return `${bytes} B`
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
    }
  } catch {
    return '未知'
  }
}

// 获取章节数量变化
const getSectionCountChange = () => {
  const diff = getSectionCount(props.version2) - getSectionCount(props.version1)
  if (diff > 0) return `+${diff}`
  if (diff < 0) return `${diff}`
  return '无变化'
}

// 获取章节数量变化样式类
const getSectionCountChangeClass = () => {
  const diff = getSectionCount(props.version2) - getSectionCount(props.version1)
  if (diff > 0) return 'change-increase'
  if (diff < 0) return 'change-decrease'
  return 'change-none'
}

// 获取组件数量变化
const getComponentCountChange = () => {
  const diff = getComponentCount(props.version2) - getComponentCount(props.version1)
  if (diff > 0) return `+${diff}`
  if (diff < 0) return `${diff}`
  return '无变化'
}

// 获取组件数量变化样式类
const getComponentCountChangeClass = () => {
  const diff = getComponentCount(props.version2) - getComponentCount(props.version1)
  if (diff > 0) return 'change-increase'
  if (diff < 0) return 'change-decrease'
  return 'change-none'
}

// 获取时间差
const getTimeDifference = () => {
  const time1 = new Date(props.version1.created_at).getTime()
  const time2 = new Date(props.version2.created_at).getTime()
  const diff = Math.abs(time2 - time1)
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
}
</script>

<style lang="scss" scoped>
.version-compare {
  .compare-header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .version-info {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .version-tag {
        display: flex;
        align-items: center;
        gap: 8px;
        
        span {
          font-size: 12px;
          color: #666;
        }
      }
      
      .vs {
        font-weight: bold;
        color: #909399;
      }
    }
  }
  
  .compare-content {
    .info-compare {
      .text-diff {
        background: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
      }
    }
    
    .structure-compare {
      .compare-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        
        .compare-panel {
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          overflow: hidden;
          
          h4 {
            margin: 0;
            padding: 12px 16px;
            background: #f5f5f5;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            font-weight: 600;
          }
          
          .structure-tree {
            padding: 16px;
            max-height: 500px;
            overflow-y: auto;
          }
        }
      }
    }
    
    .stats-compare {
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        
        .stat-item {
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;
          text-align: center;
          
          .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
          }
          
          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            
            .change-increase {
              color: #67c23a;
              margin-left: 8px;
            }
            
            .change-decrease {
              color: #f56c6c;
              margin-left: 8px;
            }
            
            .change-none {
              color: #909399;
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
}
</style>