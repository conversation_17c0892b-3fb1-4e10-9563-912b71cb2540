<template>
  <n-modal
    v-model:show="dialogVisible"
    title="版本管理"
    style="width: 90%"
    :mask-closable="false"
    :close-on-esc="false"
    class="version-management-modal"
  >
    <div class="version-management-container">
      <!-- 头部工具栏 -->
      <div class="version-toolbar">
        <n-button
          type="primary"
          @click="handleCreateVersion"
          :loading="createVersionLoading"
        >
          版本比较
        </n-button>
        <n-button @click="handleRefresh" :loading="refreshLoading">
          刷新
        </n-button>
      </div>

      <!-- 版本列表 -->
      <div class="version-list-container">
        <n-data-table
          :data="versions"
          :columns="tableColumns"
          :max-height="400"
          striped
          :row-key="(row) => row.id"
          @update:checked-row-keys="handleSelectionChange"
        />



        <!-- 分页 -->
        <div class="pagination-container">
          <n-pagination
            v-model:page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :item-count="total"
            show-size-picker
            show-quick-jumper
            @update:page="handleCurrentChange"
            @update:page-size="handleSizeChange"
          />
        </div>
      </div>

      <!-- 版本详情对话框 -->
      <n-modal
        v-model:show="previewDialogVisible"
        :title="`版本详情 - V${selectedVersion?.version}`"
        style="width: 80%"
        :mask-closable="false"
      >
        <div v-if="selectedVersion" class="version-detail">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="版本号">
              V{{ selectedVersion.version }}
            </n-descriptions-item>
            <n-descriptions-item label="发布人">
              {{ selectedVersion.creator?.username || 'admin' }}
            </n-descriptions-item>
            <n-descriptions-item label="发布时间">
              {{ formatDateTime(selectedVersion.published_at || selectedVersion.created_at) }}
            </n-descriptions-item>
            <n-descriptions-item label="状态">
              <n-tag :type="getVersionStatusType(selectedVersion.status)">
                {{ getVersionStatusText(selectedVersion.status) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="变更描述" :span="2">
              {{ selectedVersion.change_log || selectedVersion.description || '无' }}
            </n-descriptions-item>
          </n-descriptions>

          <div class="version-content">
            <h4>表单结构</h4>
            <n-scrollbar style="max-height: 300px">
              <pre class="json-content">{{ formatJSON(selectedVersion.snapshot_data) }}</pre>
            </n-scrollbar>
          </div>
        </div>
      </n-modal>

      <!-- 版本比较对话框 -->
      <n-modal
        v-model:show="compareDialogVisible"
        title="版本比较"
        style="width: 90%"
        :mask-closable="false"
      >
        <div v-if="selectedVersions.length === 2" class="version-compare">
          <div class="compare-header">
            <div class="version-info">
              <div class="version-tag">
                <n-tag type="primary">V{{ selectedVersions[0].version }}</n-tag>
                <span>{{ formatDateTime(selectedVersions[0].created_at) }}</span>
              </div>
              <span class="vs">对比</span>
              <div class="version-tag">
                <n-tag type="success">V{{ selectedVersions[1].version }}</n-tag>
                <span>{{ formatDateTime(selectedVersions[1].created_at) }}</span>
              </div>
            </div>
          </div>

          <n-tabs type="card">
            <n-tab-pane name="info" tab="基本信息">
              <n-data-table :data="getVersionComparison()" :columns="compareColumns" bordered />
            </n-tab-pane>

            <n-tab-pane name="structure" tab="表单结构">
              <div class="structure-compare">
                <div class="compare-panels">
                  <div class="compare-panel">
                    <h4>V{{ selectedVersions[0].version }}</h4>
                    <n-scrollbar style="max-height: 400px">
                      <pre class="json-content">{{ formatJSON(selectedVersions[0].snapshot_data) }}</pre>
                    </n-scrollbar>
                  </div>
                  <div class="compare-panel">
                    <h4>V{{ selectedVersions[1].version }}</h4>
                    <n-scrollbar style="max-height: 400px">
                      <pre class="json-content">{{ formatJSON(selectedVersions[1].snapshot_data) }}</pre>
                    </n-scrollbar>
                  </div>
                </div>
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
      </n-modal>
    </div>

    <template #action>
      <span class="dialog-footer">
        <n-button @click="dialogVisible = false">关闭</n-button>
        <n-button
          type="primary"
          @click="handleCompareVersions"
          :disabled="selectedVersions.length !== 2"
        >
          版本比较
        </n-button>
      </span>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { useMessage, useDialog, NButton, NTag } from 'naive-ui'
import { Eye, ArrowBackOutline as RefreshLeft } from '@vicons/ionicons5'
import { templateAPI } from '@/api'
import type { CRFVersion } from '@/types/api'
import type { DataTableColumns } from 'naive-ui'

interface Props {
  modelValue: boolean
  templateId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'rollback', version: CRFVersion): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 添加message和dialog实例
const message = useMessage()
const dialog = useDialog()

// 响应式状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const versions = ref<CRFVersion[]>([])
const selectedVersions = ref<CRFVersion[]>([])
const selectedVersion = ref<CRFVersion | null>(null)
const currentVersionId = ref<string>('')
const refreshLoading = ref(false)
const createVersionLoading = ref(false)
const previewDialogVisible = ref(false)
const compareDialogVisible = ref(false)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '—'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化JSON
const formatJSON = (obj: unknown) => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch {
    return '无效的JSON数据'
  }
}

// 获取版本状态类型
const getVersionStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取版本状态文本
const getVersionStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

// 加载版本列表
const loadVersions = async () => {
  try {
    refreshLoading.value = true
    const response = await templateAPI.getVersions(props.templateId)
    if (response.success && response.data) {
      versions.value = response.data.versions || []
      // 设置当前版本ID（通常是最新的已发布版本）
      const publishedVersions = versions.value.filter(v => v.status === 'published')
      if (publishedVersions.length > 0) {
        currentVersionId.value = publishedVersions[0].id
      }
      total.value = versions.value.length
    }
  } catch (error) {
    console.error('加载版本列表失败:', error)
    message.error('加载版本列表失败')
  } finally {
    refreshLoading.value = false
  }
}

// 处理选择变化
const handleSelectionChange = (selection: CRFVersion[]) => {
  selectedVersions.value = selection
}

// 处理预览
const handlePreview = (version: CRFVersion) => {
  selectedVersion.value = version
  previewDialogVisible.value = true
}

// 处理回滚
const handleRollback = async (version: CRFVersion) => {
  try {
    await new Promise<void>((resolve, reject) => {
      dialog.warning({
        title: '确认回滚',
        content: `确定要回滚到版本 V${version.version} 吗？此操作不可逆。`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          resolve()
        },
        onNegativeClick: () => {
          reject('cancel')
        }
      })
    })
    
    emit('rollback', version)
    dialogVisible.value = false
  } catch (error) {
    if (error !== 'cancel') {
      console.error('回滚失败:', error)
      message.error('回滚失败')
    }
  }
}

// 处理比较版本
const handleCompareVersions = () => {
  if (selectedVersions.value.length !== 2) {
    message.warning('请选择两个版本进行比较')
    return
  }
  compareDialogVisible.value = true
}

// 处理创建版本
const handleCreateVersion = () => {
  if (selectedVersions.value.length !== 2) {
    message.warning('请选择两个版本进行比较')
    return
  }
  handleCompareVersions()
}

// 处理刷新
const handleRefresh = () => {
  loadVersions()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 获取版本比较数据
const getVersionComparison = () => {
  const [v1, v2] = selectedVersions.value
  return [
    {
      field: '版本号',
      value1: v1.version,
      value2: v2.version,
      isDifferent: v1.version !== v2.version
    },
    {
      field: '发布人',
      value1: v1.creator?.username || 'admin',
      value2: v2.creator?.username || 'admin',
      isDifferent: v1.created_by !== v2.created_by
    },
    {
      field: '发布时间',
      value1: formatDateTime(v1.published_at || v1.created_at),
      value2: formatDateTime(v2.published_at || v2.created_at),
      isDifferent: v1.created_at !== v2.created_at
    },
    {
      field: '状态',
      value1: getVersionStatusText(v1.status),
      value2: getVersionStatusText(v2.status),
      isDifferent: v1.status !== v2.status
    },
    {
      field: '变更描述',
      value1: v1.change_log || v1.description || '无',
      value2: v2.change_log || v2.description || '无',
      isDifferent: (v1.change_log || v1.description) !== (v2.change_log || v2.description)
    }
  ]
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadVersions()
  }
})

onMounted(() => {
  if (dialogVisible.value) {
    loadVersions()
  }
})
</script>

<style lang="scss" scoped>
.version-management-modal {
  .version-management-container {
    .version-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;
    }

    .version-list-container {
      .version-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .version-number {
          font-weight: 600;
        }

        .latest-tag {
          font-size: 10px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 8px;
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 16px;
      }
    }
  }

  .version-detail {
    .version-content {
      margin-top: 24px;

      h4 {
        margin-bottom: 12px;
        color: #303133;
      }

      .json-content {
        background: #f5f7fa;
        padding: 16px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #606266;
        white-space: pre-wrap;
      }
    }
  }

  .version-compare {
    .compare-header {
      margin-bottom: 16px;

      .version-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 24px;

        .version-tag {
          display: flex;
          align-items: center;
          gap: 8px;

          span {
            font-size: 12px;
            color: #666;
          }
        }

        .vs {
          font-weight: bold;
          color: #909399;
          font-size: 16px;
        }
      }
    }

    .text-diff {
      background: #fff3cd;
      padding: 2px 4px;
      border-radius: 3px;
    }

    .structure-compare {
      .compare-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .compare-panel {
          border: 1px solid #ebeef5;
          border-radius: 4px;

          h4 {
            margin: 0;
            padding: 12px 16px;
            background: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
            font-weight: 600;
          }

          .json-content {
            background: #fafafa;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.5;
            color: #606266;
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>