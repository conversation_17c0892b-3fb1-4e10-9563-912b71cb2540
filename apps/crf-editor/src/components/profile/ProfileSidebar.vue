<!-- 个人资料侧边栏 -->
<template>
  <div class="profile-sidebar" :class="{ 'is-visible': visible }">
    <!-- 蒙版 -->
    <div class="sidebar-mask" @click="handleClose"></div>
    
    <!-- 侧边栏内容 -->
    <div class="sidebar-content">
      <!-- 头部 -->
      <div class="sidebar-header">
        <div class="header-left">
          <n-icon class="header-icon" :component="User" />
          <h3 class="header-title">个人资料</h3>
        </div>
        <n-button 
          text 
          @click="handleClose"
          class="close-btn"
        >
          <n-icon :component="Close" />
        </n-button>
      </div>

      <!-- 内容区域 -->
      <div class="sidebar-body">
        <n-scrollbar>
          <div class="profile-content">
            <!-- 头像部分 -->
            <div class="avatar-section">
              <div class="avatar-container">
                <div class="avatar-wrapper" @click="openAvatarEditor">
                  <n-avatar :size="80" class="user-avatar">
                    <img v-if="userProfile.avatar_url" :src="userProfile.avatar_url" :alt="userProfile.full_name" />
                    <n-icon v-else :component="User" />
                  </n-avatar>
                  <div class="avatar-overlay">
                    <n-icon class="edit-icon" :component="Camera" />
                    <span class="edit-text">编辑头像</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 基本信息 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">基本信息</h4>
                <!-- 基本信息操作按钮 -->
                <div class="section-actions" v-if="!passwordEditMode">
                  <n-button 
                    v-if="!editMode"
                    size="small"
                    type="primary"
                    @click="enterEditMode"
                  >
                    <template #icon>
                      <n-icon :component="Edit" />
                    </template>
                    编辑资料
                  </n-button>
                  <template v-else>
                    <n-button 
                      size="small"
                      @click="cancelEdit"
                    >
                      <template #icon>
                        <n-icon :component="Close" />
                      </template>
                      取消
                    </n-button>
                    <n-button 
                      size="small"
                      type="primary"
                      @click="saveProfile"
                      :loading="saving"
                    >
                      <template #icon>
                        <n-icon :component="Check" />
                      </template>
                      保存
                    </n-button>
                  </template>
                </div>
              </div>
              <n-form 
                :model="formData" 
                :rules="rules"
                ref="profileForm"
                label-width="80"
                class="profile-form"
              >
                <n-form-item label="用户名" path="username">
                  <n-input 
                    v-model:value="formData.username" 
                    disabled
                    class="readonly-input"
                  />
                </n-form-item>
                
                <n-form-item label="邮箱" path="email">
                  <n-input 
                    v-model:value="formData.email" 
                    type="text"
                    :disabled="!editMode"
                  />
                </n-form-item>
                
                <n-form-item label="姓名" path="full_name">
                  <n-input 
                    v-model:value="formData.full_name" 
                    :disabled="!editMode"
                  />
                </n-form-item>
                
                <n-form-item label="手机号" path="phone">
                  <n-input 
                    v-model:value="formData.phone" 
                    :disabled="!editMode"
                  />
                </n-form-item>
                
                <n-form-item label="部门" path="department">
                  <n-input 
                    v-model:value="formData.department" 
                    :disabled="!editMode"
                  />
                </n-form-item>
                
                <n-form-item label="职位" path="position">
                  <n-input 
                    v-model:value="formData.position" 
                    :disabled="!editMode"
                  />
                </n-form-item>
              </n-form>
            </div>

            <!-- 密码修改 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">安全设置</h4>
                <!-- 安全设置操作按钮 -->
                <div class="section-actions" v-if="!editMode">
                <n-button 
                    v-if="!passwordEditMode"
                  size="small"
                    type="warning"
                    @click="enterPasswordEditMode"
                >
                    <template #icon>
                      <n-icon :component="Lock" />
                    </template>
                    修改密码
                </n-button>
                <template v-else>
                  <n-button 
                    size="small"
                      @click="cancelPasswordEdit"
                  >
                    <template #icon>
                      <n-icon :component="Close" />
                    </template>
                    取消
                  </n-button>
                  <n-button 
                    size="small"
                      type="warning"
                      @click="changePassword"
                    :loading="saving"
                  >
                    <template #icon>
                      <n-icon :component="Check" />
                    </template>
                      修改密码
                  </n-button>
                </template>
              </div>
              </div>
              <n-form 
                :model="passwordForm" 
                :rules="passwordRules"
                ref="passwordFormRef"
                label-width="80"
                class="password-form"
              >
                <n-form-item label="新密码" path="newPassword">
                  <n-input 
                    v-model:value="passwordForm.newPassword" 
                    type="password"
                    show-password-on="click"
                    placeholder="请输入新密码"
                    :disabled="!passwordEditMode"
                  />
                </n-form-item>
                
                <n-form-item label="确认密码" path="confirmPassword">
                  <n-input 
                    v-model:value="passwordForm.confirmPassword" 
                    type="password"
                    show-password-on="click"
                    placeholder="请确认新密码"
                    :disabled="!passwordEditMode"
                  />
                </n-form-item>
              </n-form>
            </div>
          </div>
        </n-scrollbar>
      </div>
    </div>
    
    <!-- 头像编辑对话框 -->
    <AvatarEditor 
      v-model:visible="showAvatarEditor"
      :current-avatar="userProfile.avatar_url"
      @uploaded="handleAvatarUploaded"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/stores/user-store'
import { profileAPI } from '@/api/rbac'
import { storeToRefs } from 'pinia'
import { useMessage } from 'naive-ui'
import {
  PersonOutline as User,
  CloseOutline as Close,
  CameraOutline as Camera,
  CreateOutline as Edit,
  CheckmarkOutline as Check,
  LockClosedOutline as Lock
} from '@vicons/ionicons5'
import AvatarEditor from './AvatarEditor.vue'

interface ProfileFormData {
  username: string
  email: string
  full_name: string
  phone: string
  department: string
  position: string
}

interface PasswordFormData {
  newPassword: string
  confirmPassword: string
}

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [visible: boolean]
}>()

const userStore = useUserStore()
const { user } = storeToRefs(userStore)
const message = useMessage()

// 响应式数据
const editMode = ref(false)
const passwordEditMode = ref(false)
const saving = ref(false)
const profileForm = ref()
const passwordFormRef = ref()
const showAvatarEditor = ref(false)

// 表单引用
const profileFormRef = ref()
const passwordFormRefInstance = ref()

// 用户资料数据
const userProfile = ref({
  avatar_url: '',
  username: '',
  email: '',
  full_name: '',
  phone: '',
  department: '',
  position: ''
})

// 表单数据
const formData = ref<ProfileFormData>({
  username: '',
  email: '',
  full_name: '',
  phone: '',
  department: '',
  position: ''
})

// 密码表单数据
const passwordForm = ref<PasswordFormData>({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ]
}

// 密码验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: unknown, value: string, callback: (error?: Error) => void) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const isVisible = computed(() => props.visible)

// 监听visible变化
watch(isVisible, (newVal) => {
  if (newVal) {
    loadUserProfile()
  } else {
    resetForms()
  }
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const resetForms = () => {
  editMode.value = false
  passwordEditMode.value = false
  saving.value = false
  
  // 重置密码表单
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  }
}

const loadUserProfile = async () => {
  if (!user.value) return
  
  try {
    console.log('加载用户资料, userId:', user.value.id)
    const response = await profileAPI.getProfile(user.value.id)
    console.log('用户资料响应:', response)
    
    // 处理多种可能的响应格式
    let profileData = null
    if (response && typeof response === 'object') {
      if (response.success && response.data) {
        // 标准成功响应格式
        profileData = response.data.profile || response.data.user || response.data
      } else if (response.data) {
        // 有data字段但没有success字段
        profileData = response.data.profile || response.data.user || response.data
      } else if (response.user) {
        // 直接user字段
        profileData = response.user
      } else if (response.id || response.username || response.email) {
        // 直接用户数据
        profileData = response
      }
    }
    
    if (profileData) {
      userProfile.value = {
        avatar_url: profileData.avatar_url || '',
        username: profileData.username || '',
        email: profileData.email || '',
        full_name: profileData.full_name || '',
        phone: profileData.phone || '',
        department: profileData.department || '',
        position: profileData.position || ''
      }
      formData.value = { ...userProfile.value }
      console.log('用户资料加载成功:', userProfile.value)
    } else {
      console.warn('用户资料响应格式不正确，使用fallback数据')
      throw new Error('响应格式不正确')
    }
  } catch (error) {
    console.error('加载用户资料失败:', error)
    // 使用store中的用户数据作为fallback
    const userData = user.value
    userProfile.value = {
      avatar_url: userData.avatar_url || '',
      username: userData.username || '',
      email: userData.email || '',
      full_name: userData.full_name || '',
      phone: userData.phone || '',
      department: userData.department || '',
      position: userData.position || ''
    }
    formData.value = { ...userProfile.value }
    console.log('使用fallback用户数据:', userProfile.value)
  }
}


const enterEditMode = () => {
  editMode.value = true
  formData.value = { ...userProfile.value }
}

const enterPasswordEditMode = () => {
  passwordEditMode.value = true
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  }
}

const cancelEdit = () => {
  editMode.value = false
  formData.value = { ...userProfile.value }
}

const cancelPasswordEdit = () => {
  passwordEditMode.value = false
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  }
}

const saveProfile = async () => {
  if (!profileForm.value || !user.value) return
  
  try {
    await profileForm.value.validate()
    saving.value = true
    
    const response = await profileAPI.updateProfile(user.value.id, {
      email: formData.value.email,
      full_name: formData.value.full_name,
      phone: formData.value.phone,
      department: formData.value.department,
      position: formData.value.position
    })
    
    if (response.success) {
      // 更新用户资料
      userProfile.value = { ...userProfile.value, ...formData.value }
      
      // 更新store中的用户信息
      await userStore.getCurrentUser()
      
      message.success('个人资料保存成功')
      editMode.value = false
    } else {
      message.error('保存用户资料失败')
    }
    
  } catch (error) {
    console.error('保存用户资料失败:', error)
    message.error('保存用户资料失败')
  } finally {
    saving.value = false
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value || !user.value) return
  
  try {
    await passwordFormRef.value.validate()
    saving.value = true
    
    const response = await profileAPI.changePassword(user.value.id, {
      new_password: passwordForm.value.newPassword
    })
    
    if (response.success) {
      message.success('密码修改成功')
      passwordEditMode.value = false
      passwordForm.value = {
        newPassword: '',
        confirmPassword: ''
      }
    } else {
      message.error('密码修改失败')
    }
    
  } catch (error) {
    console.error('修改密码失败:', error)
    message.error('修改密码失败')
  } finally {
    saving.value = false
  }
}

const handleAvatarUpload = () => {
  if (!user.value) return
  
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      // 检查文件大小（限制为2MB）
      if (file.size > 2 * 1024 * 1024) {
        message.error('图片大小不能超过2MB')
        return
      }
      
      try {
        saving.value = true
        const response = await profileAPI.uploadAvatar(user.value!.id, file)
        
        if (response.success) {
          // 更新头像URL
          userProfile.value.avatar_url = response.data.avatar_url
          
          // 更新store中的用户信息
          await userStore.getCurrentUser()
          
          message.success('头像上传成功')
        } else {
          message.error('头像上传失败')
        }
      } catch (error) {
        console.error('上传头像失败:', error)
        message.error('上传头像失败')
      } finally {
        saving.value = false
      }
    }
  }
  input.click()
}

const openAvatarEditor = () => {
  showAvatarEditor.value = true
}

const handleAvatarUploaded = async (avatarUrl: string) => {
  console.log('ProfileSidebar: 收到头像上传成功回调, avatarUrl:', avatarUrl)
  
  try {
    // 1. 更新本地userProfile
  userProfile.value.avatar_url = avatarUrl
    
    // 2. 手动更新用户store中的用户信息
    if (user.value) {
      const updatedUser = { ...user.value, avatar_url: avatarUrl }
      await userStore.setUser(updatedUser)
      console.log('已更新用户store中的头像URL')
    }
    
    // 3. 可选：从服务器重新获取用户信息（确保同步）
  await userStore.getCurrentUser()
    
  message.success('头像更新成功')
    console.log('头像更新完成，当前用户信息:', userStore.user)
  } catch (error) {
    console.error('更新头像信息失败:', error)
    message.error('头像信息更新失败')
  }
}

// 初始化
onMounted(() => {
  if (props.visible) {
    loadUserProfile()
  }
})
</script>

<style scoped>
.profile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-sidebar.is-visible {
  opacity: 1;
  pointer-events: auto;
}

.sidebar-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
}

.sidebar-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 480px;
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.profile-sidebar.is-visible .sidebar-content {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #409eff;
}

.header-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.close-btn {
  font-size: 18px;
  color: #909399;
  padding: 8px;
}

.sidebar-body {
  flex: 1;
  overflow: hidden;
}

.profile-content {
  padding: 20px;
}

.avatar-section {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 8px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.avatar-wrapper {
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.user-avatar {
  border: 3px solid #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  transition: transform 0.2s;
}

.avatar-wrapper:hover .user-avatar {
  transform: scale(1.05);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  border-radius: 50%;
}

.edit-icon {
  font-size: 24px;
  color: white;
  margin-bottom: 4px;
}

.edit-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.form-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  height: 2px;
  background: #409eff;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  display: inline-block;
  position: relative;
  z-index: 1;
  background: white;
}

.section-actions {
  display: inline-flex;
  gap: 8px;
  margin-left: 10px;
  vertical-align: top;
  flex-shrink: 0;
}

.profile-form,
.password-form {
  margin-top: 16px;
}

.readonly-input :deep(.n-input-wrapper) {
  background-color: #f5f7fa;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-content {
    width: 100%;
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .section-actions {
    margin-left: 0;
  }
  
  .section-title {
    margin-bottom: 8px;
  }
}
</style>