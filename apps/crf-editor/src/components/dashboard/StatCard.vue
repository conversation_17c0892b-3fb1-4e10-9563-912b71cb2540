<template>
  <div class="stat-card" :class="{ clickable: $attrs.onClick }" @click="handleClick">
    <div class="stat-card-header">
      <div class="stat-icon" :style="{ backgroundColor: `${color}20` }">
        <n-icon :size="20" :color="color">
          <component :is="iconComponent" />
        </n-icon>
      </div>
      <div class="stat-trend" v-if="trend">
        <n-icon
          :size="14"
          :color="trend > 0 ? '#10b981' : '#ef4444'"
        >
          <component :is="trendIcon" />
        </n-icon>
        <span
          class="trend-value"
          :style="{ color: trend > 0 ? '#10b981' : '#ef4444' }"
        >
          {{ formatTrend(trend) }}
        </span>
      </div>
    </div>

    <div class="stat-content">
      <div class="stat-value">{{ value }}</div>
      <div class="stat-title">{{ title }}</div>
    </div>

    <div v-if="$slots.extra" class="stat-extra">
      <slot name="extra" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NIcon } from 'naive-ui'
import {
  Document,
  DataBase,
  People,
  Analytics,
  TrendingUp,
  TrendingDown,
} from '@vicons/ionicons5'

interface Props {
  title: string
  value: string | number
  color: string
  icon: string
  trend?: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: []
}>()

const iconComponent = computed(() => {
  const iconMap: Record<string, any> = {
    document: Document,
    database: DataBase,
    people: People,
    analytics: Analytics,
  }
  return iconMap[props.icon] || Document
})
const trendIcon = computed(() => props.trend && props.trend > 0 ? TrendingUp : TrendingDown)

const formatTrend = (trend: number) => {
  const absValue = Math.abs(trend)
  const sign = trend > 0 ? '+' : '-'
  return `${sign}${absValue}${typeof trend === 'number' && trend % 1 !== 0 ? '' : ''}`
}

const handleClick = () => {
  emit('click')
}
</script>

<style scoped>
.stat-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 20px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card.clickable {
  cursor: pointer;
}

.stat-card.clickable:hover {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-value {
  font-size: 12px;
  font-weight: 500;
}

.stat-content {
  margin-bottom: 12px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.stat-extra {
  padding-top: 12px;
  border-top: 1px solid #f1f5f9;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .stat-card {
    padding: 16px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
  }
}
</style>