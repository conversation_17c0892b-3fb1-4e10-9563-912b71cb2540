<template>
  <div 
    ref="containerRef"
    class="lazy-component-container"
    :class="{
      'is-visible': isVisible,
      'is-loading': isLoading,
      'has-error': hasError
    }"
    :style="containerStyle"
  >
    <!-- 加载中状态 -->
    <div 
      v-if="!isVisible && showPlaceholder"
      class="lazy-placeholder"
      :style="placeholderStyle"
    >
      <slot name="placeholder">
        <div class="default-placeholder">
          <n-skeleton 
            :repeat="skeletonRows"
            text
          />
        </div>
      </slot>
    </div>
    
    <!-- 实际内容 -->
    <div 
      v-show="isVisible"
      class="lazy-content"
      :class="{ 'fade-in': enableFadeIn && isVisible }"
    >
      <suspense>
        <template #default>
          <component 
            v-if="isVisible && componentName"
            :is="loadedComponent"
            v-bind="componentProps"
            @error="handleComponentError"
          />
          <slot v-else-if="isVisible" />
        </template>
        
        <template #fallback>
          <div class="lazy-loading">
            <slot name="loading">
              <n-spin size="small">
                <template #description>
                  组件加载中...
                </template>
              </n-spin>
            </slot>
          </div>
        </template>
      </suspense>
    </div>
    
    <!-- 错误状态 -->
    <div 
      v-if="hasError"
      class="lazy-error"
    >
      <slot name="error" :error="error" :retry="retryLoad">
        <div class="default-error">
          <n-alert
            title="组件加载失败"
            type="error"
            show-icon
          >
            <template #default>
              {{ error?.message || '未知错误' }}
            </template>
            <template #action>
              <n-button size="small" @click="retryLoad">
                重试
              </n-button>
            </template>
          </n-alert>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ref, 
  computed, 
  watch, 
  onMounted, 
  onUnmounted,
  type Component
} from 'vue'
import { useIntersectionObserver } from '@vueuse/core'
import { useTimerManager } from '@/composables/useTimerManager'
import { useErrorHandler } from '@/utils/error-handler'

// Props
interface Props {
  // 懒加载配置
  rootMargin?: string
  threshold?: number | number[]
  triggerOnce?: boolean
  disabled?: boolean
  
  // 异步组件相关
  componentName?: string
  componentLoader?: () => Promise<Component>
  componentProps?: Record<string, unknown>
  
  // 占位符配置
  showPlaceholder?: boolean
  placeholderHeight?: string | number
  skeletonRows?: number
  
  // 动画配置
  enableFadeIn?: boolean
  fadeInDuration?: number
  
  // 错误处理
  maxRetries?: number
  retryDelay?: number
  
  // 预加载
  preloadDistance?: number
  enablePreload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  rootMargin: '50px',
  threshold: 0.1,
  triggerOnce: true,
  disabled: false,
  showPlaceholder: true,
  placeholderHeight: 'auto',
  skeletonRows: 3,
  enableFadeIn: true,
  fadeInDuration: 300,
  maxRetries: 3,
  retryDelay: 1000,
  preloadDistance: 200,
  enablePreload: false
})

// Emits
const emit = defineEmits<{
  'visible': [{ isVisible: boolean; entry: IntersectionObserverEntry }]
  'loaded': [{ component: Component }]
  'error': [{ error: Error; retryCount: number }]
}>()

// Composables
const { safeSetTimeout } = useTimerManager()
const { handleError } = useErrorHandler()

// Refs
const containerRef = ref<HTMLElement>()
const isVisible = ref(false)
const isLoading = ref(false)
const hasError = ref(false)
const error = ref<Error | null>(null)
const retryCount = ref(0)
const loadedComponent = ref<Component | null>(null)

// 容器样式
const containerStyle = computed(() => {
  const style: Record<string, unknown> = {}
  
  if (props.placeholderHeight !== 'auto') {
    style.minHeight = typeof props.placeholderHeight === 'number' 
      ? props.placeholderHeight + 'px' 
      : props.placeholderHeight
  }
  
  return style
})

// 占位符样式
const placeholderStyle = computed(() => {
  const style: Record<string, unknown> = {}
  
  if (props.placeholderHeight !== 'auto') {
    style.height = typeof props.placeholderHeight === 'number' 
      ? props.placeholderHeight + 'px' 
      : props.placeholderHeight
  }
  
  return style
})

// Intersection Observer 配置
const { stop } = useIntersectionObserver(
  containerRef,
  ([entry], _observerElement) => {
    if (props.disabled || !entry) return
    
    const { isIntersecting } = entry
    
    if (isIntersecting && !isVisible.value) {
      handleVisible(entry)
    }
    
    emit('visible', { isVisible: isIntersecting, entry })
    
    // 如果设置了 triggerOnce，在变为可见后停止观察
    if (isIntersecting && props.triggerOnce) {
      stop()
    }
  },
  {
    rootMargin: props.rootMargin,
    threshold: props.threshold
  }
)

// 处理组件变为可见
const handleVisible = async (_entry: IntersectionObserverEntry) => {
  isVisible.value = true
  
  // 如果有异步组件需要加载
  if (props.componentName && props.componentLoader) {
    await loadAsyncComponent()
  }
}

// 加载异步组件
const loadAsyncComponent = async () => {
  if (!props.componentLoader || loadedComponent.value) return
  
  isLoading.value = true
  hasError.value = false
  error.value = null
  
  try {
    const component = await props.componentLoader()
    loadedComponent.value = component
    
    emit('loaded', { component })
    
    console.log(`异步组件 ${props.componentName} 加载成功`)
    
  } catch (err) {
    const errorObj = err as Error
    error.value = errorObj
    hasError.value = true
    retryCount.value++
    
    handleError(errorObj)
    emit('error', { error: errorObj, retryCount: retryCount.value })
    
    console.error(`异步组件 ${props.componentName} 加载失败:`, errorObj)
    
  } finally {
    isLoading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  if (retryCount.value >= props.maxRetries) {
    console.warn(`组件 ${props.componentName} 已达到最大重试次数`)
    return
  }
  
  safeSetTimeout(() => {
    loadAsyncComponent()
  }, props.retryDelay)
}

// 预加载功能
const handlePreload = () => {
  if (!props.enablePreload || !containerRef.value) return
  
  const rect = containerRef.value.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  const distanceFromViewport = rect.top - viewportHeight
  
  if (distanceFromViewport <= props.preloadDistance && !isVisible.value) {
    console.log(`预加载组件 ${props.componentName}`)
    handleVisible({} as IntersectionObserverEntry)
  }
}

// 监听滚动事件进行预加载
let preloadScrollHandler: (() => void) | null = null

onMounted(() => {
  if (props.enablePreload) {
    preloadScrollHandler = () => handlePreload()
    window.addEventListener('scroll', preloadScrollHandler, { passive: true })
  }
})

onUnmounted(() => {
  if (preloadScrollHandler) {
    window.removeEventListener('scroll', preloadScrollHandler)
  }
})

// 处理组件错误
const handleComponentError = (err: Error) => {
  error.value = err
  hasError.value = true
  handleError(err)
}

// 手动触发加载
const forceLoad = () => {
  if (!isVisible.value) {
    handleVisible({} as IntersectionObserverEntry)
  }
}

// 重置状态
const reset = () => {
  isVisible.value = false
  isLoading.value = false
  hasError.value = false
  error.value = null
  retryCount.value = 0
  loadedComponent.value = null
}

// 监听 disabled 状态变化
watch(() => props.disabled, (disabled) => {
  if (disabled) {
    stop()
  }
})

// 暴露方法给父组件
defineExpose({
  forceLoad,
  reset,
  retryLoad,
  isVisible,
  isLoading,
  hasError,
  error
})
</script>

<style scoped lang="scss">
.lazy-component-container {
  position: relative;
  min-height: 1px; // 确保容器有高度被 Intersection Observer 检测到
  
  &.is-loading {
    .lazy-content {
      opacity: 0.7;
    }
  }
  
  &.has-error {
    .lazy-content {
      display: none;
    }
  }
}

.lazy-placeholder {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--n-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--n-border-color-lighter);
  
  .default-placeholder {
    padding: 16px;
    width: 100%;
  }
}

.lazy-content {
  &.fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
}

.lazy-loading {
  padding: 40px 20px;
  text-align: center;
  background: var(--n-bg-color);
  border-radius: 6px;
}

.lazy-error {
  padding: 16px;
  
  .default-error {
    .n-alert {
      border-radius: 6px;
    }
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 骨架屏样式优化
:deep(.n-skeleton__item) {
  background: linear-gradient(
    90deg,
    var(--n-skeleton-color) 25%,
    var(--n-skeleton-to-color) 37%,
    var(--n-skeleton-color) 63%
  );
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .lazy-placeholder {
    .default-placeholder {
      padding: 12px;
    }
  }
  
  .lazy-loading {
    padding: 20px 12px;
  }
  
  .lazy-error {
    padding: 12px;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .lazy-content.fade-in {
    animation: none;
  }
  
  :deep(.n-skeleton__item) {
    animation: none;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .lazy-placeholder {
    border-width: 2px;
    border-color: #000;
  }
}
</style>