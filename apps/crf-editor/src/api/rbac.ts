// 简化的 API 客户端专用于 RBAC 组件
export const api = {
  async get(url: string, config?: Record<string, unknown>) {
    const token = localStorage.getItem('auth_token')
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }
    
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }
    
    const response = await fetch(`/api${url}`, {
      method: 'GET',
      headers,
      ...config
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return await response.json()
  },
  
  async post(url: string, data?: Record<string, unknown>, config?: Record<string, unknown>) {
    const token = localStorage.getItem('auth_token')
    const headers: Record<string, string> = {}
    
    // 如果data是FormData，不设置Content-Type，让浏览器自动设置
    if (!(data instanceof FormData)) {
      headers['Content-Type'] = 'application/json'
    }
    
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }
    
    // 合并额外的headers，但确保FormData时不覆盖Content-Type
    if (config?.headers) {
      Object.assign(headers, config.headers)
      if (data instanceof FormData && headers['Content-Type']) {
        delete headers['Content-Type']
      }
    }
    
    const response = await fetch(`/api${url}`, {
      method: 'POST',
      headers,
      body: data instanceof FormData ? data : JSON.stringify(data),
      ...config
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return await response.json()
  },
  
  async put(url: string, data?: Record<string, unknown>, config?: Record<string, unknown>) {
    const token = localStorage.getItem('auth_token')
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }
    
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }
    
    const response = await fetch(`/api${url}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(data),
      ...config
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return await response.json()
  },
  
  async delete(url: string, config?: Record<string, unknown>) {
    const token = localStorage.getItem('auth_token')
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }
    
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }
    
    const response = await fetch(`/api${url}`, {
      method: 'DELETE',
      headers,
      ...config
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return await response.json()
  }
}

// 用户管理 API
export const userManagementAPI = {
  // 获取用户列表
  async getUsers(params?: { page?: number; limit?: number; search?: string; role?: string }) {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.search) queryParams.append('search', params.search)
    if (params?.role) queryParams.append('role', params.role)
    
    const url = `/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await api.get(url)
  },

  // 获取用户详情
  async getUserDetail(userId: string) {
    return await api.get(`/users/${userId}`)
  },

  // 创建用户
  async createUser(userData: {
    username: string
    email: string
    password: string
    full_name?: string
    role_ids?: string[]
    project_id?: string
    is_active?: boolean
  }) {
    return await api.post('/users', userData)
  },

  // 更新用户信息
  async updateUser(userId: string, userData: {
    username?: string
    email?: string
    full_name?: string
    is_active?: boolean
  }) {
    return await api.put(`/users/${userId}`, userData)
  },

  // 更新用户角色
  async updateUserRoles(userId: string, roleData: {
    role_ids: string[]
    project_id?: string
  }) {
    return await api.put(`/users/${userId}/roles`, roleData)
  },

  // 激活用户
  async activateUser(userId: string) {
    return await api.post(`/users/${userId}/activate`)
  },

  // 停用用户
  async deactivateUser(userId: string) {
    return await api.post(`/users/${userId}/deactivate`)
  },

  // 重置密码
  async resetPassword(userId: string, newPassword: string) {
    return await api.post(`/users/${userId}/reset-password`, { new_password: newPassword })
  },

  // 批量更新用户
  async batchUpdateUsers(userIds: string[], action: string, roleIds?: string[]) {
    return await api.post('/users/batch', {
      user_ids: userIds,
      action,
      role_ids: roleIds
    })
  },

  // 获取用户统计
  async getUserStats() {
    return await api.get('/users/stats')
  },

  // 获取用户权限
  async getUserPermissions(userId: string, projectId?: string) {
    const url = projectId ? `/users/${userId}/permissions?project_id=${projectId}` : `/users/${userId}/permissions`
    return await api.get(url)
  },

  // 导出用户
  async exportUsers(format: 'csv' | 'xlsx' = 'csv', includeRoles: boolean = true) {
    const url = `/users/export?format=${format}&include_roles=${includeRoles}`
    return await api.get(url)
  }
}

// 角色管理 API
export const roleManagementAPI = {
  // 获取角色列表
  async getRoles(params?: { page?: number; limit?: number; search?: string; type?: string }) {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.search) queryParams.append('search', params.search)
    if (params?.type) queryParams.append('type', params.type)
    
    const url = `/roles${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await api.get(url)
  },

  // 获取角色详情
  async getRoleDetail(roleId: string) {
    return await api.get(`/roles/${roleId}`)
  },

  // 创建角色
  async createRole(roleData: {
    code: string
    name: string
    description?: string
    permissions: string[]
  }) {
    return await api.post('/roles', roleData)
  },

  // 更新角色
  async updateRole(roleId: string, roleData: {
    name?: string
    description?: string
    permissions?: string[]
  }) {
    return await api.put(`/roles/${roleId}`, roleData)
  },

  // 删除角色
  async deleteRole(roleId: string) {
    return await api.delete(`/roles/${roleId}`)
  },

  // 获取角色权限
  async getRolePermissions(roleId: string) {
    return await api.get(`/roles/${roleId}/permissions`)
  },

  // 更新角色权限
  async updateRolePermissions(roleId: string, permissionIds: string[]) {
    return await api.put(`/roles/${roleId}/permissions`, { permission_ids: permissionIds })
  },

  // 激活角色
  async activateRole(roleId: string) {
    return await api.put(`/roles/${roleId}`, { is_active: true })
  },

  // 停用角色
  async deactivateRole(roleId: string) {
    return await api.put(`/roles/${roleId}`, { is_active: false })
  }
}

// 权限管理 API
export const permissionAPI = {
  // 获取权限列表
  async getPermissions() {
    return await api.get('/permissions')
  },

  // 检查权限
  async checkPermission(resource: string, action: string, scope?: string) {
    const params = new URLSearchParams()
    params.append('resource', resource)
    params.append('action', action)
    if (scope) params.append('scope', scope)
    
    return await api.get(`/check-permission?${params.toString()}`)
  }
}

// 用户资料管理 API
export const profileAPI = {
  // 获取用户资料
  async getProfile(userId: string) {
    return await api.get(`/users/${userId}/profile`)
  },

  // 更新用户资料
  async updateProfile(userId: string, profileData: {
    email?: string
    full_name?: string
    phone?: string
    department?: string
    position?: string
    avatar_url?: string
  }) {
    return await api.put(`/users/${userId}/profile`, profileData)
  },

  // 修改密码
  async changePassword(userId: string, passwordData: {
    new_password: string
  }) {
    return await api.put(`/users/${userId}/password`, passwordData)
  },

  // 上传头像
  async uploadAvatar(userId: string, file: File) {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return await api.post(`/users/${userId}/avatar`, formData)
  },

  // 获取用户统计信息
  async getUserStats(userId: string) {
    return await api.get(`/users/${userId}/stats`)
  }
}