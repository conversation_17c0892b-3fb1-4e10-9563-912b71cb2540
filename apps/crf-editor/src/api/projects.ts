/**
 * 项目管理API
 * 继承BaseAPI，提供统一的项目管理接口
 */

import { BaseAPI } from '@crf/network'
import type { APIResponse } from '@crf/network'
import type { 
  Project, 
  CreateProjectRequest, 
  UpdateProjectRequest, 
  ProjectListParams, 
  ProjectListResponse,
  ProjectMember,
  AddMemberRequest
} from '@crf/types'
import { getApiClient } from './clients'



/**
 * 项目API类
 * 继承BaseAPI，提供项目管理相关的所有接口
 */
export class ProjectAPI extends BaseAPI<Project> {
  constructor() {
    super(getApiClient('project'), 'projects')
  }

  /**
   * 获取项目列表（重写基类方法以支持特定参数）
   */
  async getProjects(params: ProjectListParams = {}): Promise<APIResponse<ProjectListResponse>> {
    return this.getList(params)
  }

  /**
   * 获取项目详情
   */
  async getProject(id: string): Promise<APIResponse<Project>> {
    return this.getById(id)
  }

  /**
   * 创建项目
   */
  async createProject(data: CreateProjectRequest): Promise<APIResponse<Project>> {
    return this.create(data)
  }

  /**
   * 更新项目
   */
  async updateProject(id: string, data: UpdateProjectRequest): Promise<APIResponse<Project>> {
    return this.update(id, data)
  }

  /**
   * 删除项目（软删除）
   */
  async deleteProject(id: string): Promise<APIResponse<void>> {
    return this.delete(id)
  }

  /**
   * 恢复已删除的项目
   */
  async restoreProject(id: string): Promise<APIResponse<Project>> {
    return this.restore(id)
  }

  /**
   * 获取项目成员列表
   */
  async getProjectMembers(projectId: string): Promise<APIResponse<ProjectMember[]>> {
    const client = this.getClient()
    return client.get<ProjectMember[]>(`/${this.endpoint}/${projectId}/members`)
  }

  /**
   * 添加项目成员
   */
  async addProjectMember(
    projectId: string, 
    data: AddMemberRequest
  ): Promise<APIResponse<ProjectMember>> {
    const client = this.getClient()
    return client.post<ProjectMember>(`/${this.endpoint}/${projectId}/members`, data)
  }

  /**
   * 更新成员角色
   */
  async updateMemberRole(
    projectId: string,
    memberId: string,
    role: 'editor' | 'viewer'
  ): Promise<APIResponse<ProjectMember>> {
    const client = this.getClient()
    return client.put<ProjectMember>(`/${this.endpoint}/${projectId}/members/${memberId}`, { role })
  }

  /**
   * 移除项目成员
   */
  async removeMember(projectId: string, memberId: string): Promise<APIResponse<void>> {
    const client = this.getClient()
    return client.delete<void>(`/${this.endpoint}/${projectId}/members/${memberId}`)
  }

  /**
   * 获取用户的项目列表
   */
  async getUserProjects(userId?: string): Promise<APIResponse<ProjectListResponse>> {
    const client = this.getClient()
    const endpoint = userId ? `/users/${userId}/projects` : '/user/projects'
    return client.get<ProjectListResponse>(endpoint)
  }

  /**
   * 获取项目统计信息
   */
  async getProjectStats(projectId: string): Promise<APIResponse<{
    template_count: number
    instance_count: number
    member_count: number
    active_templates: number
    recent_activity: Array<{
      type: string
      description: string
      created_at: string
    }>
  }>> {
    return this.getStats(projectId)
  }

  /**
   * 导出项目数据
   */
  async exportProject(
    projectId: string,
    format: 'json' | 'csv' | 'excel' = 'json'
  ): Promise<APIResponse<{ download_url: string }>> {
    return this.export(projectId, { format })
  }
}

// 导出项目API实例，便于使用
export const projectAPI = new ProjectAPI()