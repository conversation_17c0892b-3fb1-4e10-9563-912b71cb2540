/**
 * 认证相关API
 * 使用BaseAPI基础类，提供统一的认证接口
 */

import { BaseAPI } from '@crf/network'
import type { APIResponse } from '@crf/network'
import type { User, LoginRequest, LoginResponse, RegisterRequest, RefreshTokenResponse } from '@crf/types'
import { getApiClient } from './clients'



/**
 * 认证API类
 * 继承BaseAPI，提供用户认证相关的所有接口
 */
export class AuthAPI extends BaseAPI<User> {
  constructor() {
    super(getApiClient('auth'), 'auth')
  }

  /**
   * 用户登录
   */
  async login(data: LoginRequest): Promise<APIResponse<LoginResponse>> {
    const client = getApiClient('public')
    return client.post<LoginResponse>('/auth/login', data)
  }

  /**
   * 用户注册
   */
  async register(data: RegisterRequest): Promise<APIResponse<LoginResponse>> {
    const client = getApiClient('public')
    return client.post<LoginResponse>('/auth/register', data)
  }

  /**
   * 用户登出
   */
  async logout(): Promise<APIResponse<void>> {
    const client = getApiClient('auth')
    return client.post<void>('/auth/logout')
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string): Promise<APIResponse<RefreshTokenResponse>> {
    const client = getApiClient('public')
    return client.post<RefreshTokenResponse>('/auth/refresh', {
      refresh_token: refreshToken
    })
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<APIResponse<User>> {
    const client = getApiClient('auth')
    return client.get<User>('/auth/me')
  }

  /**
   * 验证令牌有效性
   */
  async validateToken(): Promise<APIResponse<{ valid: boolean }>> {
    const client = getApiClient('auth')
    return client.get<{ valid: boolean }>('/auth/validate')
  }

  /**
   * 忘记密码
   */
  async forgotPassword(email: string): Promise<APIResponse<{ message: string }>> {
    const client = getApiClient('public')
    return client.post<{ message: string }>('/auth/forgot-password', { email })
  }

  /**
   * 重置密码
   */
  async resetPassword(
    token: string, 
    newPassword: string
  ): Promise<APIResponse<{ message: string }>> {
    const client = getApiClient('public')
    return client.post<{ message: string }>('/auth/reset-password', {
      token,
      password: newPassword
    })
  }

  /**
   * 修改密码
   */
  async changePassword(
    oldPassword: string,
    newPassword: string
  ): Promise<APIResponse<{ message: string }>> {
    const client = getApiClient('auth')
    return client.post<{ message: string }>('/auth/change-password', {
      old_password: oldPassword,
      new_password: newPassword
    })
  }
}

// 导出认证API实例，便于使用
export const authAPI = new AuthAPI()
