/**
 * 新的统一API系统
 * 基于现代化的HttpClient架构，提供类型安全的API调用
 */

// 核心客户端系统
export { 
  initNetworkClients, 
  destroyNetworkClients,
  httpClient,
  anonymousClient,
  uploadClient,
  minioClient,
  getApiClient
} from './clients'



// 业务API类（推荐使用）
export { AuthAPI } from './auth'
export { ProjectAPI } from './projects'
export { TemplateAPI } from './templates'

// API实例（推荐使用）
export { authAPI } from './auth'
export { projectAPI } from './projects'
export { templateAPI } from './templates'

// 类型定义
export type { 
  APIResponse
} from '@crf/network'

// 实例和历史API（保持现有接口）
export const instanceAPI = {
  // 获取实例列表
  getInstances: async (params?: {
    template_id?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }) => {
    const { httpClient } = await import('./clients')
    return httpClient.get('/instances', { params })
  },
  
  // 创建实例
  createInstance: async (instanceData: {
    template_id: string;
  }) => {
    const { httpClient } = await import('./clients')
    return httpClient.post('/instances', instanceData)
  },
  
  // 获取实例详情
  getInstance: async (id: string) => {
    const { httpClient } = await import('./clients')
    return httpClient.get(`/instances/${id}`)
  },
  
  // 更新实例数据
  updateInstance: async (id: string, data: {
    form_data: Record<string, unknown>;
  }) => {
    const { httpClient } = await import('./clients')
    return httpClient.put(`/instances/${id}`, data)
  },
  
  // 提交实例
  submitInstance: async (id: string) => {
    const { httpClient } = await import('./clients')
    return httpClient.post(`/instances/${id}/submit`)
  },
  
  // 删除实例
  deleteInstance: async (id: string) => {
    const { httpClient } = await import('./clients')
    return httpClient.delete(`/instances/${id}`)
  }
}

// 系统API
export const systemAPI = {
  // 健康检查
  healthCheck: async () => {
    const { anonymousClient } = await import('./clients')
    return anonymousClient.get('/health')
  }
}

// 用户API
export const userAPI = {
  // 获取用户列表
  getUsers: async (params?: { limit?: number; offset?: number; search?: string }) => {
    const { httpClient } = await import('./clients')
    return httpClient.get('/users', { params })
  },
  
  // 更新个人资料
  updateProfile: async (userData: {
    full_name?: string;
    email?: string;
    avatar?: string;
  }) => {
    const { httpClient } = await import('./clients')
    return httpClient.put('/user/profile', userData)
  },

  // 修改密码
  changePassword: async (passwordData: {
    current_password: string;
    new_password: string;
  }) => {
    const { httpClient } = await import('./clients')
    return httpClient.put('/user/password', passwordData)
  }
}

// 历史API
export const historyAPI = {
  // 获取历史记录列表
  getHistoryEntries: async (params?: {
    resource_type?: string;
    resource_id?: string;
    action?: string;
    limit?: number;
    offset?: number;
  }) => {
    const { httpClient } = await import('./clients')
    return httpClient.get('/history', { params })
  },

  // 获取指定资源的历史记录
  getHistory: async (resourceType: string, resourceId: string) => {
    const { httpClient } = await import('./clients')
    return httpClient.get(`/history/${resourceType}/${resourceId}`)
  }
}