/**
 * 模板管理API
 * 继承BaseAPI，提供统一的模板管理接口
 */

import { BaseAPI } from '@crf/network'
import type { APIResponse } from '@crf/network'
import type {
  CRFTemplate,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  TemplateListParams,
  TemplateListResponse,
  TemplateVersion,
  TemplateStats
} from '@crf/types'
import { getApiClient } from './clients'



/**
 * 模板管理API类
 */
export class TemplateAPI extends BaseAPI<CRFTemplate> {
  constructor() {
    super(getApiClient('template'), 'templates')
  }

  /**
   * 获取模板列表
   */
  async getTemplates(params?: TemplateListParams): Promise<TemplateListResponse> {
    return this.getList(params)
  }

  /**
   * 获取模板详情
   */
  async getTemplate(id: string): Promise<CRFTemplate> {
    return this.getById(id)
  }

  /**
   * 创建模板
   */
  async createTemplate(data: CreateTemplateRequest): Promise<CRFTemplate> {
    return this.create(data)
  }

  /**
   * 更新模板
   */
  async updateTemplate(id: string, data: UpdateTemplateRequest): Promise<CRFTemplate> {
    return this.update(id, data)
  }

  /**
   * 删除模板（软删除）
   */
  async deleteTemplate(id: string): Promise<void> {
    return this.delete(id)
  }

  /**
   * 恢复已删除的模板
   */
  async restoreTemplate(id: string): Promise<CRFTemplate> {
    return this.restore(id)
  }

  /**
   * 发布模板
   */
  async publishTemplate(id: string): Promise<CRFTemplate> {
    const client = this.getClient()
    const response = await client.post<CRFTemplate>(`/${this.endpoint}/${id}/publish`)
    return response.data
  }

  /**
   * 取消发布模板
   */
  async unpublishTemplate(id: string): Promise<CRFTemplate> {
    const client = this.getClient()
    const response = await client.post<CRFTemplate>(`/${this.endpoint}/${id}/unpublish`)
    return response.data
  }

  /**
   * 复制模板
   */
  async duplicateTemplate(id: string, name?: string): Promise<CRFTemplate> {
    const client = this.getClient()
    const response = await client.post<CRFTemplate>(`/${this.endpoint}/${id}/duplicate`, { name })
    return response.data
  }

  /**
   * 获取模板版本历史
   */
  async getTemplateVersions(id: string): Promise<TemplateVersion[]> {
    const client = this.getClient()
    const response = await client.get<TemplateVersion[]>(`/${this.endpoint}/${id}/versions`)
    return response.data
  }

  /**
   * 获取模板统计信息
   */
  async getTemplateStats(id: string): Promise<TemplateStats> {
    return this.getStats(id)
  }

  /**
   * 导出模板
   */
  async exportTemplate(id: string, format: 'json' | 'yaml' = 'json'): Promise<Blob> {
    return this.export(id, { format })
  }

  /**
   * 导入模板
   */
  async importTemplate(file: File, projectId?: string): Promise<CRFTemplate> {
    const client = this.getClient()
    const formData = new FormData()
    formData.append('file', file)
    if (projectId) {
      formData.append('project_id', projectId)
    }
    
    const response = await client.post<CRFTemplate>(`/${this.endpoint}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

    /**
   * 批量删除模板（软删除）
   */
  async batchDeleteTemplates(ids: string[]): Promise<void> {
    await this.batchDelete(ids)
  }
}

// 导出TemplateAPI实例
export const templateAPI = new TemplateAPI()