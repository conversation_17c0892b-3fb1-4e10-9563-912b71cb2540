/**
 * 应用层HTTP客户端实例
 * 为CRF编辑器应用创建专用的HTTP客户端配置
 */

import { 
  NetworkClientFactory, 
  createDevClient, 
  createProdClient,
  createAnonymousClient,
  createUploadClient 
} from '@crf/network'
import type { HttpClient } from '@crf/network'

/**
 * 客户端类型定义
 */
type ClientType = 'auth' | 'user' | 'project' | 'template' | 'instance' | 'rbac' | 'public' | 'form' | 'upload' | 'avatar' | 'default'

/**
 * 客户端未找到错误
 */
class ClientNotFoundError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ClientNotFoundError'
  }
}

/**
 * 配置验证错误
 */
class ConfigValidationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ConfigValidationError'
  }
}

// 获取环境配置
const isDev = import.meta.env.DEV
const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
const uploadBaseURL = import.meta.env.VITE_UPLOAD_BASE_URL || baseURL
const minioBaseURL = import.meta.env.VITE_MINIO_BASE_URL || 'http://localhost:9000'

/**
 * 主要的HTTP客户端实例
 * 用于所有需要认证的API请求
 */
export const httpClient: HttpClient = isDev 
  ? createDevClient(baseURL, { apiPrefix: '' }) // 禁用apiPrefix，因为baseURL已包含/api
  : createProdClient(baseURL, { apiPrefix: '' })

/**
 * 匿名HTTP客户端
 * 用于不需要认证的API请求（如登录、注册、公开表单等）
 */
export const anonymousClient: HttpClient = createAnonymousClient(baseURL, { apiPrefix: '' })

/**
 * 文件上传客户端
 * 专门用于文件上传（头像、附件等）
 */
export const uploadClient: HttpClient = createUploadClient(uploadBaseURL, { apiPrefix: '' })

/**
 * MinIO客户端（头像上传专用）
 */
export const minioClient: HttpClient = NetworkClientFactory.createClient(minioBaseURL, {
  timeout: 60000, // 1分钟超时
  withCredentials: false, // MinIO不需要凭证
  defaultHeaders: {
    // 不设置Content-Type，让浏览器处理
  },
  auth: {
    enableAutoRefresh: false // MinIO不使用JWT认证
  },
  errorHandling: {
    enableLogging: isDev,
    enableUserFriendlyMessages: true
  }
})

/**
 * 业务API客户端映射
 * 根据不同的业务场景使用不同的客户端
 */
export const apiClients = {
  // 需要认证的API
  auth: httpClient,
  user: httpClient,
  template: httpClient,
  instance: httpClient,
  project: httpClient,
  rbac: httpClient,
  
  // 匿名API
  public: anonymousClient,
  form: anonymousClient, // 公开表单填写
  
  // 文件上传
  upload: uploadClient,
  avatar: minioClient,
  
  // 默认客户端
  default: httpClient
}

/**
 * 客户端选择配置
 */
interface ClientSelectionConfig {
  requiresAuth: boolean
  isFileOperation: boolean
  isPublicEndpoint: boolean
  customTimeout?: number
  baseURL?: string
  retryAttempts?: number
  enableLogging?: boolean
}

/**
 * 客户端配置验证
 */
function validateClientConfig(type: string, config: ClientSelectionConfig): void {
  if (!config) {
    throw new ConfigValidationError(`Configuration for client type '${type}' is missing`)
  }
  
  if (config.customTimeout && (config.customTimeout < 1000 || config.customTimeout > 300000)) {
    throw new ConfigValidationError(`Invalid timeout for client type '${type}': must be between 1000ms and 300000ms`)
  }
  
  if (config.retryAttempts && (config.retryAttempts < 0 || config.retryAttempts > 5)) {
    throw new ConfigValidationError(`Invalid retry attempts for client type '${type}': must be between 0 and 5`)
  }
}

/**
 * 业务类型到客户端配置的映射
 */
const clientConfigs: Record<ClientType, ClientSelectionConfig> = {
  auth: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 3,
    enableLogging: isDev
  },
  user: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 3,
    enableLogging: isDev
  },
  project: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 3,
    enableLogging: isDev
  },
  template: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 3,
    enableLogging: isDev
  },
  instance: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 3,
    enableLogging: isDev
  },
  rbac: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 2,
    enableLogging: isDev
  },
  
  public: { 
    requiresAuth: false, 
    isFileOperation: false, 
    isPublicEndpoint: true,
    retryAttempts: 2,
    enableLogging: isDev
  },
  form: { 
    requiresAuth: false, 
    isFileOperation: false, 
    isPublicEndpoint: true,
    retryAttempts: 2,
    enableLogging: isDev
  },
  
  upload: { 
    requiresAuth: true, 
    isFileOperation: true, 
    isPublicEndpoint: false, 
    customTimeout: 60000,
    retryAttempts: 1,
    enableLogging: isDev
  },
  avatar: { 
    requiresAuth: false, 
    isFileOperation: true, 
    isPublicEndpoint: false, 
    customTimeout: 30000,
    retryAttempts: 1,
    enableLogging: isDev
  },
  
  default: { 
    requiresAuth: true, 
    isFileOperation: false, 
    isPublicEndpoint: false,
    retryAttempts: 3,
    enableLogging: isDev
  }
}

/**
 * 获取指定业务的HTTP客户端（增强版）
 */
export function getApiClient(type: ClientType = 'default'): HttpClient {
  try {
    // 验证客户端类型
    if (!type || typeof type !== 'string') {
      throw new ClientNotFoundError(`Invalid client type: ${type}`)
    }
    
    const client = apiClients[type]
    if (!client) {
      const error = new ClientNotFoundError(`Client type '${type}' not found`)
      console.error(error.message)
      
      // 记录错误但返回默认客户端以保证应用继续运行
      if (isDev) {
        console.warn('Available client types:', Object.keys(apiClients))
      }
      return apiClients.default
    }
    
    // 验证客户端配置
    const config = clientConfigs[type]
    if (config) {
      validateClientConfig(type, config)
      
      // 在开发模式下记录客户端选择
      if (isDev && config.enableLogging) {
        console.debug(`选择客户端: ${type}`, {
          requiresAuth: config.requiresAuth,
          isFileOperation: config.isFileOperation,
          isPublicEndpoint: config.isPublicEndpoint,
          timeout: config.customTimeout,
          retryAttempts: config.retryAttempts
        })
      }
    }
    
    return client
  } catch (error) {
    console.error('Error getting API client:', error)
    return apiClients.default
  }
}

/**
 * 智能客户端选择器
 * 根据请求特征自动选择最合适的客户端
 */
export function selectApiClient(options: {
  requiresAuth?: boolean
  isFileUpload?: boolean
  isPublicEndpoint?: boolean
  endpoint?: string
}): HttpClient {
  try {
    // 验证输入参数
    if (!options || typeof options !== 'object') {
      throw new ConfigValidationError('Invalid selectApiClient options')
    }
    
    const { requiresAuth = true, isFileUpload = false, isPublicEndpoint = false, endpoint } = options
    
    // 根据端点路径智能判断
    if (endpoint && typeof endpoint === 'string') {
      if (endpoint.includes('/upload') || endpoint.includes('/file')) {
        if (isDev) {
          console.debug('Auto-selected upload client based on endpoint:', endpoint)
        }
        return getApiClient('upload')
      }
      if (endpoint.includes('/avatar') || endpoint.includes('/minio')) {
        if (isDev) {
          console.debug('Auto-selected avatar client based on endpoint:', endpoint)
        }
        return getApiClient('avatar')
      }
      if (endpoint.includes('/auth/login') || endpoint.includes('/auth/register')) {
        if (isDev) {
          console.debug('Auto-selected public client based on endpoint:', endpoint)
        }
        return getApiClient('public')
      }
    }
    
    // 根据特征选择
    if (isPublicEndpoint || !requiresAuth) {
      if (isDev) {
        console.debug('Selected public client:', { isPublicEndpoint, requiresAuth })
      }
      return getApiClient('public')
    }
    
    if (isFileUpload) {
      if (isDev) {
        console.debug('Selected upload client for file upload')
      }
      return getApiClient('upload')
    }
    
    if (isDev) {
      console.debug('Selected default client')
    }
    return getApiClient('default')
  } catch (error) {
    console.error('Error in selectApiClient:', error)
    return getApiClient('default')
  }
}

/**
 * 应用初始化时的网络配置
 */
export function initNetworkClients() {
  try {
    console.log('初始化网络客户端配置')
    console.log('- 主API地址:', baseURL)
    console.log('- 上传地址:', uploadBaseURL)
    console.log('- MinIO地址:', minioBaseURL)
    console.log('- 开发模式:', isDev)
    
    // 验证必要的环境变量
    if (!baseURL) {
      throw new ConfigValidationError('VITE_API_BASE_URL is required')
    }
    
    // 验证客户端配置
    Object.entries(clientConfigs).forEach(([type, config]) => {
      try {
        validateClientConfig(type, config)
      } catch (error) {
        console.warn(`Client config validation failed for ${type}:`, error)
      }
    })
    
    // 配置全局错误处理
    if (isDev) {
      console.log('- 开发模式已启用详细日志')
      console.log('- 可用客户端类型:', Object.keys(apiClients))
    }
    
    console.log('网络客户端初始化完成')
  } catch (error) {
    console.error('网络客户端初始化失败:', error)
    throw error
  }
}

/**
 * 应用销毁时清理网络客户端
 */
export function destroyNetworkClients() {
  try {
    console.log('开始清理网络客户端资源')
    
    // 清理所有客户端的未完成请求
    const clients = Object.values(apiClients)
    let cleanupPromises: Promise<void>[] = []
    
    clients.forEach((client, index) => {
      if (client && typeof client.destroy === 'function') {
        try {
          const promise = client.destroy()
          if (promise instanceof Promise) {
            cleanupPromises.push(promise)
          }
        } catch (error) {
          console.warn(`清理客户端 ${index} 时出错:`, error)
        }
      }
    })
    
    // 等待所有清理操作完成（但不阻塞应用关闭）
    if (cleanupPromises.length > 0) {
      Promise.allSettled(cleanupPromises)
        .then(() => {
          console.log('所有网络客户端资源清理完成')
        })
        .catch((error) => {
          console.warn('部分网络客户端资源清理失败:', error)
        })
    } else {
      console.log('网络客户端资源清理完成')
    }
  } catch (error) {
    console.error('清理网络客户端资源时出错:', error)
  }
}

/**
 * 获取客户端配置信息
 */
export function getClientConfig(type: ClientType): ClientSelectionConfig | undefined {
  return clientConfigs[type]
}

/**
 * 检查客户端是否可用
 */
export function isClientAvailable(type: ClientType): boolean {
  try {
    const client = apiClients[type]
    return client !== undefined && client !== null
  } catch {
    return false
  }
}

/**
 * 获取所有可用的客户端类型
 */
export function getAvailableClientTypes(): ClientType[] {
  return Object.keys(apiClients) as ClientType[]
}

/**
 * 动态更新客户端配置（仅在开发模式下可用）
 */
export function updateClientConfig(type: ClientType, config: Partial<ClientSelectionConfig>): boolean {
  if (!isDev) {
    console.warn('updateClientConfig is only available in development mode')
    return false
  }
  
  try {
    const currentConfig = clientConfigs[type]
    if (!currentConfig) {
      console.error(`Client type '${type}' not found`)
      return false
    }
    
    const newConfig = { ...currentConfig, ...config }
    validateClientConfig(type, newConfig)
    
    clientConfigs[type] = newConfig
    console.log(`Updated config for client '${type}':`, newConfig)
    return true
  } catch (error) {
    console.error(`Failed to update config for client '${type}':`, error)
    return false
  }
}