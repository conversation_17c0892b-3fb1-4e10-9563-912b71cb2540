// API URL 调试工具
import { httpClient, anonymousClient } from '@/api'

export const debugApiUrls = () => {
  console.log('=== API URL 调试信息 ===')
  console.log('环境:', import.meta.env.DEV ? '开发环境' : '生产环境')
  
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
  console.log('API基础URL:', baseURL)
  
  console.log('\n=== 客户端实例状态 ===')
  console.log('httpClient配置完成:', !!httpClient)
  console.log('anonymousClient配置完成:', !!anonymousClient)
  
  console.log('\n=== URL 构建测试 ===')
  const testEndpoints = [
    '/auth/login',
    '/auth/register', 
    '/users',
    '/templates'
  ]
  
  testEndpoints.forEach(endpoint => {
    const fullUrl = `${baseURL}${endpoint}`
    console.log(`${endpoint} -> ${fullUrl}`)
  })
  
  console.log('\n=== 健康检查URL ===')
  const healthUrl = `${baseURL}/health`
  console.log(`健康检查: ${healthUrl}`)
  
  console.log('=== 调试完成 ===')
}

// 在开发环境下自动调试
if (import.meta.env.DEV) {
  debugApiUrls()
}