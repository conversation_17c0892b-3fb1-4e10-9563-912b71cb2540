// 表单相关工具函数

import type { FormStatus, FormTemplate } from '@/types/form'

/**
 * 格式化日期为相对时间
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string): string {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

/**
 * 格式化详细时间（包含年月日和时分秒）
 * @param dateString 日期字符串
 * @returns 格式化后的详细时间字符串
 */
export function formatDetailTime(dateString: string): string {
  if (!dateString) return ''
  const date = new Date(dateString)
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 格式化相对时间
 * @param date 日期对象
 * @returns 相对时间字符串
 */
export function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  
  if (seconds < 30) return '刚刚'
  if (seconds < 60) return `${seconds}秒前`
  if (minutes < 60) return `${minutes}分钟前`
  
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 调整颜色亮度
 * @param color 颜色值（十六进制）
 * @param amount 调整量（正数变亮，负数变暗）
 * @returns 调整后的颜色值
 */
export function adjustColorBrightness(color: string, amount: number): string {
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.slice(1)
    const num = parseInt(hex, 16)
    const r = Math.max(0, Math.min(255, (num >> 16) + amount))
    const g = Math.max(0, Math.min(255, ((num >> 8) & 0x00FF) + amount))
    const b = Math.max(0, Math.min(255, (num & 0x0000FF) + amount))
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`
  }
  // 如果是其他格式，返回原色
  return color
}

/**
 * 获取表单状态对应的标签类型
 * @param status 表单状态
 * @returns 标签类型
 */
export function getStatusType(status: FormStatus): 'warning' | 'primary' {
  switch (status) {
    case 'draft': return 'warning'
    case 'published': return 'primary'
    default: return 'warning'
  }
}

/**
 * 获取表单状态文本
 * @param status 表单状态
 * @returns 状态文本
 */
export function getStatusText(status: FormStatus): string {
  switch (status) {
    case 'draft': return '未发布'
    case 'published': return '已发布'
    default: return '未发布'
  }
}

/**
 * 生成唯一的表单名称
 * @param baseName 基础名称
 * @param existingNames 已存在的名称列表
 * @returns 唯一的名称
 */
export function generateUniqueName(baseName: string, existingNames: string[]): string {
  const timestamp = new Date().getTime()
  let uniqueName = `${baseName} - 副本 ${timestamp}`
  
  // 确保名称唯一
  let counter = 1
  while (existingNames.includes(uniqueName)) {
    uniqueName = `${baseName} - 副本 ${timestamp}-${counter}`
    counter++
  }
  
  return uniqueName
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 验证表单数据
 * @param form 表单数据
 * @returns 验证结果
 */
export function validateFormData(form: Partial<FormTemplate>): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (!form.name?.trim()) {
    errors.push('表单名称不能为空')
  }
  
  if (form.name && form.name.length > 50) {
    errors.push('表单名称长度不能超过50个字符')
  }
  
  if (form.description && form.description.length > 500) {
    errors.push('表单描述长度不能超过500个字符')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}