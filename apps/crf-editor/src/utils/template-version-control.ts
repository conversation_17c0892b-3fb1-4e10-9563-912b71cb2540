/**
 * CRF模板版本控制工具类
 * 提供模板版本管理、比较、回滚等功能
 */

import { useMessage, useDialog } from 'naive-ui'
import type { CRFVersion, FormSection as EditorFormSection } from '@/types/editor'
import { validateTemplateData, type CRFTemplateData, type FormSection as SchemaFormSection } from '@/schemas/template-schema'
import { templateAPI } from '@/api'

let message: ReturnType<typeof useMessage> | null = null
let dialog: ReturnType<typeof useDialog> | null = null

// 初始化消息和对话框实例
const initServices = () => {
  if (!message) {
    message = useMessage()
  }
  if (!dialog) {
    dialog = useDialog()
  }
}

export interface VersionComparison {
  added: string[]
  removed: string[]
  modified: string[]
  unchanged: string[]
}

export interface VersionDiff {
  sections: {
    added: EditorFormSection[]
    removed: EditorFormSection[]
    modified: Record<string, unknown>[]
  }
  components: {
    added: Record<string, unknown>[]
    removed: Record<string, unknown>[]
    modified: Record<string, unknown>[]
  }
  configuration: VersionComparison
}

/**
 * 将模板Schema中的FormSection转换为编辑器中的FormSection
 */
function convertSchemaFormSectionToEditorFormSection(section: SchemaFormSection): EditorFormSection {
  return {
    id: section.id,
    name: section.title,
    blocks: section.components.map(comp => ({
      id: comp.id,
      type: comp.type,
      title: comp.label,
      description: comp.placeholder,
      required: comp.required,
      readonly: comp.readonly,
      visible: !comp.hidden
    })),
    title: section.title,
    description: section.description
  }
}

// 创建版本
export const createVersion = async (versionData: Partial<CRFVersion>): Promise<CRFVersion> => {
  try {
    if (!versionData.template_id || !versionData.version) {
      throw new Error('模板ID和版本号是必需的')
    }

    const response = await templateAPI.createVersion({
      template_id: versionData.template_id,
      version: versionData.version,
      description: versionData.description,
      change_log: versionData.change_log
    })

    if (!response.data.version) {
      throw new Error('创建版本失败：服务器返回数据格式错误')
    }

    const status = response.data.version.status || 'draft'
    const validStatus = ['draft', 'published', 'archived'].includes(String(status)) 
      ? (String(status) as 'draft' | 'published' | 'archived') 
      : 'draft'
      
    return {
      id: String(response.data.version.id),
      template_id: versionData.template_id,
      title: String(response.data.version.title || versionData.title || ''),
      version: versionData.version,
      description: String(versionData.description || ''),
      change_log: String(versionData.change_log || ''),
      snapshot_data: response.data.version.snapshot_data as Record<string, unknown> || {} as Record<string, unknown>,
      status: validStatus,
      created_at: String(response.data.version.created_at || new Date().toISOString()),
      updated_at: String(response.data.version.updated_at || new Date().toISOString())
    }
  } catch (error) {
    console.error('创建版本失败:', error)
    throw error
  }
}

// 获取版本列表
export const getVersions = async (templateId: string): Promise<CRFVersion[]> => {
  try {
    const response = await templateAPI.getVersions(templateId)
    if (!response.data.versions) {
      return []
    }

    return response.data.versions.map(version => {
      const status = version.status || 'draft'
      const validStatus = ['draft', 'published', 'archived'].includes(String(status)) 
        ? (String(status) as 'draft' | 'published' | 'archived') 
        : 'draft'
        
      return {
        id: String(version.id),
        template_id: templateId,
        title: String(version.title || ''),
        version: String(version.version),
        description: String(version.description || ''),
        change_log: String(version.change_log || ''),
        snapshot_data: version.snapshot_data as Record<string, unknown> || {} as Record<string, unknown>,
        status: validStatus,
        created_at: String(version.created_at || new Date().toISOString()),
        updated_at: String(version.updated_at || new Date().toISOString())
      }
    })
  } catch (error) {
    console.error('获取版本列表失败:', error)
    return []
  }
}

/**
 * 比较两个版本的差异
 * @param version1 版本1数据
 * @param version2 版本2数据
 * @returns 版本差异
 */
export const compareVersions = (version1: CRFTemplateData, version2: CRFTemplateData): VersionDiff => {
  const diff: VersionDiff = {
    sections: { added: [], removed: [], modified: [] },
    components: { added: [], removed: [], modified: [] },
    configuration: { added: [], removed: [], modified: [], unchanged: [] }
  }

  // 比较表单结构
  const sections1 = version1.formStructure?.sections || []
  const sections2 = version2.formStructure?.sections || []

  // 找出新增、删除、修改的章节
  const sectionMap1 = new Map(sections1.map(s => [s.id, s]))
  const sectionMap2 = new Map(sections2.map(s => [s.id, s]))

  // 新增的章节
  sections2.forEach(section => {
    if (!sectionMap1.has(section.id)) {
      diff.sections.added.push(convertSchemaFormSectionToEditorFormSection(section))
    }
  })

  // 删除的章节
  sections1.forEach(section => {
    if (!sectionMap2.has(section.id)) {
      diff.sections.removed.push(convertSchemaFormSectionToEditorFormSection(section))
    }
  })

  // 修改的章节和组件
  sections1.forEach(section1 => {
    const section2 = sectionMap2.get(section1.id)
    if (section2) {
      // 比较章节属性
      if (JSON.stringify(section1) !== JSON.stringify(section2)) {
        diff.sections.modified.push({
          id: section1.id,
          before: section1,
          after: section2
        })
      }

      // 比较组件
      const components1 = section1.components || []
      const components2 = section2.components || []

      const componentMap1 = new Map(components1.map(c => [c.id, c]))
      const componentMap2 = new Map(components2.map(c => [c.id, c]))

      // 新增的组件
      components2.forEach(component => {
        if (!componentMap1.has(component.id)) {
          diff.components.added.push({
            ...component,
            sectionId: section1.id
          })
        }
      })

      // 删除的组件
      components1.forEach(component => {
        if (!componentMap2.has(component.id)) {
          diff.components.removed.push({
            ...component,
            sectionId: section1.id
          })
        }
      })

      // 修改的组件
      components1.forEach(component1 => {
        const component2 = componentMap2.get(component1.id)
        if (component2 && JSON.stringify(component1) !== JSON.stringify(component2)) {
          diff.components.modified.push({
            id: component1.id,
            sectionId: section1.id,
            before: component1,
            after: component2
          })
        }
      })
    }
  })

  // 比较配置
  const config1 = { ...version1.pageConfig, ...version1.styleConfig }
  const config2 = { ...version2.pageConfig, ...version2.styleConfig }

  Object.keys(config2).forEach(key => {
    if (!(key in config1)) {
      diff.configuration.added.push(key)
    } else if (config1[key] !== config2[key]) {
      diff.configuration.modified.push(key)
    } else {
      diff.configuration.unchanged.push(key)
    }
  })

  Object.keys(config1).forEach(key => {
    if (!(key in config2)) {
      diff.configuration.removed.push(key)
    }
  })

  return diff
}

/**
 * 回滚到指定版本
 * @param templateId 模板ID
 * @param version 目标版本
 * @returns Promise<是否成功>
 */
export const rollbackToVersion = async (templateId: string, version: CRFVersion): Promise<boolean> => {
  try {
    initServices()
    // 确认操作
    const confirm = await new Promise<boolean>((resolve) => {
      dialog?.warning({
        title: '确认回滚',
        content: `确定要回滚到版本 ${version.version} 吗？当前未保存的更改将丢失。`,
        positiveText: '确定回滚',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => resolve(false),
        onClose: () => resolve(false)
      })
    })

    if (!confirm) {
      return false
    }

    // 验证版本数据
    const validation = validateTemplateData(version.snapshot_data)
    if (!validation.valid) {
      message?.error(`版本数据无效: ${validation.errors.join(', ')}`)
      return false
    }

    // 更新模板数据
    const updateData: Partial<CRFVersion> = {
      snapshot_data: version.snapshot_data,
      version: incrementVersion(version.version) + '-rollback'
    }

    const response = await templateAPI.updateTemplate(templateId, updateData)

    if (response.success) {
      message?.success(`已成功回滚到版本 ${version.version}`)
      return true
    } else {
      throw new Error(response.message || '回滚失败')
    }

  } catch (error) {
    console.error('回滚失败:', error)
    message?.error(`回滚失败: ${error}`)
    return false
  }
}

/**
 * 发布版本
 * @param versionId 版本ID
 * @returns Promise<是否成功>
 */
export const publishVersion = async (versionId: string): Promise<boolean> => {
  try {
    const response = await templateAPI.publishVersion(versionId)

    if (response.success) {
      initServices()
      message?.success('版本发布成功')
      return true
    } else {
      throw new Error(response.message || '版本发布失败')
    }

  } catch (error) {
    console.error('版本发布失败:', error)
    initServices()
    message?.error(`版本发布失败: ${error}`)
    return false
  }
}

/**
 * 删除版本
 * @param versionId 版本ID
 * @returns Promise<是否成功>
 */
export const deleteVersion = async (versionId: string): Promise<boolean> => {
  try {
    initServices()
    const confirm = await new Promise<boolean>((resolve) => {
      dialog?.warning({
        title: '确认删除',
        content: '确定要删除这个版本吗？此操作不可撤销。',
        positiveText: '确定删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => resolve(false),
        onClose: () => resolve(false)
      })
    })

    if (!confirm) {
      return false
    }

    const response = await templateAPI.deleteVersion(versionId)

    if (response.success) {
      message?.success('版本删除成功')
      return true
    } else {
      throw new Error(response.message || '版本删除失败')
    }

  } catch (error) {
    console.error('版本删除失败:', error)
    message?.error(`版本删除失败: ${error}`)
    return false
  }
}

/**
 * 获取最新版本号
 * @param versions 版本列表
 * @returns 最新版本号
 */
export const getLatestVersionNumber = (versions: CRFVersion[]): string => {
  if (versions.length === 0) {
    return '0.0.0'
  }

  // 按版本号排序，获取最新版本
  const sortedVersions = versions
    .map(v => v.version)
    .filter(v => /^\\d+\\.\\d+\\.\\d+$/.test(v)) // 只考虑语义化版本
    .sort((a, b) => {
      const [aMajor, aMinor, aPatch] = a.split('.').map(Number)
      const [bMajor, bMinor, bPatch] = b.split('.').map(Number)

      if (aMajor !== bMajor) return bMajor - aMajor
      if (aMinor !== bMinor) return bMinor - aMinor
      return bPatch - aPatch
    })

  return sortedVersions[0] || '0.0.0'
}

/**
 * 递增版本号
 * @param version 当前版本号
 * @param type 递增类型
 * @returns 新版本号
 */
export const incrementVersion = (
  version: string,
  type: 'major' | 'minor' | 'patch' = 'patch'
): string => {
  const versionRegex = /^(\\d+)\\.(\\d+)\\.(\\d+)/
  const match = version.match(versionRegex)

  if (!match) {
    return '1.0.0'
  }

  let [, major, minor, patch] = match.map(Number)

  switch (type) {
    case 'major':
      major++
      minor = 0
      patch = 0
      break
    case 'minor':
      minor++
      patch = 0
      break
    case 'patch':
    default:
      patch++
      break
  }

  return `${major}.${minor}.${patch}`
}

/**
 * 生成版本更改摘要
 * @param diff 版本差异
 * @returns 更改摘要
 */
export const generateChangeSummary = (diff: VersionDiff): string => {
  const summary: string[] = []

  if (diff.sections.added.length > 0) {
    summary.push(`新增章节: ${diff.sections.added.map(s => s.title).join(', ')}`)
  }

  if (diff.sections.removed.length > 0) {
    summary.push(`删除章节: ${diff.sections.removed.map(s => s.title).join(', ')}`)
  }

  if (diff.sections.modified.length > 0) {
    summary.push(`修改章节: ${diff.sections.modified.length}个`)
  }

  if (diff.components.added.length > 0) {
    summary.push(`新增组件: ${diff.components.added.length}个`)
  }

  if (diff.components.removed.length > 0) {
    summary.push(`删除组件: ${diff.components.removed.length}个`)
  }

  if (diff.components.modified.length > 0) {
    summary.push(`修改组件: ${diff.components.modified.length}个`)
  }

  if (diff.configuration.added.length > 0 ||
    diff.configuration.removed.length > 0 ||
    diff.configuration.modified.length > 0) {
    summary.push('配置更新')
  }

  return summary.length > 0 ? summary.join('; ') : '无更改'
}