import {  defineAsyncComponent, markRaw } from 'vue'

/**
 * 动态引入组件
 * @param name 组件名
 * @param importUrl 引入所有的组件 import.meta.glob('@/components/config/**')
 * @returns
 */
export const batchDynamicComponents = (name: string, importUrl: Record<string, () => Promise<Record<string, unknown>>>) => {
  const components = importUrl
  const componentMap = Object.assign(
    {},
    ...Object.keys(components).map((item) => {
      const componentName = item?.split('/')?.pop()?.replace('.vue', '') || ''
      return {
        [componentName]: components[item]
      }
    })
  )
  const importComponent = componentMap[name]
  if (!importComponent) return null

  return markRaw(defineAsyncComponent(importComponent))
}
