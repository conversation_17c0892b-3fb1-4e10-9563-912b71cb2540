import type { 
  FormDataAdapter, 
  UnifiedFormConfig, 
  UnifiedSectionConfig, 
  UnifiedFieldConfig
} from '@/types/form-renderer'
import { FormDataSource } from '@/types/form-renderer'

// 定义模板数据类型
interface TemplateData {
  id?: string
  title?: string
  description?: string
  version?: string
  created_at?: string
  updated_at?: string
  sections?: TemplateSection[]
  template?: TemplateData
}

interface TemplateSection {
  id: string
  title: string
  description?: string
  collapsed?: boolean
  readonly?: boolean
  hidden?: boolean
  fields?: TemplateField[]
}

interface TemplateField {
  id: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  hidden?: boolean
  validation?: unknown[]
  options?: unknown[]
  default_value?: unknown
  custom_props?: Record<string, unknown>
}

/**
 * 模板数据适配器
 * 处理来自模板的数据结构 (templateData.sections[].fields[])
 */
export class TemplateDataAdapter implements FormDataAdapter {
  private templateData: TemplateData
  private formData: Record<string, unknown> = {}

  constructor(templateData: TemplateData) {
    this.templateData = templateData
  }

  toUnifiedFormat(data: TemplateData): UnifiedFormConfig {
    const template = data.template || data
    
    return {
      id: template.id || '',
      title: template.title || '',
      description: template.description || '',
      sections: this.convertSections(template.sections || []),
      metadata: {
        version: template.version,
        created_at: template.created_at,
        updated_at: template.updated_at
      }
    }
  }

  fromUnifiedFormat(config: UnifiedFormConfig): TemplateData {
    return {
      id: config.id,
      title: config.title,
      description: config.description,
      sections: config.sections.map(section => ({
        id: section.id,
        title: section.title,
        description: section.description,
        fields: section.fields.map(field => ({
          id: field.id,
          type: field.type,
          label: field.label,
          placeholder: field.placeholder,
          required: field.required,
          readonly: field.readonly,
          hidden: field.hidden,
          validation: field.validation,
          options: field.options,
          default_value: field.defaultValue,
          custom_props: field.customProps
        }))
      }))
    }
  }

  getFormData(): Record<string, unknown> {
    return this.formData
  }

  setFormData(data: Record<string, unknown>): void {
    this.formData = { ...data }
  }

  private convertSections(sections: TemplateSection[]): UnifiedSectionConfig[] {
    return sections.map(section => ({
      id: section.id,
      title: section.title,
      description: section.description,
      fields: this.convertFields(section.fields || []),
      collapsed: section.collapsed || false,
      readonly: section.readonly || false,
      hidden: section.hidden || false
    }))
  }

  private convertFields(fields: TemplateField[]): UnifiedFieldConfig[] {
    return fields.map(field => {
      // 确保validation是ValidationRule[]类型
      const validation = Array.isArray(field.validation) 
        ? field.validation.map(rule => {
            if (typeof rule === 'object' && rule !== null) {
              return {
                type: (rule as any).type || 'required',
                message: (rule as any).message || '',
                value: (rule as any).value,
                validator: (rule as any).validator
              };
            }
            return { type: 'required', message: '' };
          })
        : [];
        
      return {
        id: field.id,
        type: field.type,
        label: field.label,
        placeholder: field.placeholder,
        required: field.required || false,
        readonly: field.readonly || false,
        hidden: field.hidden || false,
        validation: validation,
        options: field.options || [],
        defaultValue: field.default_value,
        customProps: field.custom_props || {}
      };
    });
  }
}

// 定义编辑器数据类型
interface EditorData {
  id?: string
  title?: string
  description?: string
  schema?: EditorSchema[]
}

interface EditorSchema {
  id: string
  title: string
  description?: string
  collapsed?: boolean
  readonly?: boolean
  hidden?: boolean
  blocks?: EditorBlock[]
}

interface EditorBlock {
  id: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  hidden?: boolean
  validation?: unknown[]
  options?: unknown[]
  defaultValue?: unknown
  customProps?: Record<string, unknown>
}

/**
 * 编辑器数据适配器
 * 处理来自编辑器的数据结构 (schema[].blocks[])
 */
export class EditorDataAdapter implements FormDataAdapter {
  private editorData: EditorData
  private formData: Record<string, unknown> = {}

  constructor(editorData: EditorData) {
    this.editorData = editorData
  }

  toUnifiedFormat(data: EditorData): UnifiedFormConfig {
    const schema = data.schema || []
    
    return {
      id: data.id || 'preview',
      title: data.title || '表单预览',
      description: data.description || '',
      sections: this.convertBlocks(schema),
      metadata: {
        mode: 'editor',
        preview: true
      }
    }
  }

  fromUnifiedFormat(config: UnifiedFormConfig): EditorData {
    return {
      id: config.id,
      title: config.title,
      description: config.description,
      schema: config.sections.map(section => ({
        id: section.id,
        title: section.title,
        description: section.description,
        blocks: section.fields.map(field => ({
          id: field.id,
          type: field.type,
          label: field.label,
          placeholder: field.placeholder,
          required: field.required,
          readonly: field.readonly,
          hidden: field.hidden,
          validation: field.validation,
          options: field.options,
          defaultValue: field.defaultValue,
          customProps: field.customProps
        }))
      }))
    }
  }

  getFormData(): Record<string, unknown> {
    return this.formData
  }

  setFormData(data: Record<string, unknown>): void {
    this.formData = { ...data }
  }

  private convertBlocks(schema: EditorSchema[]): UnifiedSectionConfig[] {
    return schema.map(section => ({
      id: section.id,
      title: section.title,
      description: section.description,
      fields: this.convertBlocksToFields(section.blocks || []),
      collapsed: section.collapsed || false,
      readonly: section.readonly || false,
      hidden: section.hidden || false
    }))
  }

  private convertBlocksToFields(blocks: EditorBlock[]): UnifiedFieldConfig[] {
    return blocks.map(block => {
      // 确保validation是ValidationRule[]类型
      const validation = Array.isArray(block.validation) 
        ? block.validation.map(rule => {
            if (typeof rule === 'object' && rule !== null) {
              return {
                type: (rule as any).type || 'required',
                message: (rule as any).message || '',
                value: (rule as any).value,
                validator: (rule as any).validator
              };
            }
            return { type: 'required', message: '' };
          })
        : [];
        
      return {
        id: block.id,
        type: block.type,
        label: block.label,
        placeholder: block.placeholder,
        required: block.required || false,
        readonly: block.readonly || false,
        hidden: block.hidden || false,
        validation: validation,
        options: block.options || [],
        defaultValue: block.defaultValue,
        customProps: block.customProps || {}
      };
    });
  }
}

// 定义实例数据类型
interface InstanceData {
  id?: string
  patient_id?: string
  status?: string
  completion_percentage?: number
  submitted_at?: string
  form_data?: Record<string, unknown>
  template?: {
    id: string
    title: string
    description?: string
    template_data: {
      sections: TemplateSection[]
    }
  }
  instance?: InstanceData
}

/**
 * 实例数据适配器
 * 处理来自实例的数据结构
 */
export class InstanceDataAdapter implements FormDataAdapter {
  private instanceData: InstanceData
  private formData: Record<string, unknown> = {}

  constructor(instanceData: InstanceData) {
    this.instanceData = instanceData
    this.formData = instanceData.form_data || {}
  }

  toUnifiedFormat(data: InstanceData): UnifiedFormConfig {
    const instance = data.instance || data
    const template = instance.template
    
    return {
      id: template.id,
      title: template.title,
      description: template.description,
      sections: this.convertSections(template.template_data.sections || []),
      metadata: {
        instanceId: instance.id,
        patientId: instance.patient_id,
        status: instance.status,
        completionPercentage: instance.completion_percentage,
        submittedAt: instance.submitted_at
      }
    }
  }

  fromUnifiedFormat(config: UnifiedFormConfig): InstanceData {
    return {
      id: config.metadata?.instanceId ? String(config.metadata.instanceId) : undefined,
      patient_id: config.metadata?.patientId ? String(config.metadata.patientId) : undefined,
      form_data: this.formData,
      template: {
        id: config.id,
        title: config.title,
        description: config.description,
        template_data: {
          sections: config.sections.map(section => ({
            id: section.id,
            title: section.title,
            description: section.description,
            fields: section.fields.map(field => ({
              id: field.id,
              type: field.type,
              label: field.label,
              placeholder: field.placeholder,
              required: field.required,
              readonly: field.readonly,
              hidden: field.hidden,
              validation: field.validation,
              options: field.options,
              default_value: field.defaultValue,
              custom_props: field.customProps
            }))
          }))
        }
      }
    }
  }

  getFormData(): Record<string, unknown> {
    return this.formData
  }

  setFormData(data: Record<string, unknown>): void {
    this.formData = { ...data }
  }

  private convertSections(sections: TemplateSection[]): UnifiedSectionConfig[] {
    return sections.map(section => ({
      id: section.id,
      title: section.title,
      description: section.description,
      fields: this.convertFields(section.fields || []),
      collapsed: section.collapsed || false,
      readonly: section.readonly || false,
      hidden: section.hidden || false
    }))
  }

  private convertFields(fields: TemplateField[]): UnifiedFieldConfig[] {
    return fields.map(field => {
      // 确保validation是ValidationRule[]类型
      const validation = Array.isArray(field.validation) 
        ? field.validation.map(rule => {
            if (typeof rule === 'object' && rule !== null) {
              return {
                type: (rule as any).type || 'required',
                message: (rule as any).message || '',
                value: (rule as any).value,
                validator: (rule as any).validator
              };
            }
            return { type: 'required', message: '' };
          })
        : [];
        
      return {
        id: field.id,
        type: field.type,
        label: field.label,
        placeholder: field.placeholder,
        required: field.required || false,
        readonly: field.readonly || false,
        hidden: field.hidden || false,
        validation: validation,
        options: field.options || [],
        defaultValue: field.default_value,
        customProps: field.custom_props || {}
      };
    });
  }
}

/**
 * 适配器工厂
 * 根据数据源类型创建对应的适配器
 */
export class AdapterFactory {
  static createAdapter(dataSource: FormDataSource, data: TemplateData | EditorData | InstanceData): FormDataAdapter {
    switch (dataSource) {
      case FormDataSource.TEMPLATE:
        return new TemplateDataAdapter(data as TemplateData)
      case FormDataSource.EDITOR:
        return new EditorDataAdapter(data as EditorData)
      case FormDataSource.INSTANCE:
        return new InstanceDataAdapter(data as InstanceData)
      default:
        throw new Error(`Unsupported data source: ${dataSource}`)
    }
  }
}

/**
 * 数据转换工具
 * 提供便捷的数据格式转换方法
 */
export class DataConverter {
  /**
   * 将模板格式转换为统一格式
   */
  static templateToUnified(templateData: TemplateData): UnifiedFormConfig {
    const adapter = new TemplateDataAdapter(templateData)
    return adapter.toUnifiedFormat(templateData)
  }

  /**
   * 将编辑器格式转换为统一格式
   */
  static editorToUnified(editorData: EditorData): UnifiedFormConfig {
    const adapter = new EditorDataAdapter(editorData)
    return adapter.toUnifiedFormat(editorData)
  }

  /**
   * 将实例格式转换为统一格式
   */
  static instanceToUnified(instanceData: InstanceData): UnifiedFormConfig {
    const adapter = new InstanceDataAdapter(instanceData)
    return adapter.toUnifiedFormat(instanceData)
  }

  /**
   * 检测数据格式类型
   */
  static detectDataSource(data: unknown): FormDataSource {
    if (data && typeof data === 'object') {
      const obj = data as Record<string, unknown>
      if (obj.template_data || obj.sections) {
        return FormDataSource.TEMPLATE
      }
      if (obj.schema || obj.blocks) {
        return FormDataSource.EDITOR
      }
      if (obj.instance || obj.form_data) {
        return FormDataSource.INSTANCE
      }
    }
    return FormDataSource.TEMPLATE // 默认
  }
}