import type { App } from 'vue'
import { createDiscreteApi } from 'naive-ui'

const { message, notification } = createDiscreteApi(['message', 'notification'])

// 错误类型定义
export interface ErrorInfo {
  message: string
  stack?: string
  component?: string
  errorInfo?: string
  timestamp: number
  userAgent: string
  url: string
  userId?: string
}

// 错误级别
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  FATAL = 'fatal'
}

// 错误分类
export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  BUSINESS = 'business',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorQueue: ErrorInfo[] = []
  private maxQueueSize = 100
  private reportUrl?: string

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 配置错误处理器
   */
  configure(config: {
    reportUrl?: string
    maxQueueSize?: number
  }) {
    this.reportUrl = config.reportUrl || ''
    this.maxQueueSize = config.maxQueueSize || 100
  }

  /**
   * 处理错误
   */
  handleError(
    error: Error | string,
    level: ErrorLevel = ErrorLevel.ERROR,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    component?: string,
    showToUser = true
  ) {
    const errorInfo = this.createErrorInfo(error, component)

    // 记录错误
    this.logError(errorInfo, level, category)

    // 显示用户友好的错误信息
    if (showToUser) {
      this.showUserError(errorInfo, level, category)
    }

    // 上报错误
    this.reportError(errorInfo, level, category)

    // 添加到队列
    this.addToQueue(errorInfo)
  }

  /**
   * 创建错误信息对象
   */
  private createErrorInfo(error: Error | string, component?: string): ErrorInfo {
    const message = typeof error === 'string' ? error : error.message
    const stack = typeof error === 'object' ? error.stack : undefined

    return {
      message,
      stack: stack || '',
      component: component || '',
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId() || ''
    }
  }

  /**
   * 记录错误到控制台
   */
  private logError(errorInfo: ErrorInfo, level: ErrorLevel, category: ErrorCategory) {
    const logData = {
      level,
      category,
      timestamp: new Date(errorInfo.timestamp).toISOString(),
      component: errorInfo.component,
      message: errorInfo.message,
      stack: errorInfo.stack,
      url: errorInfo.url
    }

    switch (level) {
      case ErrorLevel.INFO:
        console.info('ℹ️ [CRF Error]', logData)
        break
      case ErrorLevel.WARNING:
        console.warn('⚠️ [CRF Error]', logData)
        break
      case ErrorLevel.ERROR:
        console.error('❌ [CRF Error]', logData)
        break
      case ErrorLevel.FATAL:
        console.error('💥 [CRF Fatal Error]', logData)
        break
    }
  }

  /**
   * 显示用户友好的错误信息
   */
  private showUserError(errorInfo: ErrorInfo, level: ErrorLevel, category: ErrorCategory) {
    const userMessage = this.getUserFriendlyMessage(errorInfo.message, category)

    switch (level) {
      case ErrorLevel.INFO:
        message.info(userMessage)
        break
      case ErrorLevel.WARNING:
        message.warning(userMessage)
        break
      case ErrorLevel.ERROR:
        message.error(userMessage, {
          duration: 5000,
          closable: true
        })
        break
      case ErrorLevel.FATAL:
        notification.error({
          title: '系统错误',
          content: userMessage,
          duration: 0, // 不自动关闭
          closable: true
        })
        break
    }
  }

  /**
   * 获取用户友好的错误信息
   */
  private getUserFriendlyMessage(originalMessage: string, category: ErrorCategory): string {
    // 根据错误类别返回用户友好的消息
    const messageMap = {
      [ErrorCategory.NETWORK]: '网络连接异常，请检查网络设置后重试',
      [ErrorCategory.VALIDATION]: '输入数据格式不正确，请检查后重新提交',
      [ErrorCategory.PERMISSION]: '您没有执行此操作的权限，请联系管理员',
      [ErrorCategory.BUSINESS]: '操作失败，请稍后重试',
      [ErrorCategory.SYSTEM]: '系统异常，请刷新页面重试',
      [ErrorCategory.UNKNOWN]: '发生未知错误，请刷新页面重试'
    }

    // 检查是否包含特定关键词
    if (originalMessage.includes('Network Error') || originalMessage.includes('fetch')) {
      return messageMap[ErrorCategory.NETWORK]
    }

    if (originalMessage.includes('401') || originalMessage.includes('403')) {
      return messageMap[ErrorCategory.PERMISSION]
    }

    if (originalMessage.includes('500') || originalMessage.includes('502') || originalMessage.includes('503')) {
      return messageMap[ErrorCategory.SYSTEM]
    }

    return messageMap[category] || messageMap[ErrorCategory.UNKNOWN]
  }

  /**
   * 上报错误到服务器
   */
  private async reportError(errorInfo: ErrorInfo, level: ErrorLevel, category: ErrorCategory) {
    if (!this.reportUrl) return

    try {
      await fetch(this.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...errorInfo,
          level,
          category,
          project: 'crf-editor'
        })
      })
    } catch (error) {
      // 上报失败时只记录到控制台，避免无限循环
      console.warn('Error reporting failed:', error)
    }
  }

  /**
   * 添加到错误队列
   */
  private addToQueue(errorInfo: ErrorInfo) {
    this.errorQueue.push(errorInfo)

    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
  }

  /**
   * 获取当前用户ID（需要根据实际应用实现）
   */
  private getCurrentUserId(): string | undefined {
    // 这里应该根据实际的用户认证系统获取用户ID
    return localStorage.getItem('userId') || undefined
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byCategory: {} as Record<ErrorCategory, number>,
      byComponent: {} as Record<string, number>,
      recent: this.errorQueue.slice(-10)
    }

    // 统计分类
    Object.values(ErrorCategory).forEach(category => {
      stats.byCategory[category] = 0
    })

    this.errorQueue.forEach(error => {
      // 这里需要错误信息中包含分类信息，实际使用时需要调整
      if (error.component) {
        stats.byComponent[error.component] = (stats.byComponent[error.component] || 0) + 1
      }
    })

    return stats
  }

  /**
   * 清空错误队列
   */
  clearErrors() {
    this.errorQueue = []
  }
}

/**
 * 设置Vue应用的错误处理
 */
export function setupErrorHandler(app: App) {
  const errorHandler = ErrorHandler.getInstance()

  // Vue错误处理
  app.config.errorHandler = (err, vm) => {
    const vmAsRecord = vm as unknown as Record<string, unknown>
    let component = 'Unknown'
    
    if (vmAsRecord?.$options && typeof vmAsRecord.$options === 'object') {
      component = String((vmAsRecord.$options as Record<string, unknown>).name || '')
    } else if (vmAsRecord?.$ && typeof vmAsRecord.$ === 'object') {
      const vueInstance = vmAsRecord.$ as Record<string, unknown>
      if (vueInstance.type && typeof vueInstance.type === 'object') {
        component = String((vueInstance.type as Record<string, unknown>).name || '')
      }
    }
    
    if (!component || component === 'undefined' || component === 'null') {
      component = 'Unknown'
    }

    errorHandler.handleError(
      err as Error,
      ErrorLevel.ERROR,
      ErrorCategory.SYSTEM,
      component
    )
  }

  // 未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    errorHandler.handleError(
      event.reason,
      ErrorLevel.ERROR,
      ErrorCategory.SYSTEM
    )
  })

  // 全局JavaScript错误
  window.addEventListener('error', (event) => {
    errorHandler.handleError(
      event.error || event.message,
      ErrorLevel.ERROR,
      ErrorCategory.SYSTEM
    )
  })

  return errorHandler
}

/**
 * 错误处理装饰器（用于函数）
 */
export function withErrorHandler<T extends (...args: unknown[]) => unknown>(
  fn: T,
  category: ErrorCategory = ErrorCategory.BUSINESS,
  component?: string
): T {
  return ((...args: unknown[]) => {
    try {
      const result = fn(...args)

      // 处理异步函数
      if (result instanceof Promise) {
        return result.catch((error) => {
          ErrorHandler.getInstance().handleError(error, ErrorLevel.ERROR, category, component)
          throw error
        })
      }

      return result
    } catch (error) {
      ErrorHandler.getInstance().handleError(error as Error, ErrorLevel.ERROR, category, component)
      throw error
    }
  }) as T
}

/**
 * 异步函数错误处理包装器
 */
export function asyncErrorHandler<T extends (...args: unknown[]) => Promise<unknown>>(
  fn: T,
  category: ErrorCategory = ErrorCategory.BUSINESS,
  component?: string,
  showToUser = true
): T {
  return (async (...args: unknown[]) => {
    try {
      return await fn(...args)
    } catch (error) {
      ErrorHandler.getInstance().handleError(
        error as Error,
        ErrorLevel.ERROR,
        category,
        component,
        showToUser
      )
      throw error
    }
  }) as T
}

/**
 * 组合式API：错误处理
 */
export function useErrorHandler() {
  const errorHandler = ErrorHandler.getInstance()

  const handleError = (
    error: Error | string,
    level: ErrorLevel = ErrorLevel.ERROR,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    showToUser = true
  ) => {
    errorHandler.handleError(error, level, category, undefined, showToUser)
  }

  const handleAsyncError = async <T>(
    asyncFn: () => Promise<T>,
    category: ErrorCategory = ErrorCategory.BUSINESS,
    showToUser = true
  ): Promise<T | null> => {
    try {
      return await asyncFn()
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, category, showToUser)
      return null
    }
  }

  return {
    handleError,
    handleAsyncError,
    ErrorLevel,
    ErrorCategory
  }
}
