/**
 * 高性能深拷贝工具
 * 优化了JSON.parse(JSON.stringify())的性能问题
 */

/**
 * 现代浏览器的结构化克隆API（优先使用）
 */
export const deepClone = <T>(obj: T): T => {
  // 首先检查对象是否可能包含不可克隆的内容
  if (containsUnclonableContent(obj)) {
    return customDeepClone(obj)
  }

  // 优先使用原生 structuredClone API
  if (typeof structuredClone !== 'undefined') {
    try {
      return structuredClone(obj)
    } catch (error) {
      // 静默降级，不显示警告
      return customDeepClone(obj)
    }
  }

  // 降级到自定义实现
  return customDeepClone(obj)
}

/**
 * 检查对象是否包含不可克隆的内容
 */
function containsUnclonableContent(obj: unknown): boolean {
  if (obj === null || typeof obj !== 'object') {
    return false
  }

  // 检查是否为 Vue 的响应式对象
  if ((obj as Record<string, unknown>).__v_isReactive || 
      (obj as Record<string, unknown>).__v_isReadonly || 
      (obj as Record<string, unknown>).__v_isRef) {
    return true
  }

  // 检查是否为 Proxy
  try {
    // 简单的 Proxy 检测
    if (obj.constructor && obj.constructor.name === 'Object' &&
      Object.getOwnPropertyDescriptor(obj, Symbol.toStringTag)) {
      return true
    }
  } catch (e) {
    return true
  }

  // 检查是否包含函数
  if (typeof obj === 'function') {
    return true
  }

  return false
}

/**
 * 自定义深拷贝实现（处理循环引用和特殊类型）
 */
function customDeepClone<T>(obj: T, visited = new WeakMap<object, unknown>()): T {
  // 基本类型直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  // 处理循环引用
  if (visited.has(obj as object)) {
    return visited.get(obj as object) as T
  }

  // 处理日期对象
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  // 处理正则表达式
  if (obj instanceof RegExp) {
    return new RegExp(obj.source, obj.flags) as T
  }

  // 处理函数 - 直接返回原函数（函数通常不需要克隆）
  if (typeof obj === 'function') {
    return obj
  }

  // 处理 Map
  if (obj instanceof Map) {
    const clonedMap = new Map()
    visited.set(obj as object, clonedMap)
    obj.forEach((value, key) => {
      clonedMap.set(customDeepClone(key, visited), customDeepClone(value, visited))
    })
    return clonedMap as T
  }

  // 处理 Set
  if (obj instanceof Set) {
    const clonedSet = new Set()
    visited.set(obj as object, clonedSet)
    obj.forEach(value => {
      clonedSet.add(customDeepClone(value, visited))
    })
    return clonedSet as T
  }

  // 处理数组
  if (Array.isArray(obj)) {
    const clonedArray: unknown[] = []
    visited.set(obj as object, clonedArray)

    for (let i = 0; i < obj.length; i++) {
      try {
        clonedArray[i] = customDeepClone(obj[i], visited)
      } catch (error) {
        // 如果某个元素无法克隆，跳过它
        console.warn(`Unable to clone array element at index ${i}:`, error)
        clonedArray[i] = null
      }
    }

    return clonedArray as T
  }

  // 处理普通对象和特殊对象
  try {
    // 检查是否为 Vue 的 reactive 对象或其他特殊对象
    const constructor = obj.constructor
    if (constructor && constructor.name && !['Object', 'Array'].includes(constructor.name)) {
      // 对于特殊对象，尝试提取可序列化的属性
      return extractSerializableProperties(obj, visited)
    }

    const clonedObj: Record<string, unknown> = {}
    visited.set(obj as object, clonedObj)

    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        try {
          const value = (obj as Record<string, unknown>)[key]
          // 跳过函数和不可序列化的属性
          if (typeof value === 'function' ||
            value instanceof HTMLElement ||
            value instanceof Node ||
            (value && typeof value === 'object' && value.constructor && value.constructor.name === 'WeakMap') ||
            (value && typeof value === 'object' && value.constructor && value.constructor.name === 'WeakSet')) {
            continue
          }
          clonedObj[key] = customDeepClone(value, visited)
        } catch (error) {
          console.warn(`Unable to clone property '${key}':`, error)
          // 尝试获取基本值
          try {
            const value = (obj as Record<string, unknown>)[key]
            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
              clonedObj[key] = value
            }
          } catch (e) {
            // 忽略无法访问的属性
          }
        }
      }
    }

    return clonedObj as T
  } catch (error) {
    console.warn('Failed to clone object, falling back to serializable extraction:', error)
    return extractSerializableProperties(obj, visited)
  }
}

/**
 * 提取对象中可序列化的属性
 */
function extractSerializableProperties<T>(obj: T, visited = new WeakMap<object, unknown>()): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (visited.has(obj as object)) {
    return visited.get(obj as object) as T
  }

  const result: Record<string, unknown> = {}
  visited.set(obj as object, result)

  try {
    // 尝试JSON序列化来检测可序列化的属性
    const testSerialized = JSON.stringify(obj)
    const testDeserialized = JSON.parse(testSerialized)

    // 如果JSON序列化成功，使用它
    for (const key in testDeserialized) {
      if (Object.prototype.hasOwnProperty.call(testDeserialized, key)) {
        result[key] = testDeserialized[key]
      }
    }

    return result as T
  } catch (error) {
    // JSON序列化失败，手动提取基本属性
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        try {
          const value = (obj as Record<string, unknown>)[key]
          if (typeof value === 'string' ||
            typeof value === 'number' ||
            typeof value === 'boolean' ||
            value === null) {
            result[key] = value
          } else if (Array.isArray(value)) {
            result[key] = value.map(item =>
              typeof item === 'object' ? extractSerializableProperties(item, visited) : item
            )
          } else if (typeof value === 'object' && value !== null) {
            result[key] = extractSerializableProperties(value, visited)
          }
        } catch (e) {
          // 忽略无法访问的属性
        }
      }
    }

    return result as T
  }
}

/**
 * 快速浅拷贝（用于简单对象）
 */
export const shallowClone = <T extends Record<string, unknown>>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return [...obj] as unknown as T
  }

  return { ...obj }
}

// 定义章节数据类型
interface SectionData {
  id: string
  name: string
  level?: number
  parentId?: string
  order?: number
  collapsed?: boolean
  blocks?: BlockData[]
  children?: SectionData[]
}

interface BlockData {
  id: string
  code?: string
  name: string
  title?: string
  type: string
  sectionId?: string
  order?: number
  props?: Record<string, unknown>
}

/**
 * 针对CRF章节数据的优化克隆
 * 只克隆必要的字段，提高性能
 */
export const cloneSections = (sections: SectionData[]): SectionData[] => {
  if (!Array.isArray(sections)) {
    return []
  }

  return sections.map(section => ({
    id: section.id,
    name: section.name,
    level: section.level || 0,
    parentId: section.parentId,
    order: section.order || 0,
    collapsed: Boolean(section.collapsed),
    blocks: Array.isArray(section.blocks) ? section.blocks.map((block: BlockData) => ({
      id: block.id,
      code: block.code,
      name: block.name,
      title: block.title,
      type: block.type,
      sectionId: block.sectionId,
      order: block.order || 0,
      props: shallowClone(block.props || {})
    })) : [],
    children: Array.isArray(section.children) ? cloneSections(section.children) : []
  }))
}

/**
 * 针对表单数据的优化克隆
 */
export const cloneFormData = (formData: Record<string, unknown>): Record<string, unknown> => {
  if (!formData || typeof formData !== 'object') {
    return {}
  }

  const result: Record<string, unknown> = {}

  for (const key in formData) {
    if (Object.prototype.hasOwnProperty.call(formData, key)) {
      const value = formData[key]

      // 基本类型直接赋值
      if (value === null || typeof value !== 'object') {
        result[key] = value
      }
      // 数组使用浅拷贝
      else if (Array.isArray(value)) {
        result[key] = [...value]
      }
      // 日期对象
      else if (value instanceof Date) {
        result[key] = new Date(value.getTime())
      }
      // 普通对象使用浅拷贝（表单数据通常不需要深度克隆）
      else {
        result[key] = { ...value }
      }
    }
  }

  return result
}

/**
 * 针对编辑器状态的安全克隆函数
 * 专门处理包含 Vue 响应式对象的编辑器状态
 */
export const cloneEditorState = (state: unknown): unknown => {
  if (!state || typeof state !== 'object') {
    return state
  }

  try {
    // 首先尝试 JSON 序列化（最安全的方式）
    const serialized = JSON.stringify(state, (_key, value) => {
      // 过滤掉不可序列化的属性
      if (typeof value === 'function' ||
        value instanceof HTMLElement ||
        value instanceof Node) {
        return undefined
      }

      // 处理 Vue 响应式对象
      if (value && typeof value === 'object') {
        // 如果是 Vue 响应式对象，提取原始值
        if (value.__v_isReactive || value.__v_isReadonly || value.__v_isRef) {
          // 尝试获取原始值
          try {
            return JSON.parse(JSON.stringify(value))
          } catch (e) {
            return extractPlainObject(value)
          }
        }
      }

      return value
    })

    return JSON.parse(serialized)
  } catch (error) {
    console.warn('JSON serialization failed, using fallback method:', error)
    return extractPlainObject(state)
  }
}

/**
 * 提取普通对象（去除 Vue 响应式特性）
 */
function extractPlainObject(obj: unknown): unknown {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(item => extractPlainObject(item))
  }

  const result: Record<string, unknown> = {}

  // 获取所有可枚举的属性
  for (const key in obj as Record<string, unknown>) {
    try {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = (obj as Record<string, unknown>)[key]

        // 跳过以 __ 开头的私有属性（Vue 内部属性）
        if (key.startsWith('__')) {
          continue
        }

        // 跳过函数和特殊对象
        if (typeof value === 'function' ||
          value instanceof HTMLElement ||
          value instanceof Node) {
          continue
        }

        // 递归处理对象和数组
        if (typeof value === 'object' && value !== null) {
          result[key] = extractPlainObject(value)
        } else {
          result[key] = value
        }
      }
    } catch (e) {
      // 忽略无法访问的属性
      continue
    }
  }

  return result
}

/**
 * 检测是否可以使用原生 structuredClone
 */
export const isStructuredCloneSupported = (): boolean => {
  return typeof structuredClone !== 'undefined'
}

/**
 * 性能测试工具（开发环境使用）
 */
export const performanceTest = (obj: unknown, iterations = 1000) => {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  console.group('Deep Clone Performance Test')

  // 测试原生 structuredClone
  if (isStructuredCloneSupported()) {
    const start1 = performance.now()
    for (let i = 0; i < iterations; i++) {
      structuredClone(obj)
    }
    const end1 = performance.now()
    console.log(`structuredClone: ${end1 - start1}ms`)
  }

  // 测试自定义实现
  const start2 = performance.now()
  for (let i = 0; i < iterations; i++) {
    customDeepClone(obj)
  }
  const end2 = performance.now()
  console.log(`customDeepClone: ${end2 - start2}ms`)

  // 测试JSON方法
  const start3 = performance.now()
  for (let i = 0; i < iterations; i++) {
    JSON.parse(JSON.stringify(obj))
  }
  const end3 = performance.now()
  console.log(`JSON.parse(JSON.stringify): ${end3 - start3}ms`)

  console.groupEnd()
}