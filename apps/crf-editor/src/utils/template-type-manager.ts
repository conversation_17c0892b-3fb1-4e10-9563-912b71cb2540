/**
 * CRF模板类型管理工具
 * 用于区分和管理不同类型的模板和表单
 */

import type { CRFTemplate } from '@/types/api'

export enum TemplateType {
  TEMPLATE = 'template',      // 标准模板
  CUSTOM_FORM = 'custom_form' // 自定义表单
}

export enum SourceType {
  SYSTEM_PRESET = 'system_preset', // 系统预置
  USER_CREATED = 'user_created',   // 用户创建
  IMPORTED = 'imported',           // 导入
  COPIED = 'copied'                // 复制
}

export interface TemplateFilter {
  template_type?: TemplateType
  source_type?: SourceType
  is_public?: boolean
  tags?: string[]
  created_by?: string
  project_id?: string
}

export class TemplateTypeManager {
  /**
   * 判断是否为模板
   */
  static isTemplate(template: CRFTemplate): boolean {
    return template.template_type === TemplateType.TEMPLATE
  }

  /**
   * 判断是否为自定义表单
   */
  static isCustomForm(template: CRFTemplate): boolean {
    return template.template_type === TemplateType.CUSTOM_FORM
  }

  /**
   * 判断是否为系统预置模板
   */
  static isSystemPreset(template: CRFTemplate): boolean {
    return template.source_type === SourceType.SYSTEM_PRESET
  }

  /**
   * 判断是否为用户创建
   */
  static isUserCreated(template: CRFTemplate): boolean {
    return template.source_type === SourceType.USER_CREATED
  }

  /**
   * 判断是否为公共模板
   */
  static isPublic(template: CRFTemplate): boolean {
    return template.is_public === true
  }

  /**
   * 获取模板类型的显示名称
   */
  static getTypeDisplayName(template: CRFTemplate): string {
    switch (template.template_type) {
      case TemplateType.TEMPLATE:
        return '标准模板'
      case TemplateType.CUSTOM_FORM:
        return '自定义表单'
      default:
        return '未知类型'
    }
  }

  /**
   * 获取来源类型的显示名称
   */
  static getSourceDisplayName(template: CRFTemplate): string {
    switch (template.source_type) {
      case SourceType.SYSTEM_PRESET:
        return '系统预置'
      case SourceType.USER_CREATED:
        return '用户创建'
      case SourceType.IMPORTED:
        return '导入模板'
      case SourceType.COPIED:
        return '复制模板'
      default:
        return '未知来源'
    }
  }

  /**
   * 获取模板状态的显示名称和颜色
   */
  static getStatusInfo(template: CRFTemplate): { name: string; color: string } {
    switch (template.status) {
      case 'draft':
        return { name: '未发布', color: '#909399' }
      case 'published':
        return { name: '已发布', color: '#67c23a' }
      case 'archived':
        return { name: '已归档', color: '#f56c6c' }
      default:
        return { name: '未知', color: '#909399' }
    }
  }

  /**
   * 过滤模板列表
   */
  static filterTemplates(templates: CRFTemplate[], filter: TemplateFilter): CRFTemplate[] {
    return templates.filter(template => {
      // 模板类型过滤
      if (filter.template_type && template.template_type !== filter.template_type) {
        return false
      }

      // 来源类型过滤
      if (filter.source_type && template.source_type !== filter.source_type) {
        return false
      }

      // 公开性过滤
      if (filter.is_public !== undefined && template.is_public !== filter.is_public) {
        return false
      }

      // 创建者过滤
      if (filter.created_by && template.created_by !== filter.created_by) {
        return false
      }

      // 项目过滤
      if (filter.project_id && template.project_id !== filter.project_id) {
        return false
      }

      // 标签过滤
      if (filter.tags && filter.tags.length > 0) {
        const templateTags = template.tags ? template.tags.split(',').map(tag => tag.trim()) : []
        const hasMatchingTag = filter.tags.some(tag => 
          templateTags.some(templateTag => 
            templateTag.toLowerCase().includes(tag.toLowerCase())
          )
        )
        if (!hasMatchingTag) {
          return false
        }
      }

      return true
    })
  }

  /**
   * 按类型分组模板
   */
  static groupByType(templates: CRFTemplate[]): Record<string, CRFTemplate[]> {
    const groups: Record<string, CRFTemplate[]> = {
      [TemplateType.TEMPLATE]: [],
      [TemplateType.CUSTOM_FORM]: []
    }

    templates.forEach(template => {
      if (groups[template.template_type]) {
        groups[template.template_type].push(template)
      }
    })

    return groups
  }

  /**
   * 按来源分组模板
   */
  static groupBySource(templates: CRFTemplate[]): Record<string, CRFTemplate[]> {
    const groups: Record<string, CRFTemplate[]> = {
      [SourceType.SYSTEM_PRESET]: [],
      [SourceType.USER_CREATED]: [],
      [SourceType.IMPORTED]: [],
      [SourceType.COPIED]: []
    }

    templates.forEach(template => {
      if (groups[template.source_type]) {
        groups[template.source_type].push(template)
      }
    })

    return groups
  }

  /**
   * 创建标准模板
   */
  static createTemplate(baseData: Partial<CRFTemplate>): Partial<CRFTemplate> {
    return {
      ...baseData,
      template_type: TemplateType.TEMPLATE,
      source_type: SourceType.USER_CREATED,
      is_public: false,
      usage_count: 0,
      status: 'draft'
    }
  }

  /**
   * 创建自定义表单
   */
  static createCustomForm(baseData: Partial<CRFTemplate>): Partial<CRFTemplate> {
    return {
      ...baseData,
      template_type: TemplateType.CUSTOM_FORM,
      source_type: SourceType.USER_CREATED,
      is_public: false,
      usage_count: 0,
      status: 'draft'
    }
  }

  /**
   * 复制模板
   */
  static copyTemplate(original: CRFTemplate, newName: string): Partial<CRFTemplate> {
    return {
      name: newName,
      title: `${original.title} (副本)`,
      description: original.description,
      keyword: original.keyword,
      template_type: original.template_type,
      source_type: SourceType.COPIED,
      is_public: false,
      template_data: original.template_data,
      permissions: original.permissions,
      tags: original.tags,
      usage_count: 0,
      status: 'draft',
      version: '1.0.0'
    }
  }

  /**
   * 从导入数据创建模板
   */
  static createFromImport(importData: Record<string, unknown>): Partial<CRFTemplate> {
    // 确保 template_type 是有效的枚举值
    let templateType: TemplateType = TemplateType.TEMPLATE
    if (importData.template_type && 
        (importData.template_type === TemplateType.TEMPLATE || 
         importData.template_type === TemplateType.CUSTOM_FORM)) {
      templateType = importData.template_type as TemplateType
    }

    // 处理 tags 字段，确保是字符串类型
    let tags = ''
    if (importData.tags) {
      if (Array.isArray(importData.tags)) {
        tags = (importData.tags as string[]).join(',')
      } else if (typeof importData.tags === 'string') {
        tags = importData.tags
      }
    }

    // 确保 template_data 是有效的对象
    const templateData: Record<string, unknown> = {
      ...(typeof importData.template_data === 'object' && importData.template_data !== null
        ? importData.template_data as Record<string, unknown>
        : {})
    }

    // 确保 permissions 是有效的对象
    const permissions: Record<string, unknown> = {
      ...(typeof importData.permissions === 'object' && importData.permissions !== null
        ? importData.permissions as Record<string, unknown>
        : {})
    }

    return {
      name: String(importData.name || ''),
      title: String(importData.title || ''),
      description: String(importData.description || ''),
      keyword: String(importData.keyword || ''),
      template_type: templateType,
      source_type: SourceType.IMPORTED,
      is_public: false,
      template_data: templateData,
      permissions: permissions,
      tags: tags,
      usage_count: 0,
      status: 'draft',
      version: String(importData.version || '1.0.0')
    }
  }

  /**
   * 获取可用的模板类型选项
   */
  static getTemplateTypeOptions() {
    return [
      { label: '标准模板', value: TemplateType.TEMPLATE, description: '可重复使用的标准化表单模板' },
      { label: '自定义表单', value: TemplateType.CUSTOM_FORM, description: '独立的自定义表单，不作为模板使用' }
    ]
  }

  /**
   * 获取可用的来源类型选项
   */
  static getSourceTypeOptions() {
    return [
      { label: '系统预置', value: SourceType.SYSTEM_PRESET, description: '系统内置的标准模板' },
      { label: '用户创建', value: SourceType.USER_CREATED, description: '用户从零开始创建' },
      { label: '导入模板', value: SourceType.IMPORTED, description: '从文件导入的模板' },
      { label: '复制模板', value: SourceType.COPIED, description: '从现有模板复制而来' }
    ]
  }

  /**
   * 验证模板数据完整性
   */
  static validateTemplate(template: Partial<CRFTemplate>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!template.name || template.name.trim() === '') {
      errors.push('模板名称不能为空')
    }

    if (!template.title || template.title.trim() === '') {
      errors.push('模板标题不能为空')
    }

    if (!template.template_type || !Object.values(TemplateType).includes(template.template_type as TemplateType)) {
      errors.push('模板类型无效')
    }

    if (!template.source_type || !Object.values(SourceType).includes(template.source_type as SourceType)) {
      errors.push('来源类型无效')
    }

    if (!template.template_data || typeof template.template_data !== 'object') {
      errors.push('模板数据不能为空')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 搜索模板
   */
  static searchTemplates(templates: CRFTemplate[], searchTerm: string): CRFTemplate[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return templates
    }

    const term = searchTerm.toLowerCase().trim()
    
    return templates.filter(template => {
      return (
        template.name.toLowerCase().includes(term) ||
        template.title.toLowerCase().includes(term) ||
        (template.description && template.description.toLowerCase().includes(term)) ||
        (template.keyword && template.keyword.toLowerCase().includes(term)) ||
        (template.tags && template.tags.toLowerCase().includes(term))
      )
    })
  }

  /**
   * 排序模板
   */
  static sortTemplates(
    templates: CRFTemplate[], 
    sortBy: 'name' | 'title' | 'created_at' | 'updated_at' | 'usage_count',
    order: 'asc' | 'desc' = 'desc'
  ): CRFTemplate[] {
    return [...templates].sort((a, b) => {
      let aValue: unknown = a[sortBy]
      let bValue: unknown = b[sortBy]

      // 处理日期字段
      if (sortBy === 'created_at' || sortBy === 'updated_at') {
        // 确保日期值是字符串类型
        const aStr = typeof aValue === 'string' ? aValue : String(aValue || '')
        const bStr = typeof bValue === 'string' ? bValue : String(bValue || '')
        
        aValue = new Date(aStr).getTime()
        bValue = new Date(bStr).getTime()
      }

      // 处理字符串字段
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        // 确保 bValue 是字符串类型再调用 toLowerCase
        if (typeof bValue === 'string') {
          bValue = bValue.toLowerCase()
        } else {
          bValue = String(bValue || '').toLowerCase()
        }
      }

      if (order === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })
  }
}