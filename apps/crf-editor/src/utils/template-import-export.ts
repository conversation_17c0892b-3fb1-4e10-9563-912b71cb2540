/**
 * CRF模板导入导出工具类
 * 支持JSON格式的模板导入和导出
 */

import { useMessage, useDialog, type MessageApi, type DialogApi } from 'naive-ui'
import { validateTemplateData, createDefaultTemplateData, type CRFTemplateData } from '@/schemas/template-schema'
import type { CRFTemplate } from '@/types/api'

// Using null assertion to fix type errors when these variables are used
let message: MessageApi | null = null
let dialog: DialogApi | null = null

// 初始化消息和对话框实例
const initServices = () => {
  if (!message) {
    message = useMessage()
  }
  if (!dialog) {
    dialog = useDialog()
  }
}

export class TemplateImportExport {
  /**
   * 导出模板为JSON文件
   * @param template CRF模板对象
   * @param filename 文件名（可选）
   */
  static exportTemplate(template: CRFTemplate, filename?: string): void {
    try {
      // 构建导出数据
      const exportData = {
        id: template.id,
        name: template.name,
        title: template.title,
        description: template.description,
        keyword: template.keyword,
        version: template.version,
        status: template.status,
        template_data: template.template_data,
        permissions: template.permissions,
        created_by: template.created_by,
        created_at: template.created_at,
        updated_at: template.updated_at,
        // 添加导出元数据
        export_metadata: {
          exported_at: new Date().toISOString(),
          export_version: '1.0.0',
          export_source: 'CRF Editor'
        }
      }

      // 验证数据
      const validation = validateTemplateData(exportData.template_data)
      if (!validation.valid) {
        console.warn('模板数据验证发现问题:', validation.errors)
        initServices()
        message?.warning(`模板数据存在问题，仍将导出。问题: ${validation.errors.join(', ')}`)
      }

      // 创建文件名
      const defaultFilename = `${template.name || 'template'}_${template.version || '1.0.0'}_${new Date().toISOString().slice(0, 10)}.json`
      const finalFilename = filename || defaultFilename

      // 创建并下载文件
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json;charset=utf-8' 
      })
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = finalFilename
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)

      initServices()
      message?.success(`模板 "${template.title}" 导出成功`)

      // 记录导出操作
      console.log('Template exported:', {
        templateId: template.id,
        filename: finalFilename,
        size: blob.size
      })

    } catch (error) {
      console.error('模板导出失败:', error)
      initServices()
      message?.error(`模板导出失败: ${error}`)
    }
  }

  /**
   * 从JSON文件导入模板
   * @param file 文件对象
   * @returns Promise<导入的模板数据>
   */
  static async importTemplate(file: File): Promise<Partial<CRFTemplate> | null> {
    try {
      // 验证文件类型
      if (!file.name.toLowerCase().endsWith('.json')) {
        initServices()
        message?.error('请选择JSON格式的文件')
        return null
      }

      // 验证文件大小（限制10MB）
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        initServices()
        message?.error('文件大小不能超过10MB')
        return null
      }

      // 读取文件内容
      const content = await this.readFileAsText(file)
      
      // 解析JSON
      let importData: Record<string, unknown>
      try {
        importData = JSON.parse(content)
      } catch (parseError) {
        initServices()
        message?.error('JSON文件格式无效，请检查文件内容')
        return null
      }

      // 验证导入数据结构
      const validationResult = this.validateImportData(importData)
      if (!validationResult.valid) {
        initServices()
        message?.error(`文件结构无效: ${validationResult.errors.join(', ')}`)
        return null
      }

      // 验证模板数据
      if (importData.template_data) {
        const templateValidation = validateTemplateData(importData.template_data)
        if (!templateValidation.valid) {
          initServices()
          const proceed = await new Promise<boolean>((resolve) => {
            dialog?.warning({
              title: '数据验证警告',
              content: `模板数据验证发现问题：\n${templateValidation.errors.join('\n')}\n\n是否继续导入？`,
              positiveText: '继续导入',
              negativeText: '取消',
              onPositiveClick: () => resolve(true),
              onNegativeClick: () => resolve(false),
              onClose: () => resolve(false)
            })
          })

          if (!proceed) {
            return null
          }
        }
        
        if (templateValidation.warnings.length > 0) {
          initServices()
          message?.warning(`导入成功，但发现警告: ${templateValidation.warnings.join(', ')}`)
        }
      }

      // 构建返回的模板对象
      const template: Partial<CRFTemplate> = {
        name: typeof importData.name === 'string' ? importData.name : '导入的模板',
        title: typeof importData.title === 'string' ? importData.title : '导入的模板',
        description: typeof importData.description === 'string' ? importData.description : '',
        keyword: typeof importData.keyword === 'string' ? importData.keyword : '',
        version: typeof importData.version === 'string' ? importData.version : '1.0.0',
        status: 'draft', // 导入的模板始终设为草稿状态
        template_data: typeof importData.template_data === 'object' && importData.template_data !== null
          ? importData.template_data as {
              pageConfig?: Record<string, unknown>
              formStructure?: Record<string, unknown>
              componentConfigs?: Record<string, unknown>
              validationRules?: Record<string, unknown>
              styleConfig?: Record<string, unknown>
              [key: string]: unknown
            }
          : {
              ...createDefaultTemplateData(),
              formStructure: {
                ...createDefaultTemplateData().formStructure,
                sections: createDefaultTemplateData().formStructure?.sections || []
              }
            } as {
              pageConfig?: Record<string, unknown>
              formStructure?: Record<string, unknown>
              componentConfigs?: Record<string, unknown>
              validationRules?: Record<string, unknown>
              styleConfig?: Record<string, unknown>
              [key: string]: unknown
            },
        permissions: {
          ...(typeof importData.permissions === 'object' && importData.permissions !== null
            ? importData.permissions as Record<string, unknown>
            : {})
        }
      }

      initServices()
      message?.success(`模板 "${template.title}" 导入成功`)

      // 记录导入操作
      console.log('Template imported:', {
        filename: file.name,
        templateName: template.name,
        size: file.size
      })

      return template

    } catch (error) {
      console.error('模板导入失败:', error)
      initServices()
      message?.error(`模板导入失败: ${error}`)
      return null
    }
  }

  /**
   * 批量导出多个模板
   * @param templates 模板数组
   * @param filename 压缩文件名（可选）
   */
  static async exportMultipleTemplates(templates: CRFTemplate[], filename?: string): Promise<void> {
    try {
      if (templates.length === 0) {
        initServices()
        message?.warning('请选择要导出的模板')
        return
      }

      // 如果只有一个模板，直接导出JSON文件
      if (templates.length === 1) {
        this.exportTemplate(templates[0], filename)
        return
      }

      // 多个模板时，创建包含所有模板的JSON文件
      const exportData = {
        export_metadata: {
          exported_at: new Date().toISOString(),
          export_version: '1.0.0',
          export_source: 'CRF Editor',
          template_count: templates.length
        },
        templates: templates.map(template => ({
          id: template.id,
          name: template.name,
          title: template.title,
          description: template.description,
          keyword: template.keyword,
          version: template.version,
          status: template.status,
          template_data: template.template_data,
          permissions: template.permissions,
          created_by: template.created_by,
          created_at: template.created_at,
          updated_at: template.updated_at
        }))
      }

      // 创建文件名
      const defaultFilename = `crf_templates_batch_${new Date().toISOString().slice(0, 10)}.json`
      const finalFilename = filename || defaultFilename

      // 创建并下载文件
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json;charset=utf-8' 
      })
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = finalFilename
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)

      initServices()
      message?.success(`${templates.length}个模板导出成功`)

    } catch (error) {
      console.error('批量导出失败:', error)
      initServices()
      message?.error(`批量导出失败: ${error}`)
    }
  }

  /**
   * 导入模板示例数据
   * @returns 示例模板数据
   */
  static getExampleTemplate(): Partial<CRFTemplate> {
    const exampleData = createDefaultTemplateData()
    
    // 扩展示例数据
    exampleData.formStructure!.sections = [
      {
        id: 'basic_info',
        title: '基本信息',
        description: '受试者基本信息',
        collapsible: false,
        collapsed: false,
        components: [
          {
            id: 'subject_id',
            type: 'input',
            label: '受试者编号',
            placeholder: '请输入受试者编号',
            required: true,
            validation: [
              { type: 'required', message: '受试者编号不能为空' }
            ]
          },
          {
            id: 'age',
            type: 'number',
            label: '年龄',
            placeholder: '请输入年龄',
            required: true,
            validation: [
              { type: 'required', message: '年龄不能为空' },
              { type: 'min', value: 0, message: '年龄不能小于0' },
              { type: 'max', value: 150, message: '年龄不能大于150' }
            ]
          },
          {
            id: 'gender',
            type: 'radio',
            label: '性别',
            required: true,
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' }
            ],
            validation: [
              { type: 'required', message: '请选择性别' }
            ]
          }
        ]
      },
      {
        id: 'medical_history',
        title: '病史信息',
        description: '受试者既往病史',
        collapsible: true,
        collapsed: false,
        components: [
          {
            id: 'primary_diagnosis',
            type: 'textarea',
            label: '主要诊断',
            placeholder: '请输入主要诊断信息',
            required: true,
            validation: [
              { type: 'required', message: '主要诊断不能为空' }
            ]
          },
          {
            id: 'allergies',
            type: 'checkbox',
            label: '过敏史',
            options: [
              { label: '药物过敏', value: 'drug' },
              { label: '食物过敏', value: 'food' },
              { label: '其他过敏', value: 'other' },
              { label: '无过敏史', value: 'none' }
            ]
          }
        ]
      }
    ]

    return {
      name: 'example_template',
      title: '示例CRF模板',
      description: '这是一个示例CRF模板，包含基本信息和病史信息收集',
      keyword: 'example,demo,template',
      version: '1.0.0',
      status: 'draft',
      template_data: {
        ...exampleData,
        formStructure: {
          ...exampleData.formStructure,
          sections: exampleData.formStructure?.sections || []
        }
      } as {
        pageConfig?: Record<string, unknown>
        formStructure?: Record<string, unknown>
        componentConfigs?: Record<string, unknown>
        validationRules?: Record<string, unknown>
        styleConfig?: Record<string, unknown>
        [key: string]: unknown
      },
      permissions: {
        view: ['all'],
        edit: ['admin', 'editor'],
        delete: ['admin']
      }
    }
  }

  /**
   * 读取文件为文本
   * @param file 文件对象
   * @returns Promise<文件内容>
   */
  private static readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (e) => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  /**
   * 验证导入数据结构
   * @param data 导入的数据
   * @returns 验证结果
   */
  private static validateImportData(data: Record<string, unknown>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data || typeof data !== 'object') {
      errors.push('文件内容必须是有效的JSON对象')
      return { valid: false, errors }
    }

    // 检查是否是批量导入
    if (data.templates && Array.isArray(data.templates)) {
      // 批量导入暂不支持
      errors.push('当前版本不支持批量导入，请分别导入单个模板文件')
      return { valid: false, errors }
    }

    // 检查必需字段
    if (!data.name || typeof data.name !== 'string') {
      errors.push('缺少有效的模板名称 (name)')
    }

    if (!data.title || typeof data.title !== 'string') {
      errors.push('缺少有效的模板标题 (title)')
    }

    if (!data.template_data || typeof data.template_data !== 'object') {
      errors.push('缺少有效的模板数据 (template_data)')
    }

    return { valid: errors.length === 0, errors }
  }
}