// 定义本地类型，避免依赖旧的store
export interface UnifiedFormSection {
  id: string
  name: string
  level?: number
  parentId?: string
  order?: number
  collapsed?: boolean
  blocks: UnifiedComponentBlock[]
  children: UnifiedFormSection[]
}

export interface UnifiedComponentBlock {
  id: string
  code?: string
  name: string
  title: string
  type: string
  sectionId?: string
  order?: number
  props?: Record<string, unknown>
  [key: string]: unknown
}
import type { ComponentConfig } from '@crf/types'
import { cloneSections } from './deep-clone'

// 完整的页面数据接口
export interface CompletePageData {
    // 元数据
    metadata: {
        version: string
        exportTime: string
        title: string
        description?: string
        createdBy?: string
        tags?: string[]
    }

    // 页面配置
    pageConfig: {
        title?: string
        description?: string
        keyword?: string
        [key: string]: unknown
    }

    // 章节结构（完整的层级结构）
    sections: any[] // 使用any类型避免与SectionData和UnifiedFormSection之间的类型冲突

    // 表单数据（用户填写的值）
    formData: Record<string, unknown>

    // 验证结果
    validationResults: Record<string, {
        isValid: boolean
        errors: string[]
    }>

    // 组件配置（每个组件的详细配置）
    componentConfigs: Record<string, ComponentConfig>
}

// 导出选项
export interface ExportPageOptions {
    includeFormData?: boolean      // 是否包含表单数据
    includeValidation?: boolean    // 是否包含验证结果
    selectedSections?: string[]    // 选择的章节ID列表
    format?: 'json' | 'compressed' // 导出格式
}

// 导入结果
export interface ImportResult {
    success: boolean
    data?: CompletePageData
    error?: string
    warnings?: string[]
}

/**
 * 页面数据管理器
 * 负责完整页面数据的导出和导入
 */
export class PageDataManager {

    /**
     * 导出完整的页面数据
     */
    static exportPageData({
        sections,
        formData,
        validationResults,
        pageConfig = {},
        options = {}
    }: {
        sections: UnifiedFormSection[]
        formData: Record<string, unknown>
        validationResults: Record<string, { isValid: boolean; errors: string[] }>
        pageConfig?: Record<string, unknown>
        options?: ExportPageOptions
    }): CompletePageData {
        const {
            includeFormData = true,
            includeValidation = true,
            selectedSections = []
        } = options

        // 过滤选中的章节
        const filteredSections = selectedSections.length > 0
            ? this.filterSectionsByIds(sections, selectedSections)
            : sections

        // 将UnifiedFormSection转换为SectionData格式
        const convertToSectionData = (section: UnifiedFormSection): any => {
            return {
                ...section,
                blocks: section.blocks.map(block => ({
                    ...block,
                    props: block.props || {}
                })),
                children: Array.isArray(section.children) 
                    ? section.children.map(child => convertToSectionData(child))
                    : []
            };
        };

        const sectionDataArray = filteredSections.map(section => convertToSectionData(section));

        // 收集所有组件配置
        const componentConfigs = this.collectComponentConfigs(filteredSections)

        // 过滤表单数据（只保留选中章节的数据）
        const filteredFormData = includeFormData
            ? this.filterFormDataBySections(formData, filteredSections)
            : {}

        // 过滤验证结果
        const filteredValidationResults = includeValidation
            ? this.filterValidationBySections(validationResults as Record<string, unknown>, filteredSections)
            : {}

        return {
            metadata: {
                version: '1.0.0',
                exportTime: new Date().toISOString(),
                title: String(pageConfig.title) || '未命名页面',
                description: pageConfig.description ? String(pageConfig.description) : undefined,
                createdBy: 'CRF编辑器',
                tags: ['crf', 'form', 'export']
            },
            pageConfig: pageConfig as {
                title?: string;
                description?: string;
                keyword?: string;
                [key: string]: unknown;
            },
            sections: sectionDataArray,
            formData: filteredFormData,
            validationResults: filteredValidationResults,
            componentConfigs
        }
    }

    /**
     * 导入页面数据并验证
     */
    static importPageData(data: Record<string, unknown>): ImportResult {
        try {
            // 验证数据结构
            const validation = this.validatePageData(data)
            if (!validation.isValid) {
                return {
                    success: false,
                    error: `数据格式验证失败: ${validation.errors.join(', ')}`
                }
            }

            // 数据转换和清理
            const cleanedData = this.cleanAndNormalizeData(data)

            return {
                success: true,
                data: cleanedData,
                warnings: validation.warnings
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '导入失败'
            }
        }
    }

    /**
     * 验证页面数据结构
     */
    private static validatePageData(data: Record<string, unknown>): {
        isValid: boolean
        errors: string[]
        warnings: string[]
    } {
        const errors: string[] = []
        const warnings: string[] = []

        // 检查必要字段
        if (!data) {
            errors.push('数据为空')
            return { isValid: false, errors, warnings }
        }

        if (!data.metadata) {
            errors.push('缺少元数据')
        }

        if (!Array.isArray(data.sections)) {
            errors.push('章节数据格式错误')
        } else {
            // 验证章节结构
            const sectionValidation = this.validateSections(data.sections)
            errors.push(...sectionValidation.errors)
            warnings.push(...sectionValidation.warnings)
        }

        if (data.formData && typeof data.formData !== 'object') {
            errors.push('表单数据格式错误')
        }

        if (data.componentConfigs && typeof data.componentConfigs !== 'object') {
            errors.push('组件配置格式错误')
        }

        // 版本兼容性检查
        const metadata = typeof data.metadata === 'object' && data.metadata !== null ? data.metadata as Record<string, unknown> : {};
        if (metadata.version) {
            const versionStr = String(metadata.version);
            if (!this.isVersionCompatible(versionStr)) {
                warnings.push(`版本 ${versionStr} 可能不完全兼容当前版本`)
            }
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        }
    }

    /**
     * 验证章节结构
     */
    private static validateSections(sections: unknown[]): {
        errors: string[]
        warnings: string[]
    } {
        const errors: string[] = []
        const warnings: string[] = []
        const sectionIds = new Set<string>()

        const validateSection = (section: Record<string, unknown>, level: number = 0) => {
            if (!section.id) {
                errors.push('章节缺少ID')
                return
            }

            if (sectionIds.has(String(section.id))) {
                errors.push(`重复的章节ID: ${String(section.id)}`)
            }
            sectionIds.add(String(section.id))

            if (!section.name) {
                warnings.push(`章节 ${String(section.id)} 缺少名称`)
            }

            if (!Array.isArray(section.blocks)) {
                errors.push(`章节 ${String(section.id)} 的blocks字段格式错误`)
            } else {
                // 验证组件块
                section.blocks.forEach((block: Record<string, unknown>, index: number) => {
                    if (!block.id) {
                        errors.push(`章节 ${String(section.id)} 的第 ${index + 1} 个组件缺少ID`)
                    }
                    if (!block.code) {
                        errors.push(`组件 ${block.id ? String(block.id) : index} 缺少code字段`)
                    }
                })
            }

            // 递归验证子章节
            if (section.children && Array.isArray(section.children)) {
                section.children.forEach((child: unknown) => {
                    validateSection(child as Record<string, unknown>, level + 1)
                })
            }
        }

        sections.forEach(section => validateSection(section as Record<string, unknown>))

        return { errors, warnings }
    }

    /**
     * 根据章节ID过滤章节
     */
    private static filterSectionsByIds(
        sections: UnifiedFormSection[],
        selectedIds: string[]
    ): UnifiedFormSection[] {
        const filterSection = (section: UnifiedFormSection): UnifiedFormSection | null => {
            if (!selectedIds.includes(section.id)) {
                return null
            }

            const filteredChildren = section.children
                ?.map(filterSection)
                .filter((child): child is UnifiedFormSection => child !== null)

            return {
                ...section,
                children: filteredChildren?.length ? filteredChildren : []
            }
        }

        return sections
            .map(filterSection)
            .filter((section): section is UnifiedFormSection => section !== null)
    }

    /**
     * 收集所有组件配置
     */
    private static collectComponentConfigs(sections: UnifiedFormSection[]): Record<string, ComponentConfig> {
        const configs: Record<string, ComponentConfig> = {}

        const collectFromSection = (section: UnifiedFormSection) => {
            section.blocks.forEach(block => {
                configs[block.id] = {
                    type: String(block.code || block.type || 'text'),
                    props: typeof block.props === 'object' && block.props !== null ? 
                        { ...(block.props as Record<string, unknown>) } : {}
                }
            })

            if (section.children && Array.isArray(section.children)) {
                section.children.forEach(collectFromSection)
            }
        }

        sections.forEach(collectFromSection)
        return configs
    }

    /**
     * 根据章节过滤表单数据
     */
    private static filterFormDataBySections(
        formData: Record<string, unknown>,
        sections: UnifiedFormSection[]
    ): Record<string, unknown> {
        const validBlockIds = new Set<string>()

        const collectBlockIds = (section: UnifiedFormSection) => {
            section.blocks.forEach(block => validBlockIds.add(block.id))
            if (section.children) {
                section.children.forEach(collectBlockIds)
            }
        }

        sections.forEach(collectBlockIds)

        const filteredData: Record<string, unknown> = {}
        Object.entries(formData).forEach(([key, value]) => {
            if (validBlockIds.has(key)) {
                filteredData[key] = value
            }
        })

        return filteredData
    }

    /**
     * 根据章节过滤验证结果
     */
    private static filterValidationBySections(
        validationResults: Record<string, unknown>,
        sections: UnifiedFormSection[]
    ): Record<string, { isValid: boolean; errors: string[] }> {
        const filteredData = this.filterFormDataBySections(validationResults, sections);
        const typedResults: Record<string, { isValid: boolean; errors: string[] }> = {};
        
        // 转换为正确的类型
        Object.entries(filteredData).forEach(([key, value]) => {
            if (typeof value === 'object' && value !== null) {
                const valueObj = value as Record<string, unknown>;
                typedResults[key] = {
                    isValid: Boolean(valueObj.isValid),
                    errors: Array.isArray(valueObj.errors) ? valueObj.errors.map(err => String(err)) : []
                };
            } else {
                typedResults[key] = {
                    isValid: false,
                    errors: ['无效的验证结果']
                };
            }
        });
        
        return typedResults;
    }



    /**
     * 清理和标准化数据
     */
    private static cleanAndNormalizeData(data: Record<string, unknown>): CompletePageData {
        // 标准化数据结构
        const metadata = typeof data.metadata === 'object' && data.metadata !== null ? data.metadata as Record<string, unknown> : {};
        const normalized: CompletePageData = {
            metadata: {
                version: String(metadata.version || '1.0.0'),
                exportTime: String(metadata.exportTime || new Date().toISOString()),
                title: String(metadata.title || '未命名表单'),
                description: metadata.description ? String(metadata.description) : undefined,
                createdBy: metadata.createdBy ? String(metadata.createdBy) : undefined,
                tags: Array.isArray(metadata.tags) ? metadata.tags.map(tag => String(tag)) : []
            },
            pageConfig: (typeof data.pageConfig === 'object' && data.pageConfig !== null ? 
                data.pageConfig as {
                    title?: string;
                    description?: string;
                    keyword?: string;
                    [key: string]: unknown;
                } : 
                {}
            ),
            sections: this.normalizeSections(Array.isArray(data.sections) ? data.sections : []),
            formData: (typeof data.formData === 'object' && data.formData !== null ? 
                data.formData as Record<string, unknown> : 
                {}
            ),
            validationResults: (typeof data.validationResults === 'object' && data.validationResults !== null ? 
                data.validationResults as Record<string, { isValid: boolean; errors: string[] }> : 
                {}
            ),
            componentConfigs: (typeof data.componentConfigs === 'object' && data.componentConfigs !== null ? 
                data.componentConfigs as Record<string, ComponentConfig> : 
                {}
            )
        }

        return normalized
    }

    /**
     * 标准化章节数据
     */
    private static normalizeSections(sections: unknown[]): UnifiedFormSection[] {
        const normalizeSection = (section: Record<string, unknown>, level: number = 0): UnifiedFormSection => {
            return {
                id: String(section.id || ''),
                name: String(section.name || '未命名章节'),
                level: typeof section.level === 'number' ? section.level : level,
                parentId: section.parentId ? String(section.parentId) : undefined,
                order: typeof section.order === 'number' ? section.order : level,
                collapsed: Boolean(section.collapsed),
                blocks: (Array.isArray(section.blocks) ? section.blocks : []).map((block: unknown, index: number) => {
                    const blockObj = block as Record<string, unknown>;
                    return {
                        id: String(blockObj.id || ''),
                        type: String(blockObj.type || blockObj.code || 'text'),
                        code: String(blockObj.code || 'text'),
                        name: String(blockObj.name || blockObj.title || `组件${index + 1}`),
                        title: String(blockObj.title || blockObj.name || `组件${index + 1}`),
                        props: typeof blockObj.props === 'object' && blockObj.props !== null ? 
                            blockObj.props as Record<string, unknown> : {},
                        sectionId: String(section.id || ''),
                        order: Number(blockObj.order || index)
                    };
                }),
                children: (Array.isArray(section.children) ? section.children : []).map((child: unknown) =>
                    normalizeSection(child as Record<string, unknown>, level + 1)
                )
            }
        }

        return sections.map(section => normalizeSection(section as Record<string, unknown>))
    }

    /**
     * 检查版本兼容性
     */
    private static isVersionCompatible(version: string): boolean {
        // 简单的版本兼容性检查
        const supportedVersions = ['1.0.0', '1.0.1', '1.1.0']
        return supportedVersions.includes(version)
    }

    /**
     * 生成导出文件名
     */
    static generateFileName(pageTitle?: string, format: 'json' | 'compressed' = 'json'): string {
        const timestamp = new Date().toISOString().split('T')[0]
        const title = pageTitle ? pageTitle.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_') : 'crf_page'
        const extension = format === 'compressed' ? 'zip' : 'json'

        return `${title}_${timestamp}.${extension}`
    }

    /**
     * 压缩数据（可选功能）
     */
    static compressData(data: CompletePageData): string {
        // 这里可以实现数据压缩逻辑
        // 例如移除不必要的字段、压缩JSON等
        return JSON.stringify(data)
    }
}