/**
 * Schema字段映射服务
 * 用于将表单数据与schema字段定义关联，实现数据的正确解析和显示
 */

export interface FieldInfo {
  id: string
  label: string
  type: string
  required?: boolean
  options?: Array<{ label: string; value: unknown }>
  placeholder?: string
  description?: string
  defaultValue?: unknown
}

export interface SchemaField {
  type: string
  label?: string
  model?: string
  key?: string
  config?: {
    label?: string
    placeholder?: string
    required?: boolean
    options?: Array<{ label: string; value: unknown }>
    [key: string]: unknown
  }
  children?: SchemaField[]
}

export interface FormSchema {
  list: SchemaField[]
  config?: {
    title?: string
    description?: string
  }
}

/**
 * Schema字段映射器类
 */
export class SchemaFieldMapper {
  private fieldMap = new Map<string, FieldInfo>()
  
  /**
   * 解析schema并构建字段映射
   */
  parseSchema(schema: FormSchema | string): void {
    this.fieldMap.clear()
    
    let parsedSchema: FormSchema
    
    // 处理字符串格式的schema
    if (typeof schema === 'string') {
      try {
        parsedSchema = JSON.parse(schema)
      } catch (error) {
        console.error('Schema解析失败:', error)
        return
      }
    } else {
      parsedSchema = schema
    }
    
    // 解析schema中的字段
    if (parsedSchema.list && Array.isArray(parsedSchema.list)) {
      this.parseFieldList(parsedSchema.list)
    }
  }
  
  /**
   * 递归解析字段列表
   */
  private parseFieldList(fields: SchemaField[]): void {
    fields.forEach(field => {
      this.parseField(field)
      
      // 递归处理子字段
      if (field.children && Array.isArray(field.children)) {
        this.parseFieldList(field.children)
      }
    })
  }
  
  /**
   * 解析单个字段
   */
  private parseField(field: SchemaField): void {
    // 获取字段标识符
    const fieldId = this.getFieldId(field)
    if (!fieldId) return
    
    // 构建字段信息
    const fieldInfo: FieldInfo = {
      id: fieldId,
      label: this.getFieldLabel(field),
      type: this.normalizeFieldType(field.type),
      required: field.config?.required || false,
      placeholder: field.config?.placeholder ? String(field.config.placeholder) : undefined,
      description: field.config?.description ? String(field.config.description) : undefined,
      defaultValue: field.config?.defaultValue
    }
    
    // 处理选项类型字段
    if (this.hasOptions(field.type)) {
      fieldInfo.options = this.parseFieldOptions(field)
    }
    
    this.fieldMap.set(fieldId, fieldInfo)
  }
  
  /**
   * 获取字段ID
   */
  private getFieldId(field: SchemaField): string | null {
    // 优先使用model，然后key，最后生成一个ID
    if (field.model) return field.model
    if (field.key) return field.key
    
    // 如果都没有，尝试从配置中获取
    if (field.config?.model) return field.config.model as string
    if (field.config?.key) return field.config.key as string
    
    // 根据类型和标签生成ID
    const label = this.getFieldLabel(field)
    const type = field.type || 'unknown'
    return `${type}-${label.replace(/\s+/g, '-').toLowerCase()}-${Math.random().toString(36).substr(2, 8)}`
  }
  
  /**
   * 获取字段标签
   */
  private getFieldLabel(field: SchemaField): string {
    return field.config?.label || field.label || field.type || '未命名字段'
  }
  
  /**
   * 标准化字段类型
   */
  private normalizeFieldType(type: string): string {
    const typeMap: Record<string, string> = {
      'input': 'text',
      'textarea': 'textarea',
      'number': 'number',
      'date': 'date',
      'time': 'time',
      'datetime': 'datetime',
      'select': 'select',
      'radio': 'radio',
      'checkbox': 'checkbox',
      'switch': 'boolean',
      'slider': 'number',
      'rate': 'number',
      'upload': 'file',
      'color': 'color',
      'text': 'static'
    }
    
    return typeMap[type] || type
  }
  
  /**
   * 检查字段是否有选项
   */
  private hasOptions(type: string): boolean {
    return ['select', 'radio', 'checkbox'].includes(type)
  }
  
  /**
   * 解析字段选项
   */
  private parseFieldOptions(field: SchemaField): Array<{ label: string; value: unknown }> {
    const options = field.config?.options
    if (!options || !Array.isArray(options)) return []
    
    return options.map(option => {
      if (typeof option === 'string') {
        return { label: option, value: option }
      }
      if (typeof option === 'object' && option !== null) {
        return {
          label: String(option.label || (option as Record<string, unknown>).text || option.value || option),
          value: option.value !== undefined ? option.value : option
        }
      }
      return { label: String(option), value: option }
    })
  }
  
  /**
   * 获取字段信息
   */
  getFieldInfo(fieldId: string): FieldInfo | null {
    return this.fieldMap.get(fieldId) || null
  }
  
  /**
   * 获取所有字段信息
   */
  getAllFields(): FieldInfo[] {
    return Array.from(this.fieldMap.values())
  }
  
  /**
   * 格式化字段值用于显示
   */
  formatFieldValue(fieldId: string, value: unknown): string {
    const fieldInfo = this.getFieldInfo(fieldId)
    if (!fieldInfo) {
      return this.formatGenericValue(value)
    }
    
    // 根据字段类型格式化值
    switch (fieldInfo.type) {
      case 'boolean':
        return value ? '是' : '否'
        
      case 'select':
      case 'radio':
        const selectedOption = fieldInfo.options?.find(opt => opt.value === value)
        return selectedOption?.label || String(value || '-')
        
      case 'checkbox':
        if (Array.isArray(value)) {
          const selectedLabels = value.map(val => {
            const option = fieldInfo.options?.find(opt => opt.value === val)
            return option?.label || String(val)
          })
          return selectedLabels.join(', ')
        }
        return String(value || '-')
        
      case 'date':
        if (value) {
          try {
            return new Date(value as string | number).toLocaleDateString('zh-CN')
          } catch {
            return String(value)
          }
        }
        return '-'
        
      case 'datetime':
        if (value) {
          try {
            return new Date(value as string | number).toLocaleString('zh-CN')
          } catch {
            return String(value)
          }
        }
        return '-'
        
      case 'time':
        return String(value || '-')
        
      case 'file':
        if (Array.isArray(value)) {
          return `${value.length} 个文件`
        }
        return value ? '1 个文件' : '-'
        
      default:
        return this.formatGenericValue(value)
    }
  }
  
  /**
   * 通用值格式化
   */
  private formatGenericValue(value: unknown): string {
    if (value === null || value === undefined) return '-'
    
    if (Array.isArray(value)) {
      return value.map(v => String(v)).join(', ')
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value)
    }
    
    return String(value)
  }
  
  /**
   * 获取字段显示宽度
   */
  getFieldWidth(fieldId: string): number {
    const fieldInfo = this.getFieldInfo(fieldId)
    if (!fieldInfo) return 200
    
    const widthMap: Record<string, number> = {
      text: 200,
      textarea: 300,
      number: 120,
      date: 150,
      datetime: 180,
      time: 120,
      select: 180,
      radio: 150,
      checkbox: 250,
      boolean: 100,
      file: 150,
      static: 200
    }
    
    return widthMap[fieldInfo.type] || 200
  }
  
  /**
   * 检查字段是否可排序
   */
  isFieldSortable(fieldId: string): boolean {
    const fieldInfo = this.getFieldInfo(fieldId)
    if (!fieldInfo) return false
    
    const sortableTypes = ['text', 'number', 'date', 'datetime', 'time']
    return sortableTypes.includes(fieldInfo.type)
  }
  
  /**
   * 检查字段是否可筛选
   */
  isFieldFilterable(fieldId: string): boolean {
    const fieldInfo = this.getFieldInfo(fieldId)
    if (!fieldInfo) return false
    
    const filterableTypes = ['text', 'select', 'radio', 'boolean']
    return filterableTypes.includes(fieldInfo.type)
  }
}

/**
 * 创建schema字段映射器实例
 */
export function createSchemaFieldMapper(schema?: FormSchema | string): SchemaFieldMapper {
  const mapper = new SchemaFieldMapper()
  if (schema) {
    mapper.parseSchema(schema)
  }
  return mapper
}

/**
 * 从实例数据中提取字段信息（兼容模式）
 */
export function extractFieldsFromInstances(instances: Record<string, unknown>[]): FieldInfo[] {
  const fieldMap = new Map<string, FieldInfo>()
  
  instances.forEach(instance => {
    if (instance.form_data && typeof instance.form_data === 'object') {
      Object.keys(instance.form_data).forEach(fieldId => {
        if (!fieldMap.has(fieldId)) {
          const sampleValue = instance.form_data[fieldId]
          const fieldInfo = createFieldInfoFromSample(fieldId, sampleValue)
          fieldMap.set(fieldId, fieldInfo)
        }
      })
    }
  })
  
  return Array.from(fieldMap.values())
}

/**
 * 从样本值创建字段信息（兼容模式）
 */
function createFieldInfoFromSample(fieldId: string, sampleValue: unknown): FieldInfo {
  // 尝试从fieldId中解析类型和标签
  const parts = fieldId.split('-')
  const type = parts[0] || 'text'
  
  // 生成更友好的标签
  let label = fieldId
  if (parts.length > 1) {
    label = parts.slice(1).join('-')
    // 转换为中文标签
    label = translateFieldLabel(label)
  }
  
  // 根据样本值推断类型
  let inferredType = inferTypeFromValue(sampleValue)
  if (type !== 'text') {
    inferredType = type
  }
  
  return {
    id: fieldId,
    label,
    type: inferredType,
    required: false
  }
}

/**
 * 从值推断字段类型
 */
function inferTypeFromValue(value: unknown): string {
  if (typeof value === 'boolean') return 'boolean'
  if (typeof value === 'number') return 'number'
  if (Array.isArray(value)) return 'checkbox'
  if (typeof value === 'string') {
    // 尝试检测日期格式
    if (/^\d{4}-\d{2}-\d{2}$/.test(value)) return 'date'
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) return 'datetime'
    if (/^\d{2}:\d{2}/.test(value)) return 'time'
  }
  return 'text'
}

/**
 * 翻译字段标签为中文
 */
function translateFieldLabel(label: string): string {
  const translations: Record<string, string> = {
    'patient-name': '患者姓名',
    'patient-age': '患者年龄',
    'gender': '性别',
    'medical-history': '病史',
    'symptoms': '症状',
    'severity': '严重程度',
    'visit-date': '随访日期',
    'name': '姓名',
    'age': '年龄',
    'phone': '电话',
    'email': '邮箱',
    'address': '地址',
    'date': '日期',
    'time': '时间',
    'remarks': '备注',
    'notes': '说明'
  }
  
  return translations[label] || label.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}