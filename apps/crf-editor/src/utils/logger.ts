import { createDiscrete<PERSON><PERSON> } from 'naive-ui'

const { message } = createDiscreteApi(['message'])

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export interface LogContext {
  component?: string
  action?: string
  userId?: string
  sessionId?: string
  timestamp?: number
  [key: string]: unknown
}

export class Logger {
  private static instance: Logger
  private isDev = import.meta.env.DEV

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString()
    const contextStr = context ? JSON.stringify(context) : ''
    return `[${timestamp}] [${level.toUpperCase()}] ${message} ${contextStr}`
  }

  debug(message: string, context?: LogContext): void {
    if (this.isDev) {
      console.debug(this.formatMessage(LogLevel.DEBUG, message, context))
    }
  }

  info(message: string, context?: LogContext): void {
    console.info(this.formatMessage(LogLevel.INFO, message, context))
  }

  warn(messageText: string, context?: LogContext): void {
    console.warn(this.formatMessage(LogLevel.WARN, messageText, context))
    if (!this.isDev) {
      message.warning(messageText)
    }
  }

  error(messageText: string, error?: Error, context?: LogContext): void {
    const errorContext = {
      ...context,
      stack: error?.stack,
      errorMessage: error?.message
    }
    console.error(this.formatMessage(LogLevel.ERROR, messageText, errorContext))
    
    if (!this.isDev) {
      // 在生产环境中发送错误到监控服务
      this.sendToMonitoring(messageText, error, errorContext)
    }
    
    message.error(messageText)
  }

  private async sendToMonitoring(message: string, error?: Error, context?: LogContext): Promise<void> {
    try {
      // 这里可以集成 Sentry、LogRocket 等监控服务
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          error: error?.message,
          stack: error?.stack,
          context,
          timestamp: Date.now()
        })
      })
    } catch (e) {
      console.error('Failed to send error to monitoring service:', e)
    }
  }
}

export const logger = Logger.getInstance()