/**
 * 错误消息映射配置
 * 将错误类型映射为用户友好的提示消息
 */

export interface ErrorMessageConfig {
  network: string
  timeout: string
  auth: string
  forbidden: string
  notFound: string
  conflict: string
  server: string
  validation: string
  default: string
}

// 默认错误消息配置
export const defaultErrorMessages: ErrorMessageConfig = {
  network: '网络连接失败，请检查网络设置',
  timeout: '请求超时，请稍后重试',
  auth: '登录已过期，请重新登录',
  forbidden: '权限不足，无法执行此操作',
  notFound: '请求的资源不存在',
  conflict: '数据冲突，请刷新后重试',
  server: '服务器错误，请稍后重试',
  validation: '输入信息有误，请检查后重试',
  default: '操作失败，请稍后重试'
}

/**
 * 根据错误信息获取用户友好的错误消息
 * @param error 错误对象
 * @param customMessages 自定义错误消息配置
 * @returns 用户友好的错误消息
 */
export function getErrorMessage(
  error: Error | unknown,
  customMessages?: Partial<ErrorMessageConfig>
): string {
  const messages = { ...defaultErrorMessages, ...customMessages }
  const errorMessage = error && typeof error === 'object' && 'message' in error
    ? String(error.message)
    : ''
  
  // 网络相关错误
  if (
    errorMessage.includes('Failed to fetch') ||
    errorMessage.includes('Network Error') ||
    errorMessage.includes('网络连接失败')
  ) {
    return messages.network
  }
  
  // 超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
    return messages.timeout
  }
  
  // 认证错误
  if (
    errorMessage.includes('认证失败') ||
    errorMessage.includes('登录失败') ||
    errorMessage.includes('Unauthorized')
  ) {
    return messages.auth
  }
  
  // 权限错误
  if (errorMessage.includes('权限不足') || errorMessage.includes('Forbidden')) {
    return messages.forbidden
  }
  
  // 资源不存在
  if (errorMessage.includes('Not Found') || errorMessage.includes('不存在')) {
    return messages.notFound
  }
  
  // 数据冲突
  if (errorMessage.includes('Conflict') || errorMessage.includes('冲突')) {
    return messages.conflict
  }
  
  // 服务器错误
  if (
    errorMessage.includes('Internal Server Error') ||
    errorMessage.includes('服务器错误') ||
    errorMessage.includes('500')
  ) {
    return messages.server
  }
  
  // 验证错误
  if (
    errorMessage.includes('validation') ||
    errorMessage.includes('验证失败') ||
    errorMessage.includes('格式错误')
  ) {
    return messages.validation
  }
  
  // 如果有具体的错误消息且不是技术性错误，直接返回
  if (errorMessage && !errorMessage.includes('Error:') && !errorMessage.includes('Exception:')) {
    return errorMessage
  }
  
  // 默认错误消息
  return messages.default
}

/**
 * 根据HTTP状态码获取错误消息
 * @param status HTTP状态码
 * @param customMessages 自定义错误消息配置
 * @returns 用户友好的错误消息
 */
export function getErrorMessageByStatus(
  status: number,
  customMessages?: Partial<ErrorMessageConfig>
): string {
  const messages = { ...defaultErrorMessages, ...customMessages }
  
  switch (status) {
    case 400:
      return messages.validation
    case 401:
      return messages.auth
    case 403:
      return messages.forbidden
    case 404:
      return messages.notFound
    case 409:
      return messages.conflict
    case 500:
    case 502:
    case 503:
    case 504:
      return messages.server
    default:
      return messages.default
  }
}