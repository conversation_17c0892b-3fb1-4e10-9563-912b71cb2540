/**
 * 环境配置工具类
 * 提供统一的环境变量访问和类型转换
 */

/**
 * 环境变量类型转换工具
 */
const EnvUtils = {
    /**
     * 将字符串转换为布尔值
     * @param value 环境变量值
     * @returns 布尔值
     */
    toBool(value: string | undefined): boolean {
        return value === 'true'
    },

    /**
     * 将字符串转换为数字
     * @param value 环境变量值
     * @param defaultValue 默认值
     * @returns 数字值
     */
    toNumber(value: string | undefined, defaultValue = 0): number {
        if (!value) return defaultValue
        const num = Number(value)
        return isNaN(num) ? defaultValue : num
    },

    /**
     * 将字符串转换为字符串数组
     * @param value 环境变量值，用逗号分隔
     * @param defaultValue 默认值
     * @returns 字符串数组
     */
    toArray(value: string | undefined, defaultValue: string[] = []): string[] {
        if (!value) return defaultValue
        return value.split(',').map(item => item.trim()).filter(Boolean)
    }
}

/**
 * 环境配置类
 */
export class EnvConfig {
    // 应用基础信息
    static get appTitle(): string {
        return import.meta.env.VITE_APP_TITLE || 'CRF表单编辑器'
    }

    static get appVersion(): string {
        return import.meta.env.VITE_APP_VERSION || '1.0.0'
    }

    static get appDescription(): string {
        return import.meta.env.VITE_APP_DESCRIPTION || '可视化表单编辑器'
    }

    // API 配置
    static get apiBaseUrl(): string {
        // 开发环境使用相对路径，让Vite代理处理跨域
        if (import.meta.env.DEV) {
            return ''  // 空字符串，让相对路径直接使用
        }
        return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'
    }

    static get apiTimeout(): number {
        return EnvUtils.toNumber(import.meta.env.VITE_API_TIMEOUT, 10000)
    }

    // 应用配置
    static get baseUrl(): string {
        return import.meta.env.VITE_APP_BASE_URL || '/'
    }

    static get publicPath(): string {
        return import.meta.env.VITE_APP_PUBLIC_PATH || '/'
    }

    // 功能开关
    static get enableMock(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_ENABLE_MOCK)
    }

    static get enableDevtools(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_ENABLE_DEVTOOLS)
    }

    static get enableConsoleLog(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_ENABLE_CONSOLE_LOG)
    }

    static get enableErrorLog(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_ENABLE_ERROR_LOG)
    }

    // 开发工具
    static get enableVueDevtools(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_ENABLE_VUE_DEVTOOLS)
    }

    static get enablePerformanceMonitor(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_ENABLE_PERFORMANCE_MONITOR)
    }

    // 构建配置
    static get buildSourcemap(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_BUILD_SOURCEMAP)
    }

    static get buildAnalyze(): boolean {
        return EnvUtils.toBool(import.meta.env.VITE_BUILD_ANALYZE)
    }

    // 其他配置
    static get storagePrefix(): string {
        return import.meta.env.VITE_APP_STORAGE_PREFIX || 'crf_editor_'
    }

    static get defaultLanguage(): string {
        return import.meta.env.VITE_APP_DEFAULT_LANGUAGE || 'zh-CN'
    }

    // 环境判断
    static get isDev(): boolean {
        return import.meta.env.DEV
    }

    static get isProd(): boolean {
        return import.meta.env.PROD
    }

    static get isTest(): boolean {
        return import.meta.env.MODE === 'test'
    }

    // 获取当前环境
    static get mode(): string {
        return import.meta.env.MODE
    }

    // 打印环境信息（仅开发环境）
    static printEnvInfo(): void {
        if (!this.isDev || !this.enableConsoleLog) return

        console.group('🌍 环境配置信息')
        console.log('📱 应用标题:', this.appTitle)
        console.log('🔢 应用版本:', this.appVersion)
        console.log('🌐 API 地址:', this.apiBaseUrl)
        console.log('⏱️ API 超时:', this.apiTimeout + 'ms')
        console.log('🔧 当前环境:', this.mode)
        console.log('🛠️ 开发工具:', {
            devtools: this.enableDevtools,
            vueDevtools: this.enableVueDevtools,
            performanceMonitor: this.enablePerformanceMonitor
        })
        console.log('🎛️ 功能开关:', {
            mock: this.enableMock,
            consoleLog: this.enableConsoleLog,
            errorLog: this.enableErrorLog
        })
        console.groupEnd()
    }
}

// 导出单例
export const env = EnvConfig