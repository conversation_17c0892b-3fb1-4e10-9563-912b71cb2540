{"network": {"connectionFailed": "Network connection failed", "timeout": "Request timeout", "serverError": "Server error", "notFound": "The requested resource was not found", "unauthorized": "Unauthorized access", "forbidden": "Insufficient permissions", "badRequest": "Bad request parameters"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "pattern": "Invalid format", "confirm": "The values do not match"}, "auth": {"loginFailed": "<PERSON><PERSON> failed, please check username and password", "sessionExpired": "Session expired, please login again", "accessDenied": "Access denied, insufficient permissions", "tokenInvalid": "Invalid token, please login again"}, "form": {"saveFailed": "Save failed, please try again later", "loadFailed": "Load failed, please refresh the page", "submitFailed": "Submit failed, please check network connection", "validationFailed": "Form validation failed, please check input"}, "file": {"uploadFailed": "File upload failed", "fileTooLarge": "File is too large, please select a smaller file", "invalidFileType": "Unsupported file type", "downloadFailed": "File download failed"}, "general": {"operationFailed": "Operation failed", "unknownError": "Unknown error", "tryAgain": "Please try again later", "contactSupport": "If the problem persists, please contact technical support"}}