{"buttons": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "preview": "Preview", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "start": "Start", "stop": "Stop", "pause": "Pause", "resume": "Resume", "retry": "Retry", "download": "Download", "upload": "Upload", "select": "Select", "selectAll": "Select All", "clear": "Clear", "expand": "Expand", "collapse": "Collapse", "maximize": "Maximize", "minimize": "Minimize", "fullscreen": "Fullscreen", "exit": "Exit"}, "status": {"loading": "Loading...", "saving": "Saving...", "saved": "Saved", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "pending": "Pending", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "expired": "Expired", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "syncing": "Syncing", "synced": "Synced", "draft": "Draft", "published": "Published", "archived": "Archived"}, "messages": {"welcome": "Welcome", "goodbye": "Goodbye", "thanks": "Thank you", "please": "Please", "yes": "Yes", "no": "No", "ok": "OK", "done": "Done", "ready": "Ready", "notFound": "Not Found", "noData": "No Data", "noResults": "No Results", "emptyList": "Empty List", "comingSoon": "Coming Soon", "underMaintenance": "Under Maintenance", "serviceUnavailable": "Service Unavailable", "networkError": "Network Error", "serverError": "Server Error", "permissionDenied": "Permission Denied", "accessDenied": "Access Denied", "sessionExpired": "Session Expired", "invalidInput": "Invalid Input", "required": "Required", "optional": "Optional", "recommended": "Recommended", "deprecated": "Deprecated", "experimental": "Experimental", "beta": "Beta", "new": "New", "languageChanged": "Language changed successfully", "languageChangeFailed": "Language change failed", "switchingLanguage": "Switching language..."}, "actions": {"create": "Create", "read": "Read", "update": "Update", "delete": "Delete", "view": "View", "manage": "Manage", "configure": "Configure", "customize": "Customize", "optimize": "Optimize", "analyze": "Analyze", "monitor": "Monitor", "backup": "Backup", "restore": "Rest<PERSON>", "migrate": "Migrate", "deploy": "Deploy", "build": "Build", "test": "Test", "debug": "Debug", "validate": "Validate", "verify": "Verify", "approve": "Approve", "reject": "Reject", "enable": "Enable", "disable": "Disable", "activate": "Activate", "deactivate": "Deactivate", "lock": "Lock", "unlock": "Unlock", "freeze": "Freeze", "selectLanguage": "Select Language"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This Week", "lastWeek": "Last Week", "nextWeek": "Next Week", "thisMonth": "This Month", "lastMonth": "Last Month", "nextMonth": "Next Month", "thisYear": "This Year", "lastYear": "Last Year", "nextYear": "Next Year", "second": "Second", "minute": "Minute", "hour": "Hour", "day": "Day", "week": "Week", "month": "Month", "year": "Year", "seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days", "weeks": "Weeks", "months": "Months", "years": "Years", "ago": "Ago", "later": "Later", "before": "Before", "after": "After", "since": "Since", "until": "Until", "from": "From", "to": "To"}, "units": {"byte": "Byte", "bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB", "tb": "TB", "pb": "PB", "pixel": "Pixel", "pixels": "Pixels", "percent": "Percent", "degree": "Degree", "celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit", "meter": "<PERSON>er", "kilometer": "Kilometer", "centimeter": "Centimeter", "millimeter": "Millimeter", "inch": "Inch", "foot": "Foot", "yard": "Yard", "mile": "Mile"}, "plurals": {"item": {"one": "{count} item", "other": "{count} items"}, "file": {"one": "{count} file", "other": "{count} files"}, "user": {"one": "{count} user", "other": "{count} users"}, "page": {"one": "{count} page", "other": "{count} pages"}, "result": {"one": "{count} result", "other": "{count} results"}, "record": {"one": "{count} record", "other": "{count} records"}, "character": {"one": "{count} character", "other": "{count} characters"}, "word": {"one": "{count} word", "other": "{count} words"}}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "url": "Please enter a valid URL", "phone": "Please enter a valid phone number", "number": "Please enter a valid number", "integer": "Please enter an integer", "positive": "Please enter a positive number", "negative": "Please enter a negative number", "min": "Minimum value is {min}", "max": "Maximum value is {max}", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "pattern": "Invalid format", "confirm": "Values do not match", "unique": "This value already exists", "date": "Please enter a valid date", "time": "Please enter a valid time", "dateTime": "Please enter a valid date and time", "future": "Date must be in the future", "past": "Date must be in the past", "weekday": "Must be a weekday", "weekend": "Must be a weekend"}, "permissions": {"read": "Read Permission", "write": "Write Permission", "delete": "Delete Permission", "admin": "Admin Permission", "owner": "Owner Permission", "guest": "Guest Permission", "member": "Member Permission", "moderator": "Moderator Permission", "editor": "Editor Permission", "viewer": "Viewer Permission", "contributor": "Contributor Permission", "reviewer": "Reviewer Permission", "publisher": "Publisher Permission"}, "languages": {"zh-CN": "简体中文", "zh-TW": "繁體中文", "en-US": "English", "ja-JP": "日本語", "ko-KR": "한국어", "fr-FR": "Français", "de-DE": "De<PERSON>ch", "es-ES": "Español"}, "themes": {"light": "Light Theme", "dark": "Dark Theme", "auto": "Follow System", "custom": "Custom Theme"}}