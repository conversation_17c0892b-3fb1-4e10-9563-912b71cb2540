{"dashboard": {"title": "Dashboard", "welcome": "Welcome, {name}", "stats": {"totalForms": "Total Forms", "activeForms": "Active Forms", "totalSubmissions": "Total Submissions", "todaySubmissions": "Today's Submissions"}, "recentForms": "Recent Forms", "recentSubmissions": "Recent Submissions"}, "forms": {"index": {"title": "Form List", "create": "Create New Form", "search": "Search Forms", "filterBy": "Filter by Status", "sortBy": "Sort By", "viewMode": "View Mode", "gridView": "Grid View", "listView": "List View"}, "edit": {"title": "Edit Form", "basicInfo": "Basic Information", "formDesign": "Form Design", "settings": "Form Settings", "preview": "Preview Form", "publish": "Publish Form", "saveDraft": "Save Draft", "autoSave": "Auto Save"}, "fill": {"title": "Fill Form", "submit": "Submit Form", "saveDraft": "Save Draft", "requiredField": "Required Field", "validationError": "Validation Error", "submitSuccess": "Submit Successful", "submitFailed": "Submit Failed"}}, "projects": {"index": {"title": "Project Management", "create": "Create Project", "search": "Search Projects", "filterBy": "Filter Projects", "noProjects": "No projects available"}, "create": {"title": "Create Project", "projectName": "Project Name", "projectDescription": "Project Description", "projectType": "Project Type", "startDate": "Start Date", "endDate": "End Date", "createProject": "Create Project"}}, "admin": {"index": {"title": "Admin Center", "userManagement": "User Management", "roleManagement": "Role Management", "systemSettings": "System Settings"}, "users": {"title": "User Management", "userList": "User List", "createUser": "Create User", "importUsers": "Import Users", "exportUsers": "Export Users"}, "roles": {"title": "Role Management", "roleList": "Role List", "createRole": "Create Role", "permissions": "Permission Configuration"}}}