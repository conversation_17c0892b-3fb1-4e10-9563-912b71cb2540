{"network": {"connectionFailed": "网络连接失败", "timeout": "请求超时", "serverError": "服务器错误", "notFound": "请求的资源不存在", "unauthorized": "未授权访问", "forbidden": "权限不足", "badRequest": "请求参数错误"}, "validation": {"required": "此字段为必填项", "email": "请输入有效的邮箱地址", "minLength": "最少需要 {min} 个字符", "maxLength": "最多允许 {max} 个字符", "pattern": "格式不正确", "confirm": "两次输入不一致"}, "auth": {"loginFailed": "登录失败，请检查用户名和密码", "sessionExpired": "会话已过期，请重新登录", "accessDenied": "访问被拒绝，权限不足", "tokenInvalid": "令牌无效，请重新登录"}, "form": {"saveFailed": "保存失败，请稍后重试", "loadFailed": "加载失败，请刷新页面", "submitFailed": "提交失败，请检查网络连接", "validationFailed": "表单验证失败，请检查输入"}, "file": {"uploadFailed": "文件上传失败", "fileTooLarge": "文件过大，请选择较小的文件", "invalidFileType": "不支持的文件类型", "downloadFailed": "文件下载失败"}, "general": {"operationFailed": "操作失败", "unknownError": "未知错误", "tryAgain": "请稍后重试", "contactSupport": "如问题持续，请联系技术支持"}}