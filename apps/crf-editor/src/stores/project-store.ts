import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  Project, 
  ProjectMember, 
  ProjectActivity, 
  ProjectDashboard, 
  AddMemberRequest, 
  UpdateMemberRoleRequest 
} from '@/api/projects'
import { ProjectAPI } from '@/api/projects'
import { useMessage } from 'naive-ui'

export const useProjectStore = defineStore('project', () => {
  const message = useMessage()

  // 状态
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const projectMembers = ref<ProjectMember[]>([])
  const projectActivities = ref<ProjectActivity[]>([])
  const projectDashboard = ref<ProjectDashboard | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页信息
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    pages: 0
  })

  // 计算属性
  const hasCurrentProject = computed(() => !!currentProject.value)
  const currentProjectId = computed(() => currentProject.value?.id || '')
  const isProjectOwner = computed(() => {
    if (!currentProject.value) return false
    const currentUser = getCurrentUser() // 需要从用户store获取
    return projectMembers.value.some(
      member => member.user_id === currentUser?.id && member.role === 'owner'
    )
  })

  const canManageProject = computed(() => {
    if (!currentProject.value) return false
    const currentUser = getCurrentUser()
    const currentMember = projectMembers.value.find(
      member => member.user_id === currentUser?.id
    )
    return currentMember?.permissions.project.update || false
  })

  const canManageMembers = computed(() => {
    if (!currentProject.value) return false
    const currentUser = getCurrentUser()
    const currentMember = projectMembers.value.find(
      member => member.user_id === currentUser?.id
    )
    return currentMember?.permissions.members.manage || false
  })

  // 辅助函数
  function getCurrentUser() {
    // 这里需要从用户store获取当前用户信息
    // 暂时返回null，实际实现需要导入用户store
    return null
  }

  // Actions
  const fetchProjects = async (params?: { page?: number; page_size?: number }) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.getProjects(params)
      debugger
      projects.value = response.data.projects
      const pag = response.data.pagination
      pagination.value = {
        page: Math.floor(pag.offset / pag.limit) + 1,
        pageSize: pag.limit,
        total: pag.total,
        pages: Math.ceil(pag.total / pag.limit)
      }
    } catch (err: any) {
      error.value = err.message || '获取项目列表失败'
      message.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const createProject = async (data: {
    name: string
    description?: string
    is_public?: boolean
    allow_anonymous?: boolean
  }) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.createProject(data)
      message.success(response.data.message)
      // 重新获取项目列表
      await fetchProjects()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '创建项目失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const setCurrentProject = async (projectId: string) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.getProject(projectId)
      currentProject.value = response.data.data
      
      // 同时获取项目成员信息
      await fetchProjectMembers(projectId)
      
      // 存储到localStorage用于持久化
      localStorage.setItem('current_project_id', projectId)
    } catch (err: any) {
      error.value = err.message || '获取项目详情失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProject = async (id: string, data: {
    name?: string
    description?: string
    status?: string
    is_public?: boolean
    allow_anonymous?: boolean
  }) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.updateProject(id, data)
      message.success(response.data.message)
      
      // 如果更新的是当前项目，重新获取项目信息
      if (id === currentProject.value?.id) {
        await setCurrentProject(id)
      } else {
        // 否则重新获取项目列表
        await fetchProjects()
      }
    } catch (err: any) {
      error.value = err.message || '更新项目失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteProject = async (id: string) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.deleteProject(id)
      message.success(response.data.message)
      
      // 如果删除的是当前项目，清除当前项目
      if (id === currentProject.value?.id) {
        clearCurrentProject()
      }
      
      // 重新获取项目列表
      await fetchProjects()
    } catch (err: any) {
      error.value = err.message || '删除项目失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchProjectDashboard = async (id: string) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.getProjectDashboard(id)
      projectDashboard.value = response.data.data
    } catch (err: any) {
      error.value = err.message || '获取项目仪表板失败'
      message.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchProjectMembers = async (id: string) => {
    try {
      error.value = null
      const response = await ProjectAPI.getProjectMembers(id)
      projectMembers.value = response.data.data
    } catch (err: any) {
      error.value = err.message || '获取项目成员失败'
      message.error(error.value)
    }
  }

  const addProjectMember = async (id: string, data: AddMemberRequest) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.addProjectMember(id, data)
      message.success(response.data.message)
      
      // 重新获取项目成员列表
      await fetchProjectMembers(id)
    } catch (err: any) {
      error.value = err.message || '添加项目成员失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeProjectMember = async (id: string, memberId: string) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.removeProjectMember(id, memberId)
      message.success(response.data.message)
      
      // 重新获取项目成员列表
      await fetchProjectMembers(id)
    } catch (err: any) {
      error.value = err.message || '移除项目成员失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateMemberRole = async (id: string, memberId: string, data: UpdateMemberRoleRequest) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.updateMemberRole(id, memberId, data)
      message.success(response.data.message)
      
      // 重新获取项目成员列表
      await fetchProjectMembers(id)
    } catch (err: any) {
      error.value = err.message || '更新成员角色失败'
      message.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchProjectActivities = async (id: string, params?: { page?: number; page_size?: number }) => {
    try {
      loading.value = true
      error.value = null
      const response = await ProjectAPI.getProjectActivities(id, params)
      projectActivities.value = response.data.data
    } catch (err: any) {
      error.value = err.message || '获取项目活动失败'
      message.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const clearCurrentProject = () => {
    currentProject.value = null
    projectMembers.value = []
    projectActivities.value = []
    projectDashboard.value = null
    localStorage.removeItem('current_project_id')
  }

  // 初始化时从localStorage恢复当前项目
  const initializeFromStorage = async () => {
    const savedProjectId = localStorage.getItem('current_project_id')
    if (savedProjectId) {
      try {
        await setCurrentProject(savedProjectId)
      } catch (err) {
        // 如果恢复失败，清除存储的项目ID
        localStorage.removeItem('current_project_id')
      }
    }
  }

  return {
    // 状态
    projects,
    currentProject,
    projectMembers,
    projectActivities,
    projectDashboard,
    loading,
    error,
    pagination,

    // 计算属性
    hasCurrentProject,
    currentProjectId,
    isProjectOwner,
    canManageProject,
    canManageMembers,

    // Actions
    fetchProjects,
    createProject,
    setCurrentProject,
    updateProject,
    deleteProject,
    fetchProjectDashboard,
    fetchProjectMembers,
    addProjectMember,
    removeProjectMember,
    updateMemberRole,
    fetchProjectActivities,
    clearCurrentProject,
    initializeFromStorage,
  }
})

export default useProjectStore