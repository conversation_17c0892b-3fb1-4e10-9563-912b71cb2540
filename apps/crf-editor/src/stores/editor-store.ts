/**
 * 统一编辑器状态管理Store - 完全类型安全和响应式
 */

import { defineStore } from 'pinia'
import { ref, reactive, computed, type Ref, type ComputedRef } from 'vue'
import type {
  ComponentInstance,
  HistoryEntry
} from '@crf/types/editor'
import type {
  FormSchema
} from '@crf/types/form'
import type {
  ValidationState
} from '@crf/types/validation'
import type { FormData } from '@crf/types/core'
import { EditorMode } from '@crf/types/core'


// 使用 ComponentInstance 类型作为 Block 类型
export type Block = ComponentInstance

export interface Section {
  id: string
  name: string
  blocks: Block[]
  children: Section[]
  level?: number
}

function createDefaultSection(): Section {
  return {
    id: `section-${Date.now()}`,
    name: '默认章节',
    level: 0,
    blocks: [
    ],
    children: []
  }
}

// 定义Store返回类型
interface EditorStoreReturn {
  sections: Section[]
  selectedComponent: ComponentInstance | null
  pageConfig: Record<string, unknown>
  schema: FormSchema | null
  formData: FormData
  validationResults: ValidationState
  mode: EditorMode
  history: HistoryEntry[]
  historyIndex: number
  editorConfig: Record<string, unknown>
  // 实例相关状态
  currentInstance: ComponentInstance | null
  instanceSaving: boolean
  addSection: (name?: string) => Section
  addSubSection: (parentId: string, name?: string) => Section | null
  findSectionById: (id: string) => Section | null
  deleteSection: (id: string) => void
  updateSectionName: (id: string, newName: string) => void
  selectComponent: (component: ComponentInstance | null) => void
  updateComponent: (id: string, updateData: Partial<ComponentInstance>) => boolean
  setSchema: (newSchema: FormSchema) => void
  setFormData: (data: FormData) => void
  updateFormData: (fieldId: string, value: unknown) => void
  setValidationResults: (results: ValidationState) => void
  setMode: (newMode: EditorMode) => void
  showConfigPanel: () => void
  hideConfigPanel: () => void
  getComponent: (id: string) => ComponentInstance | null
  addComponent: (component: ComponentInstance, targetSectionId?: string) => void
  findComponentSectionId: (componentId: string) => string | null
  deleteComponent: (id: string) => boolean
  updatePageConfig: (config: Partial<{ title: string; description: string; version: string; author: string; createTime: string; updateTime: string }>) => void
  selectPageConfig: () => void
  addNewFormData: (data: { schema: FormSchema; formData: FormData }) => boolean
  deleteFormData: () => boolean
  clearFormData: () => void
  // 实例相关方法
  setCurrentInstance: (instance: ComponentInstance | null) => void
  updateInstanceData: (fieldId: string, value: unknown) => void
  getInstanceProgress: () => number
  getFieldStats: () => { totalFields: number; filledFields: number; progress: number }
  clearInstanceData: () => void
  setInstanceSaving: (saving: boolean) => void
}

// 统一编辑器Store - 使用组合式API确保完全响应式
export const useEditorStore = defineStore('editor', (): EditorStoreReturn => {
  // 使用reactive确保深层响应式
  const sections = reactive<Section[]>([createDefaultSection()])

  // 使用ref确保selectedComponent的响应式
  const selectedComponent = ref<ComponentInstance | null>(null)

  // 页面配置状态
  const pageConfig = reactive({
    title: '',
    description: '',
    version: '1.0.0',
    author: '',
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  })

  // 其他状态
  const schema = ref<FormSchema | null>(null)
  const formData = reactive<FormData>({})
  const validationResults = reactive<ValidationState>({
    results: new Map(),
    isFormValid: true,
    pendingValidations: [],
    validationQueue: []
  })
  const mode = ref<EditorMode>(EditorMode.EDIT)
  const history = reactive<HistoryEntry[]>([])
  const historyIndex = ref(-1)
  const editorConfig = reactive({
    showConfigPanel: false
  })

  // 实例相关状态
  const currentInstance = ref<ComponentInstance | null>(null)
  const instanceSaving = ref(false)

  // 计算属性
  const sectionsComputed = computed(() => sections)

  // Actions
  const addSection = (name = '新章节') => {
    const id = `section-${Date.now()}`
    const section: Section = { id, name, level: 0, blocks: [], children: [] }
    sections.push(section)
    return section
  }

  const addSubSection = (parentId: string, name = '新子章节') => {
    const parent = findSectionById(parentId)
    if (!parent) return null
    const id = `section-${Date.now()}`
    const parentLevel = parent.level || 0
    const subSection: Section = { id, name, level: parentLevel + 1, blocks: [], children: [] }
    parent.children.push(subSection)
    return subSection
  }

  const findSectionById = (id: string): Section | null => {
    const search = (sectionList: Section[]): Section | null => {
      for (const section of sectionList) {
        if (section.id === id) return section
        if (section.children.length) {
          const found = search(section.children)
          if (found) return found
        }
      }
      return null
    }
    return search(sections)
  }

  const deleteSection = (id: string) => {
    const remove = (sectionList: Section[]): boolean => {
      const idx = sectionList.findIndex(s => s.id === id)
      if (idx !== -1) {
        sectionList.splice(idx, 1)
        return true
      }
      for (const section of sectionList) {
        if (remove(section.children)) return true
      }
      return false
    }
    remove(sections)
    // 保证至少有一个章节
    if (sections.length === 0) {
      sections.push(createDefaultSection())
    }
  }

  const updateSectionName = (id: string, newName: string) => {
    const section = findSectionById(id)
    if (section) {
      section.name = newName
    }
  }

  const selectComponent = (component: ComponentInstance | null) => {
    selectedComponent.value = component
  }

  // 重构updateComponent方法 - 使用响应式安全的更新方式
  const updateComponent = (id: string, updateData: Partial<ComponentInstance>) => {
    // 更新选中的组件 - 确保响应式
    if (selectedComponent.value && selectedComponent.value.id === id) {
      Object.assign(selectedComponent.value, updateData)
    }

    // 同时更新对应章节中的组件 - 使用响应式安全的方式
    const updateComponentInSection = (sectionList: Section[]): boolean => {
      for (const section of sectionList) {
        const blockIndex = section.blocks.findIndex(block => block.id === id)
        if (blockIndex !== -1) {
          const currentBlock = section.blocks[blockIndex]
          if (!currentBlock) continue

          // 创建新的block对象，确保响应式更新
          const newBlock = {
            ...currentBlock,
            ...updateData
          }

          // 处理title字段的特殊逻辑 - 允许空值
          if (updateData.props && updateData.props.hasOwnProperty('title')) {
            newBlock.title = String(updateData.props.title || '')
          }

          if (updateData.hasOwnProperty('title')) {
            const titleValue = String(updateData.title || '')
            newBlock.title = titleValue
            // 同时更新props中的title
            if (!newBlock.props) newBlock.props = {}
            newBlock.props = { ...newBlock.props, title: titleValue }
          }

          // 合并props对象，确保响应式
          if (updateData.props) {
            newBlock.props = {
              ...(currentBlock?.props || {}),
              ...updateData.props
            }
          }

          // 响应式更新 - 直接替换数组元素
          section.blocks.splice(blockIndex, 1, newBlock)

          return true
        }
        if (section.children.length > 0) {
          if (updateComponentInSection(section.children)) {
            return true
          }
        }
      }
      return false
    }

    return updateComponentInSection(sections)
  }

  const setSchema = (newSchema: FormSchema) => {
    schema.value = newSchema
  }

  const setFormData = (data: FormData) => {
    Object.assign(formData, data)
  }

  const updateFormData = (fieldId: string, value: unknown) => {
    ;(formData as Record<string, unknown>)[fieldId] = value
  }

  const setValidationResults = (results: ValidationState) => {
    Object.assign(validationResults, results)
  }

  const setMode = (newMode: EditorMode) => {
    const prevMode = mode.value
    mode.value = newMode

    // 从预览模式返回编辑模式时，清空表单数据
    if (prevMode === EditorMode.PREVIEW && newMode === EditorMode.EDIT) {
      clearFormData()
      // 取消选中组件
      selectedComponent.value = null
      console.log('🔄 切换到编辑模式，已清空预览表单数据')
    }
  }

  const clearFormData = () => {
    Object.keys(formData).forEach(key => {
      delete (formData as Record<string, unknown>)[key]
    })
  }

  const showConfigPanel = () => {
    editorConfig.showConfigPanel = true
  }

  const hideConfigPanel = () => {
    editorConfig.showConfigPanel = false
  }

  const getComponent = (id: string): ComponentInstance | null => {
    const searchInSections = (sectionList: Section[]): ComponentInstance | null => {
      for (const section of sectionList) {
        const block = section.blocks.find(b => b.id === id)
        if (block) return block
        if (section.children.length > 0) {
          const found = searchInSections(section.children)
          if (found) return found
        }
      }
      return null
    }
    return searchInSections(sections)
  }

  const addComponent = (component: ComponentInstance, targetSectionId?: string) => {
    try {
      // 如果指定了目标章节ID，添加到该章节
      if (targetSectionId) {
        const targetSection = findSectionById(targetSectionId)
        if (targetSection) {
          targetSection.blocks.push(component)
          return
        }
      }

      // 如果有选中的组件，添加到该组件所在的章节
      if (selectedComponent.value) {
        const componentSectionId = findComponentSectionId(selectedComponent.value.id)
        if (componentSectionId) {
          const targetSection = findSectionById(componentSectionId)
          if (targetSection) {
            // 找到当前组件的位置，在其后面插入新组件
            const currentIndex = targetSection.blocks.findIndex(b => b.id === selectedComponent.value?.id)
            if (currentIndex !== -1) {
              targetSection.blocks.splice(currentIndex + 1, 0, component)
            } else {
              targetSection.blocks.push(component)
            }
            return
          }
        }
      }

      // 默认添加到第一个章节
      if (sections.length > 0 && sections[0]) {
        sections[0].blocks.push(component)
      }
    } catch (error) {
      console.error('添加组件失败:', error)
    }
  }

  // 查找组件所在的章节ID
  const findComponentSectionId = (componentId: string): string | null => {
    const searchInSections = (sectionList: Section[]): string | null => {
      for (const section of sectionList) {
        const block = section.blocks.find(b => b.id === componentId)
        if (block) return section.id
        if (section.children.length > 0) {
          const found = searchInSections(section.children)
          if (found) return found
        }
      }
      return null
    }
    return searchInSections(sections)
  }

  // 删除组件
  const deleteComponent = (id: string): boolean => {
    try {
      // 在所有章节中查找并删除组件
      const deleteFromSections = (sectionList: Section[]): boolean => {
        for (const section of sectionList) {
          const blockIndex = section.blocks.findIndex(block => block.id === id)
          if (blockIndex !== -1) {
            const blockToRemove = section.blocks[blockIndex]
            if (blockToRemove) {
              // 保存历史记录
              history.push({
                type: 'component_remove',
                data: blockToRemove,
                timestamp: Date.now(),
                description: '删除组件'
              })
              historyIndex.value++
            }

            // 删除组件
            section.blocks.splice(blockIndex, 1)
            return true
          }

          // 递归查找子章节
          if (section.children && section.children.length > 0) {
            if (deleteFromSections(section.children)) {
              return true
            }
          }
        }
        return false
      }

      return deleteFromSections(sections)
    } catch (error) {
      console.error('删除组件失败:', error)
      return false
    }
  }

  // 页面配置相关方法
  const updatePageConfig = (config: Partial<typeof pageConfig>) => {
    Object.assign(pageConfig, config)
    pageConfig.updateTime = new Date().toISOString()
  }

  const selectPageConfig = () => {
    selectedComponent.value = null
    showConfigPanel()
  }

  // 复制表单数据
  const addNewFormData = (data: { schema: FormSchema; formData: FormData }): boolean => {
    try {
      // 保存当前状态到历史记录
      history.push({
        type: 'form_data_change',
        data: formData, // 只保存表单数据到历史记录
        timestamp: Date.now(),
        description: '复制表单数据'
      })
      historyIndex.value++

      // 更新数据
      schema.value = data.schema
      setFormData(data.formData)

      return true
    } catch (error) {
      console.error('添加新表单数据失败:', error)
      return false
    }
  }

  // 删除表单数据
  const deleteFormData = (): boolean => {
    try {
      // 保存当前状态到历史记录
      history.push({
        type: 'form_data_change',
        data: formData, // 只保存表单数据到历史记录
        timestamp: Date.now(),
        description: '删除表单数据'
      })
      historyIndex.value++

      // 清空表单数据
      setFormData({})

      return true
    } catch (error) {
      console.error('删除表单数据失败:', error)
      return false
    }
  }

  // 实例管理方法
  const setCurrentInstance = (instance: ComponentInstance | null) => {
    currentInstance.value = instance
    console.log('📋 设置当前实例:', instance)
  }

  const updateInstanceData = (fieldId: string, value: unknown) => {
    // 更新表单数据（实例填写时使用相同的formData）
    updateFormData(fieldId, value)
    
    // 如果有当前实例，也更新实例的form_data
    if (currentInstance.value) {
      // 使用类型断言访问动态属性
      const instance = currentInstance.value as ComponentInstance & { form_data?: Record<string, unknown>, updated_at?: string }
      
      if (!instance.form_data) {
        instance.form_data = {}
      }
      instance.form_data[fieldId] = value
      
      // 更新实例的更新时间
      instance.updated_at = new Date().toISOString()
      
      console.log('📝 更新实例数据:', { fieldId, value, instanceId: instance.id })
    }
  }

  const getInstanceProgress = (): number => {
    // 获取所有可填写的字段（从sections中收集所有组件的字段）
    const getAllFormFields = (): string[] => {
      const fields: string[] = []
      
      const collectFields = (sectionList: Section[]) => {
        sectionList.forEach(section => {
          section.blocks.forEach(block => {
            // 只收集具有表单输入功能的组件
            if (isFormInputComponent(block.type)) {
              fields.push(block.id)
            }
          })
          
          // 递归处理子章节
          if (section.children.length > 0) {
            collectFields(section.children)
          }
        })
      }
      
      collectFields(sections)
      return fields
    }
    
    // 判断是否为表单输入组件
    const isFormInputComponent = (type: string): boolean => {
      const inputTypes = [
        'text', 'textarea', 'number', 'select', 'radio', 
        'checkbox', 'switch', 'slider', 'date', 'datetime',
        'time', 'email', 'phone', 'url', 'password'
      ]
      return inputTypes.includes(type)
    }
    
    const allFormFields = getAllFormFields()
    const totalFields = allFormFields.length
    
    if (totalFields === 0) {
      return 0
    }
    
    // 计算已填写的字段数量
    const formDataObj = formData // 使用store中的formData而不是instance中的
    const filledFields = allFormFields.filter(fieldId => {
      const value = formDataObj[fieldId]
      return value !== null && value !== undefined && value !== ''
    }).length
    
    const progress = Math.round((filledFields / totalFields) * 100)
    
    // 更新实例的progress字段
    if (currentInstance.value) {
      // 使用类型断言访问动态属性
      const instance = currentInstance.value as ComponentInstance & { progress?: number, status?: string }
      instance.progress = progress
      instance.status = progress === 100 ? 'completed' : 'draft'
    }
    
    return progress
  }

  // 获取填写字段统计
  const getFieldStats = () => {
    const getAllFormFields = (): string[] => {
      const fields: string[] = []
      
      const collectFields = (sectionList: Section[]) => {
        sectionList.forEach(section => {
          section.blocks.forEach(block => {
            // 只收集具有表单输入功能的组件
            if (isFormInputComponent(block.type)) {
              fields.push(block.id)
            }
          })
          
          // 递归处理子章节
          if (section.children.length > 0) {
            collectFields(section.children)
          }
        })
      }
      
      collectFields(sections)
      return fields
    }
    
    // 判断是否为表单输入组件
    const isFormInputComponent = (type: string): boolean => {
      const inputTypes = [
        'text', 'textarea', 'number', 'select', 'radio', 
        'checkbox', 'switch', 'slider', 'date', 'datetime',
        'time', 'email', 'phone', 'url', 'password'
      ]
      return inputTypes.includes(type)
    }
    
    const allFormFields = getAllFormFields()
    const totalFields = allFormFields.length
    
    // 计算已填写的字段数量
    const formDataObj = formData
    const filledFields = allFormFields.filter(fieldId => {
      const value = formDataObj[fieldId]
      return value !== null && value !== undefined && value !== ''
    }).length
    
    return {
      totalFields,
      filledFields,
      progress: totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0
    }
  }

  const clearInstanceData = () => {
    currentInstance.value = null
    clearFormData()
    console.log('🗑️ 清空实例数据')
  }

  const setInstanceSaving = (saving: boolean) => {
    instanceSaving.value = saving
  }

  // 返回状态和方法
  return {
    // 状态
    sections: sectionsComputed.value,
    selectedComponent: selectedComponent.value,
    pageConfig,
    schema: schema.value,
    formData,
    validationResults,
    mode: mode.value,
    history,
    historyIndex: historyIndex.value,
    editorConfig,
    // 实例相关状态
    currentInstance: currentInstance.value,
    instanceSaving: instanceSaving.value,

    // 方法
    addSection,
    addSubSection,
    findSectionById,
    deleteSection,
    updateSectionName,
    selectComponent,
    updateComponent,
    setSchema,
    setFormData,
    updateFormData,
    setValidationResults,
    setMode,
    showConfigPanel,
    hideConfigPanel,
    getComponent,
    addComponent,
    findComponentSectionId,
    deleteComponent,
    updatePageConfig,
    selectPageConfig,
    addNewFormData,
    deleteFormData,
    clearFormData,
    // 实例相关方法
    setCurrentInstance,
    updateInstanceData,
    getInstanceProgress,
    getFieldStats,
    clearInstanceData,
    setInstanceSaving
  }
})
