import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api'
import { usePermissionStore } from './permission-store'

// 更新用户信息类型定义以匹配RBAC系统
interface UserInfo {
  id: string
  username: string
  email: string
  full_name?: string
  avatar_url?: string
  phone?: string
  department?: string
  position?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)

  // 获取权限store
  const permissionStore = usePermissionStore()

  // 计算属性
  const isLoggedIn = computed(() => {
    // 检查用户信息和token是否都存在
    const hasUser = !!user.value
    const hasAccessToken = !!accessToken.value || !!localStorage.getItem('auth_token')
    const hasRefreshToken = !!refreshToken.value || !!localStorage.getItem('refresh_token')
    return hasUser && hasAccessToken && hasRefreshToken
  })
  
  const currentUser = computed(() => user.value)
  const token = computed(() => accessToken.value)

  // 方法
  const setUser = async (userData: UserInfo) => {
    user.value = userData
    // 同时保存用户信息到localStorage
    localStorage.setItem('user_info', JSON.stringify(userData))
    console.log('用户信息已保存:', userData.username)
    
    // 加载用户权限信息
    if (userData.id) {
      try {
        await permissionStore.fetchUserRoles(userData.id)
      } catch (error) {
        console.error('加载用户权限失败:', error)
      }
    }
  }

  const setTokens = (accessTokenValue: string, refreshTokenValue?: string) => {
    console.log('设置Token:', accessTokenValue.substring(0, 20) + '...')
    
    accessToken.value = accessTokenValue
    localStorage.setItem('auth_token', accessTokenValue)
    
    if (refreshTokenValue) {
      refreshToken.value = refreshTokenValue
      localStorage.setItem('refresh_token', refreshTokenValue)
      console.log('Refresh Token已保存')
    }
    
    console.log('Access Token已保存到localStorage')
  }

  const clearAuth = () => {
    user.value = null
    accessToken.value = null
    refreshToken.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')
    console.log('认证状态已清除')
  }

  const login = async (credentials: { username: string; password: string }) => {
    try {
      isLoading.value = true
      const response = await authAPI.login(credentials)
      
      if (response.success && response.data) {
        await setUser(response.data.user as unknown as UserInfo)
        
        // 设置新的token对
        if (response.data.access_token) {
          setTokens(response.data.access_token, response.data.refresh_token)
        }
        
        // 登录成功后，获取完整的用户资料（包括头像）
        try {
          const { profileAPI } = await import('@/api/rbac')
          const profileResponse = await profileAPI.getProfile(String(response.data.user.id))
          if (profileResponse.success && profileResponse.data?.profile) {
            // 更新用户信息，包含完整的资料信息
            const completeUser = {
              ...response.data.user,
              ...profileResponse.data.profile
            }
            await setUser(completeUser)
            console.log('已获取完整用户资料，包括头像:', completeUser.avatar_url)
          }
        } catch (profileError) {
          console.warn('获取用户完整资料失败，使用基本信息:', profileError)
        }
        
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message || '登录失败' }
      }
    } catch (error: unknown) {
      console.error('登录失败:', error)
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      return { success: false, message: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: { 
    username: string; 
    email: string; 
    password: string; 
    full_name?: string 
  }) => {
    try {
      isLoading.value = true
      const response = await authAPI.register(userData)
      
      if (response.success && response.data) {
        await setUser(response.data.user as unknown as UserInfo)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message || '注册失败' }
      }
    } catch (error: unknown) {
      console.error('注册失败:', error)
      const errorMessage = error instanceof Error ? error.message : '注册失败'
      return { success: false, message: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      isLoading.value = true
      await authAPI.logout()
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      clearAuth()
      isLoading.value = false
    }
  }

  const getCurrentUser = async () => {
    try {
      isLoading.value = true
      console.log('尝试获取当前用户信息...')
      const response = await authAPI.getCurrentUser()
      
      console.log('获取用户信息响应:', response)
      
      if (response.success && response.data && response.data.user) {
        await setUser(response.data.user as unknown as UserInfo)
        
        // 获取完整的用户资料（包括头像）
        try {
          const { profileAPI } = await import('@/api/rbac')
          const profileResponse = await profileAPI.getProfile(String(response.data.user.id))
          if (profileResponse.success && profileResponse.data?.profile) {
            // 更新用户信息，包含完整的资料信息
            const completeUser = {
              ...response.data.user,
              ...profileResponse.data.profile
            }
            await setUser(completeUser)
            console.log('已获取完整用户资料，包括头像:', completeUser.avatar_url)
            return completeUser
          }
        } catch (profileError) {
          console.warn('获取用户完整资料失败，使用基本信息:', profileError)
        }
        
        console.log('成功设置用户信息:', response.data.user)
        return response.data.user
      }
      
      console.log('获取用户信息失败，响应数据无效')
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearAuth()
      return null
    } finally {
      isLoading.value = false
    }
  }

  const initAuth = async () => {
    const savedAccessToken = localStorage.getItem('auth_token')
    const savedRefreshToken = localStorage.getItem('refresh_token')
    const savedUser = localStorage.getItem('user_info')
    
    if (savedAccessToken && savedRefreshToken && savedUser) {
      try {
        accessToken.value = savedAccessToken
        refreshToken.value = savedRefreshToken
        user.value = JSON.parse(savedUser)
        
        console.log('从localStorage恢复认证状态:', {
          accessToken: savedAccessToken.substring(0, 20) + '...',
          refreshToken: savedRefreshToken.substring(0, 20) + '...',
          user: user.value?.username
        })
        
        // 验证token是否仍然有效
        const currentUser = await getCurrentUser()
        if (!currentUser) {
          console.log('Token已过期，清除认证状态')
          clearAuth()
        } else {
          console.log('成功恢复认证状态:', currentUser.username)
        }
      } catch (error) {
        console.error('恢复用户信息失败:', error)
        clearAuth()
      }
    } else {
      console.log('没有找到保存的认证信息')
    }
  }

  const refreshTokens = async () => {
    try {
      const response = await authAPI.refreshToken()
      
      if (response.success && response.data) {
        setTokens(response.data.access_token, response.data.refresh_token)
        console.log('Token刷新成功')
        return true
      }
      
      return false
    } catch (error) {
      console.error('Token刷新失败:', error)
      clearAuth()
      return false
    }
  }

  // 使用RBAC权限系统的权限检查方法
  const hasPermission = (resource: string, action: string, scope = 'global') => {
    return permissionStore.hasPermission(resource, action, scope)
  }

  const hasRole = (roleName: string) => {
    return permissionStore.hasRole(roleName)
  }

  const hasAnyRole = (roleNames: string[]) => {
    return permissionStore.hasAnyRole(roleNames)
  }

  return {
    // 状态
    user,
    accessToken,
    refreshToken,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    currentUser,
    token,
    
    // 方法
    setUser,
    setTokens,
    clearAuth,
    login,
    register,
    logout,
    getCurrentUser,
    initAuth,
    refreshTokens,
    hasPermission,
    hasRole,
    hasAnyRole
  }
})