// 权限管理Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, Role, Permission, UserRole } from '@/types/rbac'
import { api } from '@/api/rbac'

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const userRoles = ref<UserRole[]>([])
  const allRoles = ref<Role[]>([])
  const allPermissions = ref<Permission[]>([])
  const permissionCache = ref(new Map<string, boolean>())
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const userPermissions = computed(() => {
    const permissions = new Set<string>()
    userRoles.value.forEach(userRole => {
      if (userRole.role && userRole.role.permissions) {
        userRole.role.permissions.forEach(permission => {
          // 支持多种权限格式
          if (typeof permission === 'string') {
            permissions.add(permission)
          } else if (permission.resource && permission.action) {
            const scope = permission.scope || 'global'
            permissions.add(`${permission.resource}:${permission.action}:${scope}`)
            // 同时支持不带scope的格式
            permissions.add(`${permission.resource}:${permission.action}`)
          }
        })
      }
    })
    return Array.from(permissions)
  })

  const userRoleNames = computed(() => {
    return userRoles.value.map(ur => ur.role?.name).filter(Boolean)
  })

  // Actions
  const fetchUserRoles = async (userId: string) => {
    loading.value = true
    error.value = null
    try {
      // 优先尝试从当前用户API获取用户信息（无需额外权限）
      let userResponse
      try {
        userResponse = await api.get('/auth/me')
        console.log('当前用户API响应:', userResponse)
      } catch (authError) {
        console.log('当前用户API调用失败，尝试用户详情API:', authError)
        // 如果当前用户API失败，回退到用户详情API
        userResponse = await api.get(`/users/${userId}`)
        console.log('用户详情API响应:', userResponse)
      }
      
      // 从不同位置尝试获取角色信息
      let userRoleNames = userResponse.data?.user?.roles || 
                         userResponse.data?.user?.role_names ||
                         userResponse.data?.roles || 
                         []
      
      // 如果API没有返回角色信息，尝试从localStorage获取
      if (!userRoleNames || userRoleNames.length === 0) {
        const savedUser = localStorage.getItem('user_info')
        if (savedUser) {
          const userData = JSON.parse(savedUser)
          userRoleNames = userData.roles || []
          console.log('从localStorage获取角色:', userRoleNames)
        }
      }
      
      // 如果仍然没有角色信息，根据旧的role字段推断
      if (!userRoleNames || userRoleNames.length === 0) {
        const oldRole = userResponse.data?.user?.role
        if (oldRole) {
          // 根据旧的role字段推断新的角色
          const roleMapping = {
            'admin': ['super_admin'],
            'super_admin': ['super_admin'],
            'editor': ['researcher'],
            'viewer': ['viewer'],
            'user': ['data_entry']
          }
          userRoleNames = roleMapping[oldRole] || ['viewer']
          console.log('根据旧role字段推断角色:', oldRole, '->', userRoleNames)
        }
      }
      
      // 2. 获取所有角色信息（包含权限）
      let allRoles = []
      try {
        const rolesResponse = await api.get('/roles?include_permissions=true')
        allRoles = rolesResponse.data?.roles || rolesResponse.data || []
        console.log('角色API响应成功:', allRoles.length, '个角色')
      } catch (rolesError) {
        console.log('角色API调用失败，使用预定义权限:', rolesError)
        // 如果角色API失败（权限不足），创建基本的角色对象
        allRoles = userRoleNames.map(roleName => ({
          id: roleName,
          code: roleName,
          name: roleName,
          display_name: roleName,
          description: `基本${roleName}角色`,
          is_system: true,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          permissions: getBasicPermissionsForRole(roleName).map(perm => ({ 
            id: perm,
            resource: perm.split(':')[0] || 'template',
            action: perm.split(':')[1] || 'read',
            scope: perm.split(':')[2] || 'global',
            description: `${roleName}角色的${perm}权限`,
            is_system: true,
            created_at: new Date().toISOString()
          }))
        }))
        console.log('使用预定义角色数据:', allRoles)
      }
      
      // 3. 根据用户角色名称过滤出完整的角色信息
      const userRoleObjects = allRoles.filter(role => 
        userRoleNames.includes(role.name)
      ).map(role => ({
        id: role.id || role.name,
        user_id: userId,
        role_id: role.id || role.name,
        assigned_by: 'system',
        assigned_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        role: role
      }))
      
      userRoles.value = userRoleObjects
      console.log('用户角色数据加载成功:', {
        userRoleNames,
        userRoleObjects,
        userPermissions: userPermissions.value
      })
      
      return { roles: userRoleObjects }
    } catch (err: unknown) {
      console.error('获取用户角色失败:', err)
      error.value = (err as Error).message || '获取用户角色失败'
      
      // 如果获取失败，尝试从用户store中获取基本角色信息
      try {
        const savedUser = localStorage.getItem('user_info')
        if (savedUser) {
          const userData = JSON.parse(savedUser)
          const roleNames = userData.roles || userData.role ? [userData.role] : ['viewer']
          console.log('fallback: 从localStorage获取用户角色:', roleNames)
          
          // 创建基本的角色对象，包含基础权限
          const basicRoles = roleNames.map(roleName => ({
            id: roleName,
            user_id: userId,
            role_id: roleName,
            assigned_by: 'system',
            assigned_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            role: {
              id: roleName,
              code: roleName,
              name: roleName,
              display_name: roleName,
              description: `基本${roleName}角色`,
              is_system: true,
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              permissions: getBasicPermissionsForRole(roleName).map(perm => ({ 
                id: perm,
                resource: perm.split(':')[0] || 'template',
                action: perm.split(':')[1] || 'read',
                scope: perm.split(':')[2] || 'global',
                description: `${roleName}角色的${perm}权限`,
                is_system: true,
                created_at: new Date().toISOString()
              }))
            }
          }))
          
          userRoles.value = basicRoles
          console.log('使用基础角色权限:', basicRoles)
        }
      } catch (fallbackError) {
        console.error('fallback角色加载也失败:', fallbackError)
      }
      
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 为不同角色提供基础权限的辅助函数
  const getBasicPermissionsForRole = (roleName: string): string[] => {
    const rolePermissions = {
      'super_admin': [
        // 用户管理权限
        'user:create', 'user:read', 'user:update', 'user:delete', 'user:assign_role',
        // 角色管理权限  
        'role:create', 'role:read', 'role:update', 'role:delete', 'role:assign_permission',
        // 项目管理权限
        'project:create', 'project:read', 'project:update', 'project:delete',
        // 模板管理权限
        'template:create', 'template:read', 'template:update', 'template:delete', 'template:publish',
        // 实例管理权限
        'instance:create', 'instance:read', 'instance:update', 'instance:delete', 'instance:submit', 'instance:approve', 'instance:lock',
        // 数据管理权限
        'data:read', 'data:export', 'data:analyze',
        // 系统管理权限
        'system:config', 'system:monitor', 'system:backup', 'system:audit',
        // 通配符权限
        'user:*', 'role:*', 'template:*', 'project:*', 'system:*', '*'
      ],
      'admin': ['user:*', 'role:*', 'template:*', 'project:*', 'system:*'],
      'researcher': ['template:create', 'template:read', 'template:update', 'project:read', 'project:create'],
      'data_entry': ['template:read', 'instance:create', 'instance:read', 'instance:update'],
      'reviewer': ['template:read', 'instance:read', 'instance:review'],
      'viewer': ['template:read', 'instance:read']
    }
    
    return rolePermissions[roleName] || ['template:read']
  }

  const fetchAllRoles = async (includePermissions = true) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get(`/roles?include_permissions=${includePermissions}`)
      allRoles.value = response.data || []
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '获取角色列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchAllPermissions = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get('/permissions')
      allPermissions.value = response.data || []
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '获取权限列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const checkPermission = async (
    userId: string,
    resource: string,
    action: string,
    projectId?: string
  ): Promise<boolean> => {
    const cacheKey = `${userId}:${resource}:${action}:${projectId || 'global'}`
    
    // 检查缓存
    if (permissionCache.value.has(cacheKey)) {
      return permissionCache.value.get(cacheKey)!
    }

    try {
      const response = await api.post('/check-permission', {
        user_id: userId,
        resource,
        action,
        project_id: projectId
      })
      
      const hasPermission = response.data.has_permission || false
      
      // 缓存结果（1小时）
      permissionCache.value.set(cacheKey, hasPermission)
      setTimeout(() => {
        permissionCache.value.delete(cacheKey)
      }, 3600000) // 1小时
      
      return hasPermission
    } catch (err) {
      console.error('权限检查失败:', err)
      return false
    }
  }

  const assignRole = async (userId: string, roleId: string, projectId?: string, expiresAt?: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/user-roles', {
        user_id: userId,
        role_id: roleId,
        project_id: projectId,
        expires_at: expiresAt
      })
      
      // 清除权限缓存
      clearUserPermissionCache(userId)
      
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '分配角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeRole = async (userId: string, roleId: string, projectId?: string) => {
    loading.value = true
    error.value = null
    try {
      const url = projectId 
        ? `/user-roles/${userId}/roles/${roleId}?project_id=${projectId}`
        : `/user-roles/${userId}/roles/${roleId}`
      
      const response = await api.delete(url)
      
      // 清除权限缓存
      clearUserPermissionCache(userId)
      
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '移除角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createRole = async (roleData: { name: string; display_name: string; description?: string }) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/roles', roleData)
      
      // 刷新角色列表
      await fetchAllRoles()
      
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '创建角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateRole = async (roleId: string, updates: Partial<Role>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.put(`/roles/${roleId}`, updates)
      
      // 刷新角色列表
      await fetchAllRoles()
      
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '更新角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteRole = async (roleId: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.delete(`/roles/${roleId}`)
      
      // 刷新角色列表
      await fetchAllRoles()
      
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '删除角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const assignPermissionsToRole = async (roleId: string, permissionIds: string[]) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post(`/roles/${roleId}/permissions`, {
        permission_ids: permissionIds
      })
      
      // 刷新角色列表
      await fetchAllRoles()
      
      // 清除所有权限缓存
      clearAllPermissionCache()
      
      return response.data
    } catch (err: unknown) {
      error.value = (err as Error).message || '分配权限失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 工具方法
  const hasRole = (roleName: string): boolean => {
    return userRoleNames.value.includes(roleName)
  }

  const hasAnyRole = (roleNames: string[]): boolean => {
    return roleNames.some(role => hasRole(role))
  }

  const hasPermission = (resource: string, action: string, scope = 'global'): boolean => {
    // 检查多种权限格式
    const permissionFormats = [
      `${resource}:${action}:${scope}`,
      `${resource}:${action}`,
      `${resource}:*`,
      `${resource}:*:${scope}`,
      '*'
    ]
    
    return permissionFormats.some(format => userPermissions.value.includes(format))
  }

  const clearUserPermissionCache = (userId: string) => {
    const keysToDelete = Array.from(permissionCache.value.keys()).filter(key => 
      key.startsWith(`${userId}:`)
    )
    keysToDelete.forEach(key => permissionCache.value.delete(key))
  }

  const clearAllPermissionCache = () => {
    permissionCache.value.clear()
  }

  const isAdmin = computed(() => hasAnyRole(['super_admin', 'admin']))
  const isResearcher = computed(() => hasRole('researcher'))
  const isDataEntry = computed(() => hasRole('data_entry'))
  const isReviewer = computed(() => hasRole('reviewer'))
  const isViewer = computed(() => hasRole('viewer'))

  return {
    // 状态
    userRoles,
    allRoles,
    allPermissions,
    permissionCache,
    loading,
    error,
    
    // 计算属性
    userPermissions,
    userRoleNames,
    isAdmin,
    isResearcher,
    isDataEntry,
    isReviewer,
    isViewer,
    
    // Actions
    fetchUserRoles,
    fetchAllRoles,
    fetchAllPermissions,
    checkPermission,
    assignRole,
    removeRole,
    createRole,
    updateRole,
    deleteRole,
    assignPermissionsToRole,
    getBasicPermissionsForRole,
    
    // 工具方法
    hasRole,
    hasAnyRole,
    hasPermission,
    clearUserPermissionCache,
    clearAllPermissionCache
  }
})