<template>
  <svg 
    :width="size" 
    :height="size" 
    viewBox="0 0 18 18" 
    fill="none"
    :class="iconClass"
  >
    <path 
      d="M3.5 2C2.67157 2 2 2.67157 2 3.5V14.5C2 15.3284 2.67157 16 3.5 16H14.5C15.3284 16 16 15.3284 16 14.5V3.5C16 2.67157 15.3284 2 14.5 2H3.5ZM4 4H14V5H4V4ZM4 7H14V8H4V7ZM4 10H10V11H4V10Z" 
      :fill="color" 
      :opacity="opacity"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  opacity?: number | string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 18,
  color: 'currentColor',
  opacity: 1,
  class: ''
})

const iconClass = computed(() => `phone-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.phone-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>