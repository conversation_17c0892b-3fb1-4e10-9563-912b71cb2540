<template>
  <svg 
    :width="size" 
    :height="size" 
    viewBox="0 0 16 16" 
    fill="none"
    :class="iconClass"
  >
    <path 
      v-if="type === 'lock'"
      d="M4 7V5C4 2.79086 5.79086 1 8 1C10.2091 1 12 2.79086 12 5V7H13C13.5523 7 14 7.44772 14 8V14C14 14.5523 13.5523 15 13 15H3C2.44772 15 2 14.5523 2 14V8C2 7.44772 2.44772 7 3 7H4ZM6 7H10V5C10 3.89543 9.10457 3 8 3C6.89543 3 6 3.89543 6 5V7Z" 
      :fill="color"
    />
    <path 
      v-else-if="type === 'chat'"
      d="M2 3C2 2.44772 2.44772 2 3 2H13C13.5523 2 14 2.44772 14 3V11C14 11.5523 13.5523 12 13 12H4.41421L1.70711 14.7071C1.42111 14.9931 1 14.7889 1 14.4142V3C1 2.44772 1.44772 2 2 2Z" 
      :fill="color"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  type: 'lock' | 'chat'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  color: 'currentColor',
  class: ''
})

const iconClass = computed(() => `tab-icon tab-icon-${props.type} ${props.class}`)
</script>

<style lang="scss" scoped>
.tab-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>