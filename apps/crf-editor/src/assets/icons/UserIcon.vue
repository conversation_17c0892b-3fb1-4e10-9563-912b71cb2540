<template>
  <svg 
    :width="size" 
    :height="size" 
    viewBox="0 0 18 18" 
    fill="none"
    :class="iconClass"
  >
    <path 
      d="M9 9C11.4853 9 13.5 6.98528 13.5 4.5C13.5 2.01472 11.4853 0 9 0C6.51472 0 4.5 2.01472 4.5 4.5C4.5 6.98528 6.51472 9 9 9ZM9 11.25C6.0225 11.25 0 12.7425 0 15.75V18H18V15.75C18 12.7425 11.9775 11.25 9 11.25Z" 
      :fill="color" 
      :opacity="opacity"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  opacity?: number | string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 18,
  color: 'currentColor',
  opacity: 1,
  class: ''
})

const iconClass = computed(() => `user-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.user-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>