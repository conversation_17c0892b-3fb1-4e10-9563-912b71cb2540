<template>
  <svg 
    :width="size" 
    :height="size" 
    viewBox="0 0 24 24" 
    fill="none"
    :class="iconClass"
  >
    <path 
      d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"
      :fill="color"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  direction?: 'right' | 'left' | 'up' | 'down'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor',
  direction: 'right',
  class: ''
})

const iconClass = computed(() => {
  const baseClass = 'arrow-icon'
  const directionClass = `arrow-${props.direction}`
  return `${baseClass} ${directionClass} ${props.class}`
})
</script>

<style lang="scss" scoped>
.arrow-icon {
  display: inline-block;
  vertical-align: middle;
  transition: transform 0.2s ease;
  
  &.arrow-left {
    transform: rotate(180deg);
  }
  
  &.arrow-up {
    transform: rotate(-90deg);
  }
  
  &.arrow-down {
    transform: rotate(90deg);
  }
}
</style>