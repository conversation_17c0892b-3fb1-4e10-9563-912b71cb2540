<template>
  <div class="brand-logo-container">
    <!-- 主 Logo -->
    <svg 
      :width="size" 
      :height="size" 
      viewBox="0 0 64 64" 
      fill="none" 
      :class="mainLogoClass"
    >
      <!-- 主体表单图标 -->
      <rect x="12" y="16" width="32" height="40" rx="4" fill="url(#logoGradient)" stroke="#fff" stroke-width="1"/>
      <!-- 表单内容线条 -->
      <rect x="16" y="22" width="20" height="2" rx="1" fill="#fff" opacity="0.9"/>
      <rect x="16" y="28" width="16" height="2" rx="1" fill="#fff" opacity="0.7"/>
      <rect x="16" y="34" width="18" height="2" rx="1" fill="#fff" opacity="0.7"/>
      <rect x="16" y="40" width="14" height="2" rx="1" fill="#fff" opacity="0.7"/>
      <rect x="16" y="46" width="22" height="2" rx="1" fill="#fff" opacity="0.7"/>
      <!-- 医疗十字标志 -->
      <circle cx="50" cy="18" r="12" fill="#ffffff" stroke="url(#logoGradient)" stroke-width="2"/>
      <rect x="46" y="14" width="8" height="2" rx="1" fill="url(#logoGradient)"/>
      <rect x="49" y="11" width="2" height="8" rx="1" fill="url(#logoGradient)"/>
      <rect x="47" y="20" width="6" height="1.5" rx="0.75" fill="url(#logoGradient)" opacity="0.6"/>
      <rect x="48.25" y="18.75" width="3.5" height="1.5" rx="0.75" fill="url(#logoGradient)" opacity="0.6"/>
      
      <defs>
        <linearGradient :id="`logoGradient-${uniqueId}`" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :stop-color="primaryColor"/>
          <stop offset="100%" :stop-color="secondaryColor"/>
        </linearGradient>
      </defs>
    </svg>
    
    <!-- 装饰环 (可选) -->
    <svg 
      v-if="showRing"
      :width="ringSize" 
      :height="ringSize" 
      viewBox="0 0 84 84" 
      fill="none" 
      :class="ringClass"
    >
      <circle cx="42" cy="42" r="40" :stroke="`url(#ringGradient-${uniqueId})`" stroke-width="1" stroke-dasharray="8 4" opacity="0.3"/>
      <circle cx="42" cy="42" r="35" :stroke="`url(#ringGradient-${uniqueId})`" stroke-width="0.5" stroke-dasharray="4 2" opacity="0.2"/>
      <defs>
        <linearGradient :id="`ringGradient-${uniqueId}`" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :stop-color="primaryColor"/>
          <stop offset="50%" :stop-color="secondaryColor"/>
          <stop offset="100%" stop-color="#f093fb"/>
        </linearGradient>
      </defs>
    </svg>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  showRing?: boolean
  primaryColor?: string
  secondaryColor?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 64,
  showRing: true,
  primaryColor: '#667eea',
  secondaryColor: '#764ba2',
  class: ''
})

// 生成唯一ID避免渐变冲突
const uniqueId = Math.random().toString(36).substr(2, 9)

const ringSize = computed(() => {
  const logoSize = typeof props.size === 'number' ? props.size : parseInt(props.size.toString())
  return logoSize + 20
})

const mainLogoClass = computed(() => `brand-main-logo ${props.class}`)
const ringClass = computed(() => `brand-logo-ring ${props.class}`)
</script>

<style lang="scss" scoped>
.brand-logo-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.brand-main-logo {
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

.brand-logo-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  animation: rotate 30s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>