<template>
  <n-layout class="app-layout">
    <!-- 简化顶部导航栏 -->
    <n-layout-header class="header" bordered>
      <div class="header-container">
        <div class="header-left">
          <!-- Logo 或返回按钮 -->
          <div v-if="showBreadcrumb" class="breadcrumb-nav">
            <!-- 返回按钮 -->
            <n-button
              text
              class="header-back-button"
              @click="handleHeaderBack"
            >
              <n-icon size="18">
                <ArrowBackOutline />
              </n-icon>
              <span class="back-text">返回</span>
            </n-button>

            <!-- 面包屑导航 -->
            <div class="breadcrumb-items">
              <span
                v-for="(item, index) in breadcrumbItems"
                :key="item.key"
                class="breadcrumb-item"
                :class="{ 'clickable': item.clickable && index < breadcrumbItems.length - 1 }"
                @click="item.clickable && index < breadcrumbItems.length - 1 ? handleBreadcrumbClick(item) : undefined"
              >
                <n-icon v-if="item.icon" size="16" class="breadcrumb-icon">
                  <component :is="item.icon" />
                </n-icon>
                <span class="breadcrumb-text">{{ item.label }}</span>
                <n-icon v-if="index < breadcrumbItems.length - 1" size="14" class="breadcrumb-separator">
                  <ChevronForwardOutline />
                </n-icon>
              </span>
            </div>
          </div>

          <!-- Logo - 只在工作台显示 -->
          <div v-else class="logo">
            <div class="logo-icon">
              <img src="/favicon.svg" alt="CRF Editor" width="20" height="20" />
            </div>
            <div class="logo-content">
              <div class="logo-text">CRF表单编辑器</div>
            </div>
          </div>

          <!-- 主导航菜单 - 只在工作台显示 -->
          <div v-if="!showBreadcrumb" class="main-nav">
            <n-menu
              mode="horizontal"
              :value="activeMenuKey"
              :options="menuOptions"
              @update:value="handleMenuSelect"
            />
          </div>
        </div>
        
        <div class="header-right">
          <!--  s功能按钮区域 -->
          <div class="function-buttons">
            <!-- 语言切换按钮 - 暂时隐藏，后期再实现 -->
            <!-- 
            <n-dropdown
              :options="languageOptions"
              @select="handleLanguageChange"
              trigger="click"
              placement="bottom-end"
              :show-arrow="false"
            >
              <n-button text class="function-btn">
                <n-icon size="18">
                  <LanguageOutline />
                </n-icon>
              </n-button>
            </n-dropdown>
            -->
            
            <!-- 最大化/还原按钮 -->
            <n-button text class="function-btn" @click="toggleFullscreen">
              <n-icon size="18">
                <component :is="isFullscreen ? ContractOutline : ExpandOutline" />
              </n-icon>
            </n-button>
          </div>
          
          <!-- 用户信息区域 -->
          <div class="user-section">
            <n-dropdown
              :options="dropdownOptions"
              @select="handleUserCommand"
              trigger="click"
              placement="bottom-end"
              :show-arrow="false"
            >
              <div class="user-avatar-wrapper">
                <n-avatar
                  class="user-avatar"
                  :size="32"
                  :src="userAvatar"
                  :fallback-src="defaultDoctorAvatar"
                  @error="handleAvatarError"
                  round
                />
              </div>
            </n-dropdown>
          </div>
        </div>
      </div>
    </n-layout-header>
    
    <!-- 主要内容 -->
    <n-layout-content class="main-content">
      <router-view />
    </n-layout-content>
    
    <!-- 个人资料侧边栏 -->
    <ProfileSidebar v-model:visible="showProfileSidebar" />
  </n-layout>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import type { DropdownOption } from 'naive-ui'
import {
  DocumentTextOutline,
  ChevronDownOutline,
  LogOutOutline,
  PersonOutline,
  PeopleOutline,
  ShieldCheckmarkOutline,
  LanguageOutline,
  ExpandOutline,
  ContractOutline,
  GridOutline,
  ArrowBackOutline,
  ChevronForwardOutline,
  FolderOutline,
  SettingsOutline,
  ClipboardOutline,
  ListOutline,
  ReaderOutline,
  LayersOutline
} from '@vicons/ionicons5'
import { NIcon } from 'naive-ui'
import { h } from 'vue'
import { useUserStore } from '@/stores/user-store'
import { usePermissionStore } from '@/stores/permission-store'
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import ProfileSidebar from '@/components/profile/ProfileSidebar.vue'


defineOptions({
  name: 'AppLayout'
})

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const message = useMessage()
const dialog = useDialog()

// 侧边栏显示状态
const showProfileSidebar = ref(false)

// 面包屑导航显示状态
const showBreadcrumb = computed(() => {
  const path = route.path
  // 在工作台页面不显示面包屑
  return !path.startsWith('/dashboard') && !path.startsWith('/auth') && !path.startsWith('/editor')
})

// 面包屑导航项
interface BreadcrumbItem {
  key: string
  label: string
  path?: string
  icon?: any
  clickable?: boolean
}

const breadcrumbItems = ref<BreadcrumbItem[]>([])

// 处理 header 返回按钮点击
const handleHeaderBack = () => {
  const currentPath = route.path
  const query = route.query

  // 表单管理页面的返回逻辑
  if (currentPath === '/forms') {
    // 表单管理页面始终返回到工作台
    router.push('/dashboard')
    return
  }

  // 其他页面使用默认的浏览器返回
  router.back()
}

// 处理面包屑点击
const handleBreadcrumbClick = (item: BreadcrumbItem) => {
  if (item.path) {
    router.push(item.path)
  }
}

// 更新面包屑导航
const updateBreadcrumb = (currentRoute: any) => {
  const items: BreadcrumbItem[] = []
  const pathSegments = currentRoute.path.split('/').filter(Boolean)

  // 始终包含工作台
  items.push({
    key: 'dashboard',
    label: '工作台',
    path: '/dashboard',
    icon: GridOutline,
    clickable: true
  })

  if (pathSegments.length > 0) {
    switch (pathSegments[0]) {
      case 'projects':
        items.push({
          key: 'projects',
          label: '项目管理',
          path: '/projects',
          icon: FolderOutline,
          clickable: true
        })

        if (pathSegments[1]) {
          items.push({
            key: 'project-detail',
            label: currentRoute.meta?.projectName || '项目详情',
            icon: DocumentTextOutline,
            clickable: true,
            path: `/projects/${pathSegments[1]}`
          })

          // 如果是项目下的模板页面
          if (pathSegments[2] === 'templates') {
            items.push({
              key: 'project-templates',
              label: '模板管理',
              icon: DocumentTextOutline,
              clickable: false
            })
          }
        }
        break

      case 'forms':
        // 表单管理页面统一显示简化路径：工作台 > 表单管理
        items.push({
          key: 'forms',
          label: '表单管理',
          path: '/forms',
          icon: ClipboardOutline,
          clickable: false
        })

        // 表单数据页面
        if (pathSegments[2] === 'data') {
          items.push({
            key: 'form-data',
            label: '表单数据',
            icon: DocumentTextOutline,
            clickable: false
          })
        }
        break

      case 'admin':
        items.push({
          key: 'admin',
          label: '系统管理',
          path: '/admin',
          icon: SettingsOutline,
          clickable: true
        })

        if (pathSegments[1] === 'users') {
          items.push({
            key: 'admin-users',
            label: '用户管理',
            icon: PeopleOutline,
            clickable: false
          })
        } else if (pathSegments[1] === 'roles') {
          items.push({
            key: 'admin-roles',
            label: '角色管理',
            icon: ShieldCheckmarkOutline,
            clickable: false
          })
        }
        break
    }
  }

  breadcrumbItems.value = items
}

// 监听路由变化更新面包屑
watch(route, (newRoute) => {
  updateBreadcrumb(newRoute)
}, { immediate: true })

// 主导航菜单
const menuOptions = computed(() => [
  {
    label: '工作台',
    key: 'dashboard',
    icon: () => h(NIcon, null, { default: () => h(GridOutline) })
  }
])

// 当前激活的菜单项
const activeMenuKey = computed(() => {
  const path = route.path
  if (path.startsWith('/dashboard')) return 'dashboard'
  return 'dashboard' // 默认为工作台
})

// 菜单选择处理
const handleMenuSelect = (key: string) => {
  switch (key) {
    case 'dashboard':
      router.push('/dashboard')
      break
  }
}

// 头像相关状态
const avatarError = ref(false)

// 全屏状态
const isFullscreen = ref(false)

// 未读消息数量
const unreadCount = ref(5)

// 当前语言 - 暂时隐藏，后期再实现
// const currentLanguage = ref('zh-CN')

// 默认可爱医生头像
const defaultDoctorAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGRkY5RkMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNGRkRCQkYiLz4KPGVsbGlwc2UgY3g9IjMyIiBjeT0iNDgiIHJ4PSIxNiIgcnk9IjEyIiBmaWxsPSIjRkZEQkJGIi8+CjxjaXJjbGUgY3g9IjI3IiBjeT0iMjEiIHI9IjIiIGZpbGw9IiMzNzQxNTEiLz4KPGNpcmNsZSBjeD0iMzciIGN5PSIyMSIgcj0iMiIgZmlsbD0iIzM3NDE1MSIvPgo8cGF0aCBkPSJNMjggMjhRMzIgMzIgMzYgMjgiIHN0cm9rZT0iIzM3NDE1MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHJlY3QgeD0iMjgiIHk9IjEyIiB3aWR0aD0iOCIgaGVpZ2h0PSI0IiByeD0iMiIgZmlsbD0iIzM0RDM5OSIvPgo8Y2lyY2xlIGN4PSIzMiIgY3k9IjE0IiByPSIxLjUiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+'

// 计算属性：用户头像
const userAvatar = computed(() => {
  // 获取用户头像URL
  const avatarUrl = userStore.user?.avatar_url
  
  // 检查头像URL是否有效
  if (avatarUrl && avatarUrl.trim() !== '') {
    const trimmedUrl = avatarUrl.trim()
    
    // 如果已经是完整的HTTP/HTTPS URL，直接返回（MinIO返回的完整URL）
    if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
      return trimmedUrl
    }
    
    // 如果是绝对路径（以/开头），直接使用
    if (trimmedUrl.startsWith('/')) {
      return trimmedUrl
    }
    
    // 对于其他情况，假设是MinIO的相对路径，构建完整URL
    return trimmedUrl
  }
  
  // 没有有效的头像URL，返回null让n-avatar使用fallback
  return null
})

// 计算属性：用户显示名称（首字母）
const userDisplayName = computed(() => {
  const user = userStore.user
  if (!user) return '?'
  
  // 优先使用真实姓名的首字母
  if (user.full_name && user.full_name.trim()) {
    return user.full_name.trim()[0]
  }
  
  // 其次使用用户名的首字母
  if (user.username && user.username.trim()) {
    return user.username.trim()[0]
  }
  
  return '?'
})



// 语言选项 - 暂时隐藏，后期再实现
/*
const languageOptions = computed((): DropdownOption[] => [
  {
    label: '简体中文',
    key: 'zh-CN',
    icon: () => h('span', { style: 'font-size: 16px; margin-right: 8px;' }, '🇨🇳')
  },
  {
    label: 'English',
    key: 'en-US',
    icon: () => h('span', { style: 'font-size: 16px; margin-right: 8px;' }, '🇺🇸')
  }
])
*/

// 用户下拉菜单选项
const dropdownOptions = computed((): DropdownOption[] => {
  const options: DropdownOption[] = [
    {
      label: '个人资料',
      key: 'profile',
      icon: () => h(NIcon, null, { default: () => h(PersonOutline) })
    }
  ]
  
  // 用户管理
  if (userStore.hasPermission('user', 'read')) {
    options.push({
      type: 'divider',
      key: 'd1'
    })
    options.push({
      label: '用户管理',
      key: 'user-management',
      icon: () => h(NIcon, null, { default: () => h(PeopleOutline) })
    })
  }
  
  // 角色管理
  if (userStore.hasPermission('role', 'read')) {
    options.push({
      label: '角色管理',
      key: 'role-management',
      icon: () => h(NIcon, null, { default: () => h(ShieldCheckmarkOutline) })
    })
  }
  
  // 退出登录
  options.push({
    type: 'divider',
    key: 'd2'
  })
  options.push({
    label: '退出登录',
    key: 'logout',
    icon: () => h(NIcon, null, { default: () => h(LogOutOutline) })
  })
  
  return options
})

// 头像加载错误处理
const handleAvatarError = () => {
  console.log('用户头像加载失败，使用首字母显示')
  console.log('失败的头像URL:', userAvatar.value)
  avatarError.value = true
}

// 处理语言切换 - 暂时隐藏，后期再实现
/*
const handleLanguageChange = (key: string) => {
  currentLanguage.value = key
  localStorage.setItem('language', key)
  
  // 这里可以集成国际化库，如vue-i18n
  if (key === 'zh-CN') {
    message.success('已切换到简体中文')
  } else if (key === 'en-US') {
    message.success('Switched to English')
  }
  
  console.log('语言已切换到:', key)
}
*/

// 处理全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    // 进入全屏
    document.documentElement.requestFullscreen().then(() => {
      isFullscreen.value = true
      message.success('已进入全屏模式')
    }).catch((err) => {
      console.error('进入全屏失败:', err)
      message.error('进入全屏失败')
    })
  } else {
    // 退出全屏
    document.exitFullscreen().then(() => {
      isFullscreen.value = false
      message.success('已退出全屏模式')
    }).catch((err) => {
      console.error('退出全屏失败:', err)
      message.error('退出全屏失败')
    })
  }
}



// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 处理用户菜单命令
const handleUserCommand = async (key: string) => {
  switch (key) {
    case 'profile':
      // 显示个人资料侧边栏
      showProfileSidebar.value = true
      break
    case 'debug-permissions':
      // 调试权限信息
      debugPermissions()
      message.info('权限调试信息已输出到控制台')
      break
    case 'debug-avatar':
      // 调试头像信息
      debugAvatar()
      message.info('头像调试信息已输出到控制台')
      break
    case 'user-management':
      // 跳转到用户管理页面
      router.push('/admin/users')
      break
    case 'role-management':
      // 跳转到角色管理页面
      router.push('/admin/roles')
      break
    case 'logout':
      dialog.warning({
        title: '提示',
        content: '确定要退出登录吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
          await userStore.logout()
          message.success('已退出登录')
          router.push('/auth/login')
        }
      })
      break
  }
}

// 权限调试信息
const debugPermissions = () => {
  console.log('=== 权限调试信息 ===')
  console.log('当前用户:', userStore.user)
  console.log('用户角色:', permissionStore.userRoleNames)
  console.log('用户权限:', permissionStore.userPermissions)
  console.log('用户管理权限:', userStore.hasPermission('user', 'read'))
  console.log('角色管理权限:', userStore.hasPermission('role', 'read'))
  console.log('权限store错误:', permissionStore.error)
  console.log('==================')
}

// 头像调试信息
const debugAvatar = () => {
  console.log('=== 头像调试信息 ===')
  console.log('用户信息:', userStore.user)
  console.log('头像URL（原始）:', userStore.user?.avatar_url)
  console.log('头像URL（计算后）:', userAvatar.value)
  console.log('头像错误状态:', avatarError.value)
  console.log('用户显示名称:', userDisplayName.value)
  console.log('localStorage用户信息:', localStorage.getItem('user_info'))
  
  // 测试头像URL的可访问性
  if (userStore.user?.avatar_url) {
    const testImage = new Image()
    testImage.onload = () => {
      console.log('✅ 头像URL可以访问:', userStore.user?.avatar_url)
    }
    testImage.onerror = () => {
      console.log('❌ 头像URL无法访问:', userStore.user?.avatar_url)
    }
    testImage.src = userAvatar.value || ''
  }
  
  // 提供重新加载用户信息的方法
  console.log('提示：可以调用 await userStore.getCurrentUser() 来重新加载用户信息')
  console.log('提示：可以调用 avatarError.value = false 来重置头像错误状态')
  console.log('==================')
}



// 监听用户信息变化，重置头像错误状态
watch(() => userStore.user?.avatar_url, (newAvatarUrl, oldAvatarUrl) => {
  if (newAvatarUrl && newAvatarUrl !== oldAvatarUrl) {
    avatarError.value = false
    console.log('用户头像URL更新:', newAvatarUrl)
  }
})

// 组件挂载后检查权限
onMounted(() => {
  // 延迟调试，确保权限已加载
  setTimeout(debugPermissions, 1000)
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 从localStorage恢复语言设置 - 暂时隐藏，后期再实现
  /*
  const savedLanguage = localStorage.getItem('language')
  if (savedLanguage) {
    currentLanguage.value = savedLanguage
  }
  */
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style lang="scss" scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.header {
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: 55px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;

  .header-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
  }

  .header-left {
    display: flex;
    align-items: center;
    margin-left: 10px;
    gap: 32px;
    flex: 1;

    .breadcrumb-nav {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;

      .header-back-button {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        height: 36px;
        border-radius: 8px;
        color: #374151;
        font-weight: 600;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f3f4f6;
          color: #3b82f6;
        }

        &:active {
          background-color: #e5e7eb;
        }

        .back-text {
          font-size: 14px;
          font-weight: 600;
          white-space: nowrap;
        }
      }

      .breadcrumb-items {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .breadcrumb-item {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #6b7280;
          font-size: 14px;
          font-weight: 500;

          &.clickable {
            cursor: pointer;
            color: #3b82f6;

            &:hover {
              color: #2563eb;
            }
          }

          &:last-child {
            color: #1f2937;
            font-weight: 600;
          }

          .breadcrumb-icon {
            color: inherit;
          }

          .breadcrumb-text {
            white-space: nowrap;
          }

          .breadcrumb-separator {
            color: #d1d5db;
            margin: 0 4px;
          }
        }
      }
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      .logo-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          display: block;
          object-fit: contain;
        }
      }

      .logo-content {
        .logo-text {
          font-size: 18px;
          font-weight: 700;
          color: #1f2937;
          letter-spacing: -0.3px;
        }
      }
    }

    .main-nav {
      :deep(.n-menu) {
        background: transparent;

        .n-menu-item {
          height: 40px;
          border-radius: 6px;
          margin: 0 4px;

          &:hover {
            background: rgba(59, 130, 246, 0.1);
          }

          &.n-menu-item--selected {
            background: rgba(59, 130, 246, 0.15);
            color: #3b82f6;
            font-weight: 500;
          }
        }

        .n-menu-item-content {
          padding: 0 12px;

          .n-menu-item-content__icon {
            margin-right: 8px;
          }
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 10px;

    .search-section {
      .search-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 50px;
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        color: #64748b;
        font-size: 14px;
        transition: all 0.2s ease;
        min-width: 200px;
        justify-content: space-between;
        position: relative;

        &:hover {
          background-color: #f1f5f9;
          border-color: #cbd5e1;
          color: #475569;
        }

        .search-text {
          margin-left: 4px;
        }

        .search-shortcut {
          background-color: #e2e8f0;
          color: #64748b;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
          font-family: monospace;
          margin-left: auto;
        }
      }
    }

    .function-buttons {
      display: flex;
      align-items: center;
      gap: 8px;

      .function-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        color: #6b7280;

        &:hover {
          background-color: #f3f4f6;
          color: #374151;
        }

        &.notification-btn {
          position: relative;
        }
      }
    }

    .user-section {
      .user-avatar-wrapper {
        cursor: pointer;
        border-radius: 50%;
        transition: all 0.2s ease;
        padding: 2px;
        position: relative;

        &:hover {
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }

        .user-avatar {
          transition: all 0.2s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }
}

.main-content {
  background: #fff;
  overflow-y: auto;
  height: calc(100vh - 55px);
}

// Naive UI dropdown 样式覆盖
:deep(.n-dropdown-menu) {
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e5e7eb !important;
  padding: 8px !important;
  background: #ffffff !important;
}

:deep(.n-dropdown-option) {
  border-radius: 8px !important;
  margin: 2px 0 !important;
  padding: 10px 12px !important;
  transition: all 0.2s ease !important;
  
  &:hover {
    background-color: #f8fafc !important;
  }
}

:deep(.n-dropdown-option-body__icon) {
  margin-right: 10px !important;
  color: #6b7280 !important;
}

:deep(.n-dropdown-option-body__label) {
  font-size: 14px !important;
  color: #374151 !important;
  font-weight: 500 !important;
}

:deep(.n-dropdown-divider) {
  margin: 8px 0 !important;
  background-color: #f3f4f6 !important;
}

// 用户下拉菜单样式
:deep(.user-section .n-dropdown-menu) {
  min-width: 260px !important;
  width: 260px !important;
}

// 通知下拉菜单样式
:deep(.notification-btn .n-dropdown-menu) {
  min-width: 360px !important;
  width: 360px !important;
  max-height: 480px !important;
  overflow-y: auto !important;
}

// 通知选项样式
:deep(.notification-btn .n-dropdown-option) {
  padding: 14px 16px !important;
  
  .n-dropdown-option-body {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    
    .n-dropdown-option-body__label {
      flex: 1;
      font-size: 14px !important;
      line-height: 1.5 !important;
      color: #374151 !important;
    }
  }
}

// 通知特殊选项样式
:deep(.notification-btn .n-dropdown-option[data-key="clear-all"]) {
  border-top: 1px solid #f3f4f6 !important;
  margin-top: 8px !important;
  padding-top: 12px !important;
}

:deep(.notification-btn .n-dropdown-option[data-key="view-all"]) {
  background-color: #3b82f6 !important;
  color: white !important;
  
  .n-dropdown-option-body__label {
    color: white !important;
    font-weight: 600 !important;
  }
  
  &:hover {
    background-color: #2563eb !important;
  }
}

// 语言下拉菜单样式
:deep(.function-btn .n-dropdown-menu) {
  min-width: 180px !important;
  width: 180px !important;
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px;

    .header-left {
      margin-left: 0;
      gap: 16px;

      .logo .logo-text {
        display: none;
      }

      .breadcrumb-nav {
        gap: 12px;

        .header-back-button {
          padding: 6px 10px;
          height: 32px;

          .back-text {
            font-size: 13px;
          }
        }

        .breadcrumb-items {
          gap: 6px;

          .breadcrumb-item {
            font-size: 13px;

            .breadcrumb-text {
              max-width: 80px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }

    .nav-menu {
      gap: 16px;
    }

    .user-info .username {
      display: none;
    }
  }
}
</style>