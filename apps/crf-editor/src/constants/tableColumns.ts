import { h } from 'vue'
import { NIcon, NTag, NButton, NDropdown, NTooltip } from 'naive-ui'
import { 
  CreateOutline as Edit,
  DocumentTextOutline as Document,
  StatsChartOutline as DataLine,
  EllipsisHorizontalOutline as More,
  RefreshOutline as Restore,
  CopyOutline as Copy,
  TrashOutline as Delete
} from '@vicons/ionicons5'
import { iconComponents, adjustColorBrightness } from './icons'

// 表单状态映射
export const FORM_STATUS_MAP = {
  'draft': { text: '未发布', type: 'default' },
  'published': { text: '已发布', type: 'success' },
  'deleted': { text: '已删除', type: 'error' }
} as const

// 时间格式化函数
export const formatDetailTime = (time?: string) => {
  if (!time) return '-'
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 表格列定义函数 - 强制更新按钮样式
export const createTableColumns = (handlers: {
  handleEdit: (row: any) => void
  handleViewData: (row: any) => void
  handleRestoreForm: (row: any) => void
  getDropdownOptions: (row: any) => any[]
  handleCommand?: (cmd: string, row: any) => void
}) => [
  {
    type: 'selection',
    width: 50
  },
  {
    title: '表单',
    key: 'name',
    width: 300,
    render: (row: any) => {
      return h('div', { class: 'table-form-info' }, [
        h('div', { 
          class: 'table-form-icon', 
          style: { 
            background: `linear-gradient(135deg, ${row.iconColor || '#3b82f6'}, ${adjustColorBrightness(row.iconColor || '#3b82f6', -20)})` 
          } 
        }, [
          h('n-icon', { size: 20 }, { 
            default: () => h(iconComponents[row.icon] || iconComponents.FormOutlined) 
          })
        ]),
        h('div', { class: 'table-form-details' }, [
          h('div', { class: 'table-form-title' }, row.name || row.title),
          h('div', { class: 'table-form-description' }, row.description || '暂无')
        ])
      ])
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: any) => {
      const status = FORM_STATUS_MAP[row.status as keyof typeof FORM_STATUS_MAP] || FORM_STATUS_MAP['draft']
      return h('n-tag', {
        size: 'small',
        type: status.type
      }, { default: () => status.text })
    }
  },
  {
    title: '版本',
    key: 'version',
    width: 80,
    render: (row: any) => {
      return row.version ? h('n-tag', {
        size: 'small',
        bordered: false,
        class: 'version-tag'
      }, { default: () => `v${row.version}` }) : '-'
    }
  },
  {
    title: '更新时间',
    key: 'updated_at',
    width: 180,
    render: (row: any) => {
      return h('div', { class: 'table-time' }, [
        h('n-icon', { class: 'time-icon' }, { default: () => h(Edit) }),
        h('span', formatDetailTime(row.updated_at || row.created_at))
      ])
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: any) => {
      
      // 如果是已删除的表单，只显示恢复按钮
      if (row.status === 'deleted') {
        return h('div', { class: 'table-actions' }, [
          h(NTooltip, { trigger: 'hover', placement: 'top' }, {
            trigger: () => h(NButton, {
              circle: true,
              quaternary: true,
              type: 'primary',
              size: 'small',
              class: 'table-action-button',
              onClick: () => handlers.handleRestoreForm(row)
            }, {
              default: () => h(NIcon, { size: 16 }, { default: () => h(Restore) })
            }),
            default: () => '恢复表单'
          })
        ])
      }
      
      // 正常表单的操作 - 将常用操作直接显示
      return h('div', { class: 'table-actions' }, [
        // 编辑按钮
        h(NTooltip, { trigger: 'hover', placement: 'top' }, {
          trigger: () => h(NButton, {
            circle: true,
            quaternary: true,
            size: 'small',
            class: 'table-action-button',
            onClick: () => handlers.handleEdit(row)
          }, {
            default: () => h(NIcon, { size: 16 }, { default: () => h(Edit) })
          }),
          default: () => '编辑表单'
        }),
        // 查看数据按钮
        h(NTooltip, { trigger: 'hover', placement: 'top' }, {
          trigger: () => h(NButton, {
            circle: true,
            quaternary: true,
            size: 'small', 
            class: 'table-action-button',
            onClick: () => handlers.handleViewData(row)
          }, {
            default: () => h(NIcon, { size: 16 }, { default: () => h(DataLine) })
          }),
          default: () => '查看数据'
        }),
        // 复制按钮
        h(NTooltip, { trigger: 'hover', placement: 'top' }, {
          trigger: () => h(NButton, {
            circle: true,
            quaternary: true,
            size: 'small',
            class: 'table-action-button',
            onClick: () => handlers.handleCommand?.('duplicate', row)
          }, {
            default: () => h(NIcon, { size: 16 }, { default: () => h(Copy) })
          }),
          default: () => '复制表单'
        }),
        // 删除按钮
        h(NTooltip, { trigger: 'hover', placement: 'top' }, {
          trigger: () => h(NButton, {
            circle: true,
            quaternary: true,
            size: 'small',
            type: 'error',
            class: 'table-action-button',
            onClick: () => handlers.handleCommand?.('delete', row)
          }, {
            default: () => h(NIcon, { size: 16 }, { default: () => h(Delete) })
          }),
          default: () => '删除表单'
        }),
        // 更多操作（如果还有其他操作）
        handlers.getDropdownOptions(row).length > 2 ? h(NDropdown, {
          options: handlers.getDropdownOptions(row).filter(opt => !['duplicate', 'delete'].includes(opt.key)),
          onSelect: (cmd: string) => handlers.handleCommand?.(cmd, row)
        }, {
          default: () => h(NTooltip, { trigger: 'hover', placement: 'top' }, {
            trigger: () => h(NButton, {
              circle: true,
              quaternary: true,
              size: 'small',
              class: 'table-action-button'
            }, {
              default: () => h(NIcon, { size: 16 }, { default: () => h(More) })
            }),
            default: () => '更多操作'
          })
        }) : null
      ].filter(Boolean))
    }
  }
]

// 表格样式类名
export const TABLE_CLASSES = {
  formInfo: 'table-form-info',
  formIcon: 'table-form-icon',
  formDetails: 'table-form-details',
  formTitle: 'table-form-title',
  formDescription: 'table-form-description',
  tableTime: 'table-time',
  timeIcon: 'time-icon',
  versionTag: 'version-tag',
  tableActions: 'table-actions'
} as const