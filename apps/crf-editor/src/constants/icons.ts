// 图标组件映射，与 IconSelector 组件保持一致
import { 
  MedicineBoxOutlined, MonitorOutlined, UserOutlined, SafetyOutlined, ContactsOutlined, 
  IdcardOutlined, ProfileOutlined, FileOutlined, FileAddOutlined, FileTextOutlined, 
  FileExcelOutlined, FilePdfOutlined, FolderOutlined, FolderAddOutlined, FolderOpenOutlined, 
  BookOutlined, ReadOutlined, FormOutlined, EditOutlined, <PERSON>sOutlined, AppstoreOutlined, 
  MenuOutlined, SettingOutlined, ToolOutlined, KeyOutlined, LockOutlined, UnlockOutlined, 
  EyeOutlined, EyeInvisibleOutlined, SearchOutlined, FilterOutlined, SortAscendingOutlined,
  CheckOutlined, CloseOutlined, PlusOutlined, MinusOutlined, WarningOutlined, 
  InfoCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined,
  DownloadOutlined, UploadOutlined, ShareAltOutlined, CopyOutlined, DeleteOutlined, 
  ReloadOutlined, LeftOutlined, RightOutlined, UpOutlined, DownOutlined, SaveOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlined, <PERSON><PERSON><PERSON>Outlined, Bar<PERSON>hartOutlined, <PERSON>boardOutlined, DatabaseOutlined, 
  ControlOutlined, BuildOutlined, StarOutlined, BellOutlined, MessageOutlined, HomeOutlined, 
  EnvironmentOutlined, ClockCircleOutlined, DesktopOutlined, LinkOutlined, FullscreenOutlined, 
  AimOutlined, CompassOutlined, GlobalOutlined, CloudOutlined, HeartOutlined, LikeOutlined, 
  SmileOutlined
} from '@vicons/antd'

export const iconComponents: Record<string, any> = {
  // 医疗相关
  MedicineBoxOutlined, 
  MonitorOutlined, 
  UserOutlined, 
  SafetyOutlined, 
  ContactsOutlined, 
  IdcardOutlined, 
  ProfileOutlined,
  
  // 文件相关
  FileOutlined, 
  FileAddOutlined, 
  FileTextOutlined, 
  FileExcelOutlined, 
  FilePdfOutlined, 
  FolderOutlined, 
  FolderAddOutlined, 
  FolderOpenOutlined, 
  BookOutlined, 
  ReadOutlined, 
  FormOutlined,
  
  // 编辑相关
  EditOutlined, 
  BarsOutlined, 
  AppstoreOutlined, 
  MenuOutlined, 
  SettingOutlined, 
  ToolOutlined, 
  KeyOutlined, 
  LockOutlined, 
  UnlockOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined, 
  SearchOutlined, 
  FilterOutlined, 
  SortAscendingOutlined,
  
  // 状态相关
  CheckOutlined, 
  CloseOutlined, 
  PlusOutlined, 
  MinusOutlined, 
  WarningOutlined, 
  InfoCircleOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  QuestionCircleOutlined,
  
  // 操作相关
  DownloadOutlined, 
  UploadOutlined, 
  ShareAltOutlined, 
  CopyOutlined, 
  DeleteOutlined, 
  ReloadOutlined, 
  LeftOutlined, 
  RightOutlined, 
  UpOutlined, 
  DownOutlined, 
  SaveOutlined,
  
  // 图表相关
  LineChartOutlined, 
  PieChartOutlined, 
  BarChartOutlined, 
  DashboardOutlined, 
  DatabaseOutlined, 
  ControlOutlined, 
  BuildOutlined,
  
  // 其他常用
  StarOutlined, 
  BellOutlined, 
  MessageOutlined, 
  HomeOutlined, 
  EnvironmentOutlined, 
  ClockCircleOutlined, 
  DesktopOutlined, 
  LinkOutlined, 
  FullscreenOutlined, 
  AimOutlined, 
  CompassOutlined, 
  GlobalOutlined, 
  CloudOutlined, 
  HeartOutlined, 
  LikeOutlined, 
  SmileOutlined,
  
  // 添加默认图标映射
  Document: FormOutlined
}

// 默认图标配置
export const DEFAULT_FORM_ICON = 'FormOutlined'
export const DEFAULT_FORM_COLOR = '#3b82f6'

// 图标分类
export const ICON_CATEGORIES = {
  medical: ['MedicineBoxOutlined', 'MonitorOutlined', 'SafetyOutlined'],
  user: ['UserOutlined', 'ContactsOutlined', 'IdcardOutlined', 'ProfileOutlined'],
  file: ['FileOutlined', 'FileAddOutlined', 'FileTextOutlined', 'FileExcelOutlined', 'FilePdfOutlined', 'FormOutlined'],
  folder: ['FolderOutlined', 'FolderAddOutlined', 'FolderOpenOutlined'],
  document: ['BookOutlined', 'ReadOutlined'],
  edit: ['EditOutlined', 'BarsOutlined', 'AppstoreOutlined', 'MenuOutlined'],
  setting: ['SettingOutlined', 'ToolOutlined', 'ControlOutlined', 'BuildOutlined'],
  security: ['KeyOutlined', 'LockOutlined', 'UnlockOutlined', 'EyeOutlined', 'EyeInvisibleOutlined'],
  search: ['SearchOutlined', 'FilterOutlined', 'SortAscendingOutlined'],
  status: ['CheckOutlined', 'CloseOutlined', 'WarningOutlined', 'InfoCircleOutlined', 'CheckCircleOutlined', 'CloseCircleOutlined', 'QuestionCircleOutlined'],
  action: ['PlusOutlined', 'MinusOutlined', 'DownloadOutlined', 'UploadOutlined', 'ShareAltOutlined', 'CopyOutlined', 'DeleteOutlined', 'ReloadOutlined', 'SaveOutlined'],
  navigation: ['LeftOutlined', 'RightOutlined', 'UpOutlined', 'DownOutlined'],
  chart: ['LineChartOutlined', 'PieChartOutlined', 'BarChartOutlined', 'DashboardOutlined', 'DatabaseOutlined'],
  common: ['StarOutlined', 'BellOutlined', 'MessageOutlined', 'HomeOutlined', 'EnvironmentOutlined', 'ClockCircleOutlined', 'DesktopOutlined', 'LinkOutlined', 'FullscreenOutlined', 'AimOutlined', 'CompassOutlined', 'GlobalOutlined', 'CloudOutlined', 'HeartOutlined', 'LikeOutlined', 'SmileOutlined']
}

// 获取图标组件
export const getIconComponent = (iconName?: string) => {
  return iconComponents[iconName || DEFAULT_FORM_ICON] || iconComponents[DEFAULT_FORM_ICON]
}

// 颜色调整工具函数
export const adjustColorBrightness = (color: string, amount: number) => {
  const usePound = color[0] === '#'
  const col = usePound ? color.slice(1) : color
  const num = parseInt(col, 16)
  let r = (num >> 16) + amount
  let g = (num >> 8 & 0x00FF) + amount
  let b = (num & 0x0000FF) + amount
  r = r > 255 ? 255 : r < 0 ? 0 : r
  g = g > 255 ? 255 : g < 0 ? 0 : g
  b = b > 255 ? 255 : b < 0 ? 0 : b
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
}