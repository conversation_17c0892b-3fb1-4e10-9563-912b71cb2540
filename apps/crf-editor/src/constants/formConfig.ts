// 表单模式枚举
export enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview'
}

// 表单状态类型
export type FormStatus = 'draft' | 'published' | 'deleted'

// 表单状态相关常量
export const FORM_STATUS = {
  DRAFT: 'draft' as const,
  PUBLISHED: 'published' as const,
  DELETED: 'deleted' as const
}

// 表单状态显示配置
export const FORM_STATUS_CONFIG = {
  [FORM_STATUS.DRAFT]: {
    label: '未发布',
    type: 'warning' as const,
    color: '#f59e0b'
  },
  [FORM_STATUS.PUBLISHED]: {
    label: '已发布',
    type: 'success' as const,
    color: '#22c55e'
  },
  [FORM_STATUS.DELETED]: {
    label: '已删除',
    type: 'error' as const,
    color: '#ef4444'
  }
}

// 获取状态类型
export const getStatusType = (status: FormStatus) => {
  return FORM_STATUS_CONFIG[status]?.type || FORM_STATUS_CONFIG[FORM_STATUS.DRAFT].type
}

// 获取状态标签
export const getStatusLabel = (status: FormStatus) => {
  return FORM_STATUS_CONFIG[status]?.label || FORM_STATUS_CONFIG[FORM_STATUS.DRAFT].label
}

// 获取状态颜色
export const getStatusColor = (status: FormStatus) => {
  return FORM_STATUS_CONFIG[status]?.color || FORM_STATUS_CONFIG[FORM_STATUS.DRAFT].color
}

// 视图模式类型
export type ViewMode = 'grid' | 'list'

// 视图模式常量
export const VIEW_MODE = {
  GRID: 'grid' as const,
  LIST: 'list' as const
}

// 标签页类型
export type TabType = 'published' | 'unpublished' | 'deleted' | 'all'

// 标签页常量
export const TAB_TYPES = {
  PUBLISHED: 'published' as const,
  UNPUBLISHED: 'unpublished' as const,
  DELETED: 'deleted' as const,
  ALL: 'all' as const
}

// 标签页配置
export const TAB_CONFIG = {
  [TAB_TYPES.PUBLISHED]: {
    label: '已发布',
    color: '#22c55e',
    backgroundColor: '#f0fdf4',
    borderColor: '#22c55e'
  },
  [TAB_TYPES.UNPUBLISHED]: {
    label: '未发布',
    color: '#f59e0b',
    backgroundColor: '#fffbeb',
    borderColor: '#f59e0b'
  },
  [TAB_TYPES.DELETED]: {
    label: '已删除',
    color: '#ef4444',
    backgroundColor: '#fef2f2',
    borderColor: '#ef4444'
  },
  [TAB_TYPES.ALL]: {
    label: '全部',
    color: '#64748b',
    backgroundColor: '#f8fafc',
    borderColor: '#64748b'
  }
}

// 创建表单默认配置
export const CREATE_FORM_DEFAULT = {
  project_id: 'default',
  name: '',
  title: '',
  description: '',
  icon: 'FormOutlined',
  iconColor: '#3b82f6',
  keyword: '',
  version: '1.0.0',
  template_type: 'custom_form' as const,
  source_type: 'user_created' as const,
  template_data: {
    page_config: {},
    form_structure: [],
    component_configs: {},
    validation_rules: {},
    style_config: {}
  },
  permissions: {}
}

// 表单验证规则
export const FORM_VALIDATION_RULES = {
  name: [
    { required: true, message: '请输入表单名称', trigger: 'blur' },
    { min: 1, max: 50, message: '表单名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '表单描述不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
}

// 搜索配置
export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 1,
  MAX_SEARCH_LENGTH: 100
}

// 动画配置
export const ANIMATION_CONFIG = {
  TAB_SWITCH_DURATION: 300,
  HIGHLIGHT_DURATION: 2000,
  OPERATION_DELAY: {
    SUCCESS_MESSAGE: 100,
    COMPLETION: 600,
    HIGHLIGHT: 800
  }
}

// 错误消息配置
export const ERROR_MESSAGES = {
  FORM_NAME_EXISTS: '表单名称已存在，请使用其他名称',
  FORM_NAME_REQUIRED: '请输入表单名称',
  FORM_DELETE_HAS_INSTANCES: '该表单包含填写数据，无法删除。请先清理相关数据后再尝试删除。',
  FORM_NOT_PUBLISHED: '表单需要先发布后才能进行填写',
  NETWORK_ERROR: '网络错误，请稍后重试',
  UNKNOWN_ERROR: '未知错误，请稍后重试'
}

// 成功消息配置
export const SUCCESS_MESSAGES = {
  FORM_CREATED: '表单创建成功',
  FORM_DUPLICATED: '表单复制成功，已跳转到未发布列表',
  FORM_DELETED: '删除成功',
  BATCH_DELETE: '批量删除成功'
}

// 警告消息配置
export const WARNING_MESSAGES = {
  FORM_NOT_PUBLISHED_FILL: '⚠️ 表单需要先发布后才能进行填写'
}