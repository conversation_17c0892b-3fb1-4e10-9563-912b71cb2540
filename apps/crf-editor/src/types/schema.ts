/**
 * CRF 表单编辑器 Schema 类型定义
 */

// 基础类型
export type ComponentType = 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'number' | 'layout' | 'divider' | 'title'

// 验证规则类型
export interface ValidationRule {
    type?: 'required' | 'pattern' | 'min' | 'max' | 'minLength' | 'maxLength' | 'custom'
    message: string
    value?: unknown
    validator?: (value: unknown) => boolean | Promise<boolean>
}

// 组件属性基础接口
export interface BaseComponentProps {
    id: string
    label?: string
    placeholder?: string
    required?: boolean
    disabled?: boolean
    readonly?: boolean
    visible?: boolean
    description?: string
    defaultValue?: unknown
    rules?: ValidationRule[]
    style?: Record<string, unknown>
    className?: string
}

// 文本输入组件属性
export interface TextComponentProps extends BaseComponentProps {
    type: 'text'
    maxLength?: number
    minLength?: number
    pattern?: string
    showWordLimit?: boolean
}

// 多行文本组件属性
export interface TextareaComponentProps extends BaseComponentProps {
    type: 'textarea'
    rows?: number
    maxLength?: number
    minLength?: number
    autosize?: boolean | { minRows?: number; maxRows?: number }
    showWordLimit?: boolean
}

// 选择器组件属性
export interface SelectComponentProps extends BaseComponentProps {
    type: 'select'
    options: Array<{ label: string; value: unknown; disabled?: boolean }>
    multiple?: boolean
    clearable?: boolean
    filterable?: boolean
    allowCreate?: boolean
    remote?: boolean
    remoteMethod?: string
}

// 单选框组件属性
export interface RadioComponentProps extends BaseComponentProps {
    type: 'radio'
    options: Array<{ label: string; value: unknown; disabled?: boolean }>
    direction?: 'horizontal' | 'vertical'
}

// 复选框组件属性
export interface CheckboxComponentProps extends BaseComponentProps {
    type: 'checkbox'
    options: Array<{ label: string; value: unknown; disabled?: boolean }>
    direction?: 'horizontal' | 'vertical'
    min?: number
    max?: number
}

// 日期组件属性
export interface DateComponentProps extends BaseComponentProps {
    type: 'date'
    format?: string
    valueFormat?: string
    rangeSeparator?: string
    startPlaceholder?: string
    endPlaceholder?: string
    dateType?: 'date' | 'datetime' | 'daterange' | 'datetimerange' | 'month' | 'year'
}

// 数字输入组件属性
export interface NumberComponentProps extends BaseComponentProps {
    type: 'number'
    min?: number
    max?: number
    step?: number
    precision?: number
    controls?: boolean
    controlsPosition?: 'right' | ''
}

// 布局组件属性
export interface LayoutComponentProps extends BaseComponentProps {
    type: 'layout'
    columns: number
    gutter?: number
    justify?: 'start' | 'end' | 'center' | 'space-around' | 'space-between'
    align?: 'top' | 'middle' | 'bottom'
    children: ComponentBlock[][]
}

// 分割线组件属性
export interface DividerComponentProps extends BaseComponentProps {
    type: 'divider'
    direction?: 'horizontal' | 'vertical'
    contentPosition?: 'left' | 'center' | 'right'
    borderStyle?: 'solid' | 'dashed' | 'dotted'
}

// 标题组件属性
export interface TitleComponentProps extends BaseComponentProps {
    type: 'title'
    level?: 1 | 2 | 3 | 4 | 5 | 6
    content: string
    align?: 'left' | 'center' | 'right'
}

// 组件属性联合类型
export type ComponentProps =
    | TextComponentProps
    | TextareaComponentProps
    | SelectComponentProps
    | RadioComponentProps
    | CheckboxComponentProps
    | DateComponentProps
    | NumberComponentProps
    | LayoutComponentProps
    | DividerComponentProps
    | TitleComponentProps

// 组件块定义
export interface ComponentBlock {
    id: string
    code: ComponentType
    name: string
    props: ComponentProps
    children?: ComponentBlock[]
    parentId?: string
    sort?: number
    createTime?: number
    updateTime?: number
}

// 表单分组定义
export interface FormSection {
    id: string
    name: string
    description?: string
    blocks: ComponentBlock[]
    collapsed?: boolean
    sort?: number
}

// 全局配置
export interface GlobalConfig {
    title: string
    description?: string
    submitText?: string
    resetText?: string
    showSubmit?: boolean
    showReset?: boolean
    labelWidth?: string
    labelPosition?: 'left' | 'right' | 'top'
    size?: 'large' | 'default' | 'small'
    disabled?: boolean
    validateOnRuleChange?: boolean
    hideRequiredAsterisk?: boolean
    showMessage?: boolean
    inlineMessage?: boolean
    statusIcon?: boolean
    theme?: 'light' | 'dark'
    customCSS?: string
}

// 表单 Schema 主结构
export interface FormSchema {
    version: string
    id: string
    title: string
    description?: string
    sections: FormSection[]
    globalConfig: GlobalConfig
    createTime: number
    updateTime: number
    author?: string
    tags?: string[]
    status?: 'draft' | 'published' | 'archived'
}

// 组件配置 Schema
export interface ComponentConfigSchema {
    code: ComponentType
    name: string
    description: string
    icon: string
    iconColor?: string
    category: string
    tags?: string[]
    defaultProps: Partial<ComponentProps>
    configSchema: ConfigFieldSchema[]
    preview?: string
}

// 配置字段 Schema
export interface ConfigFieldSchema {
    key: string
    label: string
    type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'switch' | 'number' | 'color' | 'slider'
    defaultValue?: unknown
    options?: Array<{ label: string; value: unknown }>
    rules?: ValidationRule[]
    description?: string
    placeholder?: string
    visible?: boolean | ((props: Record<string, unknown>) => boolean)
    disabled?: boolean | ((props: Record<string, unknown>) => boolean)
    group?: string
}

// 表单数据类型
export interface FormData {
    [key: string]: unknown
}

// 表单验证结果
export interface ValidationResult {
    valid: boolean
    errors: Array<{
        field: string
        message: string
        value: unknown
    }>
}

// 导出/导入配置
export interface ExportConfig {
    format: 'json' | 'yaml' | 'xml'
    includeData?: boolean
    includeConfig?: boolean
    compress?: boolean
}

export interface ImportConfig {
    format: 'json' | 'yaml' | 'xml'
    merge?: boolean
    overwrite?: boolean
    validate?: boolean
}

// 编辑器状态
export interface EditorState {
    currentSchema: FormSchema
    selectedComponent?: ComponentBlock
    selectedSection?: FormSection
    clipboard?: ComponentBlock
    history: FormSchema[]
    historyIndex: number
    isDirty: boolean
    isPreview: boolean
}

// 事件类型
export interface EditorEvent {
    type: 'component-select' | 'component-add' | 'component-delete' | 'component-update' | 'section-add' | 'section-delete' | 'schema-update'
    payload: unknown
    timestamp: number
}