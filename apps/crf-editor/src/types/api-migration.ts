/**
 * API迁移过程中的临时类型定义
 * 用于解决新旧架构的类型兼容性问题
 */

import type { APIResponse } from '@crf/network'

// 临时类型映射，用于向后兼容
export type HttpResponse<T> = APIResponse<T>

// 用户相关类型
export interface User {
  id: string
  username: string
  full_name?: string
  email?: string
  avatar_url?: string
  role?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

// 响应包装类型
export interface UserProfileResponse {
  user: User
}

export interface PasswordChangeResponse {
  message: string
}

// 模板相关类型
export interface Template {
  id: string
  name: string
  description?: string
  schema: Record<string, any>
  version: number
  is_published: boolean
  created_at: string
  updated_at: string
  [key: string]: any
}

// 项目相关类型
export interface Project {
  id: string
  name: string
  description?: string
  created_at: string
  updated_at: string
  [key: string]: any
}

// 表单结构类型
export interface FormStructure {
  sections?: any[]
  [key: string]: any
}

// 页面配置类型
export interface PageConfig {
  [key: string]: any
}

// 样式配置类型
export interface StyleConfig {
  [key: string]: any
}

// 版本信息类型
export interface VersionInfo {
  version: number
  [key: string]: any
}

// 通用对象类型，用于临时解决unknown类型问题
export type AnyObject = Record<string, any>

// 类型断言工具函数
export function assertUser(data: unknown): User {
  return data as User
}

export function assertTemplate(data: unknown): Template {
  return data as Template
}

export function assertProject(data: unknown): Project {
  return data as Project
}

export function assertFormStructure(data: unknown): FormStructure {
  return data as FormStructure
}

export function assertPageConfig(data: unknown): PageConfig {
  return data as PageConfig
}

export function assertStyleConfig(data: unknown): StyleConfig {
  return data as StyleConfig
}

export function assertVersionInfo(data: unknown): VersionInfo {
  return data as VersionInfo
}

export function assertAnyObject(data: unknown): AnyObject {
  return data as AnyObject
}

// 字符串断言工具
export function assertString(value: unknown): string {
  return String(value || '')
}

// 对象属性安全访问
export function safeGet<T = any>(obj: unknown, key: string, defaultValue?: T): T {
  const o = obj as any
  return o && typeof o === 'object' && key in o ? o[key] : defaultValue
}

// 数组断言工具
export function assertArray<T = any>(value: unknown): T[] {
  return Array.isArray(value) ? value : []
}

// 布尔值断言工具
export function assertBoolean(value: unknown): boolean {
  return Boolean(value)
}

// 数字断言工具
export function assertNumber(value: unknown): number {
  const num = Number(value)
  return isNaN(num) ? 0 : num
}