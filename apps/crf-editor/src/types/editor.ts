// 编辑器类型定义

// 覆盖层类型枚举
export enum OverlayType {
  WARNING = 'warning',
  SUCCESS = 'success',
  ERROR = 'error',
  LOADING = 'loading',
  INFO = 'info'
}

export interface BaseBlock {
  id: string
  type: string
  title: string
  description?: string
  required?: boolean
  readonly?: boolean
  visible?: boolean
  helpText?: string
  [key: string]: unknown
}

export interface FormSection {
  id: string
  name: string
  blocks: BaseBlock[]
  children?: FormSection[]
  title?: string
  [key: string]: unknown
}

export interface EditorState {
  mode: 'edit' | 'preview' | 'publish'
  isSaved: boolean
  showOverlay: boolean
  overlayMessage: string
  overlayType: 'loading' | 'error' | 'success' | 'warning'
  canExit: boolean
  canContinue: boolean
}

// CRF版本类型定义
export interface CRFVersion {
  id: string
  template_id: string
  title: string
  version: string
  description?: string
  change_log?: string
  snapshot_data: Record<string, unknown>
  status: 'draft' | 'published' | 'archived'
  created_at: string
  updated_at: string
}