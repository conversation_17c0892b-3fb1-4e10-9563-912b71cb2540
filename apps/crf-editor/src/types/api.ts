// API Response Types
export interface APIResponse<T = unknown> {
  success?: boolean
  data?: T
  message?: string
  status?: string
  timestamp?: number
}

export interface APIError {
  error: string
  message?: string
  status_code?: number
  details?: Record<string, unknown>
}

export interface PaginationInfo {
  total: number
  limit: number
  offset: number
  has_next?: boolean
  has_prev?: boolean
}

// User Types
export interface User {
  id: string
  username: string
  email: string
  full_name?: string
  role: string
  avatar_url?: string
  is_active: boolean
  // 新增多种登录方式支持
  phone?: string
  wechat_open_id?: string
  wechat_union_id?: string
  login_type: 'password' | 'sms' | 'wechat'
  created_at: string
  updated_at: string
}

// Project Types
export interface Project {
  id: string
  name: string
  description?: string
  created_by: string
  status: 'draft' | 'active' | 'completed' | 'archived'
  settings?: Record<string, unknown>
  created_at: string
  updated_at: string
  creator?: User
}

// Template Types
export interface CRFTemplate {
  id: string
  project_id: string
  name: string
  title: string
  description?: string
  keyword?: string
  version: string
  status: 'draft' | 'published' | 'archived'
  // 模板类型：区分标准模板和自定义表单
  template_type: 'template' | 'custom_form'
  // 模板来源：系统预置、用户创建、导入等
  source_type: 'system_preset' | 'user_created' | 'imported' | 'copied'
  // 是否为公共模板（可被其他用户使用）
  is_public: boolean
  // 统一的模板数据存储（替代多个分散的字段）
  template_data: {
    pageConfig?: Record<string, unknown>
    formStructure?: Record<string, unknown>
    componentConfigs?: Record<string, unknown>
    validationRules?: Record<string, unknown>
    styleConfig?: Record<string, unknown>
    [key: string]: unknown
  }
  permissions?: Record<string, unknown>
  // 标签，便于分类和搜索
  tags?: string
  // 使用统计
  usage_count: number
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  project?: Project
  creator?: User
  updater?: User
}

export interface CRFVersion {
  id: string
  template_id: string
  version: string
  title: string
  description?: string
  snapshot_data: Record<string, unknown>
  change_log?: string
  status: 'draft' | 'published' | 'archived'
  published_at?: string
  published_by?: string
  created_by: string
  created_at: string
  template?: CRFTemplate
  creator?: User
  publisher?: User
}

// Instance Types
export interface CRFInstance {
  id: string
  template_id: string
  template_version: string
  instance_name?: string
  subject_id?: string
  visit_id?: string
  form_data?: Record<string, unknown>
  validation_results?: Record<string, unknown>
  status: 'draft' | 'in_progress' | 'completed' | 'locked' | 'rejected'
  completion_percentage: number
  locked_by?: string
  locked_at?: string
  reviewed_by?: string
  reviewed_at?: string
  review_comment?: string
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  template?: CRFTemplate
  creator?: User
  updater?: User
  locker?: User
  reviewer?: User
}

// Auto-save Types
export interface AutoSave {
  id: string
  user_id: string
  resource_type: string
  resource_id: string
  save_data: Record<string, unknown>
  saved_at: string
  expires_at: string
  user?: User
}

// History Types
export interface OperationHistory {
  id: string
  user_id: string
  resource_type: string
  resource_id: string
  action: string
  description?: string
  before_data?: Record<string, unknown>
  after_data?: Record<string, unknown>
  client_info?: Record<string, unknown>
  created_at: string
  user?: User
}

// System Types
export interface SystemSetting {
  id: string
  key: string
  value: unknown
  description?: string
  created_at: string
  updated_at: string
}

// Session Types
export interface UserSession {
  id: string
  user_id: string
  session_token: string
  ip_address?: string
  user_agent?: string
  created_at: string
  expires_at: string
  last_activity: string
  user?: User
}