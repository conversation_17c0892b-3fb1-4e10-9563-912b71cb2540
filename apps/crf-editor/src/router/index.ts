import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user-store'
import { createDiscreteApi } from 'naive-ui'

// 布局组件
import AppLayout from '@/layouts/AppLayout.vue'

// 页面组件
import LoginPage from '@/pages/auth/login.vue'
import RegisterPage from '@/pages/auth/register.vue'
import EditorPage from '@/pages/editor/index.vue'
import ProjectsPage from '@/pages/projects/index.vue'

import InstancesPage from '@/pages/instances/index.vue'
import FormFillIndexPage from '@/pages/forms/form-fill-index.vue'
import FormFillEditPage from '@/pages/forms/edit.vue'
import FormFillViewPage from '@/pages/forms/view.vue'

// 管理页面组件
import AdminIndexPage from '@/pages/admin/index.vue'
import AdminUsersPage from '@/pages/admin/users.vue'
import AdminRolesPage from '@/pages/admin/roles.vue'

const router = createRouter({
    history: createWebHistory(),
    routes: [
        // 重定向到工作台
        {
            path: '/',
            redirect: '/dashboard'
        },

        // 认证相关路由
        {
            path: '/auth',
            children: [
                {
                    path: 'login',
                    name: 'Login',
                    component: LoginPage,
                    meta: {
                        title: '登录 - CRF表单编辑器',
                        requiresAuth: false,
                        hideForAuth: true
                    }
                },
                {
                    path: 'register',
                    name: 'Register',
                    component: RegisterPage,
                    meta: {
                        title: '注册 - CRF表单编辑器',
                        requiresAuth: false,
                        hideForAuth: true
                    }
                }
            ]
        },

        // 主应用路由
        {
            path: '/',
            component: AppLayout,
            meta: { requiresAuth: true },
            children: [
                {
                    path: 'dashboard',
                    name: 'Dashboard',
                    component: () => import('@/pages/dashboard/index.vue'),
                    meta: {
                        title: '工作台 - CRF表单编辑器'
                    }
                },
                {
                    path: 'projects',
                    name: 'Projects',
                    component: ProjectsPage,
                    meta: {
                        title: '项目管理 - CRF表单编辑器'
                    }
                },
                {
                    path: 'projects/:id',
                    name: 'ProjectDetail',
                    component: () => import('@/pages/projects/[id]/index.vue'),
                    meta: {
                        title: '项目详情 - CRF表单编辑器'
                    }
                },
                {
                    path: 'projects/:id/templates',
                    name: 'ProjectTemplates',
                    component: () => import('@/pages/projects/[id]/templates.vue'),
                    meta: {
                        title: '项目模板 - CRF表单编辑器'
                    }
                },

                {
                    path: 'forms',
                    name: 'Forms',
                    component: () => import('@/pages/forms/index.vue'),
                    meta: {
                        title: '表单管理 - CRF表单编辑器'
                    }
                },
                {
                    path: 'forms/:id/data',
                    name: 'FormData',
                    component: () => import('@/pages/forms/[id]/data.vue'),
                    meta: {
                        title: '表单数据 - CRF表单编辑器'
                    }
                },
                {
                    path: 'instances',
                    name: 'Instances',
                    component: InstancesPage,
                    meta: {
                        title: '数据管理 - CRF表单编辑器'
                    }
                },
                {
                    path: 'form-fill',
                    name: 'FormFillIndex',
                    component: FormFillIndexPage,
                    meta: {
                        title: '表单填写 - CRF表单编辑器'
                    }
                },
                // 管理相关路由
                {
                    path: 'admin',
                    name: 'Admin',
                    component: AdminIndexPage,
                    meta: {
                        title: '系统管理 - CRF表单编辑器',
                        requiresPermission: {
                            resource: 'user',
                            action: 'read'
                        }
                    }
                },
                {
                    path: 'admin/users',
                    name: 'AdminUsers',
                    component: AdminUsersPage,
                    meta: {
                        title: '用户管理 - CRF表单编辑器',
                        requiresPermission: {
                            resource: 'user',
                            action: 'read'
                        }
                    }
                },
                {
                    path: 'admin/roles',
                    name: 'AdminRoles',
                    component: AdminRolesPage,
                    meta: {
                        title: '角色管理 - CRF表单编辑器',
                        requiresPermission: {
                            resource: 'role',
                            action: 'read'
                        }
                    }
                },
                {
                    path: 'settings',
                    name: 'Settings',
                    component: () => import('@/pages/settings/index.vue'),
                    meta: {
                        title: '系统设置 - CRF表单编辑器'
                    }
                },
                {
                    path: 'user/profile',
                    name: 'UserProfile',
                    component: () => import('@/pages/user/profile.vue'),
                    meta: {
                        title: '个人资料 - CRF表单编辑器'
                    }
                }
            ]
        },

        // 编辑器页面（全屏显示）
        {
            path: '/editor',
            name: 'Editor',
            component: EditorPage,
            meta: {
                title: 'CRF表单编辑器',
                requiresAuth: true
            }
        },

        // 表单填写相关页面（需要登录）
        {
            path: '/form-fill/edit/:id',
            name: 'FormFillEdit',
            component: FormFillEditPage,
            meta: {
                title: '填写表单 - CRF表单编辑器',
                requiresAuth: true
            }
        },
        {
            path: '/form-fill/view/:id',
            name: 'FormFillView',
            component: FormFillViewPage,
            meta: {
                title: '查看表单 - CRF表单编辑器',
                requiresAuth: true
            }
        },

        // 表单填写页面（公开访问）
        {
            path: '/forms/:id/fill',
            name: 'FormFill',
            component: () => import('@/pages/forms/fill.vue'),
            meta: {
                title: '填写表单 - CRF表单编辑器',
                requiresAuth: false // 允许匿名用户填写
            }
        },

        // 404 页面
        {
            path: '/:pathMatch(.*)*',
            name: 'NotFound',
            component: () => import('@/pages/error/404.vue'),
            meta: {
                title: '页面未找到'
            }
        }
    ]
})

// 创建独立的 message API（用于在 setup 外部使用）
const { message } = createDiscreteApi(['message'])

// 路由守卫
router.beforeEach(async (to, _from, next) => {
    const userStore = useUserStore()

    console.log('路由守卫检查:', {
        路径: to.path,
        需要认证: !!to.meta?.requiresAuth,
        需要权限: !!to.meta?.requiresPermission,
        当前登录状态: userStore.isLoggedIn,
        用户信息: !!userStore.user,
        Token存在: !!userStore.token,
        本地Token存在: !!localStorage.getItem('auth_token')
    })

    // 设置页面标题
    if (to.meta?.title) {
        document.title = to.meta.title as string
    }

    // 如果用户已登录但访问登录/注册页面，重定向到工作台
    if (to.meta?.hideForAuth && userStore.isLoggedIn) {
        console.log('已登录用户访问登录页，重定向到工作台')
        next('/dashboard')
        return
    }

    // 检查是否需要认证
    if (to.meta?.requiresAuth) {
        // 检查是否已登录
        if (!userStore.isLoggedIn) {
            console.log('用户未登录，尝试初始化认证状态')
            // 尝试从本地存储恢复认证状态
            await userStore.initAuth()

            console.log('初始化后的登录状态:', userStore.isLoggedIn)

            // 如果初始化后仍未登录，重定向到登录页
            if (!userStore.isLoggedIn) {
                console.log('认证失败，重定向到登录页')
                next('/auth/login')
                return
            }
        }

        // 检查权限要求
        if (to.meta?.requiresPermission && typeof to.meta.requiresPermission === 'object') {
            const permission = to.meta.requiresPermission as { resource: string; action: string; scope?: string }
            const hasPermission = userStore.hasPermission(
                permission.resource,
                permission.action,
                permission.scope || 'global'
            )

            console.log('权限检查:', {
                需要权限: permission,
                拥有权限: hasPermission,
                用户角色: userStore.user
            })

            if (!hasPermission) {
                console.log('权限不足，重定向到工作台')
                message.error('您没有访问此页面的权限')
                next('/dashboard')
                return
            }
        }
    }

    console.log('路由守卫通过，允许访问')
    next()
})

export default router